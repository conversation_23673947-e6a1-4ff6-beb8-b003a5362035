{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar defaultOption = {\n  show: true,\n  // zlevel: 0,\n  z: 0,\n  // Inverse the axis.\n  inverse: false,\n  // Axis name displayed.\n  name: '',\n  // 'start' | 'middle' | 'end'\n  nameLocation: 'end',\n  // By degree. By default auto rotate by nameLocation.\n  nameRotate: null,\n  nameTruncate: {\n    maxWidth: null,\n    ellipsis: '...',\n    placeholder: '.'\n  },\n  // Use global text style by default.\n  nameTextStyle: {},\n  // The gap between axisName and axisLine.\n  nameGap: 15,\n  // Default `false` to support tooltip.\n  silent: false,\n  // Default `false` to avoid legacy user event listener fail.\n  triggerEvent: false,\n  tooltip: {\n    show: false\n  },\n  axisPointer: {},\n  axisLine: {\n    show: true,\n    onZero: true,\n    onZeroAxisIndex: null,\n    lineStyle: {\n      color: '#6E7079',\n      width: 1,\n      type: 'solid'\n    },\n    // The arrow at both ends the the axis.\n    symbol: ['none', 'none'],\n    symbolSize: [10, 15]\n  },\n  axisTick: {\n    show: true,\n    // Whether axisTick is inside the grid or outside the grid.\n    inside: false,\n    // The length of axisTick.\n    length: 5,\n    lineStyle: {\n      width: 1\n    }\n  },\n  axisLabel: {\n    show: true,\n    // Whether axisLabel is inside the grid or outside the grid.\n    inside: false,\n    rotate: 0,\n    // true | false | null/undefined (auto)\n    showMinLabel: null,\n    // true | false | null/undefined (auto)\n    showMaxLabel: null,\n    margin: 8,\n    // formatter: null,\n    fontSize: 12\n  },\n  splitLine: {\n    show: true,\n    showMinLine: true,\n    showMaxLine: true,\n    lineStyle: {\n      color: ['#E0E6F1'],\n      width: 1,\n      type: 'solid'\n    }\n  },\n  splitArea: {\n    show: false,\n    areaStyle: {\n      color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']\n    }\n  }\n};\nvar categoryAxis = zrUtil.merge({\n  // The gap at both ends of the axis. For categoryAxis, boolean.\n  boundaryGap: true,\n  // Set false to faster category collection.\n  deduplication: null,\n  // splitArea: {\n  // show: false\n  // },\n  splitLine: {\n    show: false\n  },\n  axisTick: {\n    // If tick is align with label when boundaryGap is true\n    alignWithLabel: false,\n    interval: 'auto'\n  },\n  axisLabel: {\n    interval: 'auto'\n  }\n}, defaultOption);\nvar valueAxis = zrUtil.merge({\n  boundaryGap: [0, 0],\n  axisLine: {\n    // Not shown when other axis is categoryAxis in cartesian\n    show: 'auto'\n  },\n  axisTick: {\n    // Not shown when other axis is categoryAxis in cartesian\n    show: 'auto'\n  },\n  // TODO\n  // min/max: [30, datamin, 60] or [20, datamin] or [datamin, 60]\n  splitNumber: 5,\n  minorTick: {\n    // Minor tick, not available for cateogry axis.\n    show: false,\n    // Split number of minor ticks. The value should be in range of (0, 100)\n    splitNumber: 5,\n    // Length of minor tick\n    length: 3,\n    // Line style\n    lineStyle: {\n      // Default to be same with axisTick\n    }\n  },\n  minorSplitLine: {\n    show: false,\n    lineStyle: {\n      color: '#F4F7FD',\n      width: 1\n    }\n  }\n}, defaultOption);\nvar timeAxis = zrUtil.merge({\n  splitNumber: 6,\n  axisLabel: {\n    // To eliminate labels that are not nice\n    showMinLabel: false,\n    showMaxLabel: false,\n    rich: {\n      primary: {\n        fontWeight: 'bold'\n      }\n    }\n  },\n  splitLine: {\n    show: false\n  }\n}, valueAxis);\nvar logAxis = zrUtil.defaults({\n  logBase: 10\n}, valueAxis);\nexport default {\n  category: categoryAxis,\n  value: valueAxis,\n  time: timeAxis,\n  log: logAxis\n};", "map": {"version": 3, "names": ["zrUtil", "defaultOption", "show", "z", "inverse", "name", "nameLocation", "nameRotate", "nameTruncate", "max<PERSON><PERSON><PERSON>", "ellipsis", "placeholder", "nameTextStyle", "nameGap", "silent", "triggerEvent", "tooltip", "axisPointer", "axisLine", "onZero", "onZeroAxisIndex", "lineStyle", "color", "width", "type", "symbol", "symbolSize", "axisTick", "inside", "length", "axisLabel", "rotate", "showMinLabel", "showMaxLabel", "margin", "fontSize", "splitLine", "showMinLine", "showMaxLine", "splitArea", "areaStyle", "categoryAxis", "merge", "boundaryGap", "deduplication", "alignWithLabel", "interval", "valueAxis", "splitNumber", "minor<PERSON><PERSON>", "minorSplitLine", "timeAxis", "rich", "primary", "fontWeight", "logAxis", "defaults", "logBase", "category", "value", "time", "log"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/coord/axisDefault.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar defaultOption = {\n  show: true,\n  // zlevel: 0,\n  z: 0,\n  // Inverse the axis.\n  inverse: false,\n  // Axis name displayed.\n  name: '',\n  // 'start' | 'middle' | 'end'\n  nameLocation: 'end',\n  // By degree. By default auto rotate by nameLocation.\n  nameRotate: null,\n  nameTruncate: {\n    maxWidth: null,\n    ellipsis: '...',\n    placeholder: '.'\n  },\n  // Use global text style by default.\n  nameTextStyle: {},\n  // The gap between axisName and axisLine.\n  nameGap: 15,\n  // Default `false` to support tooltip.\n  silent: false,\n  // Default `false` to avoid legacy user event listener fail.\n  triggerEvent: false,\n  tooltip: {\n    show: false\n  },\n  axisPointer: {},\n  axisLine: {\n    show: true,\n    onZero: true,\n    onZeroAxisIndex: null,\n    lineStyle: {\n      color: '#6E7079',\n      width: 1,\n      type: 'solid'\n    },\n    // The arrow at both ends the the axis.\n    symbol: ['none', 'none'],\n    symbolSize: [10, 15]\n  },\n  axisTick: {\n    show: true,\n    // Whether axisTick is inside the grid or outside the grid.\n    inside: false,\n    // The length of axisTick.\n    length: 5,\n    lineStyle: {\n      width: 1\n    }\n  },\n  axisLabel: {\n    show: true,\n    // Whether axisLabel is inside the grid or outside the grid.\n    inside: false,\n    rotate: 0,\n    // true | false | null/undefined (auto)\n    showMinLabel: null,\n    // true | false | null/undefined (auto)\n    showMaxLabel: null,\n    margin: 8,\n    // formatter: null,\n    fontSize: 12\n  },\n  splitLine: {\n    show: true,\n    showMinLine: true,\n    showMaxLine: true,\n    lineStyle: {\n      color: ['#E0E6F1'],\n      width: 1,\n      type: 'solid'\n    }\n  },\n  splitArea: {\n    show: false,\n    areaStyle: {\n      color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']\n    }\n  }\n};\nvar categoryAxis = zrUtil.merge({\n  // The gap at both ends of the axis. For categoryAxis, boolean.\n  boundaryGap: true,\n  // Set false to faster category collection.\n  deduplication: null,\n  // splitArea: {\n  // show: false\n  // },\n  splitLine: {\n    show: false\n  },\n  axisTick: {\n    // If tick is align with label when boundaryGap is true\n    alignWithLabel: false,\n    interval: 'auto'\n  },\n  axisLabel: {\n    interval: 'auto'\n  }\n}, defaultOption);\nvar valueAxis = zrUtil.merge({\n  boundaryGap: [0, 0],\n  axisLine: {\n    // Not shown when other axis is categoryAxis in cartesian\n    show: 'auto'\n  },\n  axisTick: {\n    // Not shown when other axis is categoryAxis in cartesian\n    show: 'auto'\n  },\n  // TODO\n  // min/max: [30, datamin, 60] or [20, datamin] or [datamin, 60]\n  splitNumber: 5,\n  minorTick: {\n    // Minor tick, not available for cateogry axis.\n    show: false,\n    // Split number of minor ticks. The value should be in range of (0, 100)\n    splitNumber: 5,\n    // Length of minor tick\n    length: 3,\n    // Line style\n    lineStyle: {\n      // Default to be same with axisTick\n    }\n  },\n  minorSplitLine: {\n    show: false,\n    lineStyle: {\n      color: '#F4F7FD',\n      width: 1\n    }\n  }\n}, defaultOption);\nvar timeAxis = zrUtil.merge({\n  splitNumber: 6,\n  axisLabel: {\n    // To eliminate labels that are not nice\n    showMinLabel: false,\n    showMaxLabel: false,\n    rich: {\n      primary: {\n        fontWeight: 'bold'\n      }\n    }\n  },\n  splitLine: {\n    show: false\n  }\n}, valueAxis);\nvar logAxis = zrUtil.defaults({\n  logBase: 10\n}, valueAxis);\nexport default {\n  category: categoryAxis,\n  value: valueAxis,\n  time: timeAxis,\n  log: logAxis\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,aAAa,GAAG;EAClBC,IAAI,EAAE,IAAI;EACV;EACAC,CAAC,EAAE,CAAC;EACJ;EACAC,OAAO,EAAE,KAAK;EACd;EACAC,IAAI,EAAE,EAAE;EACR;EACAC,YAAY,EAAE,KAAK;EACnB;EACAC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE;IACZC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC;EACD;EACAC,aAAa,EAAE,CAAC,CAAC;EACjB;EACAC,OAAO,EAAE,EAAE;EACX;EACAC,MAAM,EAAE,KAAK;EACb;EACAC,YAAY,EAAE,KAAK;EACnBC,OAAO,EAAE;IACPd,IAAI,EAAE;EACR,CAAC;EACDe,WAAW,EAAE,CAAC,CAAC;EACfC,QAAQ,EAAE;IACRhB,IAAI,EAAE,IAAI;IACViB,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;IACR,CAAC;IACD;IACAC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IACxBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE;EACrB,CAAC;EACDC,QAAQ,EAAE;IACRzB,IAAI,EAAE,IAAI;IACV;IACA0B,MAAM,EAAE,KAAK;IACb;IACAC,MAAM,EAAE,CAAC;IACTR,SAAS,EAAE;MACTE,KAAK,EAAE;IACT;EACF,CAAC;EACDO,SAAS,EAAE;IACT5B,IAAI,EAAE,IAAI;IACV;IACA0B,MAAM,EAAE,KAAK;IACbG,MAAM,EAAE,CAAC;IACT;IACAC,YAAY,EAAE,IAAI;IAClB;IACAC,YAAY,EAAE,IAAI;IAClBC,MAAM,EAAE,CAAC;IACT;IACAC,QAAQ,EAAE;EACZ,CAAC;EACDC,SAAS,EAAE;IACTlC,IAAI,EAAE,IAAI;IACVmC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBjB,SAAS,EAAE;MACTC,KAAK,EAAE,CAAC,SAAS,CAAC;MAClBC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;IACR;EACF,CAAC;EACDe,SAAS,EAAE;IACTrC,IAAI,EAAE,KAAK;IACXsC,SAAS,EAAE;MACTlB,KAAK,EAAE,CAAC,uBAAuB,EAAE,uBAAuB;IAC1D;EACF;AACF,CAAC;AACD,IAAImB,YAAY,GAAGzC,MAAM,CAAC0C,KAAK,CAAC;EAC9B;EACAC,WAAW,EAAE,IAAI;EACjB;EACAC,aAAa,EAAE,IAAI;EACnB;EACA;EACA;EACAR,SAAS,EAAE;IACTlC,IAAI,EAAE;EACR,CAAC;EACDyB,QAAQ,EAAE;IACR;IACAkB,cAAc,EAAE,KAAK;IACrBC,QAAQ,EAAE;EACZ,CAAC;EACDhB,SAAS,EAAE;IACTgB,QAAQ,EAAE;EACZ;AACF,CAAC,EAAE7C,aAAa,CAAC;AACjB,IAAI8C,SAAS,GAAG/C,MAAM,CAAC0C,KAAK,CAAC;EAC3BC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACnBzB,QAAQ,EAAE;IACR;IACAhB,IAAI,EAAE;EACR,CAAC;EACDyB,QAAQ,EAAE;IACR;IACAzB,IAAI,EAAE;EACR,CAAC;EACD;EACA;EACA8C,WAAW,EAAE,CAAC;EACdC,SAAS,EAAE;IACT;IACA/C,IAAI,EAAE,KAAK;IACX;IACA8C,WAAW,EAAE,CAAC;IACd;IACAnB,MAAM,EAAE,CAAC;IACT;IACAR,SAAS,EAAE;MACT;IAAA;EAEJ,CAAC;EACD6B,cAAc,EAAE;IACdhD,IAAI,EAAE,KAAK;IACXmB,SAAS,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE;IACT;EACF;AACF,CAAC,EAAEtB,aAAa,CAAC;AACjB,IAAIkD,QAAQ,GAAGnD,MAAM,CAAC0C,KAAK,CAAC;EAC1BM,WAAW,EAAE,CAAC;EACdlB,SAAS,EAAE;IACT;IACAE,YAAY,EAAE,KAAK;IACnBC,YAAY,EAAE,KAAK;IACnBmB,IAAI,EAAE;MACJC,OAAO,EAAE;QACPC,UAAU,EAAE;MACd;IACF;EACF,CAAC;EACDlB,SAAS,EAAE;IACTlC,IAAI,EAAE;EACR;AACF,CAAC,EAAE6C,SAAS,CAAC;AACb,IAAIQ,OAAO,GAAGvD,MAAM,CAACwD,QAAQ,CAAC;EAC5BC,OAAO,EAAE;AACX,CAAC,EAAEV,SAAS,CAAC;AACb,eAAe;EACbW,QAAQ,EAAEjB,YAAY;EACtBkB,KAAK,EAAEZ,SAAS;EAChBa,IAAI,EAAET,QAAQ;EACdU,GAAG,EAAEN;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}