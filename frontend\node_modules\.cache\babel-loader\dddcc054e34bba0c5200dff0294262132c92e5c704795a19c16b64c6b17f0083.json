{"ast": null, "code": "var round = Math.round;\nexport function subPixelOptimizeLine(outputShape, inputShape, style) {\n  if (!inputShape) {\n    return;\n  }\n  var x1 = inputShape.x1;\n  var x2 = inputShape.x2;\n  var y1 = inputShape.y1;\n  var y2 = inputShape.y2;\n  outputShape.x1 = x1;\n  outputShape.x2 = x2;\n  outputShape.y1 = y1;\n  outputShape.y2 = y2;\n  var lineWidth = style && style.lineWidth;\n  if (!lineWidth) {\n    return outputShape;\n  }\n  if (round(x1 * 2) === round(x2 * 2)) {\n    outputShape.x1 = outputShape.x2 = subPixelOptimize(x1, lineWidth, true);\n  }\n  if (round(y1 * 2) === round(y2 * 2)) {\n    outputShape.y1 = outputShape.y2 = subPixelOptimize(y1, lineWidth, true);\n  }\n  return outputShape;\n}\nexport function subPixelOptimizeRect(outputShape, inputShape, style) {\n  if (!inputShape) {\n    return;\n  }\n  var originX = inputShape.x;\n  var originY = inputShape.y;\n  var originWidth = inputShape.width;\n  var originHeight = inputShape.height;\n  outputShape.x = originX;\n  outputShape.y = originY;\n  outputShape.width = originWidth;\n  outputShape.height = originHeight;\n  var lineWidth = style && style.lineWidth;\n  if (!lineWidth) {\n    return outputShape;\n  }\n  outputShape.x = subPixelOptimize(originX, lineWidth, true);\n  outputShape.y = subPixelOptimize(originY, lineWidth, true);\n  outputShape.width = Math.max(subPixelOptimize(originX + originWidth, lineWidth, false) - outputShape.x, originWidth === 0 ? 0 : 1);\n  outputShape.height = Math.max(subPixelOptimize(originY + originHeight, lineWidth, false) - outputShape.y, originHeight === 0 ? 0 : 1);\n  return outputShape;\n}\nexport function subPixelOptimize(position, lineWidth, positiveOrNegative) {\n  if (!lineWidth) {\n    return position;\n  }\n  var doubledPosition = round(position * 2);\n  return (doubledPosition + round(lineWidth)) % 2 === 0 ? doubledPosition / 2 : (doubledPosition + (positiveOrNegative ? 1 : -1)) / 2;\n}", "map": {"version": 3, "names": ["round", "Math", "subPixelOptimizeLine", "outputShape", "inputShape", "style", "x1", "x2", "y1", "y2", "lineWidth", "subPixelOptimize", "subPixelOptimizeRect", "originX", "x", "originY", "y", "originWidth", "width", "originHeight", "height", "max", "position", "positiveOrNegative", "doubledPosition"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/zrender/lib/graphic/helper/subPixelOptimize.js"], "sourcesContent": ["var round = Math.round;\nexport function subPixelOptimizeLine(outputShape, inputShape, style) {\n    if (!inputShape) {\n        return;\n    }\n    var x1 = inputShape.x1;\n    var x2 = inputShape.x2;\n    var y1 = inputShape.y1;\n    var y2 = inputShape.y2;\n    outputShape.x1 = x1;\n    outputShape.x2 = x2;\n    outputShape.y1 = y1;\n    outputShape.y2 = y2;\n    var lineWidth = style && style.lineWidth;\n    if (!lineWidth) {\n        return outputShape;\n    }\n    if (round(x1 * 2) === round(x2 * 2)) {\n        outputShape.x1 = outputShape.x2 = subPixelOptimize(x1, lineWidth, true);\n    }\n    if (round(y1 * 2) === round(y2 * 2)) {\n        outputShape.y1 = outputShape.y2 = subPixelOptimize(y1, lineWidth, true);\n    }\n    return outputShape;\n}\nexport function subPixelOptimizeRect(outputShape, inputShape, style) {\n    if (!inputShape) {\n        return;\n    }\n    var originX = inputShape.x;\n    var originY = inputShape.y;\n    var originWidth = inputShape.width;\n    var originHeight = inputShape.height;\n    outputShape.x = originX;\n    outputShape.y = originY;\n    outputShape.width = originWidth;\n    outputShape.height = originHeight;\n    var lineWidth = style && style.lineWidth;\n    if (!lineWidth) {\n        return outputShape;\n    }\n    outputShape.x = subPixelOptimize(originX, lineWidth, true);\n    outputShape.y = subPixelOptimize(originY, lineWidth, true);\n    outputShape.width = Math.max(subPixelOptimize(originX + originWidth, lineWidth, false) - outputShape.x, originWidth === 0 ? 0 : 1);\n    outputShape.height = Math.max(subPixelOptimize(originY + originHeight, lineWidth, false) - outputShape.y, originHeight === 0 ? 0 : 1);\n    return outputShape;\n}\nexport function subPixelOptimize(position, lineWidth, positiveOrNegative) {\n    if (!lineWidth) {\n        return position;\n    }\n    var doubledPosition = round(position * 2);\n    return (doubledPosition + round(lineWidth)) % 2 === 0\n        ? doubledPosition / 2\n        : (doubledPosition + (positiveOrNegative ? 1 : -1)) / 2;\n}\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,IAAI,CAACD,KAAK;AACtB,OAAO,SAASE,oBAAoBA,CAACC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAE;EACjE,IAAI,CAACD,UAAU,EAAE;IACb;EACJ;EACA,IAAIE,EAAE,GAAGF,UAAU,CAACE,EAAE;EACtB,IAAIC,EAAE,GAAGH,UAAU,CAACG,EAAE;EACtB,IAAIC,EAAE,GAAGJ,UAAU,CAACI,EAAE;EACtB,IAAIC,EAAE,GAAGL,UAAU,CAACK,EAAE;EACtBN,WAAW,CAACG,EAAE,GAAGA,EAAE;EACnBH,WAAW,CAACI,EAAE,GAAGA,EAAE;EACnBJ,WAAW,CAACK,EAAE,GAAGA,EAAE;EACnBL,WAAW,CAACM,EAAE,GAAGA,EAAE;EACnB,IAAIC,SAAS,GAAGL,KAAK,IAAIA,KAAK,CAACK,SAAS;EACxC,IAAI,CAACA,SAAS,EAAE;IACZ,OAAOP,WAAW;EACtB;EACA,IAAIH,KAAK,CAACM,EAAE,GAAG,CAAC,CAAC,KAAKN,KAAK,CAACO,EAAE,GAAG,CAAC,CAAC,EAAE;IACjCJ,WAAW,CAACG,EAAE,GAAGH,WAAW,CAACI,EAAE,GAAGI,gBAAgB,CAACL,EAAE,EAAEI,SAAS,EAAE,IAAI,CAAC;EAC3E;EACA,IAAIV,KAAK,CAACQ,EAAE,GAAG,CAAC,CAAC,KAAKR,KAAK,CAACS,EAAE,GAAG,CAAC,CAAC,EAAE;IACjCN,WAAW,CAACK,EAAE,GAAGL,WAAW,CAACM,EAAE,GAAGE,gBAAgB,CAACH,EAAE,EAAEE,SAAS,EAAE,IAAI,CAAC;EAC3E;EACA,OAAOP,WAAW;AACtB;AACA,OAAO,SAASS,oBAAoBA,CAACT,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAE;EACjE,IAAI,CAACD,UAAU,EAAE;IACb;EACJ;EACA,IAAIS,OAAO,GAAGT,UAAU,CAACU,CAAC;EAC1B,IAAIC,OAAO,GAAGX,UAAU,CAACY,CAAC;EAC1B,IAAIC,WAAW,GAAGb,UAAU,CAACc,KAAK;EAClC,IAAIC,YAAY,GAAGf,UAAU,CAACgB,MAAM;EACpCjB,WAAW,CAACW,CAAC,GAAGD,OAAO;EACvBV,WAAW,CAACa,CAAC,GAAGD,OAAO;EACvBZ,WAAW,CAACe,KAAK,GAAGD,WAAW;EAC/Bd,WAAW,CAACiB,MAAM,GAAGD,YAAY;EACjC,IAAIT,SAAS,GAAGL,KAAK,IAAIA,KAAK,CAACK,SAAS;EACxC,IAAI,CAACA,SAAS,EAAE;IACZ,OAAOP,WAAW;EACtB;EACAA,WAAW,CAACW,CAAC,GAAGH,gBAAgB,CAACE,OAAO,EAAEH,SAAS,EAAE,IAAI,CAAC;EAC1DP,WAAW,CAACa,CAAC,GAAGL,gBAAgB,CAACI,OAAO,EAAEL,SAAS,EAAE,IAAI,CAAC;EAC1DP,WAAW,CAACe,KAAK,GAAGjB,IAAI,CAACoB,GAAG,CAACV,gBAAgB,CAACE,OAAO,GAAGI,WAAW,EAAEP,SAAS,EAAE,KAAK,CAAC,GAAGP,WAAW,CAACW,CAAC,EAAEG,WAAW,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAClId,WAAW,CAACiB,MAAM,GAAGnB,IAAI,CAACoB,GAAG,CAACV,gBAAgB,CAACI,OAAO,GAAGI,YAAY,EAAET,SAAS,EAAE,KAAK,CAAC,GAAGP,WAAW,CAACa,CAAC,EAAEG,YAAY,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACrI,OAAOhB,WAAW;AACtB;AACA,OAAO,SAASQ,gBAAgBA,CAACW,QAAQ,EAAEZ,SAAS,EAAEa,kBAAkB,EAAE;EACtE,IAAI,CAACb,SAAS,EAAE;IACZ,OAAOY,QAAQ;EACnB;EACA,IAAIE,eAAe,GAAGxB,KAAK,CAACsB,QAAQ,GAAG,CAAC,CAAC;EACzC,OAAO,CAACE,eAAe,GAAGxB,KAAK,CAACU,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAC/Cc,eAAe,GAAG,CAAC,GACnB,CAACA,eAAe,IAAID,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}