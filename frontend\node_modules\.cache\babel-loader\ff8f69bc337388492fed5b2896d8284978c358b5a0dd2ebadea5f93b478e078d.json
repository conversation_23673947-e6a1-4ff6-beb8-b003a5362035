{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parseConditionalExpression } from '../../util/conditionalExpression.js';\nimport { hasOwn, createHashMap } from 'zrender/lib/core/util.js';\nimport { makePrintable, throwError } from '../../util/log.js';\nexport var filterTransform = {\n  type: 'echarts:filter',\n  // PENDING: enhance to filter by index rather than create new data\n  transform: function (params) {\n    // [Caveat] Fail-Fast:\n    // Do not return the whole dataset unless user config indicates it explicitly.\n    // For example, if no condition is specified by mistake, returning an empty result\n    // is better than returning the entire raw source for the user to find the mistake.\n    var upstream = params.upstream;\n    var rawItem;\n    var condition = parseConditionalExpression(params.config, {\n      valueGetterAttrMap: createHashMap({\n        dimension: true\n      }),\n      prepareGetValue: function (exprOption) {\n        var errMsg = '';\n        var dimLoose = exprOption.dimension;\n        if (!hasOwn(exprOption, 'dimension')) {\n          if (process.env.NODE_ENV !== 'production') {\n            errMsg = makePrintable('Relation condition must has prop \"dimension\" specified.', 'Illegal condition:', exprOption);\n          }\n          throwError(errMsg);\n        }\n        var dimInfo = upstream.getDimensionInfo(dimLoose);\n        if (!dimInfo) {\n          if (process.env.NODE_ENV !== 'production') {\n            errMsg = makePrintable('Can not find dimension info via: ' + dimLoose + '.\\n', 'Existing dimensions: ', upstream.cloneAllDimensionInfo(), '.\\n', 'Illegal condition:', exprOption, '.\\n');\n          }\n          throwError(errMsg);\n        }\n        return {\n          dimIdx: dimInfo.index\n        };\n      },\n      getValue: function (param) {\n        return upstream.retrieveValueFromItem(rawItem, param.dimIdx);\n      }\n    });\n    var resultData = [];\n    for (var i = 0, len = upstream.count(); i < len; i++) {\n      rawItem = upstream.getRawDataItem(i);\n      if (condition.evaluate()) {\n        resultData.push(rawItem);\n      }\n    }\n    return {\n      data: resultData\n    };\n  }\n};", "map": {"version": 3, "names": ["parseConditionalExpression", "hasOwn", "createHashMap", "makePrintable", "throwError", "filterTransform", "type", "transform", "params", "upstream", "rawItem", "condition", "config", "valueGetterAttrMap", "dimension", "prepareGetValue", "exprOption", "errMsg", "dimLoose", "process", "env", "NODE_ENV", "dimInfo", "getDimensionInfo", "cloneAllDimensionInfo", "dimIdx", "index", "getValue", "param", "retrieveValueFromItem", "resultData", "i", "len", "count", "getRawDataItem", "evaluate", "push", "data"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/component/transform/filterTransform.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parseConditionalExpression } from '../../util/conditionalExpression.js';\nimport { hasOwn, createHashMap } from 'zrender/lib/core/util.js';\nimport { makePrintable, throwError } from '../../util/log.js';\nexport var filterTransform = {\n  type: 'echarts:filter',\n  // PENDING: enhance to filter by index rather than create new data\n  transform: function (params) {\n    // [Caveat] Fail-Fast:\n    // Do not return the whole dataset unless user config indicates it explicitly.\n    // For example, if no condition is specified by mistake, returning an empty result\n    // is better than returning the entire raw source for the user to find the mistake.\n    var upstream = params.upstream;\n    var rawItem;\n    var condition = parseConditionalExpression(params.config, {\n      valueGetterAttrMap: createHashMap({\n        dimension: true\n      }),\n      prepareGetValue: function (exprOption) {\n        var errMsg = '';\n        var dimLoose = exprOption.dimension;\n        if (!hasOwn(exprOption, 'dimension')) {\n          if (process.env.NODE_ENV !== 'production') {\n            errMsg = makePrintable('Relation condition must has prop \"dimension\" specified.', 'Illegal condition:', exprOption);\n          }\n          throwError(errMsg);\n        }\n        var dimInfo = upstream.getDimensionInfo(dimLoose);\n        if (!dimInfo) {\n          if (process.env.NODE_ENV !== 'production') {\n            errMsg = makePrintable('Can not find dimension info via: ' + dimLoose + '.\\n', 'Existing dimensions: ', upstream.cloneAllDimensionInfo(), '.\\n', 'Illegal condition:', exprOption, '.\\n');\n          }\n          throwError(errMsg);\n        }\n        return {\n          dimIdx: dimInfo.index\n        };\n      },\n      getValue: function (param) {\n        return upstream.retrieveValueFromItem(rawItem, param.dimIdx);\n      }\n    });\n    var resultData = [];\n    for (var i = 0, len = upstream.count(); i < len; i++) {\n      rawItem = upstream.getRawDataItem(i);\n      if (condition.evaluate()) {\n        resultData.push(rawItem);\n      }\n    }\n    return {\n      data: resultData\n    };\n  }\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,0BAA0B,QAAQ,qCAAqC;AAChF,SAASC,MAAM,EAAEC,aAAa,QAAQ,0BAA0B;AAChE,SAASC,aAAa,EAAEC,UAAU,QAAQ,mBAAmB;AAC7D,OAAO,IAAIC,eAAe,GAAG;EAC3BC,IAAI,EAAE,gBAAgB;EACtB;EACAC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;IAC3B;IACA;IACA;IACA;IACA,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IAC9B,IAAIC,OAAO;IACX,IAAIC,SAAS,GAAGX,0BAA0B,CAACQ,MAAM,CAACI,MAAM,EAAE;MACxDC,kBAAkB,EAAEX,aAAa,CAAC;QAChCY,SAAS,EAAE;MACb,CAAC,CAAC;MACFC,eAAe,EAAE,SAAAA,CAAUC,UAAU,EAAE;QACrC,IAAIC,MAAM,GAAG,EAAE;QACf,IAAIC,QAAQ,GAAGF,UAAU,CAACF,SAAS;QACnC,IAAI,CAACb,MAAM,CAACe,UAAU,EAAE,WAAW,CAAC,EAAE;UACpC,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzCJ,MAAM,GAAGd,aAAa,CAAC,yDAAyD,EAAE,oBAAoB,EAAEa,UAAU,CAAC;UACrH;UACAZ,UAAU,CAACa,MAAM,CAAC;QACpB;QACA,IAAIK,OAAO,GAAGb,QAAQ,CAACc,gBAAgB,CAACL,QAAQ,CAAC;QACjD,IAAI,CAACI,OAAO,EAAE;UACZ,IAAIH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzCJ,MAAM,GAAGd,aAAa,CAAC,mCAAmC,GAAGe,QAAQ,GAAG,KAAK,EAAE,uBAAuB,EAAET,QAAQ,CAACe,qBAAqB,CAAC,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAER,UAAU,EAAE,KAAK,CAAC;UAC3L;UACAZ,UAAU,CAACa,MAAM,CAAC;QACpB;QACA,OAAO;UACLQ,MAAM,EAAEH,OAAO,CAACI;QAClB,CAAC;MACH,CAAC;MACDC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACzB,OAAOnB,QAAQ,CAACoB,qBAAqB,CAACnB,OAAO,EAAEkB,KAAK,CAACH,MAAM,CAAC;MAC9D;IACF,CAAC,CAAC;IACF,IAAIK,UAAU,GAAG,EAAE;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGvB,QAAQ,CAACwB,KAAK,CAAC,CAAC,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpDrB,OAAO,GAAGD,QAAQ,CAACyB,cAAc,CAACH,CAAC,CAAC;MACpC,IAAIpB,SAAS,CAACwB,QAAQ,CAAC,CAAC,EAAE;QACxBL,UAAU,CAACM,IAAI,CAAC1B,OAAO,CAAC;MAC1B;IACF;IACA,OAAO;MACL2B,IAAI,EAAEP;IACR,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}