import { createSlice } from '@reduxjs/toolkit';

// 模拟物料数据
const mockMaterials = [
  {
    id: 'MAT-001',
    code: 'CPU-I7-12700K',
    name: 'Intel Core i7-12700K',
    specification: '12核20线程 3.6GHz',
    unit: '个',
    standardCost: 2800,
    currentCost: 2850,
    supplier: '英特尔',
    category: 'CPU',
    status: 'active',
    stockQuantity: 50,
    minStock: 10,
    createTime: '2024-01-01'
  },
  {
    id: 'MAT-002',
    code: 'MB-ASUS-Z690',
    name: 'ASUS ROG STRIX Z690-E',
    specification: 'ATX主板 LGA1700',
    unit: '个',
    standardCost: 1800,
    currentCost: 1850,
    supplier: '华硕',
    category: '主板',
    status: 'active',
    stockQuantity: 30,
    minStock: 5,
    createTime: '2024-01-01'
  },
  {
    id: 'MAT-003',
    code: 'RAM-32G-DDR5',
    name: '金士顿 DDR5 32GB',
    specification: '32GB DDR5-5600 套装',
    unit: '套',
    standardCost: 1200,
    currentCost: 1250,
    supplier: '金士顿',
    category: '内存',
    status: 'active',
    stockQuantity: 80,
    minStock: 20,
    createTime: '2024-01-01'
  },
  {
    id: 'MAT-004',
    code: 'SSD-1TB-NVME',
    name: '三星 980 PRO 1TB',
    specification: 'NVMe M.2 SSD 1TB',
    unit: '个',
    standardCost: 800,
    currentCost: 820,
    supplier: '三星',
    category: '存储',
    status: 'active',
    stockQuantity: 60,
    minStock: 15,
    createTime: '2024-01-01'
  },
  {
    id: 'MAT-005',
    code: 'GPU-RTX4070',
    name: 'NVIDIA RTX 4070',
    specification: '12GB GDDR6X 显卡',
    unit: '个',
    standardCost: 4500,
    currentCost: 4600,
    supplier: '英伟达',
    category: '显卡',
    status: 'active',
    stockQuantity: 25,
    minStock: 5,
    createTime: '2024-01-01'
  },
  {
    id: 'MAT-006',
    code: 'CASE-ATX-001',
    name: '酷冷至尊 H500P',
    specification: 'ATX中塔机箱',
    unit: '个',
    standardCost: 600,
    currentCost: 620,
    supplier: '酷冷至尊',
    category: '机箱',
    status: 'active',
    stockQuantity: 40,
    minStock: 10,
    createTime: '2024-01-01'
  },
  {
    id: 'MAT-007',
    code: 'PSU-850W-80PLUS',
    name: '海盗船 RM850x',
    specification: '850W 80PLUS金牌',
    unit: '个',
    standardCost: 900,
    currentCost: 920,
    supplier: '海盗船',
    category: '电源',
    status: 'active',
    stockQuantity: 35,
    minStock: 8,
    createTime: '2024-01-01'
  }
];

// 模拟BOM数据
const mockBOMs = [
  {
    id: 'BOM-001',
    code: 'BOM-SERVER-001',
    name: '高性能服务器配置',
    version: 'V1.0',
    status: 'active',
    type: 'product',
    description: '适用于企业级应用的高性能服务器',
    totalCost: 12640,
    createTime: '2024-01-10',
    updateTime: '2024-01-15',
    createdBy: '张工程师',
    items: [
      {
        id: 1,
        materialId: 'MAT-001',
        materialCode: 'CPU-I7-12700K',
        materialName: 'Intel Core i7-12700K',
        specification: '12核20线程 3.6GHz',
        quantity: 2,
        unit: '个',
        unitCost: 2850,
        totalCost: 5700,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 2,
        materialId: 'MAT-002',
        materialCode: 'MB-ASUS-Z690',
        materialName: 'ASUS ROG STRIX Z690-E',
        specification: 'ATX主板 LGA1700',
        quantity: 2,
        unit: '个',
        unitCost: 1850,
        totalCost: 3700,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 3,
        materialId: 'MAT-003',
        materialCode: 'RAM-32G-DDR5',
        materialName: '金士顿 DDR5 32GB',
        specification: '32GB DDR5-5600 套装',
        quantity: 2,
        unit: '套',
        unitCost: 1250,
        totalCost: 2500,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 4,
        materialId: 'MAT-004',
        materialCode: 'SSD-1TB-NVME',
        materialName: '三星 980 PRO 1TB',
        specification: 'NVMe M.2 SSD 1TB',
        quantity: 1,
        unit: '个',
        unitCost: 820,
        totalCost: 820,
        level: 1,
        parentId: null,
        children: []
      }
    ]
  },
  {
    id: 'BOM-002',
    code: 'BOM-WORKSTATION-001',
    name: '图形工作站配置',
    version: 'V1.0',
    status: 'active',
    type: 'product',
    description: '适用于设计和渲染的专业工作站',
    totalCost: 18540,
    createTime: '2024-01-12',
    updateTime: '2024-01-18',
    createdBy: '李工程师',
    items: [
      {
        id: 1,
        materialId: 'MAT-001',
        materialCode: 'CPU-I7-12700K',
        materialName: 'Intel Core i7-12700K',
        specification: '12核20线程 3.6GHz',
        quantity: 1,
        unit: '个',
        unitCost: 2850,
        totalCost: 2850,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 2,
        materialId: 'MAT-002',
        materialCode: 'MB-ASUS-Z690',
        materialName: 'ASUS ROG STRIX Z690-E',
        specification: 'ATX主板 LGA1700',
        quantity: 1,
        unit: '个',
        unitCost: 1850,
        totalCost: 1850,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 3,
        materialId: 'MAT-003',
        materialCode: 'RAM-32G-DDR5',
        materialName: '金士顿 DDR5 32GB',
        specification: '32GB DDR5-5600 套装',
        quantity: 2,
        unit: '套',
        unitCost: 1250,
        totalCost: 2500,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 4,
        materialId: 'MAT-004',
        materialCode: 'SSD-1TB-NVME',
        materialName: '三星 980 PRO 1TB',
        specification: 'NVMe M.2 SSD 1TB',
        quantity: 2,
        unit: '个',
        unitCost: 820,
        totalCost: 1640,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 5,
        materialId: 'MAT-005',
        materialCode: 'GPU-RTX4070',
        materialName: 'NVIDIA RTX 4070',
        specification: '12GB GDDR6X 显卡',
        quantity: 2,
        unit: '个',
        unitCost: 4600,
        totalCost: 9200,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 6,
        materialId: 'MAT-006',
        materialCode: 'CASE-ATX-001',
        materialName: '酷冷至尊 H500P',
        specification: 'ATX中塔机箱',
        quantity: 1,
        unit: '个',
        unitCost: 620,
        totalCost: 620,
        level: 1,
        parentId: null,
        children: []
      }
    ]
  },
  {
    id: 'BOM-003',
    code: 'BOM-BASIC-PC-001',
    name: '基础办公电脑配置',
    version: 'V1.0',
    status: 'active',
    type: 'product',
    description: '适用于日常办公的基础配置',
    totalCost: 6790,
    createTime: '2024-01-15',
    updateTime: '2024-01-20',
    createdBy: '王工程师',
    items: [
      {
        id: 1,
        materialId: 'MAT-001',
        materialCode: 'CPU-I7-12700K',
        materialName: 'Intel Core i7-12700K',
        specification: '12核20线程 3.6GHz',
        quantity: 1,
        unit: '个',
        unitCost: 2850,
        totalCost: 2850,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 2,
        materialId: 'MAT-002',
        materialCode: 'MB-ASUS-Z690',
        materialName: 'ASUS ROG STRIX Z690-E',
        specification: 'ATX主板 LGA1700',
        quantity: 1,
        unit: '个',
        unitCost: 1850,
        totalCost: 1850,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 3,
        materialId: 'MAT-003',
        materialCode: 'RAM-32G-DDR5',
        materialName: '金士顿 DDR5 32GB',
        specification: '32GB DDR5-5600 套装',
        quantity: 1,
        unit: '套',
        unitCost: 1250,
        totalCost: 1250,
        level: 1,
        parentId: null,
        children: []
      },
      {
        id: 4,
        materialId: 'MAT-004',
        materialCode: 'SSD-1TB-NVME',
        materialName: '三星 980 PRO 1TB',
        specification: 'NVMe M.2 SSD 1TB',
        quantity: 1,
        unit: '个',
        unitCost: 820,
        totalCost: 820,
        level: 1,
        parentId: null,
        children: []
      }
    ]
  }
];

const initialState = {
  boms: mockBOMs,
  materials: mockMaterials,
  currentBOM: null,
  currentMaterial: null,
  loading: false,
  error: null,
  searchKeyword: '',
  filters: {
    status: '',
    type: '',
    category: ''
  },
  pagination: {
    current: 1,
    pageSize: 10,
    total: mockBOMs.length
  }
};

const bomSlice = createSlice({
  name: 'bom',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setBOMs: (state, action) => {
      state.boms = action.payload;
      state.pagination.total = action.payload.length;
    },
    setMaterials: (state, action) => {
      state.materials = action.payload;
    },
    addBOM: (state, action) => {
      const newBOM = {
        ...action.payload,
        id: `BOM-${String(state.boms.length + 1).padStart(3, '0')}`,
        createTime: new Date().toISOString().split('T')[0],
        updateTime: new Date().toISOString().split('T')[0],
        status: 'active'
      };
      state.boms.unshift(newBOM);
      state.pagination.total = state.boms.length;
    },
    updateBOM: (state, action) => {
      const index = state.boms.findIndex(b => b.id === action.payload.id);
      if (index !== -1) {
        state.boms[index] = { 
          ...state.boms[index], 
          ...action.payload,
          updateTime: new Date().toISOString().split('T')[0]
        };
      }
    },
    deleteBOM: (state, action) => {
      state.boms = state.boms.filter(b => b.id !== action.payload);
      state.pagination.total = state.boms.length;
    },
    addMaterial: (state, action) => {
      const newMaterial = {
        ...action.payload,
        id: `MAT-${String(state.materials.length + 1).padStart(3, '0')}`,
        createTime: new Date().toISOString().split('T')[0],
        status: 'active'
      };
      state.materials.unshift(newMaterial);
    },
    updateMaterial: (state, action) => {
      const index = state.materials.findIndex(m => m.id === action.payload.id);
      if (index !== -1) {
        state.materials[index] = { ...state.materials[index], ...action.payload };
      }
    },
    deleteMaterial: (state, action) => {
      state.materials = state.materials.filter(m => m.id !== action.payload);
    },
    setCurrentBOM: (state, action) => {
      state.currentBOM = action.payload;
    },
    setCurrentMaterial: (state, action) => {
      state.currentMaterial = action.payload;
    },
    setSearchKeyword: (state, action) => {
      state.searchKeyword = action.payload;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    resetFilters: (state) => {
      state.searchKeyword = '';
      state.filters = {
        status: '',
        type: '',
        category: ''
      };
      state.pagination.current = 1;
    },
    calculateBOMCost: (state, action) => {
      const { bomId } = action.payload;
      const bom = state.boms.find(b => b.id === bomId);
      if (bom) {
        bom.totalCost = bom.items.reduce((sum, item) => sum + item.totalCost, 0);
      }
    }
  }
});

export const {
  setLoading,
  setError,
  clearError,
  setBOMs,
  setMaterials,
  addBOM,
  updateBOM,
  deleteBOM,
  addMaterial,
  updateMaterial,
  deleteMaterial,
  setCurrentBOM,
  setCurrentMaterial,
  setSearchKeyword,
  setFilters,
  setPagination,
  resetFilters,
  calculateBOMCost
} = bomSlice.actions;

// 选择器
export const selectBOMs = (state) => state.bom.boms;
export const selectMaterials = (state) => state.bom.materials;
export const selectCurrentBOM = (state) => state.bom.currentBOM;
export const selectCurrentMaterial = (state) => state.bom.currentMaterial;
export const selectBOMLoading = (state) => state.bom.loading;
export const selectBOMError = (state) => state.bom.error;
export const selectBOMFilters = (state) => state.bom.filters;
export const selectBOMPagination = (state) => state.bom.pagination;

// 过滤后的BOM列表
export const selectFilteredBOMs = (state) => {
  const { boms, searchKeyword, filters } = state.bom;
  
  return boms.filter(bom => {
    // 搜索关键词过滤
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      if (!bom.code.toLowerCase().includes(keyword) &&
          !bom.name.toLowerCase().includes(keyword) &&
          !bom.description.toLowerCase().includes(keyword)) {
        return false;
      }
    }
    
    // 状态过滤
    if (filters.status && bom.status !== filters.status) {
      return false;
    }
    
    // 类型过滤
    if (filters.type && bom.type !== filters.type) {
      return false;
    }
    
    return true;
  });
};

// 过滤后的物料列表
export const selectFilteredMaterials = (state) => {
  const { materials, searchKeyword, filters } = state.bom;
  
  return materials.filter(material => {
    // 搜索关键词过滤
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      if (!material.code.toLowerCase().includes(keyword) &&
          !material.name.toLowerCase().includes(keyword) &&
          !material.specification.toLowerCase().includes(keyword)) {
        return false;
      }
    }
    
    // 分类过滤
    if (filters.category && material.category !== filters.category) {
      return false;
    }
    
    // 状态过滤
    if (filters.status && material.status !== filters.status) {
      return false;
    }
    
    return true;
  });
};

export default bomSlice.reducer;
