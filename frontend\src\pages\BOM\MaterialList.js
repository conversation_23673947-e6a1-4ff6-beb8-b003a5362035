import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  InputNumber,
  message,
  Tooltip,
  Badge
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  DatabaseOutlined,
  WarningOutlined
} from '@ant-design/icons';

import {
  selectFilteredMaterials,
  selectBOMLoading,
  selectBOMFilters,
  setSearchKeyword,
  setFilters,
  resetFilters,
  addMaterial,
  updateMaterial,
  deleteMaterial
} from '../../store/slices/bomSlice';

const { Title, Text } = Typography;
const { Option } = Select;

const MaterialList = () => {
  const dispatch = useDispatch();
  const materials = useSelector(selectFilteredMaterials);
  const loading = useSelector(selectBOMLoading);
  const filters = useSelector(selectBOMFilters);

  const [modalVisible, setModalVisible] = useState(false);
  const [editingMaterial, setEditingMaterial] = useState(null);
  const [form] = Form.useForm();

  const handleSearch = (value) => {
    dispatch(setSearchKeyword(value));
  };

  const handleFilterChange = (key, value) => {
    dispatch(setFilters({ [key]: value }));
  };

  const handleAdd = () => {
    setEditingMaterial(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (material) => {
    setEditingMaterial(material);
    form.setFieldsValue(material);
    setModalVisible(true);
  };

  const handleDelete = (material) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除物料"${material.name}"吗？`,
      onOk: () => {
        dispatch(deleteMaterial(material.id));
        message.success('删除成功');
      }
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      if (editingMaterial) {
        dispatch(updateMaterial({ ...editingMaterial, ...values }));
        message.success('更新成功');
      } else {
        dispatch(addMaterial(values));
        message.success('添加成功');
      }
      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const getCategoryColor = (category) => {
    const colors = {
      'CPU': 'red',
      '主板': 'blue',
      '内存': 'green',
      '存储': 'orange',
      '显卡': 'purple',
      '机箱': 'cyan',
      '电源': 'gold'
    };
    return colors[category] || 'default';
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'success' : 'error';
  };

  const getStockStatus = (current, min) => {
    if (current <= min) return { status: 'error', text: '库存不足' };
    if (current <= min * 2) return { status: 'warning', text: '库存偏低' };
    return { status: 'success', text: '库存正常' };
  };

  const columns = [
    {
      title: '物料编码',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      fixed: 'left',
      render: (text) => (
        <Text strong style={{ color: '#1890ff' }}>{text}</Text>
      )
    },
    {
      title: '物料名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true
    },
    {
      title: '规格型号',
      dataIndex: 'specification',
      key: 'specification',
      width: 200,
      ellipsis: true
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => (
        <Tag color={getCategoryColor(category)}>{category}</Tag>
      )
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 60
    },
    {
      title: '标准成本',
      dataIndex: 'standardCost',
      key: 'standardCost',
      width: 100,
      render: (cost) => formatCurrency(cost)
    },
    {
      title: '当前成本',
      dataIndex: 'currentCost',
      key: 'currentCost',
      width: 100,
      render: (cost, record) => (
        <Space direction="vertical" size={0}>
          <Text strong style={{ color: '#52c41a' }}>
            {formatCurrency(cost)}
          </Text>
          {cost !== record.standardCost && (
            <Text style={{ fontSize: '12px', color: '#8c8c8c' }}>
              差异: {formatCurrency(cost - record.standardCost)}
            </Text>
          )}
        </Space>
      )
    },
    {
      title: '库存情况',
      key: 'stock',
      width: 120,
      render: (_, record) => {
        const stockStatus = getStockStatus(record.stockQuantity, record.minStock);
        return (
          <Space direction="vertical" size={0}>
            <Text>现存: {record.stockQuantity}</Text>
            <Badge 
              status={stockStatus.status} 
              text={stockStatus.text}
              style={{ fontSize: '12px' }}
            />
          </Space>
        );
      }
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 120
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Badge 
          status={getStatusColor(status)} 
          text={status === 'active' ? '启用' : '停用'}
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 100
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 计算统计数据
  const stats = {
    total: materials.length,
    active: materials.filter(m => m.status === 'active').length,
    lowStock: materials.filter(m => m.stockQuantity <= m.minStock).length,
    categories: new Set(materials.map(m => m.category)).size,
    totalValue: materials.reduce((sum, m) => sum + (m.currentCost * m.stockQuantity), 0)
  };

  return (
    <div>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ margin: 0 }}>物料管理</Title>
        <Space>
          <Button icon={<ReloadOutlined />}>刷新</Button>
          <Button icon={<ExportOutlined />}>导出</Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增物料
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="物料总数" 
              value={stats.total} 
              suffix="个"
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="启用中" value={stats.active} suffix="个" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="库存不足" 
              value={stats.lowStock} 
              suffix="个"
              prefix={<WarningOutlined />}
              valueStyle={{ color: stats.lowStock > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="物料分类" value={stats.categories} suffix="类" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="库存总值" 
              value={stats.totalValue} 
              formatter={(value) => formatCurrency(value)}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索物料编码、名称、规格"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="分类"
              allowClear
              style={{ width: '100%' }}
              value={filters.category}
              onChange={(value) => handleFilterChange('category', value)}
            >
              <Option value="CPU">CPU</Option>
              <Option value="主板">主板</Option>
              <Option value="内存">内存</Option>
              <Option value="存储">存储</Option>
              <Option value="显卡">显卡</Option>
              <Option value="机箱">机箱</Option>
              <Option value="电源">电源</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="active">启用</Option>
              <Option value="inactive">停用</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Button onClick={() => dispatch(resetFilters())}>重置</Button>
          </Col>
        </Row>
      </Card>

      {/* 物料列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={materials}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
          }}
          scroll={{ x: 1400 }}
          size="small"
        />
      </Card>

      {/* 新增/编辑物料弹窗 */}
      <Modal
        title={editingMaterial ? '编辑物料' : '新增物料'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active',
            unit: '个',
            minStock: 10,
            stockQuantity: 0
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="物料编码"
                rules={[{ required: true, message: '请输入物料编码' }]}
              >
                <Input placeholder="请输入物料编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="物料名称"
                rules={[{ required: true, message: '请输入物料名称' }]}
              >
                <Input placeholder="请输入物料名称" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="specification"
            label="规格型号"
          >
            <Input placeholder="请输入规格型号" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  <Option value="CPU">CPU</Option>
                  <Option value="主板">主板</Option>
                  <Option value="内存">内存</Option>
                  <Option value="存储">存储</Option>
                  <Option value="显卡">显卡</Option>
                  <Option value="机箱">机箱</Option>
                  <Option value="电源">电源</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
              >
                <Select>
                  <Option value="个">个</Option>
                  <Option value="套">套</Option>
                  <Option value="台">台</Option>
                  <Option value="块">块</Option>
                  <Option value="条">条</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label="状态"
              >
                <Select>
                  <Option value="active">启用</Option>
                  <Option value="inactive">停用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="standardCost"
                label="标准成本"
                rules={[{ required: true, message: '请输入标准成本' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入标准成本"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="currentCost"
                label="当前成本"
                rules={[{ required: true, message: '请输入当前成本' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入当前成本"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="stockQuantity"
                label="库存数量"
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入库存数量"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="minStock"
                label="最低库存"
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入最低库存"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="supplier"
                label="供应商"
              >
                <Input placeholder="请输入供应商" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default MaterialList;
