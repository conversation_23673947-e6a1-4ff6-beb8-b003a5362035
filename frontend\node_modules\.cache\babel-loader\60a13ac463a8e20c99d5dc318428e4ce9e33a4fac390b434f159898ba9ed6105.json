{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n// 模拟订单数据\nconst mockOrders = [{\n  id: 'ORD-2024-001',\n  customerId: 1,\n  customerName: '华为技术有限公司',\n  title: '服务器设备采购',\n  amount: 500000,\n  status: 'completed',\n  paymentStatus: 'paid',\n  progress: 100,\n  estimatedProfit: 125000,\n  actualProfit: 120000,\n  profitMargin: 24,\n  actualProfitMargin: 24,\n  createTime: '2024-01-15',\n  deliveryDate: '2024-02-15',\n  completedDate: '2024-02-10',\n  salesperson: '李业务',\n  priority: 'high',\n  costs: {\n    material: 300000,\n    labor: 50000,\n    other: 30000,\n    total: 380000\n  },\n  payments: [{\n    date: '2024-01-20',\n    amount: 150000,\n    type: '预付款'\n  }, {\n    date: '2024-02-15',\n    amount: 350000,\n    type: '尾款'\n  }],\n  tasks: [{\n    name: '需求分析',\n    status: 'completed',\n    assignee: '张工程师'\n  }, {\n    name: '方案设计',\n    status: 'completed',\n    assignee: '李工程师'\n  }, {\n    name: '设备采购',\n    status: 'completed',\n    assignee: '王采购'\n  }, {\n    name: '安装调试',\n    status: 'completed',\n    assignee: '赵技术'\n  }],\n  bomId: 'BOM-001',\n  bomName: '高性能服务器配置'\n}, {\n  id: 'ORD-2024-002',\n  customerId: 2,\n  customerName: '小米科技有限公司',\n  title: '智能设备定制开发',\n  amount: 300000,\n  status: 'in_progress',\n  paymentStatus: 'partial',\n  progress: 75,\n  estimatedProfit: 90000,\n  actualProfit: 85000,\n  profitMargin: 30,\n  actualProfitMargin: 28.3,\n  createTime: '2024-01-20',\n  deliveryDate: '2024-03-20',\n  completedDate: null,\n  salesperson: '李业务',\n  priority: 'medium',\n  costs: {\n    material: 150000,\n    labor: 45000,\n    other: 20000,\n    total: 215000\n  },\n  payments: [{\n    date: '2024-01-25',\n    amount: 90000,\n    type: '预付款'\n  }],\n  receivables: [{\n    amount: 210000,\n    dueDate: '2024-03-25',\n    status: 'pending'\n  }],\n  tasks: [{\n    name: '需求调研',\n    status: 'completed',\n    assignee: '张工程师'\n  }, {\n    name: '原型开发',\n    status: 'completed',\n    assignee: '李工程师'\n  }, {\n    name: '功能测试',\n    status: 'in_progress',\n    assignee: '王测试'\n  }, {\n    name: '交付部署',\n    status: 'pending',\n    assignee: '赵技术'\n  }],\n  bomId: 'BOM-002',\n  bomName: '图形工作站配置'\n}, {\n  id: 'ORD-2024-003',\n  customerId: 3,\n  customerName: '阿里巴巴集团',\n  title: '数据分析平台建设',\n  amount: 800000,\n  status: 'pending',\n  paymentStatus: 'unpaid',\n  progress: 10,\n  estimatedProfit: 200000,\n  actualProfit: 0,\n  profitMargin: 25,\n  actualProfitMargin: 0,\n  createTime: '2024-02-01',\n  deliveryDate: '2024-05-01',\n  completedDate: null,\n  salesperson: '李业务',\n  priority: 'high',\n  costs: {\n    material: 400000,\n    labor: 150000,\n    other: 50000,\n    total: 600000\n  },\n  payments: [],\n  receivables: [{\n    amount: 240000,\n    dueDate: '2024-02-15',\n    status: 'overdue'\n  }, {\n    amount: 560000,\n    dueDate: '2024-05-15',\n    status: 'pending'\n  }],\n  tasks: [{\n    name: '需求分析',\n    status: 'in_progress',\n    assignee: '张工程师'\n  }, {\n    name: '架构设计',\n    status: 'pending',\n    assignee: '李工程师'\n  }, {\n    name: '开发实施',\n    status: 'pending',\n    assignee: '开发团队'\n  }, {\n    name: '测试验收',\n    status: 'pending',\n    assignee: '王测试'\n  }],\n  bomId: 'BOM-003',\n  bomName: '基础办公电脑配置'\n}];\nconst initialState = {\n  orders: mockOrders,\n  currentOrder: null,\n  loading: false,\n  error: null,\n  searchKeyword: '',\n  filters: {\n    status: '',\n    paymentStatus: '',\n    salesperson: '',\n    priority: '',\n    dateRange: []\n  },\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: mockOrders.length\n  }\n};\nconst orderSlice = createSlice({\n  name: 'order',\n  initialState,\n  reducers: {\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    setOrders: (state, action) => {\n      state.orders = action.payload;\n      state.pagination.total = action.payload.length;\n    },\n    addOrder: (state, action) => {\n      const newOrder = {\n        ...action.payload,\n        id: `ORD-${new Date().getFullYear()}-${String(state.orders.length + 1).padStart(3, '0')}`,\n        createTime: new Date().toISOString().split('T')[0],\n        status: 'pending',\n        paymentStatus: 'unpaid',\n        progress: 0,\n        actualProfit: 0,\n        actualProfitMargin: 0,\n        payments: [],\n        tasks: []\n      };\n      state.orders.unshift(newOrder);\n      state.pagination.total = state.orders.length;\n    },\n    updateOrder: (state, action) => {\n      const index = state.orders.findIndex(o => o.id === action.payload.id);\n      if (index !== -1) {\n        state.orders[index] = {\n          ...state.orders[index],\n          ...action.payload\n        };\n      }\n    },\n    updateOrderStatus: (state, action) => {\n      const {\n        id,\n        status\n      } = action.payload;\n      const order = state.orders.find(o => o.id === id);\n      if (order) {\n        order.status = status;\n        if (status === 'completed') {\n          order.progress = 100;\n          order.completedDate = new Date().toISOString().split('T')[0];\n        }\n      }\n    },\n    updateOrderProgress: (state, action) => {\n      const {\n        id,\n        progress\n      } = action.payload;\n      const order = state.orders.find(o => o.id === id);\n      if (order) {\n        order.progress = progress;\n        if (progress === 100 && order.status !== 'completed') {\n          order.status = 'completed';\n          order.completedDate = new Date().toISOString().split('T')[0];\n        }\n      }\n    },\n    addPayment: (state, action) => {\n      const {\n        orderId,\n        payment\n      } = action.payload;\n      const order = state.orders.find(o => o.id === orderId);\n      if (order) {\n        order.payments.push(payment);\n        const totalPaid = order.payments.reduce((sum, p) => sum + p.amount, 0);\n        if (totalPaid >= order.amount) {\n          order.paymentStatus = 'paid';\n        } else if (totalPaid > 0) {\n          order.paymentStatus = 'partial';\n        }\n      }\n    },\n    deleteOrder: (state, action) => {\n      state.orders = state.orders.filter(o => o.id !== action.payload);\n      state.pagination.total = state.orders.length;\n    },\n    setCurrentOrder: (state, action) => {\n      state.currentOrder = action.payload;\n    },\n    setSearchKeyword: (state, action) => {\n      state.searchKeyword = action.payload;\n    },\n    setFilters: (state, action) => {\n      state.filters = {\n        ...state.filters,\n        ...action.payload\n      };\n    },\n    setPagination: (state, action) => {\n      state.pagination = {\n        ...state.pagination,\n        ...action.payload\n      };\n    },\n    resetFilters: state => {\n      state.searchKeyword = '';\n      state.filters = {\n        status: '',\n        paymentStatus: '',\n        salesperson: '',\n        priority: '',\n        dateRange: []\n      };\n      state.pagination.current = 1;\n    }\n  }\n});\nexport const {\n  setLoading,\n  setError,\n  clearError,\n  setOrders,\n  addOrder,\n  updateOrder,\n  updateOrderStatus,\n  updateOrderProgress,\n  addPayment,\n  deleteOrder,\n  setCurrentOrder,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters\n} = orderSlice.actions;\n\n// 选择器\nexport const selectOrders = state => state.order.orders;\nexport const selectCurrentOrder = state => state.order.currentOrder;\nexport const selectOrderLoading = state => state.order.loading;\nexport const selectOrderError = state => state.order.error;\nexport const selectOrderFilters = state => state.order.filters;\nexport const selectOrderPagination = state => state.order.pagination;\n\n// 过滤后的订单列表\nexport const selectFilteredOrders = state => {\n  const {\n    orders,\n    searchKeyword,\n    filters\n  } = state.order;\n  return orders.filter(order => {\n    // 搜索关键词过滤\n    if (searchKeyword) {\n      const keyword = searchKeyword.toLowerCase();\n      if (!order.id.toLowerCase().includes(keyword) && !order.customerName.toLowerCase().includes(keyword) && !order.title.toLowerCase().includes(keyword)) {\n        return false;\n      }\n    }\n\n    // 状态过滤\n    if (filters.status && order.status !== filters.status) {\n      return false;\n    }\n\n    // 付款状态过滤\n    if (filters.paymentStatus && order.paymentStatus !== filters.paymentStatus) {\n      return false;\n    }\n\n    // 业务员过滤\n    if (filters.salesperson && order.salesperson !== filters.salesperson) {\n      return false;\n    }\n\n    // 优先级过滤\n    if (filters.priority && order.priority !== filters.priority) {\n      return false;\n    }\n    return true;\n  });\n};\nexport default orderSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "mockOrders", "id", "customerId", "customerName", "title", "amount", "status", "paymentStatus", "progress", "estimatedProfit", "actualProfit", "profitMargin", "actualProfitMargin", "createTime", "deliveryDate", "completedDate", "salesperson", "priority", "costs", "material", "labor", "other", "total", "payments", "date", "type", "tasks", "name", "assignee", "bomId", "bom<PERSON>ame", "receivables", "dueDate", "initialState", "orders", "currentOrder", "loading", "error", "searchKeyword", "filters", "date<PERSON><PERSON><PERSON>", "pagination", "current", "pageSize", "length", "orderSlice", "reducers", "setLoading", "state", "action", "payload", "setError", "clearError", "setOrders", "addOrder", "newOrder", "Date", "getFullYear", "String", "padStart", "toISOString", "split", "unshift", "updateOrder", "index", "findIndex", "o", "updateOrderStatus", "order", "find", "updateOrderProgress", "addPayment", "orderId", "payment", "push", "totalPaid", "reduce", "sum", "p", "deleteOrder", "filter", "setCurrentOrder", "setSearchKeyword", "setFilters", "setPagination", "resetFilters", "actions", "selectOrders", "selectCurrentOrder", "selectOrderLoading", "selectOrderError", "selectOrderFilters", "selectOrderPagination", "selectFilteredOrders", "keyword", "toLowerCase", "includes", "reducer"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/store/slices/orderSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n// 模拟订单数据\nconst mockOrders = [\n  {\n    id: 'ORD-2024-001',\n    customerId: 1,\n    customerName: '华为技术有限公司',\n    title: '服务器设备采购',\n    amount: 500000,\n    status: 'completed',\n    paymentStatus: 'paid',\n    progress: 100,\n    estimatedProfit: 125000,\n    actualProfit: 120000,\n    profitMargin: 24,\n    actualProfitMargin: 24,\n    createTime: '2024-01-15',\n    deliveryDate: '2024-02-15',\n    completedDate: '2024-02-10',\n    salesperson: '李业务',\n    priority: 'high',\n    costs: {\n      material: 300000,\n      labor: 50000,\n      other: 30000,\n      total: 380000\n    },\n    payments: [\n      { date: '2024-01-20', amount: 150000, type: '预付款' },\n      { date: '2024-02-15', amount: 350000, type: '尾款' }\n    ],\n    tasks: [\n      { name: '需求分析', status: 'completed', assignee: '张工程师' },\n      { name: '方案设计', status: 'completed', assignee: '李工程师' },\n      { name: '设备采购', status: 'completed', assignee: '王采购' },\n      { name: '安装调试', status: 'completed', assignee: '赵技术' }\n    ],\n    bomId: 'BOM-001',\n    bomName: '高性能服务器配置'\n  },\n  {\n    id: 'ORD-2024-002',\n    customerId: 2,\n    customerName: '小米科技有限公司',\n    title: '智能设备定制开发',\n    amount: 300000,\n    status: 'in_progress',\n    paymentStatus: 'partial',\n    progress: 75,\n    estimatedProfit: 90000,\n    actualProfit: 85000,\n    profitMargin: 30,\n    actualProfitMargin: 28.3,\n    createTime: '2024-01-20',\n    deliveryDate: '2024-03-20',\n    completedDate: null,\n    salesperson: '李业务',\n    priority: 'medium',\n    costs: {\n      material: 150000,\n      labor: 45000,\n      other: 20000,\n      total: 215000\n    },\n    payments: [\n      { date: '2024-01-25', amount: 90000, type: '预付款' }\n    ],\n    receivables: [\n      { amount: 210000, dueDate: '2024-03-25', status: 'pending' }\n    ],\n    tasks: [\n      { name: '需求调研', status: 'completed', assignee: '张工程师' },\n      { name: '原型开发', status: 'completed', assignee: '李工程师' },\n      { name: '功能测试', status: 'in_progress', assignee: '王测试' },\n      { name: '交付部署', status: 'pending', assignee: '赵技术' }\n    ],\n    bomId: 'BOM-002',\n    bomName: '图形工作站配置'\n  },\n  {\n    id: 'ORD-2024-003',\n    customerId: 3,\n    customerName: '阿里巴巴集团',\n    title: '数据分析平台建设',\n    amount: 800000,\n    status: 'pending',\n    paymentStatus: 'unpaid',\n    progress: 10,\n    estimatedProfit: 200000,\n    actualProfit: 0,\n    profitMargin: 25,\n    actualProfitMargin: 0,\n    createTime: '2024-02-01',\n    deliveryDate: '2024-05-01',\n    completedDate: null,\n    salesperson: '李业务',\n    priority: 'high',\n    costs: {\n      material: 400000,\n      labor: 150000,\n      other: 50000,\n      total: 600000\n    },\n    payments: [],\n    receivables: [\n      { amount: 240000, dueDate: '2024-02-15', status: 'overdue' },\n      { amount: 560000, dueDate: '2024-05-15', status: 'pending' }\n    ],\n    tasks: [\n      { name: '需求分析', status: 'in_progress', assignee: '张工程师' },\n      { name: '架构设计', status: 'pending', assignee: '李工程师' },\n      { name: '开发实施', status: 'pending', assignee: '开发团队' },\n      { name: '测试验收', status: 'pending', assignee: '王测试' }\n    ],\n    bomId: 'BOM-003',\n    bomName: '基础办公电脑配置'\n  }\n];\n\nconst initialState = {\n  orders: mockOrders,\n  currentOrder: null,\n  loading: false,\n  error: null,\n  searchKeyword: '',\n  filters: {\n    status: '',\n    paymentStatus: '',\n    salesperson: '',\n    priority: '',\n    dateRange: []\n  },\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: mockOrders.length\n  }\n};\n\nconst orderSlice = createSlice({\n  name: 'order',\n  initialState,\n  reducers: {\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setOrders: (state, action) => {\n      state.orders = action.payload;\n      state.pagination.total = action.payload.length;\n    },\n    addOrder: (state, action) => {\n      const newOrder = {\n        ...action.payload,\n        id: `ORD-${new Date().getFullYear()}-${String(state.orders.length + 1).padStart(3, '0')}`,\n        createTime: new Date().toISOString().split('T')[0],\n        status: 'pending',\n        paymentStatus: 'unpaid',\n        progress: 0,\n        actualProfit: 0,\n        actualProfitMargin: 0,\n        payments: [],\n        tasks: []\n      };\n      state.orders.unshift(newOrder);\n      state.pagination.total = state.orders.length;\n    },\n    updateOrder: (state, action) => {\n      const index = state.orders.findIndex(o => o.id === action.payload.id);\n      if (index !== -1) {\n        state.orders[index] = { ...state.orders[index], ...action.payload };\n      }\n    },\n    updateOrderStatus: (state, action) => {\n      const { id, status } = action.payload;\n      const order = state.orders.find(o => o.id === id);\n      if (order) {\n        order.status = status;\n        if (status === 'completed') {\n          order.progress = 100;\n          order.completedDate = new Date().toISOString().split('T')[0];\n        }\n      }\n    },\n    updateOrderProgress: (state, action) => {\n      const { id, progress } = action.payload;\n      const order = state.orders.find(o => o.id === id);\n      if (order) {\n        order.progress = progress;\n        if (progress === 100 && order.status !== 'completed') {\n          order.status = 'completed';\n          order.completedDate = new Date().toISOString().split('T')[0];\n        }\n      }\n    },\n    addPayment: (state, action) => {\n      const { orderId, payment } = action.payload;\n      const order = state.orders.find(o => o.id === orderId);\n      if (order) {\n        order.payments.push(payment);\n        const totalPaid = order.payments.reduce((sum, p) => sum + p.amount, 0);\n        if (totalPaid >= order.amount) {\n          order.paymentStatus = 'paid';\n        } else if (totalPaid > 0) {\n          order.paymentStatus = 'partial';\n        }\n      }\n    },\n    deleteOrder: (state, action) => {\n      state.orders = state.orders.filter(o => o.id !== action.payload);\n      state.pagination.total = state.orders.length;\n    },\n    setCurrentOrder: (state, action) => {\n      state.currentOrder = action.payload;\n    },\n    setSearchKeyword: (state, action) => {\n      state.searchKeyword = action.payload;\n    },\n    setFilters: (state, action) => {\n      state.filters = { ...state.filters, ...action.payload };\n    },\n    setPagination: (state, action) => {\n      state.pagination = { ...state.pagination, ...action.payload };\n    },\n    resetFilters: (state) => {\n      state.searchKeyword = '';\n      state.filters = {\n        status: '',\n        paymentStatus: '',\n        salesperson: '',\n        priority: '',\n        dateRange: []\n      };\n      state.pagination.current = 1;\n    }\n  }\n});\n\nexport const {\n  setLoading,\n  setError,\n  clearError,\n  setOrders,\n  addOrder,\n  updateOrder,\n  updateOrderStatus,\n  updateOrderProgress,\n  addPayment,\n  deleteOrder,\n  setCurrentOrder,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters\n} = orderSlice.actions;\n\n// 选择器\nexport const selectOrders = (state) => state.order.orders;\nexport const selectCurrentOrder = (state) => state.order.currentOrder;\nexport const selectOrderLoading = (state) => state.order.loading;\nexport const selectOrderError = (state) => state.order.error;\nexport const selectOrderFilters = (state) => state.order.filters;\nexport const selectOrderPagination = (state) => state.order.pagination;\n\n// 过滤后的订单列表\nexport const selectFilteredOrders = (state) => {\n  const { orders, searchKeyword, filters } = state.order;\n  \n  return orders.filter(order => {\n    // 搜索关键词过滤\n    if (searchKeyword) {\n      const keyword = searchKeyword.toLowerCase();\n      if (!order.id.toLowerCase().includes(keyword) &&\n          !order.customerName.toLowerCase().includes(keyword) &&\n          !order.title.toLowerCase().includes(keyword)) {\n        return false;\n      }\n    }\n    \n    // 状态过滤\n    if (filters.status && order.status !== filters.status) {\n      return false;\n    }\n    \n    // 付款状态过滤\n    if (filters.paymentStatus && order.paymentStatus !== filters.paymentStatus) {\n      return false;\n    }\n    \n    // 业务员过滤\n    if (filters.salesperson && order.salesperson !== filters.salesperson) {\n      return false;\n    }\n    \n    // 优先级过滤\n    if (filters.priority && order.priority !== filters.priority) {\n      return false;\n    }\n    \n    return true;\n  });\n};\n\nexport default orderSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA,MAAMC,UAAU,GAAG,CACjB;EACEC,EAAE,EAAE,cAAc;EAClBC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,UAAU;EACxBC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,MAAM;EACdC,MAAM,EAAE,WAAW;EACnBC,aAAa,EAAE,MAAM;EACrBC,QAAQ,EAAE,GAAG;EACbC,eAAe,EAAE,MAAM;EACvBC,YAAY,EAAE,MAAM;EACpBC,YAAY,EAAE,EAAE;EAChBC,kBAAkB,EAAE,EAAE;EACtBC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,YAAY;EAC1BC,aAAa,EAAE,YAAY;EAC3BC,WAAW,EAAE,KAAK;EAClBC,QAAQ,EAAE,MAAM;EAChBC,KAAK,EAAE;IACLC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IAAEC,IAAI,EAAE,YAAY;IAAEnB,MAAM,EAAE,MAAM;IAAEoB,IAAI,EAAE;EAAM,CAAC,EACnD;IAAED,IAAI,EAAE,YAAY;IAAEnB,MAAM,EAAE,MAAM;IAAEoB,IAAI,EAAE;EAAK,CAAC,CACnD;EACDC,KAAK,EAAE,CACL;IAAEC,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,WAAW;IAAEsB,QAAQ,EAAE;EAAO,CAAC,EACvD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,WAAW;IAAEsB,QAAQ,EAAE;EAAO,CAAC,EACvD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,WAAW;IAAEsB,QAAQ,EAAE;EAAM,CAAC,EACtD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,WAAW;IAAEsB,QAAQ,EAAE;EAAM,CAAC,CACvD;EACDC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE;AACX,CAAC,EACD;EACE7B,EAAE,EAAE,cAAc;EAClBC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,UAAU;EACxBC,KAAK,EAAE,UAAU;EACjBC,MAAM,EAAE,MAAM;EACdC,MAAM,EAAE,aAAa;EACrBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,KAAK;EACtBC,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE,EAAE;EAChBC,kBAAkB,EAAE,IAAI;EACxBC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,YAAY;EAC1BC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,KAAK;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE;IACLC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IAAEC,IAAI,EAAE,YAAY;IAAEnB,MAAM,EAAE,KAAK;IAAEoB,IAAI,EAAE;EAAM,CAAC,CACnD;EACDM,WAAW,EAAE,CACX;IAAE1B,MAAM,EAAE,MAAM;IAAE2B,OAAO,EAAE,YAAY;IAAE1B,MAAM,EAAE;EAAU,CAAC,CAC7D;EACDoB,KAAK,EAAE,CACL;IAAEC,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,WAAW;IAAEsB,QAAQ,EAAE;EAAO,CAAC,EACvD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,WAAW;IAAEsB,QAAQ,EAAE;EAAO,CAAC,EACvD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,aAAa;IAAEsB,QAAQ,EAAE;EAAM,CAAC,EACxD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,SAAS;IAAEsB,QAAQ,EAAE;EAAM,CAAC,CACrD;EACDC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE;AACX,CAAC,EACD;EACE7B,EAAE,EAAE,cAAc;EAClBC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,QAAQ;EACtBC,KAAK,EAAE,UAAU;EACjBC,MAAM,EAAE,MAAM;EACdC,MAAM,EAAE,SAAS;EACjBC,aAAa,EAAE,QAAQ;EACvBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,MAAM;EACvBC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,EAAE;EAChBC,kBAAkB,EAAE,CAAC;EACrBC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,YAAY;EAC1BC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,KAAK;EAClBC,QAAQ,EAAE,MAAM;EAChBC,KAAK,EAAE;IACLC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,EAAE;EACZQ,WAAW,EAAE,CACX;IAAE1B,MAAM,EAAE,MAAM;IAAE2B,OAAO,EAAE,YAAY;IAAE1B,MAAM,EAAE;EAAU,CAAC,EAC5D;IAAED,MAAM,EAAE,MAAM;IAAE2B,OAAO,EAAE,YAAY;IAAE1B,MAAM,EAAE;EAAU,CAAC,CAC7D;EACDoB,KAAK,EAAE,CACL;IAAEC,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,aAAa;IAAEsB,QAAQ,EAAE;EAAO,CAAC,EACzD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,SAAS;IAAEsB,QAAQ,EAAE;EAAO,CAAC,EACrD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,SAAS;IAAEsB,QAAQ,EAAE;EAAO,CAAC,EACrD;IAAED,IAAI,EAAE,MAAM;IAAErB,MAAM,EAAE,SAAS;IAAEsB,QAAQ,EAAE;EAAM,CAAC,CACrD;EACDC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE;AACX,CAAC,CACF;AAED,MAAMG,YAAY,GAAG;EACnBC,MAAM,EAAElC,UAAU;EAClBmC,YAAY,EAAE,IAAI;EAClBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,aAAa,EAAE,EAAE;EACjBC,OAAO,EAAE;IACPjC,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE,EAAE;IACjBS,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZuB,SAAS,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZrB,KAAK,EAAEtB,UAAU,CAAC4C;EACpB;AACF,CAAC;AAED,MAAMC,UAAU,GAAG9C,WAAW,CAAC;EAC7B4B,IAAI,EAAE,OAAO;EACbM,YAAY;EACZa,QAAQ,EAAE;IACRC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACZ,OAAO,GAAGa,MAAM,CAACC,OAAO;IAChC,CAAC;IACDC,QAAQ,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACX,KAAK,GAAGY,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDE,UAAU,EAAGJ,KAAK,IAAK;MACrBA,KAAK,CAACX,KAAK,GAAG,IAAI;IACpB,CAAC;IACDgB,SAAS,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MAC5BD,KAAK,CAACd,MAAM,GAAGe,MAAM,CAACC,OAAO;MAC7BF,KAAK,CAACP,UAAU,CAACnB,KAAK,GAAG2B,MAAM,CAACC,OAAO,CAACN,MAAM;IAChD,CAAC;IACDU,QAAQ,EAAEA,CAACN,KAAK,EAAEC,MAAM,KAAK;MAC3B,MAAMM,QAAQ,GAAG;QACf,GAAGN,MAAM,CAACC,OAAO;QACjBjD,EAAE,EAAE,OAAO,IAAIuD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACV,KAAK,CAACd,MAAM,CAACU,MAAM,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QACzF9C,UAAU,EAAE,IAAI2C,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDvD,MAAM,EAAE,SAAS;QACjBC,aAAa,EAAE,QAAQ;QACvBC,QAAQ,EAAE,CAAC;QACXE,YAAY,EAAE,CAAC;QACfE,kBAAkB,EAAE,CAAC;QACrBW,QAAQ,EAAE,EAAE;QACZG,KAAK,EAAE;MACT,CAAC;MACDsB,KAAK,CAACd,MAAM,CAAC4B,OAAO,CAACP,QAAQ,CAAC;MAC9BP,KAAK,CAACP,UAAU,CAACnB,KAAK,GAAG0B,KAAK,CAACd,MAAM,CAACU,MAAM;IAC9C,CAAC;IACDmB,WAAW,EAAEA,CAACf,KAAK,EAAEC,MAAM,KAAK;MAC9B,MAAMe,KAAK,GAAGhB,KAAK,CAACd,MAAM,CAAC+B,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACjE,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAACjD,EAAE,CAAC;MACrE,IAAI+D,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBhB,KAAK,CAACd,MAAM,CAAC8B,KAAK,CAAC,GAAG;UAAE,GAAGhB,KAAK,CAACd,MAAM,CAAC8B,KAAK,CAAC;UAAE,GAAGf,MAAM,CAACC;QAAQ,CAAC;MACrE;IACF,CAAC;IACDiB,iBAAiB,EAAEA,CAACnB,KAAK,EAAEC,MAAM,KAAK;MACpC,MAAM;QAAEhD,EAAE;QAAEK;MAAO,CAAC,GAAG2C,MAAM,CAACC,OAAO;MACrC,MAAMkB,KAAK,GAAGpB,KAAK,CAACd,MAAM,CAACmC,IAAI,CAACH,CAAC,IAAIA,CAAC,CAACjE,EAAE,KAAKA,EAAE,CAAC;MACjD,IAAImE,KAAK,EAAE;QACTA,KAAK,CAAC9D,MAAM,GAAGA,MAAM;QACrB,IAAIA,MAAM,KAAK,WAAW,EAAE;UAC1B8D,KAAK,CAAC5D,QAAQ,GAAG,GAAG;UACpB4D,KAAK,CAACrD,aAAa,GAAG,IAAIyC,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9D;MACF;IACF,CAAC;IACDS,mBAAmB,EAAEA,CAACtB,KAAK,EAAEC,MAAM,KAAK;MACtC,MAAM;QAAEhD,EAAE;QAAEO;MAAS,CAAC,GAAGyC,MAAM,CAACC,OAAO;MACvC,MAAMkB,KAAK,GAAGpB,KAAK,CAACd,MAAM,CAACmC,IAAI,CAACH,CAAC,IAAIA,CAAC,CAACjE,EAAE,KAAKA,EAAE,CAAC;MACjD,IAAImE,KAAK,EAAE;QACTA,KAAK,CAAC5D,QAAQ,GAAGA,QAAQ;QACzB,IAAIA,QAAQ,KAAK,GAAG,IAAI4D,KAAK,CAAC9D,MAAM,KAAK,WAAW,EAAE;UACpD8D,KAAK,CAAC9D,MAAM,GAAG,WAAW;UAC1B8D,KAAK,CAACrD,aAAa,GAAG,IAAIyC,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9D;MACF;IACF,CAAC;IACDU,UAAU,EAAEA,CAACvB,KAAK,EAAEC,MAAM,KAAK;MAC7B,MAAM;QAAEuB,OAAO;QAAEC;MAAQ,CAAC,GAAGxB,MAAM,CAACC,OAAO;MAC3C,MAAMkB,KAAK,GAAGpB,KAAK,CAACd,MAAM,CAACmC,IAAI,CAACH,CAAC,IAAIA,CAAC,CAACjE,EAAE,KAAKuE,OAAO,CAAC;MACtD,IAAIJ,KAAK,EAAE;QACTA,KAAK,CAAC7C,QAAQ,CAACmD,IAAI,CAACD,OAAO,CAAC;QAC5B,MAAME,SAAS,GAAGP,KAAK,CAAC7C,QAAQ,CAACqD,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACzE,MAAM,EAAE,CAAC,CAAC;QACtE,IAAIsE,SAAS,IAAIP,KAAK,CAAC/D,MAAM,EAAE;UAC7B+D,KAAK,CAAC7D,aAAa,GAAG,MAAM;QAC9B,CAAC,MAAM,IAAIoE,SAAS,GAAG,CAAC,EAAE;UACxBP,KAAK,CAAC7D,aAAa,GAAG,SAAS;QACjC;MACF;IACF,CAAC;IACDwE,WAAW,EAAEA,CAAC/B,KAAK,EAAEC,MAAM,KAAK;MAC9BD,KAAK,CAACd,MAAM,GAAGc,KAAK,CAACd,MAAM,CAAC8C,MAAM,CAACd,CAAC,IAAIA,CAAC,CAACjE,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAAC;MAChEF,KAAK,CAACP,UAAU,CAACnB,KAAK,GAAG0B,KAAK,CAACd,MAAM,CAACU,MAAM;IAC9C,CAAC;IACDqC,eAAe,EAAEA,CAACjC,KAAK,EAAEC,MAAM,KAAK;MAClCD,KAAK,CAACb,YAAY,GAAGc,MAAM,CAACC,OAAO;IACrC,CAAC;IACDgC,gBAAgB,EAAEA,CAAClC,KAAK,EAAEC,MAAM,KAAK;MACnCD,KAAK,CAACV,aAAa,GAAGW,MAAM,CAACC,OAAO;IACtC,CAAC;IACDiC,UAAU,EAAEA,CAACnC,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACT,OAAO,GAAG;QAAE,GAAGS,KAAK,CAACT,OAAO;QAAE,GAAGU,MAAM,CAACC;MAAQ,CAAC;IACzD,CAAC;IACDkC,aAAa,EAAEA,CAACpC,KAAK,EAAEC,MAAM,KAAK;MAChCD,KAAK,CAACP,UAAU,GAAG;QAAE,GAAGO,KAAK,CAACP,UAAU;QAAE,GAAGQ,MAAM,CAACC;MAAQ,CAAC;IAC/D,CAAC;IACDmC,YAAY,EAAGrC,KAAK,IAAK;MACvBA,KAAK,CAACV,aAAa,GAAG,EAAE;MACxBU,KAAK,CAACT,OAAO,GAAG;QACdjC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,EAAE;QACjBS,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZuB,SAAS,EAAE;MACb,CAAC;MACDQ,KAAK,CAACP,UAAU,CAACC,OAAO,GAAG,CAAC;IAC9B;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXK,UAAU;EACVI,QAAQ;EACRC,UAAU;EACVC,SAAS;EACTC,QAAQ;EACRS,WAAW;EACXI,iBAAiB;EACjBG,mBAAmB;EACnBC,UAAU;EACVQ,WAAW;EACXE,eAAe;EACfC,gBAAgB;EAChBC,UAAU;EACVC,aAAa;EACbC;AACF,CAAC,GAAGxC,UAAU,CAACyC,OAAO;;AAEtB;AACA,OAAO,MAAMC,YAAY,GAAIvC,KAAK,IAAKA,KAAK,CAACoB,KAAK,CAAClC,MAAM;AACzD,OAAO,MAAMsD,kBAAkB,GAAIxC,KAAK,IAAKA,KAAK,CAACoB,KAAK,CAACjC,YAAY;AACrE,OAAO,MAAMsD,kBAAkB,GAAIzC,KAAK,IAAKA,KAAK,CAACoB,KAAK,CAAChC,OAAO;AAChE,OAAO,MAAMsD,gBAAgB,GAAI1C,KAAK,IAAKA,KAAK,CAACoB,KAAK,CAAC/B,KAAK;AAC5D,OAAO,MAAMsD,kBAAkB,GAAI3C,KAAK,IAAKA,KAAK,CAACoB,KAAK,CAAC7B,OAAO;AAChE,OAAO,MAAMqD,qBAAqB,GAAI5C,KAAK,IAAKA,KAAK,CAACoB,KAAK,CAAC3B,UAAU;;AAEtE;AACA,OAAO,MAAMoD,oBAAoB,GAAI7C,KAAK,IAAK;EAC7C,MAAM;IAAEd,MAAM;IAAEI,aAAa;IAAEC;EAAQ,CAAC,GAAGS,KAAK,CAACoB,KAAK;EAEtD,OAAOlC,MAAM,CAAC8C,MAAM,CAACZ,KAAK,IAAI;IAC5B;IACA,IAAI9B,aAAa,EAAE;MACjB,MAAMwD,OAAO,GAAGxD,aAAa,CAACyD,WAAW,CAAC,CAAC;MAC3C,IAAI,CAAC3B,KAAK,CAACnE,EAAE,CAAC8F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,IACzC,CAAC1B,KAAK,CAACjE,YAAY,CAAC4F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,IACnD,CAAC1B,KAAK,CAAChE,KAAK,CAAC2F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;QAChD,OAAO,KAAK;MACd;IACF;;IAEA;IACA,IAAIvD,OAAO,CAACjC,MAAM,IAAI8D,KAAK,CAAC9D,MAAM,KAAKiC,OAAO,CAACjC,MAAM,EAAE;MACrD,OAAO,KAAK;IACd;;IAEA;IACA,IAAIiC,OAAO,CAAChC,aAAa,IAAI6D,KAAK,CAAC7D,aAAa,KAAKgC,OAAO,CAAChC,aAAa,EAAE;MAC1E,OAAO,KAAK;IACd;;IAEA;IACA,IAAIgC,OAAO,CAACvB,WAAW,IAAIoD,KAAK,CAACpD,WAAW,KAAKuB,OAAO,CAACvB,WAAW,EAAE;MACpE,OAAO,KAAK;IACd;;IAEA;IACA,IAAIuB,OAAO,CAACtB,QAAQ,IAAImD,KAAK,CAACnD,QAAQ,KAAKsB,OAAO,CAACtB,QAAQ,EAAE;MAC3D,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;AAED,eAAe4B,UAAU,CAACoD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}