{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * @file Visual solution, for consistent option specification.\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapping from './VisualMapping.js';\nimport { getItemVisualFromData, setItemVisualFromData } from './helper.js';\nvar each = zrUtil.each;\nfunction hasKeys(obj) {\n  if (obj) {\n    for (var name_1 in obj) {\n      if (obj.hasOwnProperty(name_1)) {\n        return true;\n      }\n    }\n  }\n}\nexport function createVisualMappings(option, stateList, supplementVisualOption) {\n  var visualMappings = {};\n  each(stateList, function (state) {\n    var mappings = visualMappings[state] = createMappings();\n    each(option[state], function (visualData, visualType) {\n      if (!VisualMapping.isValidType(visualType)) {\n        return;\n      }\n      var mappingOption = {\n        type: visualType,\n        visual: visualData\n      };\n      supplementVisualOption && supplementVisualOption(mappingOption, state);\n      mappings[visualType] = new VisualMapping(mappingOption);\n      // Prepare a alpha for opacity, for some case that opacity\n      // is not supported, such as rendering using gradient color.\n      if (visualType === 'opacity') {\n        mappingOption = zrUtil.clone(mappingOption);\n        mappingOption.type = 'colorAlpha';\n        mappings.__hidden.__alphaForOpacity = new VisualMapping(mappingOption);\n      }\n    });\n  });\n  return visualMappings;\n  function createMappings() {\n    var Creater = function () {};\n    // Make sure hidden fields will not be visited by\n    // object iteration (with hasOwnProperty checking).\n    Creater.prototype.__hidden = Creater.prototype;\n    var obj = new Creater();\n    return obj;\n  }\n}\nexport function replaceVisualOption(thisOption, newOption, keys) {\n  // Visual attributes merge is not supported, otherwise it\n  // brings overcomplicated merge logic. See #2853. So if\n  // newOption has anyone of these keys, all of these keys\n  // will be reset. Otherwise, all keys remain.\n  var has;\n  zrUtil.each(keys, function (key) {\n    if (newOption.hasOwnProperty(key) && hasKeys(newOption[key])) {\n      has = true;\n    }\n  });\n  has && zrUtil.each(keys, function (key) {\n    if (newOption.hasOwnProperty(key) && hasKeys(newOption[key])) {\n      thisOption[key] = zrUtil.clone(newOption[key]);\n    } else {\n      delete thisOption[key];\n    }\n  });\n}\n/**\r\n * @param stateList\r\n * @param visualMappings\r\n * @param list\r\n * @param getValueState param: valueOrIndex, return: state.\r\n * @param scope Scope for getValueState\r\n * @param dimension Concrete dimension, if used.\r\n */\n// ???! handle brush?\nexport function applyVisual(stateList, visualMappings, data, getValueState, scope, dimension) {\n  var visualTypesMap = {};\n  zrUtil.each(stateList, function (state) {\n    var visualTypes = VisualMapping.prepareVisualTypes(visualMappings[state]);\n    visualTypesMap[state] = visualTypes;\n  });\n  var dataIndex;\n  function getVisual(key) {\n    return getItemVisualFromData(data, dataIndex, key);\n  }\n  function setVisual(key, value) {\n    setItemVisualFromData(data, dataIndex, key, value);\n  }\n  if (dimension == null) {\n    data.each(eachItem);\n  } else {\n    data.each([dimension], eachItem);\n  }\n  function eachItem(valueOrIndex, index) {\n    dataIndex = dimension == null ? valueOrIndex // First argument is index\n    : index;\n    var rawDataItem = data.getRawDataItem(dataIndex);\n    // Consider performance\n    // @ts-ignore\n    if (rawDataItem && rawDataItem.visualMap === false) {\n      return;\n    }\n    var valueState = getValueState.call(scope, valueOrIndex);\n    var mappings = visualMappings[valueState];\n    var visualTypes = visualTypesMap[valueState];\n    for (var i = 0, len = visualTypes.length; i < len; i++) {\n      var type = visualTypes[i];\n      mappings[type] && mappings[type].applyVisual(valueOrIndex, getVisual, setVisual);\n    }\n  }\n}\n/**\r\n * @param data\r\n * @param stateList\r\n * @param visualMappings <state, Object.<visualType, module:echarts/visual/VisualMapping>>\r\n * @param getValueState param: valueOrIndex, return: state.\r\n * @param dim dimension or dimension index.\r\n */\nexport function incrementalApplyVisual(stateList, visualMappings, getValueState, dim) {\n  var visualTypesMap = {};\n  zrUtil.each(stateList, function (state) {\n    var visualTypes = VisualMapping.prepareVisualTypes(visualMappings[state]);\n    visualTypesMap[state] = visualTypes;\n  });\n  return {\n    progress: function progress(params, data) {\n      var dimIndex;\n      if (dim != null) {\n        dimIndex = data.getDimensionIndex(dim);\n      }\n      function getVisual(key) {\n        return getItemVisualFromData(data, dataIndex, key);\n      }\n      function setVisual(key, value) {\n        setItemVisualFromData(data, dataIndex, key, value);\n      }\n      var dataIndex;\n      var store = data.getStore();\n      while ((dataIndex = params.next()) != null) {\n        var rawDataItem = data.getRawDataItem(dataIndex);\n        // Consider performance\n        // @ts-ignore\n        if (rawDataItem && rawDataItem.visualMap === false) {\n          continue;\n        }\n        var value = dim != null ? store.get(dimIndex, dataIndex) : dataIndex;\n        var valueState = getValueState(value);\n        var mappings = visualMappings[valueState];\n        var visualTypes = visualTypesMap[valueState];\n        for (var i = 0, len = visualTypes.length; i < len; i++) {\n          var type = visualTypes[i];\n          mappings[type] && mappings[type].applyVisual(value, getVisual, setVisual);\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "VisualMapping", "getItemVisualFromData", "setItemVisualFromData", "each", "<PERSON><PERSON><PERSON><PERSON>", "obj", "name_1", "hasOwnProperty", "createVisualMappings", "option", "stateList", "supplementVisualOption", "visualMappings", "state", "mappings", "createMappings", "visualData", "visualType", "isValidType", "mappingOption", "type", "visual", "clone", "__hidden", "__alphaForOpacity", "Creater", "prototype", "replaceVisualOption", "thisOption", "newOption", "keys", "has", "key", "applyVisual", "data", "getValueState", "scope", "dimension", "visualTypesMap", "visualTypes", "prepareVisualTypes", "dataIndex", "getVisual", "setVisual", "value", "eachItem", "valueOrIndex", "index", "rawDataItem", "getRawDataItem", "visualMap", "valueState", "call", "i", "len", "length", "incrementalApplyVisual", "dim", "progress", "params", "dimIndex", "getDimensionIndex", "store", "getStore", "next", "get"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/visual/visualSolution.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * @file Visual solution, for consistent option specification.\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapping from './VisualMapping.js';\nimport { getItemVisualFromData, setItemVisualFromData } from './helper.js';\nvar each = zrUtil.each;\nfunction hasKeys(obj) {\n  if (obj) {\n    for (var name_1 in obj) {\n      if (obj.hasOwnProperty(name_1)) {\n        return true;\n      }\n    }\n  }\n}\nexport function createVisualMappings(option, stateList, supplementVisualOption) {\n  var visualMappings = {};\n  each(stateList, function (state) {\n    var mappings = visualMappings[state] = createMappings();\n    each(option[state], function (visualData, visualType) {\n      if (!VisualMapping.isValidType(visualType)) {\n        return;\n      }\n      var mappingOption = {\n        type: visualType,\n        visual: visualData\n      };\n      supplementVisualOption && supplementVisualOption(mappingOption, state);\n      mappings[visualType] = new VisualMapping(mappingOption);\n      // Prepare a alpha for opacity, for some case that opacity\n      // is not supported, such as rendering using gradient color.\n      if (visualType === 'opacity') {\n        mappingOption = zrUtil.clone(mappingOption);\n        mappingOption.type = 'colorAlpha';\n        mappings.__hidden.__alphaForOpacity = new VisualMapping(mappingOption);\n      }\n    });\n  });\n  return visualMappings;\n  function createMappings() {\n    var Creater = function () {};\n    // Make sure hidden fields will not be visited by\n    // object iteration (with hasOwnProperty checking).\n    Creater.prototype.__hidden = Creater.prototype;\n    var obj = new Creater();\n    return obj;\n  }\n}\nexport function replaceVisualOption(thisOption, newOption, keys) {\n  // Visual attributes merge is not supported, otherwise it\n  // brings overcomplicated merge logic. See #2853. So if\n  // newOption has anyone of these keys, all of these keys\n  // will be reset. Otherwise, all keys remain.\n  var has;\n  zrUtil.each(keys, function (key) {\n    if (newOption.hasOwnProperty(key) && hasKeys(newOption[key])) {\n      has = true;\n    }\n  });\n  has && zrUtil.each(keys, function (key) {\n    if (newOption.hasOwnProperty(key) && hasKeys(newOption[key])) {\n      thisOption[key] = zrUtil.clone(newOption[key]);\n    } else {\n      delete thisOption[key];\n    }\n  });\n}\n/**\r\n * @param stateList\r\n * @param visualMappings\r\n * @param list\r\n * @param getValueState param: valueOrIndex, return: state.\r\n * @param scope Scope for getValueState\r\n * @param dimension Concrete dimension, if used.\r\n */\n// ???! handle brush?\nexport function applyVisual(stateList, visualMappings, data, getValueState, scope, dimension) {\n  var visualTypesMap = {};\n  zrUtil.each(stateList, function (state) {\n    var visualTypes = VisualMapping.prepareVisualTypes(visualMappings[state]);\n    visualTypesMap[state] = visualTypes;\n  });\n  var dataIndex;\n  function getVisual(key) {\n    return getItemVisualFromData(data, dataIndex, key);\n  }\n  function setVisual(key, value) {\n    setItemVisualFromData(data, dataIndex, key, value);\n  }\n  if (dimension == null) {\n    data.each(eachItem);\n  } else {\n    data.each([dimension], eachItem);\n  }\n  function eachItem(valueOrIndex, index) {\n    dataIndex = dimension == null ? valueOrIndex // First argument is index\n    : index;\n    var rawDataItem = data.getRawDataItem(dataIndex);\n    // Consider performance\n    // @ts-ignore\n    if (rawDataItem && rawDataItem.visualMap === false) {\n      return;\n    }\n    var valueState = getValueState.call(scope, valueOrIndex);\n    var mappings = visualMappings[valueState];\n    var visualTypes = visualTypesMap[valueState];\n    for (var i = 0, len = visualTypes.length; i < len; i++) {\n      var type = visualTypes[i];\n      mappings[type] && mappings[type].applyVisual(valueOrIndex, getVisual, setVisual);\n    }\n  }\n}\n/**\r\n * @param data\r\n * @param stateList\r\n * @param visualMappings <state, Object.<visualType, module:echarts/visual/VisualMapping>>\r\n * @param getValueState param: valueOrIndex, return: state.\r\n * @param dim dimension or dimension index.\r\n */\nexport function incrementalApplyVisual(stateList, visualMappings, getValueState, dim) {\n  var visualTypesMap = {};\n  zrUtil.each(stateList, function (state) {\n    var visualTypes = VisualMapping.prepareVisualTypes(visualMappings[state]);\n    visualTypesMap[state] = visualTypes;\n  });\n  return {\n    progress: function progress(params, data) {\n      var dimIndex;\n      if (dim != null) {\n        dimIndex = data.getDimensionIndex(dim);\n      }\n      function getVisual(key) {\n        return getItemVisualFromData(data, dataIndex, key);\n      }\n      function setVisual(key, value) {\n        setItemVisualFromData(data, dataIndex, key, value);\n      }\n      var dataIndex;\n      var store = data.getStore();\n      while ((dataIndex = params.next()) != null) {\n        var rawDataItem = data.getRawDataItem(dataIndex);\n        // Consider performance\n        // @ts-ignore\n        if (rawDataItem && rawDataItem.visualMap === false) {\n          continue;\n        }\n        var value = dim != null ? store.get(dimIndex, dataIndex) : dataIndex;\n        var valueState = getValueState(value);\n        var mappings = visualMappings[valueState];\n        var visualTypes = visualTypesMap[valueState];\n        for (var i = 0, len = visualTypes.length; i < len; i++) {\n          var type = visualTypes[i];\n          mappings[type] && mappings[type].applyVisual(value, getVisual, setVisual);\n        }\n      }\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,aAAa;AAC1E,IAAIC,IAAI,GAAGJ,MAAM,CAACI,IAAI;AACtB,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,IAAIA,GAAG,EAAE;IACP,KAAK,IAAIC,MAAM,IAAID,GAAG,EAAE;MACtB,IAAIA,GAAG,CAACE,cAAc,CAACD,MAAM,CAAC,EAAE;QAC9B,OAAO,IAAI;MACb;IACF;EACF;AACF;AACA,OAAO,SAASE,oBAAoBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,sBAAsB,EAAE;EAC9E,IAAIC,cAAc,GAAG,CAAC,CAAC;EACvBT,IAAI,CAACO,SAAS,EAAE,UAAUG,KAAK,EAAE;IAC/B,IAAIC,QAAQ,GAAGF,cAAc,CAACC,KAAK,CAAC,GAAGE,cAAc,CAAC,CAAC;IACvDZ,IAAI,CAACM,MAAM,CAACI,KAAK,CAAC,EAAE,UAAUG,UAAU,EAAEC,UAAU,EAAE;MACpD,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAACD,UAAU,CAAC,EAAE;QAC1C;MACF;MACA,IAAIE,aAAa,GAAG;QAClBC,IAAI,EAAEH,UAAU;QAChBI,MAAM,EAAEL;MACV,CAAC;MACDL,sBAAsB,IAAIA,sBAAsB,CAACQ,aAAa,EAAEN,KAAK,CAAC;MACtEC,QAAQ,CAACG,UAAU,CAAC,GAAG,IAAIjB,aAAa,CAACmB,aAAa,CAAC;MACvD;MACA;MACA,IAAIF,UAAU,KAAK,SAAS,EAAE;QAC5BE,aAAa,GAAGpB,MAAM,CAACuB,KAAK,CAACH,aAAa,CAAC;QAC3CA,aAAa,CAACC,IAAI,GAAG,YAAY;QACjCN,QAAQ,CAACS,QAAQ,CAACC,iBAAiB,GAAG,IAAIxB,aAAa,CAACmB,aAAa,CAAC;MACxE;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOP,cAAc;EACrB,SAASG,cAAcA,CAAA,EAAG;IACxB,IAAIU,OAAO,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;IAC5B;IACA;IACAA,OAAO,CAACC,SAAS,CAACH,QAAQ,GAAGE,OAAO,CAACC,SAAS;IAC9C,IAAIrB,GAAG,GAAG,IAAIoB,OAAO,CAAC,CAAC;IACvB,OAAOpB,GAAG;EACZ;AACF;AACA,OAAO,SAASsB,mBAAmBA,CAACC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAE;EAC/D;EACA;EACA;EACA;EACA,IAAIC,GAAG;EACPhC,MAAM,CAACI,IAAI,CAAC2B,IAAI,EAAE,UAAUE,GAAG,EAAE;IAC/B,IAAIH,SAAS,CAACtB,cAAc,CAACyB,GAAG,CAAC,IAAI5B,OAAO,CAACyB,SAAS,CAACG,GAAG,CAAC,CAAC,EAAE;MAC5DD,GAAG,GAAG,IAAI;IACZ;EACF,CAAC,CAAC;EACFA,GAAG,IAAIhC,MAAM,CAACI,IAAI,CAAC2B,IAAI,EAAE,UAAUE,GAAG,EAAE;IACtC,IAAIH,SAAS,CAACtB,cAAc,CAACyB,GAAG,CAAC,IAAI5B,OAAO,CAACyB,SAAS,CAACG,GAAG,CAAC,CAAC,EAAE;MAC5DJ,UAAU,CAACI,GAAG,CAAC,GAAGjC,MAAM,CAACuB,KAAK,CAACO,SAAS,CAACG,GAAG,CAAC,CAAC;IAChD,CAAC,MAAM;MACL,OAAOJ,UAAU,CAACI,GAAG,CAAC;IACxB;EACF,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACvB,SAAS,EAAEE,cAAc,EAAEsB,IAAI,EAAEC,aAAa,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC5F,IAAIC,cAAc,GAAG,CAAC,CAAC;EACvBvC,MAAM,CAACI,IAAI,CAACO,SAAS,EAAE,UAAUG,KAAK,EAAE;IACtC,IAAI0B,WAAW,GAAGvC,aAAa,CAACwC,kBAAkB,CAAC5B,cAAc,CAACC,KAAK,CAAC,CAAC;IACzEyB,cAAc,CAACzB,KAAK,CAAC,GAAG0B,WAAW;EACrC,CAAC,CAAC;EACF,IAAIE,SAAS;EACb,SAASC,SAASA,CAACV,GAAG,EAAE;IACtB,OAAO/B,qBAAqB,CAACiC,IAAI,EAAEO,SAAS,EAAET,GAAG,CAAC;EACpD;EACA,SAASW,SAASA,CAACX,GAAG,EAAEY,KAAK,EAAE;IAC7B1C,qBAAqB,CAACgC,IAAI,EAAEO,SAAS,EAAET,GAAG,EAAEY,KAAK,CAAC;EACpD;EACA,IAAIP,SAAS,IAAI,IAAI,EAAE;IACrBH,IAAI,CAAC/B,IAAI,CAAC0C,QAAQ,CAAC;EACrB,CAAC,MAAM;IACLX,IAAI,CAAC/B,IAAI,CAAC,CAACkC,SAAS,CAAC,EAAEQ,QAAQ,CAAC;EAClC;EACA,SAASA,QAAQA,CAACC,YAAY,EAAEC,KAAK,EAAE;IACrCN,SAAS,GAAGJ,SAAS,IAAI,IAAI,GAAGS,YAAY,CAAC;IAAA,EAC3CC,KAAK;IACP,IAAIC,WAAW,GAAGd,IAAI,CAACe,cAAc,CAACR,SAAS,CAAC;IAChD;IACA;IACA,IAAIO,WAAW,IAAIA,WAAW,CAACE,SAAS,KAAK,KAAK,EAAE;MAClD;IACF;IACA,IAAIC,UAAU,GAAGhB,aAAa,CAACiB,IAAI,CAAChB,KAAK,EAAEU,YAAY,CAAC;IACxD,IAAIhC,QAAQ,GAAGF,cAAc,CAACuC,UAAU,CAAC;IACzC,IAAIZ,WAAW,GAAGD,cAAc,CAACa,UAAU,CAAC;IAC5C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGf,WAAW,CAACgB,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACtD,IAAIjC,IAAI,GAAGmB,WAAW,CAACc,CAAC,CAAC;MACzBvC,QAAQ,CAACM,IAAI,CAAC,IAAIN,QAAQ,CAACM,IAAI,CAAC,CAACa,WAAW,CAACa,YAAY,EAAEJ,SAAS,EAAEC,SAAS,CAAC;IAClF;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASa,sBAAsBA,CAAC9C,SAAS,EAAEE,cAAc,EAAEuB,aAAa,EAAEsB,GAAG,EAAE;EACpF,IAAInB,cAAc,GAAG,CAAC,CAAC;EACvBvC,MAAM,CAACI,IAAI,CAACO,SAAS,EAAE,UAAUG,KAAK,EAAE;IACtC,IAAI0B,WAAW,GAAGvC,aAAa,CAACwC,kBAAkB,CAAC5B,cAAc,CAACC,KAAK,CAAC,CAAC;IACzEyB,cAAc,CAACzB,KAAK,CAAC,GAAG0B,WAAW;EACrC,CAAC,CAAC;EACF,OAAO;IACLmB,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAEzB,IAAI,EAAE;MACxC,IAAI0B,QAAQ;MACZ,IAAIH,GAAG,IAAI,IAAI,EAAE;QACfG,QAAQ,GAAG1B,IAAI,CAAC2B,iBAAiB,CAACJ,GAAG,CAAC;MACxC;MACA,SAASf,SAASA,CAACV,GAAG,EAAE;QACtB,OAAO/B,qBAAqB,CAACiC,IAAI,EAAEO,SAAS,EAAET,GAAG,CAAC;MACpD;MACA,SAASW,SAASA,CAACX,GAAG,EAAEY,KAAK,EAAE;QAC7B1C,qBAAqB,CAACgC,IAAI,EAAEO,SAAS,EAAET,GAAG,EAAEY,KAAK,CAAC;MACpD;MACA,IAAIH,SAAS;MACb,IAAIqB,KAAK,GAAG5B,IAAI,CAAC6B,QAAQ,CAAC,CAAC;MAC3B,OAAO,CAACtB,SAAS,GAAGkB,MAAM,CAACK,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;QAC1C,IAAIhB,WAAW,GAAGd,IAAI,CAACe,cAAc,CAACR,SAAS,CAAC;QAChD;QACA;QACA,IAAIO,WAAW,IAAIA,WAAW,CAACE,SAAS,KAAK,KAAK,EAAE;UAClD;QACF;QACA,IAAIN,KAAK,GAAGa,GAAG,IAAI,IAAI,GAAGK,KAAK,CAACG,GAAG,CAACL,QAAQ,EAAEnB,SAAS,CAAC,GAAGA,SAAS;QACpE,IAAIU,UAAU,GAAGhB,aAAa,CAACS,KAAK,CAAC;QACrC,IAAI9B,QAAQ,GAAGF,cAAc,CAACuC,UAAU,CAAC;QACzC,IAAIZ,WAAW,GAAGD,cAAc,CAACa,UAAU,CAAC;QAC5C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGf,WAAW,CAACgB,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UACtD,IAAIjC,IAAI,GAAGmB,WAAW,CAACc,CAAC,CAAC;UACzBvC,QAAQ,CAACM,IAAI,CAAC,IAAIN,QAAQ,CAACM,IAAI,CAAC,CAACa,WAAW,CAACW,KAAK,EAAEF,SAAS,EAAEC,SAAS,CAAC;QAC3E;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}