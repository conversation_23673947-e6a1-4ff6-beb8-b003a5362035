import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  Typography,
  Space,
  Drawer
} from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  FileTextOutlined,
  ShoppingCartOutlined,
  MoneyCollectOutlined,
  BarChartOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuOutlined,
  TeamOutlined,
  AppstoreOutlined,
  DatabaseOutlined
} from '@ant-design/icons';

import { logout, selectUser, hasPermission, selectPermissions } from '../../store/slices/authSlice';
import { selectUnreadAlerts } from '../../store/slices/dashboardSlice';

const { Header, Sider, Content } = AntLayout;
const { Text } = Typography;

const Layout = ({ children }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const user = useSelector(selectUser);
  const permissions = useSelector(selectPermissions);
  const unreadAlerts = useSelector(selectUnreadAlerts);
  
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  // 菜单配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '经营仪表板',
      permission: 'dashboard:view'
    },
    {
      key: '/customers',
      icon: <TeamOutlined />,
      label: '客户管理',
      permission: 'customer:view'
    },
    {
      key: '/quotations',
      icon: <FileTextOutlined />,
      label: '报价管理',
      permission: 'quotation:view'
    },
    {
      key: '/orders',
      icon: <ShoppingCartOutlined />,
      label: '订单管理',
      permission: 'order:view'
    },
    {
      key: '/receivables',
      icon: <MoneyCollectOutlined />,
      label: '应收款管理',
      permission: 'receivable:view'
    },
    {
      key: 'bom',
      icon: <AppstoreOutlined />,
      label: 'BOM管理',
      permission: 'bom:view',
      children: [
        {
          key: '/bom',
          label: 'BOM清单',
          permission: 'bom:view'
        },
        {
          key: '/materials',
          label: '物料管理',
          permission: 'material:view'
        }
      ]
    },
    {
      key: 'analysis',
      icon: <BarChartOutlined />,
      label: '分析报表',
      permission: 'report:view',
      children: [
        {
          key: '/analysis/profit',
          label: '利润分析',
          permission: 'profit:view'
        },
        {
          key: '/analysis/performance',
          label: '绩效管理',
          permission: 'profit:view'
        },
        {
          key: '/analysis/business',
          label: '经营分析',
          permission: 'report:view'
        },
        {
          key: '/analysis/financial',
          label: '财务报表',
          permission: 'report:view'
        }
      ]
    },
    {
      key: '/help',
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
      permission: '*'
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      permission: 'settings:manage'
    }
  ];

  // 过滤有权限的菜单项
  const filterMenuItems = (items) => {
    return items.filter(item => {
      if (!hasPermission(permissions, item.permission)) {
        return false;
      }
      if (item.children) {
        item.children = filterMenuItems(item.children);
        return item.children.length > 0;
      }
      return true;
    });
  };

  const filteredMenuItems = filterMenuItems(menuItems);

  const handleMenuClick = ({ key }) => {
    navigate(key);
    setMobileMenuVisible(false);
  };

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ];

  const getRoleDisplayName = (role) => {
    const roleMap = {
      admin: '系统管理员',
      boss: '老板/高管',
      sales: '业务员',
      finance: '财务人员',
      production: '生产人员'
    };
    return roleMap[role] || role;
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    // 处理分析报表的子菜单
    if (path.startsWith('/analysis/')) {
      return [path];
    }
    return [path];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const path = location.pathname;
    if (path.startsWith('/analysis/')) {
      return ['analysis'];
    }
    return [];
  };

  const siderContent = (
    <Menu
      theme="dark"
      mode="inline"
      selectedKeys={getSelectedKeys()}
      defaultOpenKeys={getOpenKeys()}
      items={filteredMenuItems}
      onClick={handleMenuClick}
      style={{ borderRight: 0 }}
    />
  );

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      {/* 桌面端侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="desktop-only"
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        <div style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold',
          borderBottom: '1px solid #001529'
        }}>
          {collapsed ? 'SME' : '经营管理系统'}
        </div>
        {siderContent}
      </Sider>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title="菜单"
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        className="mobile-only"
        bodyStyle={{ padding: 0 }}
      >
        {siderContent}
      </Drawer>

      <AntLayout style={{ marginLeft: collapsed ? 80 : 200 }} className="desktop-only">
        <Header style={{
          padding: '0 24px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0',
          position: 'sticky',
          top: 0,
          zIndex: 100
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuOutlined /> : <MenuOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: '16px', width: 64, height: 64 }}
          />

          <Space size="large">
            <Badge count={unreadAlerts.length} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: '16px' }}
                onClick={() => navigate('/dashboard')}
              />
            </Badge>

            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <div>
                  <Text strong>{user?.name}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {getRoleDisplayName(user?.role)}
                  </Text>
                </div>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        <Content style={{
          margin: '24px',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 112px)'
        }}>
          {children}
        </Content>
      </AntLayout>

      {/* 移动端布局 */}
      <AntLayout className="mobile-only">
        <Header style={{
          padding: '0 16px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setMobileMenuVisible(true)}
            style={{ fontSize: '16px' }}
          />

          <Text strong style={{ fontSize: '16px' }}>经营管理系统</Text>

          <Space>
            <Badge count={unreadAlerts.length} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: '16px' }}
                onClick={() => navigate('/dashboard')}
              />
            </Badge>

            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Avatar icon={<UserOutlined />} style={{ cursor: 'pointer' }} />
            </Dropdown>
          </Space>
        </Header>

        <Content style={{
          margin: '16px',
          padding: '16px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 96px)'
        }}>
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
