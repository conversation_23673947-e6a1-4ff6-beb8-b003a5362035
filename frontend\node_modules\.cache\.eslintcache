[{"D:\\customerDemo\\Link-BOM\\frontend\\src\\index.js": "1", "D:\\customerDemo\\Link-BOM\\frontend\\src\\App.js": "2", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\index.js": "3", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\authSlice.js": "4", "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Layout\\Layout.js": "5", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Auth\\Login.js": "6", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Customer\\CustomerList.js": "7", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Quotation\\QuotationDetail.js": "8", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Customer\\CustomerDetail.js": "9", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Quotation\\QuotationList.js": "10", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Dashboard\\Dashboard.js": "11", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Order\\OrderDetail.js": "12", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Receivable\\ReceivableList.js": "13", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Order\\OrderList.js": "14", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\BusinessAnalysis.js": "15", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\ProfitAnalysis.js": "16", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\PerformanceManagement.js": "17", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\FinancialReports.js": "18", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Receivable\\ReceivableDetail.js": "19", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Help\\HelpCenter.js": "20", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Settings\\Settings.js": "21", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\customerSlice.js": "22", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\orderSlice.js": "23", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\dashboardSlice.js": "24", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\quotationSlice.js": "25", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\receivableSlice.js": "26", "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\OrderStatusChart.js": "27", "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\ReceivableAgingChart.js": "28", "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\RevenueChart.js": "29", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\bomSlice.js": "30", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\BOM\\BOMDetail.js": "31", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\BOM\\MaterialList.js": "32", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\BOM\\BOMList.js": "33"}, {"size": 900, "mtime": 1756213080951, "results": "34", "hashOfConfig": "35"}, {"size": 5562, "mtime": 1756251850580, "results": "36", "hashOfConfig": "35"}, {"size": 819, "mtime": 1756251764549, "results": "37", "hashOfConfig": "35"}, {"size": 5068, "mtime": 1756251918522, "results": "38", "hashOfConfig": "35"}, {"size": 9587, "mtime": 1756251881153, "results": "39", "hashOfConfig": "35"}, {"size": 6219, "mtime": 1756213361676, "results": "40", "hashOfConfig": "35"}, {"size": 15467, "mtime": 1756213537269, "results": "41", "hashOfConfig": "35"}, {"size": 1017, "mtime": 1756213640313, "results": "42", "hashOfConfig": "35"}, {"size": 12757, "mtime": 1756213584566, "results": "43", "hashOfConfig": "35"}, {"size": 11452, "mtime": 1756252136844, "results": "44", "hashOfConfig": "35"}, {"size": 9959, "mtime": 1756213435379, "results": "45", "hashOfConfig": "35"}, {"size": 1023, "mtime": 1756213690951, "results": "46", "hashOfConfig": "35"}, {"size": 7836, "mtime": 1756213723510, "results": "47", "hashOfConfig": "35"}, {"size": 10400, "mtime": 1756252201733, "results": "48", "hashOfConfig": "35"}, {"size": 571, "mtime": 1756213762061, "results": "49", "hashOfConfig": "35"}, {"size": 552, "mtime": 1756213744630, "results": "50", "hashOfConfig": "35"}, {"size": 569, "mtime": 1756213753464, "results": "51", "hashOfConfig": "35"}, {"size": 559, "mtime": 1756213770328, "results": "52", "hashOfConfig": "35"}, {"size": 1032, "mtime": 1756213735889, "results": "53", "hashOfConfig": "35"}, {"size": 553, "mtime": 1756213778223, "results": "54", "hashOfConfig": "35"}, {"size": 531, "mtime": 1756213786736, "results": "55", "hashOfConfig": "35"}, {"size": 6638, "mtime": 1756213162005, "results": "56", "hashOfConfig": "35"}, {"size": 8845, "mtime": 1756252184041, "results": "57", "hashOfConfig": "35"}, {"size": 5277, "mtime": 1756213302172, "results": "58", "hashOfConfig": "35"}, {"size": 8166, "mtime": 1756252119781, "results": "59", "hashOfConfig": "35"}, {"size": 9267, "mtime": 1756213272125, "results": "60", "hashOfConfig": "35"}, {"size": 1467, "mtime": 1756213460534, "results": "61", "hashOfConfig": "35"}, {"size": 2024, "mtime": 1756213472964, "results": "62", "hashOfConfig": "35"}, {"size": 2128, "mtime": 1756213449761, "results": "63", "hashOfConfig": "35"}, {"size": 14069, "mtime": 1756251825973, "results": "64", "hashOfConfig": "35"}, {"size": 12736, "mtime": 1756252021221, "results": "65", "hashOfConfig": "35"}, {"size": 15739, "mtime": 1756252078250, "results": "66", "hashOfConfig": "35"}, {"size": 13664, "mtime": 1756251973705, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11yy90s", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\customerDemo\\Link-BOM\\frontend\\src\\index.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\App.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\index.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\authSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Layout\\Layout.js", ["167"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Auth\\Login.js", ["168"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Customer\\CustomerList.js", ["169", "170"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Quotation\\QuotationDetail.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Customer\\CustomerDetail.js", ["171"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Quotation\\QuotationList.js", ["172", "173", "174"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Dashboard\\Dashboard.js", ["175", "176", "177", "178", "179"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Order\\OrderDetail.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Receivable\\ReceivableList.js", ["180", "181"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Order\\OrderList.js", ["182", "183", "184"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\BusinessAnalysis.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\ProfitAnalysis.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\PerformanceManagement.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\FinancialReports.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Receivable\\ReceivableDetail.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Help\\HelpCenter.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Settings\\Settings.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\customerSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\orderSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\dashboardSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\quotationSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\receivableSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\OrderStatusChart.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\ReceivableAgingChart.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\RevenueChart.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\bomSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\BOM\\BOMDetail.js", ["185"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\BOM\\MaterialList.js", ["186"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\BOM\\BOMList.js", ["187", "188"], [], {"ruleId": "189", "severity": 1, "message": "190", "line": 29, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 29, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "193", "line": 1, "column": 17, "nodeType": "191", "messageId": "192", "endLine": 1, "endColumn": 25}, {"ruleId": "189", "severity": 1, "message": "194", "line": 24, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 24, "endColumn": 17}, {"ruleId": "189", "severity": 1, "message": "195", "line": 151, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 151, "endColumn": 23}, {"ruleId": "189", "severity": 1, "message": "196", "line": 17, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 17, "endColumn": 9}, {"ruleId": "189", "severity": 1, "message": "193", "line": 1, "column": 17, "nodeType": "191", "messageId": "192", "endLine": 1, "endColumn": 25}, {"ruleId": "189", "severity": 1, "message": "197", "line": 19, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 19, "endColumn": 11}, {"ruleId": "189", "severity": 1, "message": "194", "line": 23, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 23, "endColumn": 17}, {"ruleId": "189", "severity": 1, "message": "198", "line": 15, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 15, "endColumn": 6}, {"ruleId": "189", "severity": 1, "message": "199", "line": 16, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 16, "endColumn": 10}, {"ruleId": "189", "severity": 1, "message": "200", "line": 20, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 20, "endColumn": 20}, {"ruleId": "189", "severity": 1, "message": "201", "line": 23, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 23, "endColumn": 15}, {"ruleId": "189", "severity": 1, "message": "202", "line": 94, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 94, "endColumn": 22}, {"ruleId": "189", "severity": 1, "message": "203", "line": 2, "column": 23, "nodeType": "191", "messageId": "192", "endLine": 2, "endColumn": 34}, {"ruleId": "189", "severity": 1, "message": "194", "line": 19, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 19, "endColumn": 17}, {"ruleId": "189", "severity": 1, "message": "193", "line": 1, "column": 17, "nodeType": "191", "messageId": "192", "endLine": 1, "endColumn": 25}, {"ruleId": "189", "severity": 1, "message": "204", "line": 18, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 18, "endColumn": 8}, {"ruleId": "189", "severity": 1, "message": "194", "line": 22, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 22, "endColumn": 17}, {"ruleId": "189", "severity": 1, "message": "205", "line": 21, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 21, "endColumn": 7}, {"ruleId": "189", "severity": 1, "message": "194", "line": 24, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 24, "endColumn": 17}, {"ruleId": "189", "severity": 1, "message": "194", "line": 24, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 24, "endColumn": 17}, {"ruleId": "189", "severity": 1, "message": "195", "line": 139, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 139, "endColumn": 23}, "no-unused-vars", "'DatabaseOutlined' is defined but never used.", "Identifier", "unusedVar", "'useState' is defined but never used.", "'SearchOutlined' is defined but never used.", "'getStatusColor' is assigned a value but never used.", "'Avatar' is defined but never used.", "'Progress' is defined but never used.", "'Tag' is defined but never used.", "'Divider' is defined but never used.", "'ArrowDownOutlined' is defined but never used.", "'TeamOutlined' is defined but never used.", "'getAlertColor' is assigned a value but never used.", "'useDispatch' is defined but never used.", "'Badge' is defined but never used.", "'Tree' is defined but never used."]