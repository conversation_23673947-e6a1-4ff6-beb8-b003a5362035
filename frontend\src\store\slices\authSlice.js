import { createSlice } from '@reduxjs/toolkit';

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    password: '123456',
    name: '系统管理员',
    role: 'admin',
    avatar: null,
    phone: '13800138000',
    email: '<EMAIL>'
  },
  {
    id: 2,
    username: 'boss',
    password: '123456',
    name: '张总',
    role: 'boss',
    avatar: null,
    phone: '13800138001',
    email: '<EMAIL>'
  },
  {
    id: 3,
    username: 'sales',
    password: '123456',
    name: '李业务',
    role: 'sales',
    avatar: null,
    phone: '13800138002',
    email: '<EMAIL>'
  },
  {
    id: 4,
    username: 'finance',
    password: '123456',
    name: '王会计',
    role: 'finance',
    avatar: null,
    phone: '13800138003',
    email: '<EMAIL>'
  },
  {
    id: 5,
    username: 'production',
    password: '123456',
    name: '赵生产',
    role: 'production',
    avatar: null,
    phone: '13800138004',
    email: '<EMAIL>'
  }
];

// 角色权限配置
const rolePermissions = {
  admin: ['*'], // 所有权限
  boss: [
    'dashboard:view',
    'customer:view',
    'order:view',
    'quotation:view',
    'receivable:view',
    'bom:view',
    'material:view',
    'profit:view',
    'report:view',
    'settings:manage'
  ],
  sales: [
    'dashboard:view',
    'customer:manage',
    'quotation:manage',
    'order:view',
    'receivable:view',
    'bom:view',
    'material:view'
  ],
  finance: [
    'dashboard:view',
    'order:view',
    'receivable:manage',
    'profit:view',
    'report:view'
  ],
  production: [
    'dashboard:view',
    'order:manage',
    'cost:manage',
    'bom:manage',
    'material:manage'
  ]
};

const initialState = {
  isAuthenticated: false,
  user: null,
  token: null,
  permissions: [],
  loading: false,
  error: null
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.permissions = rolePermissions[action.payload.user.role] || [];
      state.error = null;
      
      // 保存到localStorage
      localStorage.setItem('token', action.payload.token);
      localStorage.setItem('user', JSON.stringify(action.payload.user));
    },
    loginFailure: (state, action) => {
      state.loading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.permissions = [];
      state.error = action.payload;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.permissions = [];
      state.error = null;
      
      // 清除localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    },
    clearError: (state) => {
      state.error = null;
    },
    initializeAuth: (state) => {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      
      if (token && user) {
        try {
          const userData = JSON.parse(user);
          state.isAuthenticated = true;
          state.user = userData;
          state.token = token;
          state.permissions = rolePermissions[userData.role] || [];
        } catch (error) {
          // 清除无效数据
          localStorage.removeItem('token');
          localStorage.removeItem('user');
        }
      }
    }
  }
});

// 模拟登录异步操作
export const login = (credentials) => async (dispatch) => {
  dispatch(loginStart());
  
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const user = mockUsers.find(
      u => u.username === credentials.username && u.password === credentials.password
    );
    
    if (user) {
      const token = `mock_token_${user.id}_${Date.now()}`;
      const { password, ...userWithoutPassword } = user;
      
      dispatch(loginSuccess({
        user: userWithoutPassword,
        token
      }));
    } else {
      dispatch(loginFailure('用户名或密码错误'));
    }
  } catch (error) {
    dispatch(loginFailure('登录失败，请重试'));
  }
};

export const { 
  loginStart, 
  loginSuccess, 
  loginFailure, 
  logout, 
  clearError, 
  initializeAuth 
} = authSlice.actions;

// 选择器
export const selectAuth = (state) => state.auth;
export const selectUser = (state) => state.auth.user;
export const selectPermissions = (state) => state.auth.permissions;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;

// 权限检查函数
export const hasPermission = (permissions, permission) => {
  if (!permissions || permissions.length === 0) return false;
  if (permissions.includes('*')) return true;
  return permissions.includes(permission);
};

export default authSlice.reducer;
