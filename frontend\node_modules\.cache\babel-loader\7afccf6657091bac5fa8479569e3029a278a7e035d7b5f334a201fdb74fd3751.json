{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport AxisBuilder from '../axis/AxisBuilder.js';\nvar PolarAxisPointer = /** @class */function (_super) {\n  __extends(PolarAxisPointer, _super);\n  function PolarAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  PolarAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    if (axis.dim === 'angle') {\n      this.animationThreshold = Math.PI / 18;\n    }\n    var polar = axis.polar;\n    var otherAxis = polar.getOtherAxis(axis);\n    var otherExtent = otherAxis.getExtent();\n    var coordValue = axis.dataToCoord(value);\n    var axisPointerType = axisPointerModel.get('type');\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, polar, coordValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var labelMargin = axisPointerModel.get(['label', 'margin']);\n    var labelPos = getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin);\n    viewHelper.buildLabelElOption(elOption, axisModel, axisPointerModel, api, labelPos);\n  };\n  return PolarAxisPointer;\n}(BaseAxisPointer);\n;\nfunction getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin) {\n  var axis = axisModel.axis;\n  var coord = axis.dataToCoord(value);\n  var axisAngle = polar.getAngleAxis().getExtent()[0];\n  axisAngle = axisAngle / 180 * Math.PI;\n  var radiusExtent = polar.getRadiusAxis().getExtent();\n  var position;\n  var align;\n  var verticalAlign;\n  if (axis.dim === 'radius') {\n    var transform = matrix.create();\n    matrix.rotate(transform, transform, axisAngle);\n    matrix.translate(transform, transform, [polar.cx, polar.cy]);\n    position = graphic.applyTransform([coord, -labelMargin], transform);\n    var labelRotation = axisModel.getModel('axisLabel').get('rotate') || 0;\n    // @ts-ignore\n    var labelLayout = AxisBuilder.innerTextLayout(axisAngle, labelRotation * Math.PI / 180, -1);\n    align = labelLayout.textAlign;\n    verticalAlign = labelLayout.textVerticalAlign;\n  } else {\n    // angle axis\n    var r = radiusExtent[1];\n    position = polar.coordToPoint([r + labelMargin, coord]);\n    var cx = polar.cx;\n    var cy = polar.cy;\n    align = Math.abs(position[0] - cx) / r < 0.3 ? 'center' : position[0] > cx ? 'left' : 'right';\n    verticalAlign = Math.abs(position[1] - cy) / r < 0.3 ? 'middle' : position[1] > cy ? 'top' : 'bottom';\n  }\n  return {\n    position: position,\n    align: align,\n    verticalAlign: verticalAlign\n  };\n}\nvar pointerShapeBuilder = {\n  line: function (axis, polar, coordValue, otherExtent) {\n    return axis.dim === 'angle' ? {\n      type: 'Line',\n      shape: viewHelper.makeLineShape(polar.coordToPoint([otherExtent[0], coordValue]), polar.coordToPoint([otherExtent[1], coordValue]))\n    } : {\n      type: 'Circle',\n      shape: {\n        cx: polar.cx,\n        cy: polar.cy,\n        r: coordValue\n      }\n    };\n  },\n  shadow: function (axis, polar, coordValue, otherExtent) {\n    var bandWidth = Math.max(1, axis.getBandWidth());\n    var radian = Math.PI / 180;\n    return axis.dim === 'angle' ? {\n      type: 'Sector',\n      shape: viewHelper.makeSectorShape(polar.cx, polar.cy, otherExtent[0], otherExtent[1],\n      // In ECharts y is negative if angle is positive\n      (-coordValue - bandWidth / 2) * radian, (-coordValue + bandWidth / 2) * radian)\n    } : {\n      type: 'Sector',\n      shape: viewHelper.makeSectorShape(polar.cx, polar.cy, coordValue - bandWidth / 2, coordValue + bandWidth / 2, 0, Math.PI * 2)\n    };\n  }\n};\nexport default PolarAxisPointer;", "map": {"version": 3, "names": ["__extends", "BaseAxisPointer", "graphic", "viewHelper", "matrix", "AxisBuilder", "PolarAxisPointer", "_super", "apply", "arguments", "prototype", "makeElOption", "elOption", "value", "axisModel", "axisPointerModel", "api", "axis", "dim", "animationThreshold", "Math", "PI", "polar", "otherAxis", "getOtherAxis", "otherExtent", "getExtent", "coordValue", "dataToCoord", "axisPointerType", "get", "elStyle", "buildElStyle", "pointerOption", "pointerShapeBuilder", "style", "<PERSON><PERSON><PERSON>", "type", "pointer", "labelMargin", "labelPos", "getLabelPosition", "buildLabelElOption", "coord", "axisAngle", "getAngleAxis", "radiusExtent", "getRadiusAxis", "position", "align", "verticalAlign", "transform", "create", "rotate", "translate", "cx", "cy", "applyTransform", "labelRotation", "getModel", "labelLayout", "innerTextLayout", "textAlign", "textVerticalAlign", "r", "coordToPoint", "abs", "line", "shape", "makeLineShape", "shadow", "bandWidth", "max", "getBandWidth", "radian", "makeSectorShape"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/component/axisPointer/PolarAxisPointer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport AxisBuilder from '../axis/AxisBuilder.js';\nvar PolarAxisPointer = /** @class */function (_super) {\n  __extends(PolarAxisPointer, _super);\n  function PolarAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  PolarAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    if (axis.dim === 'angle') {\n      this.animationThreshold = Math.PI / 18;\n    }\n    var polar = axis.polar;\n    var otherAxis = polar.getOtherAxis(axis);\n    var otherExtent = otherAxis.getExtent();\n    var coordValue = axis.dataToCoord(value);\n    var axisPointerType = axisPointerModel.get('type');\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, polar, coordValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var labelMargin = axisPointerModel.get(['label', 'margin']);\n    var labelPos = getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin);\n    viewHelper.buildLabelElOption(elOption, axisModel, axisPointerModel, api, labelPos);\n  };\n  return PolarAxisPointer;\n}(BaseAxisPointer);\n;\nfunction getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin) {\n  var axis = axisModel.axis;\n  var coord = axis.dataToCoord(value);\n  var axisAngle = polar.getAngleAxis().getExtent()[0];\n  axisAngle = axisAngle / 180 * Math.PI;\n  var radiusExtent = polar.getRadiusAxis().getExtent();\n  var position;\n  var align;\n  var verticalAlign;\n  if (axis.dim === 'radius') {\n    var transform = matrix.create();\n    matrix.rotate(transform, transform, axisAngle);\n    matrix.translate(transform, transform, [polar.cx, polar.cy]);\n    position = graphic.applyTransform([coord, -labelMargin], transform);\n    var labelRotation = axisModel.getModel('axisLabel').get('rotate') || 0;\n    // @ts-ignore\n    var labelLayout = AxisBuilder.innerTextLayout(axisAngle, labelRotation * Math.PI / 180, -1);\n    align = labelLayout.textAlign;\n    verticalAlign = labelLayout.textVerticalAlign;\n  } else {\n    // angle axis\n    var r = radiusExtent[1];\n    position = polar.coordToPoint([r + labelMargin, coord]);\n    var cx = polar.cx;\n    var cy = polar.cy;\n    align = Math.abs(position[0] - cx) / r < 0.3 ? 'center' : position[0] > cx ? 'left' : 'right';\n    verticalAlign = Math.abs(position[1] - cy) / r < 0.3 ? 'middle' : position[1] > cy ? 'top' : 'bottom';\n  }\n  return {\n    position: position,\n    align: align,\n    verticalAlign: verticalAlign\n  };\n}\nvar pointerShapeBuilder = {\n  line: function (axis, polar, coordValue, otherExtent) {\n    return axis.dim === 'angle' ? {\n      type: 'Line',\n      shape: viewHelper.makeLineShape(polar.coordToPoint([otherExtent[0], coordValue]), polar.coordToPoint([otherExtent[1], coordValue]))\n    } : {\n      type: 'Circle',\n      shape: {\n        cx: polar.cx,\n        cy: polar.cy,\n        r: coordValue\n      }\n    };\n  },\n  shadow: function (axis, polar, coordValue, otherExtent) {\n    var bandWidth = Math.max(1, axis.getBandWidth());\n    var radian = Math.PI / 180;\n    return axis.dim === 'angle' ? {\n      type: 'Sector',\n      shape: viewHelper.makeSectorShape(polar.cx, polar.cy, otherExtent[0], otherExtent[1],\n      // In ECharts y is negative if angle is positive\n      (-coordValue - bandWidth / 2) * radian, (-coordValue + bandWidth / 2) * radian)\n    } : {\n      type: 'Sector',\n      shape: viewHelper.makeSectorShape(polar.cx, polar.cy, coordValue - bandWidth / 2, coordValue + bandWidth / 2, 0, Math.PI * 2)\n    };\n  }\n};\nexport default PolarAxisPointer;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,UAAU,MAAM,iBAAiB;AAC7C,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACpDP,SAAS,CAACM,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACA;AACF;AACA;EACEH,gBAAgB,CAACI,SAAS,CAACC,YAAY,GAAG,UAAUC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,EAAE;IACrG,IAAIC,IAAI,GAAGH,SAAS,CAACG,IAAI;IACzB,IAAIA,IAAI,CAACC,GAAG,KAAK,OAAO,EAAE;MACxB,IAAI,CAACC,kBAAkB,GAAGC,IAAI,CAACC,EAAE,GAAG,EAAE;IACxC;IACA,IAAIC,KAAK,GAAGL,IAAI,CAACK,KAAK;IACtB,IAAIC,SAAS,GAAGD,KAAK,CAACE,YAAY,CAACP,IAAI,CAAC;IACxC,IAAIQ,WAAW,GAAGF,SAAS,CAACG,SAAS,CAAC,CAAC;IACvC,IAAIC,UAAU,GAAGV,IAAI,CAACW,WAAW,CAACf,KAAK,CAAC;IACxC,IAAIgB,eAAe,GAAGd,gBAAgB,CAACe,GAAG,CAAC,MAAM,CAAC;IAClD,IAAID,eAAe,IAAIA,eAAe,KAAK,MAAM,EAAE;MACjD,IAAIE,OAAO,GAAG5B,UAAU,CAAC6B,YAAY,CAACjB,gBAAgB,CAAC;MACvD,IAAIkB,aAAa,GAAGC,mBAAmB,CAACL,eAAe,CAAC,CAACZ,IAAI,EAAEK,KAAK,EAAEK,UAAU,EAAEF,WAAW,CAAC;MAC9FQ,aAAa,CAACE,KAAK,GAAGJ,OAAO;MAC7BnB,QAAQ,CAACwB,UAAU,GAAGH,aAAa,CAACI,IAAI;MACxCzB,QAAQ,CAAC0B,OAAO,GAAGL,aAAa;IAClC;IACA,IAAIM,WAAW,GAAGxB,gBAAgB,CAACe,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3D,IAAIU,QAAQ,GAAGC,gBAAgB,CAAC5B,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAEO,KAAK,EAAEiB,WAAW,CAAC;IACvFpC,UAAU,CAACuC,kBAAkB,CAAC9B,QAAQ,EAAEE,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,EAAEwB,QAAQ,CAAC;EACrF,CAAC;EACD,OAAOlC,gBAAgB;AACzB,CAAC,CAACL,eAAe,CAAC;AAClB;AACA,SAASwC,gBAAgBA,CAAC5B,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAEO,KAAK,EAAEiB,WAAW,EAAE;EAChF,IAAItB,IAAI,GAAGH,SAAS,CAACG,IAAI;EACzB,IAAI0B,KAAK,GAAG1B,IAAI,CAACW,WAAW,CAACf,KAAK,CAAC;EACnC,IAAI+B,SAAS,GAAGtB,KAAK,CAACuB,YAAY,CAAC,CAAC,CAACnB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EACnDkB,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGxB,IAAI,CAACC,EAAE;EACrC,IAAIyB,YAAY,GAAGxB,KAAK,CAACyB,aAAa,CAAC,CAAC,CAACrB,SAAS,CAAC,CAAC;EACpD,IAAIsB,QAAQ;EACZ,IAAIC,KAAK;EACT,IAAIC,aAAa;EACjB,IAAIjC,IAAI,CAACC,GAAG,KAAK,QAAQ,EAAE;IACzB,IAAIiC,SAAS,GAAG/C,MAAM,CAACgD,MAAM,CAAC,CAAC;IAC/BhD,MAAM,CAACiD,MAAM,CAACF,SAAS,EAAEA,SAAS,EAAEP,SAAS,CAAC;IAC9CxC,MAAM,CAACkD,SAAS,CAACH,SAAS,EAAEA,SAAS,EAAE,CAAC7B,KAAK,CAACiC,EAAE,EAAEjC,KAAK,CAACkC,EAAE,CAAC,CAAC;IAC5DR,QAAQ,GAAG9C,OAAO,CAACuD,cAAc,CAAC,CAACd,KAAK,EAAE,CAACJ,WAAW,CAAC,EAAEY,SAAS,CAAC;IACnE,IAAIO,aAAa,GAAG5C,SAAS,CAAC6C,QAAQ,CAAC,WAAW,CAAC,CAAC7B,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;IACtE;IACA,IAAI8B,WAAW,GAAGvD,WAAW,CAACwD,eAAe,CAACjB,SAAS,EAAEc,aAAa,GAAGtC,IAAI,CAACC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;IAC3F4B,KAAK,GAAGW,WAAW,CAACE,SAAS;IAC7BZ,aAAa,GAAGU,WAAW,CAACG,iBAAiB;EAC/C,CAAC,MAAM;IACL;IACA,IAAIC,CAAC,GAAGlB,YAAY,CAAC,CAAC,CAAC;IACvBE,QAAQ,GAAG1B,KAAK,CAAC2C,YAAY,CAAC,CAACD,CAAC,GAAGzB,WAAW,EAAEI,KAAK,CAAC,CAAC;IACvD,IAAIY,EAAE,GAAGjC,KAAK,CAACiC,EAAE;IACjB,IAAIC,EAAE,GAAGlC,KAAK,CAACkC,EAAE;IACjBP,KAAK,GAAG7B,IAAI,CAAC8C,GAAG,CAAClB,QAAQ,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,GAAGS,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAGhB,QAAQ,CAAC,CAAC,CAAC,GAAGO,EAAE,GAAG,MAAM,GAAG,OAAO;IAC7FL,aAAa,GAAG9B,IAAI,CAAC8C,GAAG,CAAClB,QAAQ,CAAC,CAAC,CAAC,GAAGQ,EAAE,CAAC,GAAGQ,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAGhB,QAAQ,CAAC,CAAC,CAAC,GAAGQ,EAAE,GAAG,KAAK,GAAG,QAAQ;EACvG;EACA,OAAO;IACLR,QAAQ,EAAEA,QAAQ;IAClBC,KAAK,EAAEA,KAAK;IACZC,aAAa,EAAEA;EACjB,CAAC;AACH;AACA,IAAIhB,mBAAmB,GAAG;EACxBiC,IAAI,EAAE,SAAAA,CAAUlD,IAAI,EAAEK,KAAK,EAAEK,UAAU,EAAEF,WAAW,EAAE;IACpD,OAAOR,IAAI,CAACC,GAAG,KAAK,OAAO,GAAG;MAC5BmB,IAAI,EAAE,MAAM;MACZ+B,KAAK,EAAEjE,UAAU,CAACkE,aAAa,CAAC/C,KAAK,CAAC2C,YAAY,CAAC,CAACxC,WAAW,CAAC,CAAC,CAAC,EAAEE,UAAU,CAAC,CAAC,EAAEL,KAAK,CAAC2C,YAAY,CAAC,CAACxC,WAAW,CAAC,CAAC,CAAC,EAAEE,UAAU,CAAC,CAAC;IACpI,CAAC,GAAG;MACFU,IAAI,EAAE,QAAQ;MACd+B,KAAK,EAAE;QACLb,EAAE,EAAEjC,KAAK,CAACiC,EAAE;QACZC,EAAE,EAAElC,KAAK,CAACkC,EAAE;QACZQ,CAAC,EAAErC;MACL;IACF,CAAC;EACH,CAAC;EACD2C,MAAM,EAAE,SAAAA,CAAUrD,IAAI,EAAEK,KAAK,EAAEK,UAAU,EAAEF,WAAW,EAAE;IACtD,IAAI8C,SAAS,GAAGnD,IAAI,CAACoD,GAAG,CAAC,CAAC,EAAEvD,IAAI,CAACwD,YAAY,CAAC,CAAC,CAAC;IAChD,IAAIC,MAAM,GAAGtD,IAAI,CAACC,EAAE,GAAG,GAAG;IAC1B,OAAOJ,IAAI,CAACC,GAAG,KAAK,OAAO,GAAG;MAC5BmB,IAAI,EAAE,QAAQ;MACd+B,KAAK,EAAEjE,UAAU,CAACwE,eAAe,CAACrD,KAAK,CAACiC,EAAE,EAAEjC,KAAK,CAACkC,EAAE,EAAE/B,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC;MACpF;MACA,CAAC,CAACE,UAAU,GAAG4C,SAAS,GAAG,CAAC,IAAIG,MAAM,EAAE,CAAC,CAAC/C,UAAU,GAAG4C,SAAS,GAAG,CAAC,IAAIG,MAAM;IAChF,CAAC,GAAG;MACFrC,IAAI,EAAE,QAAQ;MACd+B,KAAK,EAAEjE,UAAU,CAACwE,eAAe,CAACrD,KAAK,CAACiC,EAAE,EAAEjC,KAAK,CAACkC,EAAE,EAAE7B,UAAU,GAAG4C,SAAS,GAAG,CAAC,EAAE5C,UAAU,GAAG4C,SAAS,GAAG,CAAC,EAAE,CAAC,EAAEnD,IAAI,CAACC,EAAE,GAAG,CAAC;IAC9H,CAAC;EACH;AACF,CAAC;AACD,eAAef,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}