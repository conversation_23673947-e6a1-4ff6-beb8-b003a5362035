{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n// 模拟报价单数据\nconst mockQuotations = [{\n  id: 'QUO-2024-001',\n  customerId: 1,\n  customerName: '华为技术有限公司',\n  title: '服务器设备采购报价',\n  totalAmount: 500000,\n  estimatedCost: 375000,\n  estimatedProfit: 125000,\n  profitMargin: 25,\n  status: 'accepted',\n  validUntil: '2024-02-15',\n  createTime: '2024-01-10',\n  salesperson: '李业务',\n  items: [{\n    id: 1,\n    name: '服务器主机',\n    specification: 'Dell PowerEdge R750',\n    quantity: 10,\n    unitPrice: 35000,\n    totalPrice: 350000,\n    unitCost: 28000,\n    totalCost: 280000\n  }, {\n    id: 2,\n    name: '网络设备',\n    specification: 'Cisco Catalyst 9300',\n    quantity: 5,\n    unitPrice: 15000,\n    totalPrice: 75000,\n    unitCost: 12000,\n    totalCost: 60000\n  }, {\n    id: 3,\n    name: '安装调试费',\n    specification: '现场安装调试服务',\n    quantity: 1,\n    unitPrice: 75000,\n    totalPrice: 75000,\n    unitCost: 35000,\n    totalCost: 35000\n  }],\n  notes: '包含一年免费维护服务',\n  convertedToOrder: 'ORD-2024-001',\n  bomId: 'BOM-001',\n  bomName: '高性能服务器配置'\n}, {\n  id: 'QUO-2024-002',\n  customerId: 2,\n  customerName: '小米科技有限公司',\n  title: '智能设备定制开发报价',\n  totalAmount: 300000,\n  estimatedCost: 210000,\n  estimatedProfit: 90000,\n  profitMargin: 30,\n  status: 'pending',\n  validUntil: '2024-03-01',\n  createTime: '2024-01-15',\n  salesperson: '李业务',\n  items: [{\n    id: 1,\n    name: '硬件开发',\n    specification: '定制PCB设计及生产',\n    quantity: 1,\n    unitPrice: 150000,\n    totalPrice: 150000,\n    unitCost: 100000,\n    totalCost: 100000\n  }, {\n    id: 2,\n    name: '软件开发',\n    specification: '嵌入式软件开发',\n    quantity: 1,\n    unitPrice: 100000,\n    totalPrice: 100000,\n    unitCost: 70000,\n    totalCost: 70000\n  }, {\n    id: 3,\n    name: '测试验证',\n    specification: '功能测试及认证',\n    quantity: 1,\n    unitPrice: 50000,\n    totalPrice: 50000,\n    unitCost: 40000,\n    totalCost: 40000\n  }],\n  notes: '开发周期8周，包含技术支持',\n  convertedToOrder: null,\n  bomId: 'BOM-002',\n  bomName: '图形工作站配置'\n}, {\n  id: 'QUO-2024-003',\n  customerId: 4,\n  customerName: '腾讯科技有限公司',\n  title: '云服务解决方案报价',\n  totalAmount: 450000,\n  estimatedCost: 315000,\n  estimatedProfit: 135000,\n  profitMargin: 30,\n  status: 'draft',\n  validUntil: '2024-02-28',\n  createTime: '2024-01-25',\n  salesperson: '李业务',\n  items: [{\n    id: 1,\n    name: '云服务器',\n    specification: '高性能云服务器配置',\n    quantity: 20,\n    unitPrice: 15000,\n    totalPrice: 300000,\n    unitCost: 10500,\n    totalCost: 210000\n  }, {\n    id: 2,\n    name: '数据库服务',\n    specification: '高可用数据库集群',\n    quantity: 2,\n    unitPrice: 50000,\n    totalPrice: 100000,\n    unitCost: 35000,\n    totalCost: 70000\n  }, {\n    id: 3,\n    name: '技术支持',\n    specification: '7x24小时技术支持',\n    quantity: 1,\n    unitPrice: 50000,\n    totalPrice: 50000,\n    unitCost: 35000,\n    totalCost: 35000\n  }],\n  notes: '首年服务费用，续费享受优惠',\n  convertedToOrder: null,\n  bomId: 'BOM-003',\n  bomName: '基础办公电脑配置'\n}];\nconst initialState = {\n  quotations: mockQuotations,\n  currentQuotation: null,\n  loading: false,\n  error: null,\n  searchKeyword: '',\n  filters: {\n    status: '',\n    salesperson: '',\n    dateRange: []\n  },\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: mockQuotations.length\n  }\n};\nconst quotationSlice = createSlice({\n  name: 'quotation',\n  initialState,\n  reducers: {\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    setQuotations: (state, action) => {\n      state.quotations = action.payload;\n      state.pagination.total = action.payload.length;\n    },\n    addQuotation: (state, action) => {\n      const newQuotation = {\n        ...action.payload,\n        id: `QUO-${new Date().getFullYear()}-${String(state.quotations.length + 1).padStart(3, '0')}`,\n        createTime: new Date().toISOString().split('T')[0],\n        status: 'draft',\n        convertedToOrder: null\n      };\n      state.quotations.unshift(newQuotation);\n      state.pagination.total = state.quotations.length;\n    },\n    updateQuotation: (state, action) => {\n      const index = state.quotations.findIndex(q => q.id === action.payload.id);\n      if (index !== -1) {\n        state.quotations[index] = {\n          ...state.quotations[index],\n          ...action.payload\n        };\n      }\n    },\n    updateQuotationStatus: (state, action) => {\n      const {\n        id,\n        status\n      } = action.payload;\n      const quotation = state.quotations.find(q => q.id === id);\n      if (quotation) {\n        quotation.status = status;\n      }\n    },\n    convertToOrder: (state, action) => {\n      const {\n        quotationId,\n        orderId\n      } = action.payload;\n      const quotation = state.quotations.find(q => q.id === quotationId);\n      if (quotation) {\n        quotation.status = 'accepted';\n        quotation.convertedToOrder = orderId;\n      }\n    },\n    deleteQuotation: (state, action) => {\n      state.quotations = state.quotations.filter(q => q.id !== action.payload);\n      state.pagination.total = state.quotations.length;\n    },\n    setCurrentQuotation: (state, action) => {\n      state.currentQuotation = action.payload;\n    },\n    setSearchKeyword: (state, action) => {\n      state.searchKeyword = action.payload;\n    },\n    setFilters: (state, action) => {\n      state.filters = {\n        ...state.filters,\n        ...action.payload\n      };\n    },\n    setPagination: (state, action) => {\n      state.pagination = {\n        ...state.pagination,\n        ...action.payload\n      };\n    },\n    resetFilters: state => {\n      state.searchKeyword = '';\n      state.filters = {\n        status: '',\n        salesperson: '',\n        dateRange: []\n      };\n      state.pagination.current = 1;\n    }\n  }\n});\nexport const {\n  setLoading,\n  setError,\n  clearError,\n  setQuotations,\n  addQuotation,\n  updateQuotation,\n  updateQuotationStatus,\n  convertToOrder,\n  deleteQuotation,\n  setCurrentQuotation,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters\n} = quotationSlice.actions;\n\n// 选择器\nexport const selectQuotations = state => state.quotation.quotations;\nexport const selectCurrentQuotation = state => state.quotation.currentQuotation;\nexport const selectQuotationLoading = state => state.quotation.loading;\nexport const selectQuotationError = state => state.quotation.error;\nexport const selectQuotationFilters = state => state.quotation.filters;\nexport const selectQuotationPagination = state => state.quotation.pagination;\n\n// 过滤后的报价单列表\nexport const selectFilteredQuotations = state => {\n  const {\n    quotations,\n    searchKeyword,\n    filters\n  } = state.quotation;\n  return quotations.filter(quotation => {\n    // 搜索关键词过滤\n    if (searchKeyword) {\n      const keyword = searchKeyword.toLowerCase();\n      if (!quotation.id.toLowerCase().includes(keyword) && !quotation.customerName.toLowerCase().includes(keyword) && !quotation.title.toLowerCase().includes(keyword)) {\n        return false;\n      }\n    }\n\n    // 状态过滤\n    if (filters.status && quotation.status !== filters.status) {\n      return false;\n    }\n\n    // 业务员过滤\n    if (filters.salesperson && quotation.salesperson !== filters.salesperson) {\n      return false;\n    }\n    return true;\n  });\n};\nexport default quotationSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "mockQuotations", "id", "customerId", "customerName", "title", "totalAmount", "estimatedCost", "estimatedProfit", "profitMargin", "status", "validUntil", "createTime", "salesperson", "items", "name", "specification", "quantity", "unitPrice", "totalPrice", "unitCost", "totalCost", "notes", "convertedToOrder", "bomId", "bom<PERSON>ame", "initialState", "quotations", "currentQuotation", "loading", "error", "searchKeyword", "filters", "date<PERSON><PERSON><PERSON>", "pagination", "current", "pageSize", "total", "length", "quotationSlice", "reducers", "setLoading", "state", "action", "payload", "setError", "clearError", "setQuotations", "addQuotation", "newQuotation", "Date", "getFullYear", "String", "padStart", "toISOString", "split", "unshift", "updateQuotation", "index", "findIndex", "q", "updateQuotationStatus", "quotation", "find", "convertToOrder", "quotationId", "orderId", "deleteQuotation", "filter", "setCurrentQuotation", "setSearchKeyword", "setFilters", "setPagination", "resetFilters", "actions", "selectQuotations", "selectCurrentQuotation", "selectQuotationLoading", "selectQuotationError", "selectQuotationFilters", "selectQuotationPagination", "selectFilteredQuotations", "keyword", "toLowerCase", "includes", "reducer"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/store/slices/quotationSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n// 模拟报价单数据\nconst mockQuotations = [\n  {\n    id: 'QUO-2024-001',\n    customerId: 1,\n    customerName: '华为技术有限公司',\n    title: '服务器设备采购报价',\n    totalAmount: 500000,\n    estimatedCost: 375000,\n    estimatedProfit: 125000,\n    profitMargin: 25,\n    status: 'accepted',\n    validUntil: '2024-02-15',\n    createTime: '2024-01-10',\n    salesperson: '李业务',\n    items: [\n      {\n        id: 1,\n        name: '服务器主机',\n        specification: 'Dell PowerEdge R750',\n        quantity: 10,\n        unitPrice: 35000,\n        totalPrice: 350000,\n        unitCost: 28000,\n        totalCost: 280000\n      },\n      {\n        id: 2,\n        name: '网络设备',\n        specification: 'Cisco Catalyst 9300',\n        quantity: 5,\n        unitPrice: 15000,\n        totalPrice: 75000,\n        unitCost: 12000,\n        totalCost: 60000\n      },\n      {\n        id: 3,\n        name: '安装调试费',\n        specification: '现场安装调试服务',\n        quantity: 1,\n        unitPrice: 75000,\n        totalPrice: 75000,\n        unitCost: 35000,\n        totalCost: 35000\n      }\n    ],\n    notes: '包含一年免费维护服务',\n    convertedToOrder: 'ORD-2024-001',\n    bomId: 'BOM-001',\n    bomName: '高性能服务器配置'\n  },\n  {\n    id: 'QUO-2024-002',\n    customerId: 2,\n    customerName: '小米科技有限公司',\n    title: '智能设备定制开发报价',\n    totalAmount: 300000,\n    estimatedCost: 210000,\n    estimatedProfit: 90000,\n    profitMargin: 30,\n    status: 'pending',\n    validUntil: '2024-03-01',\n    createTime: '2024-01-15',\n    salesperson: '李业务',\n    items: [\n      {\n        id: 1,\n        name: '硬件开发',\n        specification: '定制PCB设计及生产',\n        quantity: 1,\n        unitPrice: 150000,\n        totalPrice: 150000,\n        unitCost: 100000,\n        totalCost: 100000\n      },\n      {\n        id: 2,\n        name: '软件开发',\n        specification: '嵌入式软件开发',\n        quantity: 1,\n        unitPrice: 100000,\n        totalPrice: 100000,\n        unitCost: 70000,\n        totalCost: 70000\n      },\n      {\n        id: 3,\n        name: '测试验证',\n        specification: '功能测试及认证',\n        quantity: 1,\n        unitPrice: 50000,\n        totalPrice: 50000,\n        unitCost: 40000,\n        totalCost: 40000\n      }\n    ],\n    notes: '开发周期8周，包含技术支持',\n    convertedToOrder: null,\n    bomId: 'BOM-002',\n    bomName: '图形工作站配置'\n  },\n  {\n    id: 'QUO-2024-003',\n    customerId: 4,\n    customerName: '腾讯科技有限公司',\n    title: '云服务解决方案报价',\n    totalAmount: 450000,\n    estimatedCost: 315000,\n    estimatedProfit: 135000,\n    profitMargin: 30,\n    status: 'draft',\n    validUntil: '2024-02-28',\n    createTime: '2024-01-25',\n    salesperson: '李业务',\n    items: [\n      {\n        id: 1,\n        name: '云服务器',\n        specification: '高性能云服务器配置',\n        quantity: 20,\n        unitPrice: 15000,\n        totalPrice: 300000,\n        unitCost: 10500,\n        totalCost: 210000\n      },\n      {\n        id: 2,\n        name: '数据库服务',\n        specification: '高可用数据库集群',\n        quantity: 2,\n        unitPrice: 50000,\n        totalPrice: 100000,\n        unitCost: 35000,\n        totalCost: 70000\n      },\n      {\n        id: 3,\n        name: '技术支持',\n        specification: '7x24小时技术支持',\n        quantity: 1,\n        unitPrice: 50000,\n        totalPrice: 50000,\n        unitCost: 35000,\n        totalCost: 35000\n      }\n    ],\n    notes: '首年服务费用，续费享受优惠',\n    convertedToOrder: null,\n    bomId: 'BOM-003',\n    bomName: '基础办公电脑配置'\n  }\n];\n\nconst initialState = {\n  quotations: mockQuotations,\n  currentQuotation: null,\n  loading: false,\n  error: null,\n  searchKeyword: '',\n  filters: {\n    status: '',\n    salesperson: '',\n    dateRange: []\n  },\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: mockQuotations.length\n  }\n};\n\nconst quotationSlice = createSlice({\n  name: 'quotation',\n  initialState,\n  reducers: {\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setQuotations: (state, action) => {\n      state.quotations = action.payload;\n      state.pagination.total = action.payload.length;\n    },\n    addQuotation: (state, action) => {\n      const newQuotation = {\n        ...action.payload,\n        id: `QUO-${new Date().getFullYear()}-${String(state.quotations.length + 1).padStart(3, '0')}`,\n        createTime: new Date().toISOString().split('T')[0],\n        status: 'draft',\n        convertedToOrder: null\n      };\n      state.quotations.unshift(newQuotation);\n      state.pagination.total = state.quotations.length;\n    },\n    updateQuotation: (state, action) => {\n      const index = state.quotations.findIndex(q => q.id === action.payload.id);\n      if (index !== -1) {\n        state.quotations[index] = { ...state.quotations[index], ...action.payload };\n      }\n    },\n    updateQuotationStatus: (state, action) => {\n      const { id, status } = action.payload;\n      const quotation = state.quotations.find(q => q.id === id);\n      if (quotation) {\n        quotation.status = status;\n      }\n    },\n    convertToOrder: (state, action) => {\n      const { quotationId, orderId } = action.payload;\n      const quotation = state.quotations.find(q => q.id === quotationId);\n      if (quotation) {\n        quotation.status = 'accepted';\n        quotation.convertedToOrder = orderId;\n      }\n    },\n    deleteQuotation: (state, action) => {\n      state.quotations = state.quotations.filter(q => q.id !== action.payload);\n      state.pagination.total = state.quotations.length;\n    },\n    setCurrentQuotation: (state, action) => {\n      state.currentQuotation = action.payload;\n    },\n    setSearchKeyword: (state, action) => {\n      state.searchKeyword = action.payload;\n    },\n    setFilters: (state, action) => {\n      state.filters = { ...state.filters, ...action.payload };\n    },\n    setPagination: (state, action) => {\n      state.pagination = { ...state.pagination, ...action.payload };\n    },\n    resetFilters: (state) => {\n      state.searchKeyword = '';\n      state.filters = {\n        status: '',\n        salesperson: '',\n        dateRange: []\n      };\n      state.pagination.current = 1;\n    }\n  }\n});\n\nexport const {\n  setLoading,\n  setError,\n  clearError,\n  setQuotations,\n  addQuotation,\n  updateQuotation,\n  updateQuotationStatus,\n  convertToOrder,\n  deleteQuotation,\n  setCurrentQuotation,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters\n} = quotationSlice.actions;\n\n// 选择器\nexport const selectQuotations = (state) => state.quotation.quotations;\nexport const selectCurrentQuotation = (state) => state.quotation.currentQuotation;\nexport const selectQuotationLoading = (state) => state.quotation.loading;\nexport const selectQuotationError = (state) => state.quotation.error;\nexport const selectQuotationFilters = (state) => state.quotation.filters;\nexport const selectQuotationPagination = (state) => state.quotation.pagination;\n\n// 过滤后的报价单列表\nexport const selectFilteredQuotations = (state) => {\n  const { quotations, searchKeyword, filters } = state.quotation;\n  \n  return quotations.filter(quotation => {\n    // 搜索关键词过滤\n    if (searchKeyword) {\n      const keyword = searchKeyword.toLowerCase();\n      if (!quotation.id.toLowerCase().includes(keyword) &&\n          !quotation.customerName.toLowerCase().includes(keyword) &&\n          !quotation.title.toLowerCase().includes(keyword)) {\n        return false;\n      }\n    }\n    \n    // 状态过滤\n    if (filters.status && quotation.status !== filters.status) {\n      return false;\n    }\n    \n    // 业务员过滤\n    if (filters.salesperson && quotation.salesperson !== filters.salesperson) {\n      return false;\n    }\n    \n    return true;\n  });\n};\n\nexport default quotationSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA,MAAMC,cAAc,GAAG,CACrB;EACEC,EAAE,EAAE,cAAc;EAClBC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,UAAU;EACxBC,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,MAAM;EACnBC,aAAa,EAAE,MAAM;EACrBC,eAAe,EAAE,MAAM;EACvBC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,UAAU;EAClBC,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,YAAY;EACxBC,WAAW,EAAE,KAAK;EAClBC,KAAK,EAAE,CACL;IACEZ,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE,qBAAqB;IACpCC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,aAAa,EAAE,qBAAqB;IACpCC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE,UAAU;IACzBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,KAAK,EAAE,YAAY;EACnBC,gBAAgB,EAAE,cAAc;EAChCC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE;AACX,CAAC,EACD;EACEvB,EAAE,EAAE,cAAc;EAClBC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,UAAU;EACxBC,KAAK,EAAE,YAAY;EACnBC,WAAW,EAAE,MAAM;EACnBC,aAAa,EAAE,MAAM;EACrBC,eAAe,EAAE,KAAK;EACtBC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,YAAY;EACxBC,WAAW,EAAE,KAAK;EAClBC,KAAK,EAAE,CACL;IACEZ,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,aAAa,EAAE,YAAY;IAC3BC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,KAAK,EAAE,eAAe;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE;AACX,CAAC,EACD;EACEvB,EAAE,EAAE,cAAc;EAClBC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,UAAU;EACxBC,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,MAAM;EACnBC,aAAa,EAAE,MAAM;EACrBC,eAAe,EAAE,MAAM;EACvBC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,OAAO;EACfC,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,YAAY;EACxBC,WAAW,EAAE,KAAK;EAClBC,KAAK,EAAE,CACL;IACEZ,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,aAAa,EAAE,WAAW;IAC1BC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE,UAAU;IACzBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,aAAa,EAAE,YAAY;IAC3BC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,CACF;EACDC,KAAK,EAAE,eAAe;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE;AACX,CAAC,CACF;AAED,MAAMC,YAAY,GAAG;EACnBC,UAAU,EAAE1B,cAAc;EAC1B2B,gBAAgB,EAAE,IAAI;EACtBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,aAAa,EAAE,EAAE;EACjBC,OAAO,EAAE;IACPtB,MAAM,EAAE,EAAE;IACVG,WAAW,EAAE,EAAE;IACfoB,SAAS,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAEpC,cAAc,CAACqC;EACxB;AACF,CAAC;AAED,MAAMC,cAAc,GAAGvC,WAAW,CAAC;EACjCe,IAAI,EAAE,WAAW;EACjBW,YAAY;EACZc,QAAQ,EAAE;IACRC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACb,OAAO,GAAGc,MAAM,CAACC,OAAO;IAChC,CAAC;IACDC,QAAQ,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACZ,KAAK,GAAGa,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDE,UAAU,EAAGJ,KAAK,IAAK;MACrBA,KAAK,CAACZ,KAAK,GAAG,IAAI;IACpB,CAAC;IACDiB,aAAa,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MAChCD,KAAK,CAACf,UAAU,GAAGgB,MAAM,CAACC,OAAO;MACjCF,KAAK,CAACR,UAAU,CAACG,KAAK,GAAGM,MAAM,CAACC,OAAO,CAACN,MAAM;IAChD,CAAC;IACDU,YAAY,EAAEA,CAACN,KAAK,EAAEC,MAAM,KAAK;MAC/B,MAAMM,YAAY,GAAG;QACnB,GAAGN,MAAM,CAACC,OAAO;QACjB1C,EAAE,EAAE,OAAO,IAAIgD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACV,KAAK,CAACf,UAAU,CAACW,MAAM,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC7FzC,UAAU,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClD7C,MAAM,EAAE,OAAO;QACfa,gBAAgB,EAAE;MACpB,CAAC;MACDmB,KAAK,CAACf,UAAU,CAAC6B,OAAO,CAACP,YAAY,CAAC;MACtCP,KAAK,CAACR,UAAU,CAACG,KAAK,GAAGK,KAAK,CAACf,UAAU,CAACW,MAAM;IAClD,CAAC;IACDmB,eAAe,EAAEA,CAACf,KAAK,EAAEC,MAAM,KAAK;MAClC,MAAMe,KAAK,GAAGhB,KAAK,CAACf,UAAU,CAACgC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKyC,MAAM,CAACC,OAAO,CAAC1C,EAAE,CAAC;MACzE,IAAIwD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBhB,KAAK,CAACf,UAAU,CAAC+B,KAAK,CAAC,GAAG;UAAE,GAAGhB,KAAK,CAACf,UAAU,CAAC+B,KAAK,CAAC;UAAE,GAAGf,MAAM,CAACC;QAAQ,CAAC;MAC7E;IACF,CAAC;IACDiB,qBAAqB,EAAEA,CAACnB,KAAK,EAAEC,MAAM,KAAK;MACxC,MAAM;QAAEzC,EAAE;QAAEQ;MAAO,CAAC,GAAGiC,MAAM,CAACC,OAAO;MACrC,MAAMkB,SAAS,GAAGpB,KAAK,CAACf,UAAU,CAACoC,IAAI,CAACH,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKA,EAAE,CAAC;MACzD,IAAI4D,SAAS,EAAE;QACbA,SAAS,CAACpD,MAAM,GAAGA,MAAM;MAC3B;IACF,CAAC;IACDsD,cAAc,EAAEA,CAACtB,KAAK,EAAEC,MAAM,KAAK;MACjC,MAAM;QAAEsB,WAAW;QAAEC;MAAQ,CAAC,GAAGvB,MAAM,CAACC,OAAO;MAC/C,MAAMkB,SAAS,GAAGpB,KAAK,CAACf,UAAU,CAACoC,IAAI,CAACH,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAK+D,WAAW,CAAC;MAClE,IAAIH,SAAS,EAAE;QACbA,SAAS,CAACpD,MAAM,GAAG,UAAU;QAC7BoD,SAAS,CAACvC,gBAAgB,GAAG2C,OAAO;MACtC;IACF,CAAC;IACDC,eAAe,EAAEA,CAACzB,KAAK,EAAEC,MAAM,KAAK;MAClCD,KAAK,CAACf,UAAU,GAAGe,KAAK,CAACf,UAAU,CAACyC,MAAM,CAACR,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKyC,MAAM,CAACC,OAAO,CAAC;MACxEF,KAAK,CAACR,UAAU,CAACG,KAAK,GAAGK,KAAK,CAACf,UAAU,CAACW,MAAM;IAClD,CAAC;IACD+B,mBAAmB,EAAEA,CAAC3B,KAAK,EAAEC,MAAM,KAAK;MACtCD,KAAK,CAACd,gBAAgB,GAAGe,MAAM,CAACC,OAAO;IACzC,CAAC;IACD0B,gBAAgB,EAAEA,CAAC5B,KAAK,EAAEC,MAAM,KAAK;MACnCD,KAAK,CAACX,aAAa,GAAGY,MAAM,CAACC,OAAO;IACtC,CAAC;IACD2B,UAAU,EAAEA,CAAC7B,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACV,OAAO,GAAG;QAAE,GAAGU,KAAK,CAACV,OAAO;QAAE,GAAGW,MAAM,CAACC;MAAQ,CAAC;IACzD,CAAC;IACD4B,aAAa,EAAEA,CAAC9B,KAAK,EAAEC,MAAM,KAAK;MAChCD,KAAK,CAACR,UAAU,GAAG;QAAE,GAAGQ,KAAK,CAACR,UAAU;QAAE,GAAGS,MAAM,CAACC;MAAQ,CAAC;IAC/D,CAAC;IACD6B,YAAY,EAAG/B,KAAK,IAAK;MACvBA,KAAK,CAACX,aAAa,GAAG,EAAE;MACxBW,KAAK,CAACV,OAAO,GAAG;QACdtB,MAAM,EAAE,EAAE;QACVG,WAAW,EAAE,EAAE;QACfoB,SAAS,EAAE;MACb,CAAC;MACDS,KAAK,CAACR,UAAU,CAACC,OAAO,GAAG,CAAC;IAC9B;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXM,UAAU;EACVI,QAAQ;EACRC,UAAU;EACVC,aAAa;EACbC,YAAY;EACZS,eAAe;EACfI,qBAAqB;EACrBG,cAAc;EACdG,eAAe;EACfE,mBAAmB;EACnBC,gBAAgB;EAChBC,UAAU;EACVC,aAAa;EACbC;AACF,CAAC,GAAGlC,cAAc,CAACmC,OAAO;;AAE1B;AACA,OAAO,MAAMC,gBAAgB,GAAIjC,KAAK,IAAKA,KAAK,CAACoB,SAAS,CAACnC,UAAU;AACrE,OAAO,MAAMiD,sBAAsB,GAAIlC,KAAK,IAAKA,KAAK,CAACoB,SAAS,CAAClC,gBAAgB;AACjF,OAAO,MAAMiD,sBAAsB,GAAInC,KAAK,IAAKA,KAAK,CAACoB,SAAS,CAACjC,OAAO;AACxE,OAAO,MAAMiD,oBAAoB,GAAIpC,KAAK,IAAKA,KAAK,CAACoB,SAAS,CAAChC,KAAK;AACpE,OAAO,MAAMiD,sBAAsB,GAAIrC,KAAK,IAAKA,KAAK,CAACoB,SAAS,CAAC9B,OAAO;AACxE,OAAO,MAAMgD,yBAAyB,GAAItC,KAAK,IAAKA,KAAK,CAACoB,SAAS,CAAC5B,UAAU;;AAE9E;AACA,OAAO,MAAM+C,wBAAwB,GAAIvC,KAAK,IAAK;EACjD,MAAM;IAAEf,UAAU;IAAEI,aAAa;IAAEC;EAAQ,CAAC,GAAGU,KAAK,CAACoB,SAAS;EAE9D,OAAOnC,UAAU,CAACyC,MAAM,CAACN,SAAS,IAAI;IACpC;IACA,IAAI/B,aAAa,EAAE;MACjB,MAAMmD,OAAO,GAAGnD,aAAa,CAACoD,WAAW,CAAC,CAAC;MAC3C,IAAI,CAACrB,SAAS,CAAC5D,EAAE,CAACiF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,IAC7C,CAACpB,SAAS,CAAC1D,YAAY,CAAC+E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,IACvD,CAACpB,SAAS,CAACzD,KAAK,CAAC8E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;QACpD,OAAO,KAAK;MACd;IACF;;IAEA;IACA,IAAIlD,OAAO,CAACtB,MAAM,IAAIoD,SAAS,CAACpD,MAAM,KAAKsB,OAAO,CAACtB,MAAM,EAAE;MACzD,OAAO,KAAK;IACd;;IAEA;IACA,IAAIsB,OAAO,CAACnB,WAAW,IAAIiD,SAAS,CAACjD,WAAW,KAAKmB,OAAO,CAACnB,WAAW,EAAE;MACxE,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;AAED,eAAe0B,cAAc,CAAC8C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}