{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport MapDraw from '../helper/MapDraw.js';\nimport ComponentView from '../../view/Component.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { findEventDispatcher } from '../../util/event.js';\nvar GeoView = /** @class */function (_super) {\n  __extends(GeoView, _super);\n  function GeoView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GeoView.type;\n    _this.focusBlurEnabled = true;\n    return _this;\n  }\n  GeoView.prototype.init = function (ecModel, api) {\n    this._api = api;\n  };\n  GeoView.prototype.render = function (geoModel, ecModel, api, payload) {\n    this._model = geoModel;\n    if (!geoModel.get('show')) {\n      this._mapDraw && this._mapDraw.remove();\n      this._mapDraw = null;\n      return;\n    }\n    if (!this._mapDraw) {\n      this._mapDraw = new MapDraw(api);\n    }\n    var mapDraw = this._mapDraw;\n    mapDraw.draw(geoModel, ecModel, api, this, payload);\n    mapDraw.group.on('click', this._handleRegionClick, this);\n    mapDraw.group.silent = geoModel.get('silent');\n    this.group.add(mapDraw.group);\n    this.updateSelectStatus(geoModel, ecModel, api);\n  };\n  GeoView.prototype._handleRegionClick = function (e) {\n    var eventData;\n    findEventDispatcher(e.target, function (current) {\n      return (eventData = getECData(current).eventData) != null;\n    }, true);\n    if (eventData) {\n      this._api.dispatchAction({\n        type: 'geoToggleSelect',\n        geoId: this._model.id,\n        name: eventData.name\n      });\n    }\n  };\n  GeoView.prototype.updateSelectStatus = function (model, ecModel, api) {\n    var _this = this;\n    this._mapDraw.group.traverse(function (node) {\n      var eventData = getECData(node).eventData;\n      if (eventData) {\n        _this._model.isSelected(eventData.name) ? api.enterSelect(node) : api.leaveSelect(node);\n        // No need to traverse children.\n        return true;\n      }\n    });\n  };\n  GeoView.prototype.findHighDownDispatchers = function (name) {\n    return this._mapDraw && this._mapDraw.findHighDownDispatchers(name, this._model);\n  };\n  GeoView.prototype.dispose = function () {\n    this._mapDraw && this._mapDraw.remove();\n  };\n  GeoView.type = 'geo';\n  return GeoView;\n}(ComponentView);\nexport default GeoView;", "map": {"version": 3, "names": ["__extends", "MapDraw", "ComponentView", "getECData", "find<PERSON>vent<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "_this", "apply", "arguments", "type", "focusBlurEnabled", "prototype", "init", "ecModel", "api", "_api", "render", "geoModel", "payload", "_model", "get", "_mapDraw", "remove", "mapDraw", "draw", "group", "on", "_handleRegionClick", "silent", "add", "updateSelectStatus", "e", "eventData", "target", "current", "dispatchAction", "geoId", "id", "name", "model", "traverse", "node", "isSelected", "enterSelect", "leaveSelect", "findHighDownDispatchers", "dispose"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/component/geo/GeoView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport MapDraw from '../helper/MapDraw.js';\nimport ComponentView from '../../view/Component.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { findEventDispatcher } from '../../util/event.js';\nvar GeoView = /** @class */function (_super) {\n  __extends(GeoView, _super);\n  function GeoView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GeoView.type;\n    _this.focusBlurEnabled = true;\n    return _this;\n  }\n  GeoView.prototype.init = function (ecModel, api) {\n    this._api = api;\n  };\n  GeoView.prototype.render = function (geoModel, ecModel, api, payload) {\n    this._model = geoModel;\n    if (!geoModel.get('show')) {\n      this._mapDraw && this._mapDraw.remove();\n      this._mapDraw = null;\n      return;\n    }\n    if (!this._mapDraw) {\n      this._mapDraw = new MapDraw(api);\n    }\n    var mapDraw = this._mapDraw;\n    mapDraw.draw(geoModel, ecModel, api, this, payload);\n    mapDraw.group.on('click', this._handleRegionClick, this);\n    mapDraw.group.silent = geoModel.get('silent');\n    this.group.add(mapDraw.group);\n    this.updateSelectStatus(geoModel, ecModel, api);\n  };\n  GeoView.prototype._handleRegionClick = function (e) {\n    var eventData;\n    findEventDispatcher(e.target, function (current) {\n      return (eventData = getECData(current).eventData) != null;\n    }, true);\n    if (eventData) {\n      this._api.dispatchAction({\n        type: 'geoToggleSelect',\n        geoId: this._model.id,\n        name: eventData.name\n      });\n    }\n  };\n  GeoView.prototype.updateSelectStatus = function (model, ecModel, api) {\n    var _this = this;\n    this._mapDraw.group.traverse(function (node) {\n      var eventData = getECData(node).eventData;\n      if (eventData) {\n        _this._model.isSelected(eventData.name) ? api.enterSelect(node) : api.leaveSelect(node);\n        // No need to traverse children.\n        return true;\n      }\n    });\n  };\n  GeoView.prototype.findHighDownDispatchers = function (name) {\n    return this._mapDraw && this._mapDraw.findHighDownDispatchers(name, this._model);\n  };\n  GeoView.prototype.dispose = function () {\n    this._mapDraw && this._mapDraw.remove();\n  };\n  GeoView.type = 'geo';\n  return GeoView;\n}(ComponentView);\nexport default GeoView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,IAAIC,OAAO,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC3CN,SAAS,CAACK,OAAO,EAAEC,MAAM,CAAC;EAC1B,SAASD,OAAOA,CAAA,EAAG;IACjB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,OAAO,CAACK,IAAI;IACzBH,KAAK,CAACI,gBAAgB,GAAG,IAAI;IAC7B,OAAOJ,KAAK;EACd;EACAF,OAAO,CAACO,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IAC/C,IAAI,CAACC,IAAI,GAAGD,GAAG;EACjB,CAAC;EACDV,OAAO,CAACO,SAAS,CAACK,MAAM,GAAG,UAAUC,QAAQ,EAAEJ,OAAO,EAAEC,GAAG,EAAEI,OAAO,EAAE;IACpE,IAAI,CAACC,MAAM,GAAGF,QAAQ;IACtB,IAAI,CAACA,QAAQ,CAACG,GAAG,CAAC,MAAM,CAAC,EAAE;MACzB,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,MAAM,CAAC,CAAC;MACvC,IAAI,CAACD,QAAQ,GAAG,IAAI;MACpB;IACF;IACA,IAAI,CAAC,IAAI,CAACA,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAIrB,OAAO,CAACc,GAAG,CAAC;IAClC;IACA,IAAIS,OAAO,GAAG,IAAI,CAACF,QAAQ;IAC3BE,OAAO,CAACC,IAAI,CAACP,QAAQ,EAAEJ,OAAO,EAAEC,GAAG,EAAE,IAAI,EAAEI,OAAO,CAAC;IACnDK,OAAO,CAACE,KAAK,CAACC,EAAE,CAAC,OAAO,EAAE,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAAC;IACxDJ,OAAO,CAACE,KAAK,CAACG,MAAM,GAAGX,QAAQ,CAACG,GAAG,CAAC,QAAQ,CAAC;IAC7C,IAAI,CAACK,KAAK,CAACI,GAAG,CAACN,OAAO,CAACE,KAAK,CAAC;IAC7B,IAAI,CAACK,kBAAkB,CAACb,QAAQ,EAAEJ,OAAO,EAAEC,GAAG,CAAC;EACjD,CAAC;EACDV,OAAO,CAACO,SAAS,CAACgB,kBAAkB,GAAG,UAAUI,CAAC,EAAE;IAClD,IAAIC,SAAS;IACb7B,mBAAmB,CAAC4B,CAAC,CAACE,MAAM,EAAE,UAAUC,OAAO,EAAE;MAC/C,OAAO,CAACF,SAAS,GAAG9B,SAAS,CAACgC,OAAO,CAAC,CAACF,SAAS,KAAK,IAAI;IAC3D,CAAC,EAAE,IAAI,CAAC;IACR,IAAIA,SAAS,EAAE;MACb,IAAI,CAACjB,IAAI,CAACoB,cAAc,CAAC;QACvB1B,IAAI,EAAE,iBAAiB;QACvB2B,KAAK,EAAE,IAAI,CAACjB,MAAM,CAACkB,EAAE;QACrBC,IAAI,EAAEN,SAAS,CAACM;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EACDlC,OAAO,CAACO,SAAS,CAACmB,kBAAkB,GAAG,UAAUS,KAAK,EAAE1B,OAAO,EAAEC,GAAG,EAAE;IACpE,IAAIR,KAAK,GAAG,IAAI;IAChB,IAAI,CAACe,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAC3C,IAAIT,SAAS,GAAG9B,SAAS,CAACuC,IAAI,CAAC,CAACT,SAAS;MACzC,IAAIA,SAAS,EAAE;QACb1B,KAAK,CAACa,MAAM,CAACuB,UAAU,CAACV,SAAS,CAACM,IAAI,CAAC,GAAGxB,GAAG,CAAC6B,WAAW,CAACF,IAAI,CAAC,GAAG3B,GAAG,CAAC8B,WAAW,CAACH,IAAI,CAAC;QACvF;QACA,OAAO,IAAI;MACb;IACF,CAAC,CAAC;EACJ,CAAC;EACDrC,OAAO,CAACO,SAAS,CAACkC,uBAAuB,GAAG,UAAUP,IAAI,EAAE;IAC1D,OAAO,IAAI,CAACjB,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACwB,uBAAuB,CAACP,IAAI,EAAE,IAAI,CAACnB,MAAM,CAAC;EAClF,CAAC;EACDf,OAAO,CAACO,SAAS,CAACmC,OAAO,GAAG,YAAY;IACtC,IAAI,CAACzB,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,MAAM,CAAC,CAAC;EACzC,CAAC;EACDlB,OAAO,CAACK,IAAI,GAAG,KAAK;EACpB,OAAOL,OAAO;AAChB,CAAC,CAACH,aAAa,CAAC;AAChB,eAAeG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}