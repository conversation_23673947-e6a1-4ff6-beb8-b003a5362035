{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as layout from '../../util/layout.js';\nimport * as numberUtil from '../../util/number.js';\n// (24*60*60*1000)\nvar PROXIMATE_ONE_DAY = 86400000;\nvar Calendar = /** @class */function () {\n  function Calendar(calendarModel, ecModel, api) {\n    this.type = 'calendar';\n    this.dimensions = Calendar.dimensions;\n    // Required in createListFromData\n    this.getDimensionsInfo = Calendar.getDimensionsInfo;\n    this._model = calendarModel;\n  }\n  Calendar.getDimensionsInfo = function () {\n    return [{\n      name: 'time',\n      type: 'time'\n    }, 'value'];\n  };\n  Calendar.prototype.getRangeInfo = function () {\n    return this._rangeInfo;\n  };\n  Calendar.prototype.getModel = function () {\n    return this._model;\n  };\n  Calendar.prototype.getRect = function () {\n    return this._rect;\n  };\n  Calendar.prototype.getCellWidth = function () {\n    return this._sw;\n  };\n  Calendar.prototype.getCellHeight = function () {\n    return this._sh;\n  };\n  Calendar.prototype.getOrient = function () {\n    return this._orient;\n  };\n  /**\r\n   * getFirstDayOfWeek\r\n   *\r\n   * @example\r\n   *     0 : start at Sunday\r\n   *     1 : start at Monday\r\n   *\r\n   * @return {number}\r\n   */\n  Calendar.prototype.getFirstDayOfWeek = function () {\n    return this._firstDayOfWeek;\n  };\n  /**\r\n   * get date info\r\n   * }\r\n   */\n  Calendar.prototype.getDateInfo = function (date) {\n    date = numberUtil.parseDate(date);\n    var y = date.getFullYear();\n    var m = date.getMonth() + 1;\n    var mStr = m < 10 ? '0' + m : '' + m;\n    var d = date.getDate();\n    var dStr = d < 10 ? '0' + d : '' + d;\n    var day = date.getDay();\n    day = Math.abs((day + 7 - this.getFirstDayOfWeek()) % 7);\n    return {\n      y: y + '',\n      m: mStr,\n      d: dStr,\n      day: day,\n      time: date.getTime(),\n      formatedDate: y + '-' + mStr + '-' + dStr,\n      date: date\n    };\n  };\n  Calendar.prototype.getNextNDay = function (date, n) {\n    n = n || 0;\n    if (n === 0) {\n      return this.getDateInfo(date);\n    }\n    date = new Date(this.getDateInfo(date).time);\n    date.setDate(date.getDate() + n);\n    return this.getDateInfo(date);\n  };\n  Calendar.prototype.update = function (ecModel, api) {\n    this._firstDayOfWeek = +this._model.getModel('dayLabel').get('firstDay');\n    this._orient = this._model.get('orient');\n    this._lineWidth = this._model.getModel('itemStyle').getItemStyle().lineWidth || 0;\n    this._rangeInfo = this._getRangeInfo(this._initRangeOption());\n    var weeks = this._rangeInfo.weeks || 1;\n    var whNames = ['width', 'height'];\n    var cellSize = this._model.getCellSize().slice();\n    var layoutParams = this._model.getBoxLayoutParams();\n    var cellNumbers = this._orient === 'horizontal' ? [weeks, 7] : [7, weeks];\n    zrUtil.each([0, 1], function (idx) {\n      if (cellSizeSpecified(cellSize, idx)) {\n        layoutParams[whNames[idx]] = cellSize[idx] * cellNumbers[idx];\n      }\n    });\n    var whGlobal = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var calendarRect = this._rect = layout.getLayoutRect(layoutParams, whGlobal);\n    zrUtil.each([0, 1], function (idx) {\n      if (!cellSizeSpecified(cellSize, idx)) {\n        cellSize[idx] = calendarRect[whNames[idx]] / cellNumbers[idx];\n      }\n    });\n    function cellSizeSpecified(cellSize, idx) {\n      return cellSize[idx] != null && cellSize[idx] !== 'auto';\n    }\n    // Has been calculated out number.\n    this._sw = cellSize[0];\n    this._sh = cellSize[1];\n  };\n  /**\r\n   * Convert a time data(time, value) item to (x, y) point.\r\n   */\n  // TODO Clamp of calendar is not same with cartesian coordinate systems.\n  // It will return NaN if data exceeds.\n  Calendar.prototype.dataToPoint = function (data, clamp) {\n    zrUtil.isArray(data) && (data = data[0]);\n    clamp == null && (clamp = true);\n    var dayInfo = this.getDateInfo(data);\n    var range = this._rangeInfo;\n    var date = dayInfo.formatedDate;\n    // if not in range return [NaN, NaN]\n    if (clamp && !(dayInfo.time >= range.start.time && dayInfo.time < range.end.time + PROXIMATE_ONE_DAY)) {\n      return [NaN, NaN];\n    }\n    var week = dayInfo.day;\n    var nthWeek = this._getRangeInfo([range.start.time, date]).nthWeek;\n    if (this._orient === 'vertical') {\n      return [this._rect.x + week * this._sw + this._sw / 2, this._rect.y + nthWeek * this._sh + this._sh / 2];\n    }\n    return [this._rect.x + nthWeek * this._sw + this._sw / 2, this._rect.y + week * this._sh + this._sh / 2];\n  };\n  /**\r\n   * Convert a (x, y) point to time data\r\n   */\n  Calendar.prototype.pointToData = function (point) {\n    var date = this.pointToDate(point);\n    return date && date.time;\n  };\n  /**\r\n   * Convert a time date item to (x, y) four point.\r\n   */\n  Calendar.prototype.dataToRect = function (data, clamp) {\n    var point = this.dataToPoint(data, clamp);\n    return {\n      contentShape: {\n        x: point[0] - (this._sw - this._lineWidth) / 2,\n        y: point[1] - (this._sh - this._lineWidth) / 2,\n        width: this._sw - this._lineWidth,\n        height: this._sh - this._lineWidth\n      },\n      center: point,\n      tl: [point[0] - this._sw / 2, point[1] - this._sh / 2],\n      tr: [point[0] + this._sw / 2, point[1] - this._sh / 2],\n      br: [point[0] + this._sw / 2, point[1] + this._sh / 2],\n      bl: [point[0] - this._sw / 2, point[1] + this._sh / 2]\n    };\n  };\n  /**\r\n   * Convert a (x, y) point to time date\r\n   *\r\n   * @param  {Array} point point\r\n   * @return {Object}       date\r\n   */\n  Calendar.prototype.pointToDate = function (point) {\n    var nthX = Math.floor((point[0] - this._rect.x) / this._sw) + 1;\n    var nthY = Math.floor((point[1] - this._rect.y) / this._sh) + 1;\n    var range = this._rangeInfo.range;\n    if (this._orient === 'vertical') {\n      return this._getDateByWeeksAndDay(nthY, nthX - 1, range);\n    }\n    return this._getDateByWeeksAndDay(nthX, nthY - 1, range);\n  };\n  Calendar.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.dataToPoint(value) : null;\n  };\n  Calendar.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.pointToData(pixel) : null;\n  };\n  Calendar.prototype.containPoint = function (point) {\n    console.warn('Not implemented.');\n    return false;\n  };\n  /**\r\n   * initRange\r\n   * Normalize to an [start, end] array\r\n   */\n  Calendar.prototype._initRangeOption = function () {\n    var range = this._model.get('range');\n    var normalizedRange;\n    // Convert [1990] to 1990\n    if (zrUtil.isArray(range) && range.length === 1) {\n      range = range[0];\n    }\n    if (!zrUtil.isArray(range)) {\n      var rangeStr = range.toString();\n      // One year.\n      if (/^\\d{4}$/.test(rangeStr)) {\n        normalizedRange = [rangeStr + '-01-01', rangeStr + '-12-31'];\n      }\n      // One month\n      if (/^\\d{4}[\\/|-]\\d{1,2}$/.test(rangeStr)) {\n        var start = this.getDateInfo(rangeStr);\n        var firstDay = start.date;\n        firstDay.setMonth(firstDay.getMonth() + 1);\n        var end = this.getNextNDay(firstDay, -1);\n        normalizedRange = [start.formatedDate, end.formatedDate];\n      }\n      // One day\n      if (/^\\d{4}[\\/|-]\\d{1,2}[\\/|-]\\d{1,2}$/.test(rangeStr)) {\n        normalizedRange = [rangeStr, rangeStr];\n      }\n    } else {\n      normalizedRange = range;\n    }\n    if (!normalizedRange) {\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.logError('Invalid date range.');\n      }\n      // Not handling it.\n      return range;\n    }\n    var tmp = this._getRangeInfo(normalizedRange);\n    if (tmp.start.time > tmp.end.time) {\n      normalizedRange.reverse();\n    }\n    return normalizedRange;\n  };\n  /**\r\n   * range info\r\n   *\r\n   * @private\r\n   * @param  {Array} range range ['2017-01-01', '2017-07-08']\r\n   *  If range[0] > range[1], they will not be reversed.\r\n   * @return {Object}       obj\r\n   */\n  Calendar.prototype._getRangeInfo = function (range) {\n    var parsedRange = [this.getDateInfo(range[0]), this.getDateInfo(range[1])];\n    var reversed;\n    if (parsedRange[0].time > parsedRange[1].time) {\n      reversed = true;\n      parsedRange.reverse();\n    }\n    var allDay = Math.floor(parsedRange[1].time / PROXIMATE_ONE_DAY) - Math.floor(parsedRange[0].time / PROXIMATE_ONE_DAY) + 1;\n    // Consider case1 (#11677 #10430):\n    // Set the system timezone as \"UK\", set the range to `['2016-07-01', '2016-12-31']`\n    // Consider case2:\n    // Firstly set system timezone as \"Time Zone: America/Toronto\",\n    // ```\n    // let first = new Date(1478412000000 - 3600 * 1000 * 2.5);\n    // let second = new Date(1478412000000);\n    // let allDays = Math.floor(second / ONE_DAY) - Math.floor(first / ONE_DAY) + 1;\n    // ```\n    // will get wrong result because of DST. So we should fix it.\n    var date = new Date(parsedRange[0].time);\n    var startDateNum = date.getDate();\n    var endDateNum = parsedRange[1].date.getDate();\n    date.setDate(startDateNum + allDay - 1);\n    // The bias can not over a month, so just compare date.\n    var dateNum = date.getDate();\n    if (dateNum !== endDateNum) {\n      var sign = date.getTime() - parsedRange[1].time > 0 ? 1 : -1;\n      while ((dateNum = date.getDate()) !== endDateNum && (date.getTime() - parsedRange[1].time) * sign > 0) {\n        allDay -= sign;\n        date.setDate(dateNum - sign);\n      }\n    }\n    var weeks = Math.floor((allDay + parsedRange[0].day + 6) / 7);\n    var nthWeek = reversed ? -weeks + 1 : weeks - 1;\n    reversed && parsedRange.reverse();\n    return {\n      range: [parsedRange[0].formatedDate, parsedRange[1].formatedDate],\n      start: parsedRange[0],\n      end: parsedRange[1],\n      allDay: allDay,\n      weeks: weeks,\n      // From 0.\n      nthWeek: nthWeek,\n      fweek: parsedRange[0].day,\n      lweek: parsedRange[1].day\n    };\n  };\n  /**\r\n   * get date by nthWeeks and week day in range\r\n   *\r\n   * @private\r\n   * @param  {number} nthWeek the week\r\n   * @param  {number} day   the week day\r\n   * @param  {Array} range [d1, d2]\r\n   * @return {Object}\r\n   */\n  Calendar.prototype._getDateByWeeksAndDay = function (nthWeek, day, range) {\n    var rangeInfo = this._getRangeInfo(range);\n    if (nthWeek > rangeInfo.weeks || nthWeek === 0 && day < rangeInfo.fweek || nthWeek === rangeInfo.weeks && day > rangeInfo.lweek) {\n      return null;\n    }\n    var nthDay = (nthWeek - 1) * 7 - rangeInfo.fweek + day;\n    var date = new Date(rangeInfo.start.time);\n    date.setDate(+rangeInfo.start.d + nthDay);\n    return this.getDateInfo(date);\n  };\n  Calendar.create = function (ecModel, api) {\n    var calendarList = [];\n    ecModel.eachComponent('calendar', function (calendarModel) {\n      var calendar = new Calendar(calendarModel, ecModel, api);\n      calendarList.push(calendar);\n      calendarModel.coordinateSystem = calendar;\n    });\n    ecModel.eachSeries(function (calendarSeries) {\n      if (calendarSeries.get('coordinateSystem') === 'calendar') {\n        // Inject coordinate system\n        calendarSeries.coordinateSystem = calendarList[calendarSeries.get('calendarIndex') || 0];\n      }\n    });\n    return calendarList;\n  };\n  Calendar.dimensions = ['time', 'value'];\n  return Calendar;\n}();\nfunction getCoordSys(finder) {\n  var calendarModel = finder.calendarModel;\n  var seriesModel = finder.seriesModel;\n  var coordSys = calendarModel ? calendarModel.coordinateSystem : seriesModel ? seriesModel.coordinateSystem : null;\n  return coordSys;\n}\nexport default Calendar;", "map": {"version": 3, "names": ["zrUtil", "layout", "numberUtil", "PROXIMATE_ONE_DAY", "Calendar", "calendarModel", "ecModel", "api", "type", "dimensions", "getDimensionsInfo", "_model", "name", "prototype", "getRangeInfo", "_rangeInfo", "getModel", "getRect", "_rect", "get<PERSON>ell<PERSON><PERSON>th", "_sw", "getCellHeight", "_sh", "getOrient", "_orient", "getFirstDayOfWeek", "_firstDayOfWeek", "getDateInfo", "date", "parseDate", "y", "getFullYear", "m", "getMonth", "mStr", "d", "getDate", "dStr", "day", "getDay", "Math", "abs", "time", "getTime", "formatedDate", "getNextNDay", "n", "Date", "setDate", "update", "get", "_lineWidth", "getItemStyle", "lineWidth", "_getRangeInfo", "_initRangeOption", "weeks", "whNames", "cellSize", "getCellSize", "slice", "layoutParams", "getBoxLayoutParams", "cellNumbers", "each", "idx", "cellSizeSpecified", "whGlobal", "width", "getWidth", "height", "getHeight", "calendarRect", "getLayoutRect", "dataToPoint", "data", "clamp", "isArray", "dayInfo", "range", "start", "end", "NaN", "week", "nthWeek", "x", "pointToData", "point", "pointToDate", "dataToRect", "contentShape", "center", "tl", "tr", "br", "bl", "nthX", "floor", "nthY", "_getDateByWeeksAndDay", "convertToPixel", "finder", "value", "coordSys", "getCoordSys", "convertFromPixel", "pixel", "containPoint", "console", "warn", "normalizedRange", "length", "rangeStr", "toString", "test", "firstDay", "setMonth", "process", "env", "NODE_ENV", "logError", "tmp", "reverse", "parsed<PERSON><PERSON><PERSON>", "reversed", "allDay", "startDateNum", "endDateNum", "dateNum", "sign", "fweek", "lweek", "rangeInfo", "nthDay", "create", "calendarList", "eachComponent", "calendar", "push", "coordinateSystem", "eachSeries", "calendarSeries", "seriesModel"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/coord/calendar/Calendar.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as layout from '../../util/layout.js';\nimport * as numberUtil from '../../util/number.js';\n// (24*60*60*1000)\nvar PROXIMATE_ONE_DAY = 86400000;\nvar Calendar = /** @class */function () {\n  function Calendar(calendarModel, ecModel, api) {\n    this.type = 'calendar';\n    this.dimensions = Calendar.dimensions;\n    // Required in createListFromData\n    this.getDimensionsInfo = Calendar.getDimensionsInfo;\n    this._model = calendarModel;\n  }\n  Calendar.getDimensionsInfo = function () {\n    return [{\n      name: 'time',\n      type: 'time'\n    }, 'value'];\n  };\n  Calendar.prototype.getRangeInfo = function () {\n    return this._rangeInfo;\n  };\n  Calendar.prototype.getModel = function () {\n    return this._model;\n  };\n  Calendar.prototype.getRect = function () {\n    return this._rect;\n  };\n  Calendar.prototype.getCellWidth = function () {\n    return this._sw;\n  };\n  Calendar.prototype.getCellHeight = function () {\n    return this._sh;\n  };\n  Calendar.prototype.getOrient = function () {\n    return this._orient;\n  };\n  /**\r\n   * getFirstDayOfWeek\r\n   *\r\n   * @example\r\n   *     0 : start at Sunday\r\n   *     1 : start at Monday\r\n   *\r\n   * @return {number}\r\n   */\n  Calendar.prototype.getFirstDayOfWeek = function () {\n    return this._firstDayOfWeek;\n  };\n  /**\r\n   * get date info\r\n   * }\r\n   */\n  Calendar.prototype.getDateInfo = function (date) {\n    date = numberUtil.parseDate(date);\n    var y = date.getFullYear();\n    var m = date.getMonth() + 1;\n    var mStr = m < 10 ? '0' + m : '' + m;\n    var d = date.getDate();\n    var dStr = d < 10 ? '0' + d : '' + d;\n    var day = date.getDay();\n    day = Math.abs((day + 7 - this.getFirstDayOfWeek()) % 7);\n    return {\n      y: y + '',\n      m: mStr,\n      d: dStr,\n      day: day,\n      time: date.getTime(),\n      formatedDate: y + '-' + mStr + '-' + dStr,\n      date: date\n    };\n  };\n  Calendar.prototype.getNextNDay = function (date, n) {\n    n = n || 0;\n    if (n === 0) {\n      return this.getDateInfo(date);\n    }\n    date = new Date(this.getDateInfo(date).time);\n    date.setDate(date.getDate() + n);\n    return this.getDateInfo(date);\n  };\n  Calendar.prototype.update = function (ecModel, api) {\n    this._firstDayOfWeek = +this._model.getModel('dayLabel').get('firstDay');\n    this._orient = this._model.get('orient');\n    this._lineWidth = this._model.getModel('itemStyle').getItemStyle().lineWidth || 0;\n    this._rangeInfo = this._getRangeInfo(this._initRangeOption());\n    var weeks = this._rangeInfo.weeks || 1;\n    var whNames = ['width', 'height'];\n    var cellSize = this._model.getCellSize().slice();\n    var layoutParams = this._model.getBoxLayoutParams();\n    var cellNumbers = this._orient === 'horizontal' ? [weeks, 7] : [7, weeks];\n    zrUtil.each([0, 1], function (idx) {\n      if (cellSizeSpecified(cellSize, idx)) {\n        layoutParams[whNames[idx]] = cellSize[idx] * cellNumbers[idx];\n      }\n    });\n    var whGlobal = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var calendarRect = this._rect = layout.getLayoutRect(layoutParams, whGlobal);\n    zrUtil.each([0, 1], function (idx) {\n      if (!cellSizeSpecified(cellSize, idx)) {\n        cellSize[idx] = calendarRect[whNames[idx]] / cellNumbers[idx];\n      }\n    });\n    function cellSizeSpecified(cellSize, idx) {\n      return cellSize[idx] != null && cellSize[idx] !== 'auto';\n    }\n    // Has been calculated out number.\n    this._sw = cellSize[0];\n    this._sh = cellSize[1];\n  };\n  /**\r\n   * Convert a time data(time, value) item to (x, y) point.\r\n   */\n  // TODO Clamp of calendar is not same with cartesian coordinate systems.\n  // It will return NaN if data exceeds.\n  Calendar.prototype.dataToPoint = function (data, clamp) {\n    zrUtil.isArray(data) && (data = data[0]);\n    clamp == null && (clamp = true);\n    var dayInfo = this.getDateInfo(data);\n    var range = this._rangeInfo;\n    var date = dayInfo.formatedDate;\n    // if not in range return [NaN, NaN]\n    if (clamp && !(dayInfo.time >= range.start.time && dayInfo.time < range.end.time + PROXIMATE_ONE_DAY)) {\n      return [NaN, NaN];\n    }\n    var week = dayInfo.day;\n    var nthWeek = this._getRangeInfo([range.start.time, date]).nthWeek;\n    if (this._orient === 'vertical') {\n      return [this._rect.x + week * this._sw + this._sw / 2, this._rect.y + nthWeek * this._sh + this._sh / 2];\n    }\n    return [this._rect.x + nthWeek * this._sw + this._sw / 2, this._rect.y + week * this._sh + this._sh / 2];\n  };\n  /**\r\n   * Convert a (x, y) point to time data\r\n   */\n  Calendar.prototype.pointToData = function (point) {\n    var date = this.pointToDate(point);\n    return date && date.time;\n  };\n  /**\r\n   * Convert a time date item to (x, y) four point.\r\n   */\n  Calendar.prototype.dataToRect = function (data, clamp) {\n    var point = this.dataToPoint(data, clamp);\n    return {\n      contentShape: {\n        x: point[0] - (this._sw - this._lineWidth) / 2,\n        y: point[1] - (this._sh - this._lineWidth) / 2,\n        width: this._sw - this._lineWidth,\n        height: this._sh - this._lineWidth\n      },\n      center: point,\n      tl: [point[0] - this._sw / 2, point[1] - this._sh / 2],\n      tr: [point[0] + this._sw / 2, point[1] - this._sh / 2],\n      br: [point[0] + this._sw / 2, point[1] + this._sh / 2],\n      bl: [point[0] - this._sw / 2, point[1] + this._sh / 2]\n    };\n  };\n  /**\r\n   * Convert a (x, y) point to time date\r\n   *\r\n   * @param  {Array} point point\r\n   * @return {Object}       date\r\n   */\n  Calendar.prototype.pointToDate = function (point) {\n    var nthX = Math.floor((point[0] - this._rect.x) / this._sw) + 1;\n    var nthY = Math.floor((point[1] - this._rect.y) / this._sh) + 1;\n    var range = this._rangeInfo.range;\n    if (this._orient === 'vertical') {\n      return this._getDateByWeeksAndDay(nthY, nthX - 1, range);\n    }\n    return this._getDateByWeeksAndDay(nthX, nthY - 1, range);\n  };\n  Calendar.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.dataToPoint(value) : null;\n  };\n  Calendar.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.pointToData(pixel) : null;\n  };\n  Calendar.prototype.containPoint = function (point) {\n    console.warn('Not implemented.');\n    return false;\n  };\n  /**\r\n   * initRange\r\n   * Normalize to an [start, end] array\r\n   */\n  Calendar.prototype._initRangeOption = function () {\n    var range = this._model.get('range');\n    var normalizedRange;\n    // Convert [1990] to 1990\n    if (zrUtil.isArray(range) && range.length === 1) {\n      range = range[0];\n    }\n    if (!zrUtil.isArray(range)) {\n      var rangeStr = range.toString();\n      // One year.\n      if (/^\\d{4}$/.test(rangeStr)) {\n        normalizedRange = [rangeStr + '-01-01', rangeStr + '-12-31'];\n      }\n      // One month\n      if (/^\\d{4}[\\/|-]\\d{1,2}$/.test(rangeStr)) {\n        var start = this.getDateInfo(rangeStr);\n        var firstDay = start.date;\n        firstDay.setMonth(firstDay.getMonth() + 1);\n        var end = this.getNextNDay(firstDay, -1);\n        normalizedRange = [start.formatedDate, end.formatedDate];\n      }\n      // One day\n      if (/^\\d{4}[\\/|-]\\d{1,2}[\\/|-]\\d{1,2}$/.test(rangeStr)) {\n        normalizedRange = [rangeStr, rangeStr];\n      }\n    } else {\n      normalizedRange = range;\n    }\n    if (!normalizedRange) {\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.logError('Invalid date range.');\n      }\n      // Not handling it.\n      return range;\n    }\n    var tmp = this._getRangeInfo(normalizedRange);\n    if (tmp.start.time > tmp.end.time) {\n      normalizedRange.reverse();\n    }\n    return normalizedRange;\n  };\n  /**\r\n   * range info\r\n   *\r\n   * @private\r\n   * @param  {Array} range range ['2017-01-01', '2017-07-08']\r\n   *  If range[0] > range[1], they will not be reversed.\r\n   * @return {Object}       obj\r\n   */\n  Calendar.prototype._getRangeInfo = function (range) {\n    var parsedRange = [this.getDateInfo(range[0]), this.getDateInfo(range[1])];\n    var reversed;\n    if (parsedRange[0].time > parsedRange[1].time) {\n      reversed = true;\n      parsedRange.reverse();\n    }\n    var allDay = Math.floor(parsedRange[1].time / PROXIMATE_ONE_DAY) - Math.floor(parsedRange[0].time / PROXIMATE_ONE_DAY) + 1;\n    // Consider case1 (#11677 #10430):\n    // Set the system timezone as \"UK\", set the range to `['2016-07-01', '2016-12-31']`\n    // Consider case2:\n    // Firstly set system timezone as \"Time Zone: America/Toronto\",\n    // ```\n    // let first = new Date(1478412000000 - 3600 * 1000 * 2.5);\n    // let second = new Date(1478412000000);\n    // let allDays = Math.floor(second / ONE_DAY) - Math.floor(first / ONE_DAY) + 1;\n    // ```\n    // will get wrong result because of DST. So we should fix it.\n    var date = new Date(parsedRange[0].time);\n    var startDateNum = date.getDate();\n    var endDateNum = parsedRange[1].date.getDate();\n    date.setDate(startDateNum + allDay - 1);\n    // The bias can not over a month, so just compare date.\n    var dateNum = date.getDate();\n    if (dateNum !== endDateNum) {\n      var sign = date.getTime() - parsedRange[1].time > 0 ? 1 : -1;\n      while ((dateNum = date.getDate()) !== endDateNum && (date.getTime() - parsedRange[1].time) * sign > 0) {\n        allDay -= sign;\n        date.setDate(dateNum - sign);\n      }\n    }\n    var weeks = Math.floor((allDay + parsedRange[0].day + 6) / 7);\n    var nthWeek = reversed ? -weeks + 1 : weeks - 1;\n    reversed && parsedRange.reverse();\n    return {\n      range: [parsedRange[0].formatedDate, parsedRange[1].formatedDate],\n      start: parsedRange[0],\n      end: parsedRange[1],\n      allDay: allDay,\n      weeks: weeks,\n      // From 0.\n      nthWeek: nthWeek,\n      fweek: parsedRange[0].day,\n      lweek: parsedRange[1].day\n    };\n  };\n  /**\r\n   * get date by nthWeeks and week day in range\r\n   *\r\n   * @private\r\n   * @param  {number} nthWeek the week\r\n   * @param  {number} day   the week day\r\n   * @param  {Array} range [d1, d2]\r\n   * @return {Object}\r\n   */\n  Calendar.prototype._getDateByWeeksAndDay = function (nthWeek, day, range) {\n    var rangeInfo = this._getRangeInfo(range);\n    if (nthWeek > rangeInfo.weeks || nthWeek === 0 && day < rangeInfo.fweek || nthWeek === rangeInfo.weeks && day > rangeInfo.lweek) {\n      return null;\n    }\n    var nthDay = (nthWeek - 1) * 7 - rangeInfo.fweek + day;\n    var date = new Date(rangeInfo.start.time);\n    date.setDate(+rangeInfo.start.d + nthDay);\n    return this.getDateInfo(date);\n  };\n  Calendar.create = function (ecModel, api) {\n    var calendarList = [];\n    ecModel.eachComponent('calendar', function (calendarModel) {\n      var calendar = new Calendar(calendarModel, ecModel, api);\n      calendarList.push(calendar);\n      calendarModel.coordinateSystem = calendar;\n    });\n    ecModel.eachSeries(function (calendarSeries) {\n      if (calendarSeries.get('coordinateSystem') === 'calendar') {\n        // Inject coordinate system\n        calendarSeries.coordinateSystem = calendarList[calendarSeries.get('calendarIndex') || 0];\n      }\n    });\n    return calendarList;\n  };\n  Calendar.dimensions = ['time', 'value'];\n  return Calendar;\n}();\nfunction getCoordSys(finder) {\n  var calendarModel = finder.calendarModel;\n  var seriesModel = finder.seriesModel;\n  var coordSys = calendarModel ? calendarModel.coordinateSystem : seriesModel ? seriesModel.coordinateSystem : null;\n  return coordSys;\n}\nexport default Calendar;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD;AACA,IAAIC,iBAAiB,GAAG,QAAQ;AAChC,IAAIC,QAAQ,GAAG,aAAa,YAAY;EACtC,SAASA,QAAQA,CAACC,aAAa,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAC7C,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACC,UAAU,GAAGL,QAAQ,CAACK,UAAU;IACrC;IACA,IAAI,CAACC,iBAAiB,GAAGN,QAAQ,CAACM,iBAAiB;IACnD,IAAI,CAACC,MAAM,GAAGN,aAAa;EAC7B;EACAD,QAAQ,CAACM,iBAAiB,GAAG,YAAY;IACvC,OAAO,CAAC;MACNE,IAAI,EAAE,MAAM;MACZJ,IAAI,EAAE;IACR,CAAC,EAAE,OAAO,CAAC;EACb,CAAC;EACDJ,QAAQ,CAACS,SAAS,CAACC,YAAY,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACC,UAAU;EACxB,CAAC;EACDX,QAAQ,CAACS,SAAS,CAACG,QAAQ,GAAG,YAAY;IACxC,OAAO,IAAI,CAACL,MAAM;EACpB,CAAC;EACDP,QAAQ,CAACS,SAAS,CAACI,OAAO,GAAG,YAAY;IACvC,OAAO,IAAI,CAACC,KAAK;EACnB,CAAC;EACDd,QAAQ,CAACS,SAAS,CAACM,YAAY,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACC,GAAG;EACjB,CAAC;EACDhB,QAAQ,CAACS,SAAS,CAACQ,aAAa,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACC,GAAG;EACjB,CAAC;EACDlB,QAAQ,CAACS,SAAS,CAACU,SAAS,GAAG,YAAY;IACzC,OAAO,IAAI,CAACC,OAAO;EACrB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEpB,QAAQ,CAACS,SAAS,CAACY,iBAAiB,GAAG,YAAY;IACjD,OAAO,IAAI,CAACC,eAAe;EAC7B,CAAC;EACD;AACF;AACA;AACA;EACEtB,QAAQ,CAACS,SAAS,CAACc,WAAW,GAAG,UAAUC,IAAI,EAAE;IAC/CA,IAAI,GAAG1B,UAAU,CAAC2B,SAAS,CAACD,IAAI,CAAC;IACjC,IAAIE,CAAC,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC1B,IAAIC,CAAC,GAAGJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;IAC3B,IAAIC,IAAI,GAAGF,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,CAAC,GAAG,EAAE,GAAGA,CAAC;IACpC,IAAIG,CAAC,GAAGP,IAAI,CAACQ,OAAO,CAAC,CAAC;IACtB,IAAIC,IAAI,GAAGF,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,CAAC,GAAG,EAAE,GAAGA,CAAC;IACpC,IAAIG,GAAG,GAAGV,IAAI,CAACW,MAAM,CAAC,CAAC;IACvBD,GAAG,GAAGE,IAAI,CAACC,GAAG,CAAC,CAACH,GAAG,GAAG,CAAC,GAAG,IAAI,CAACb,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC;IACxD,OAAO;MACLK,CAAC,EAAEA,CAAC,GAAG,EAAE;MACTE,CAAC,EAAEE,IAAI;MACPC,CAAC,EAAEE,IAAI;MACPC,GAAG,EAAEA,GAAG;MACRI,IAAI,EAAEd,IAAI,CAACe,OAAO,CAAC,CAAC;MACpBC,YAAY,EAAEd,CAAC,GAAG,GAAG,GAAGI,IAAI,GAAG,GAAG,GAAGG,IAAI;MACzCT,IAAI,EAAEA;IACR,CAAC;EACH,CAAC;EACDxB,QAAQ,CAACS,SAAS,CAACgC,WAAW,GAAG,UAAUjB,IAAI,EAAEkB,CAAC,EAAE;IAClDA,CAAC,GAAGA,CAAC,IAAI,CAAC;IACV,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,IAAI,CAACnB,WAAW,CAACC,IAAI,CAAC;IAC/B;IACAA,IAAI,GAAG,IAAImB,IAAI,CAAC,IAAI,CAACpB,WAAW,CAACC,IAAI,CAAC,CAACc,IAAI,CAAC;IAC5Cd,IAAI,CAACoB,OAAO,CAACpB,IAAI,CAACQ,OAAO,CAAC,CAAC,GAAGU,CAAC,CAAC;IAChC,OAAO,IAAI,CAACnB,WAAW,CAACC,IAAI,CAAC;EAC/B,CAAC;EACDxB,QAAQ,CAACS,SAAS,CAACoC,MAAM,GAAG,UAAU3C,OAAO,EAAEC,GAAG,EAAE;IAClD,IAAI,CAACmB,eAAe,GAAG,CAAC,IAAI,CAACf,MAAM,CAACK,QAAQ,CAAC,UAAU,CAAC,CAACkC,GAAG,CAAC,UAAU,CAAC;IACxE,IAAI,CAAC1B,OAAO,GAAG,IAAI,CAACb,MAAM,CAACuC,GAAG,CAAC,QAAQ,CAAC;IACxC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACxC,MAAM,CAACK,QAAQ,CAAC,WAAW,CAAC,CAACoC,YAAY,CAAC,CAAC,CAACC,SAAS,IAAI,CAAC;IACjF,IAAI,CAACtC,UAAU,GAAG,IAAI,CAACuC,aAAa,CAAC,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;IAC7D,IAAIC,KAAK,GAAG,IAAI,CAACzC,UAAU,CAACyC,KAAK,IAAI,CAAC;IACtC,IAAIC,OAAO,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;IACjC,IAAIC,QAAQ,GAAG,IAAI,CAAC/C,MAAM,CAACgD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IAChD,IAAIC,YAAY,GAAG,IAAI,CAAClD,MAAM,CAACmD,kBAAkB,CAAC,CAAC;IACnD,IAAIC,WAAW,GAAG,IAAI,CAACvC,OAAO,KAAK,YAAY,GAAG,CAACgC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEA,KAAK,CAAC;IACzExD,MAAM,CAACgE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAUC,GAAG,EAAE;MACjC,IAAIC,iBAAiB,CAACR,QAAQ,EAAEO,GAAG,CAAC,EAAE;QACpCJ,YAAY,CAACJ,OAAO,CAACQ,GAAG,CAAC,CAAC,GAAGP,QAAQ,CAACO,GAAG,CAAC,GAAGF,WAAW,CAACE,GAAG,CAAC;MAC/D;IACF,CAAC,CAAC;IACF,IAAIE,QAAQ,GAAG;MACbC,KAAK,EAAE7D,GAAG,CAAC8D,QAAQ,CAAC,CAAC;MACrBC,MAAM,EAAE/D,GAAG,CAACgE,SAAS,CAAC;IACxB,CAAC;IACD,IAAIC,YAAY,GAAG,IAAI,CAACtD,KAAK,GAAGjB,MAAM,CAACwE,aAAa,CAACZ,YAAY,EAAEM,QAAQ,CAAC;IAC5EnE,MAAM,CAACgE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAUC,GAAG,EAAE;MACjC,IAAI,CAACC,iBAAiB,CAACR,QAAQ,EAAEO,GAAG,CAAC,EAAE;QACrCP,QAAQ,CAACO,GAAG,CAAC,GAAGO,YAAY,CAACf,OAAO,CAACQ,GAAG,CAAC,CAAC,GAAGF,WAAW,CAACE,GAAG,CAAC;MAC/D;IACF,CAAC,CAAC;IACF,SAASC,iBAAiBA,CAACR,QAAQ,EAAEO,GAAG,EAAE;MACxC,OAAOP,QAAQ,CAACO,GAAG,CAAC,IAAI,IAAI,IAAIP,QAAQ,CAACO,GAAG,CAAC,KAAK,MAAM;IAC1D;IACA;IACA,IAAI,CAAC7C,GAAG,GAAGsC,QAAQ,CAAC,CAAC,CAAC;IACtB,IAAI,CAACpC,GAAG,GAAGoC,QAAQ,CAAC,CAAC,CAAC;EACxB,CAAC;EACD;AACF;AACA;EACE;EACA;EACAtD,QAAQ,CAACS,SAAS,CAAC6D,WAAW,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACtD5E,MAAM,CAAC6E,OAAO,CAACF,IAAI,CAAC,KAAKA,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC;IACxCC,KAAK,IAAI,IAAI,KAAKA,KAAK,GAAG,IAAI,CAAC;IAC/B,IAAIE,OAAO,GAAG,IAAI,CAACnD,WAAW,CAACgD,IAAI,CAAC;IACpC,IAAII,KAAK,GAAG,IAAI,CAAChE,UAAU;IAC3B,IAAIa,IAAI,GAAGkD,OAAO,CAAClC,YAAY;IAC/B;IACA,IAAIgC,KAAK,IAAI,EAAEE,OAAO,CAACpC,IAAI,IAAIqC,KAAK,CAACC,KAAK,CAACtC,IAAI,IAAIoC,OAAO,CAACpC,IAAI,GAAGqC,KAAK,CAACE,GAAG,CAACvC,IAAI,GAAGvC,iBAAiB,CAAC,EAAE;MACrG,OAAO,CAAC+E,GAAG,EAAEA,GAAG,CAAC;IACnB;IACA,IAAIC,IAAI,GAAGL,OAAO,CAACxC,GAAG;IACtB,IAAI8C,OAAO,GAAG,IAAI,CAAC9B,aAAa,CAAC,CAACyB,KAAK,CAACC,KAAK,CAACtC,IAAI,EAAEd,IAAI,CAAC,CAAC,CAACwD,OAAO;IAClE,IAAI,IAAI,CAAC5D,OAAO,KAAK,UAAU,EAAE;MAC/B,OAAO,CAAC,IAAI,CAACN,KAAK,CAACmE,CAAC,GAAGF,IAAI,GAAG,IAAI,CAAC/D,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG,CAAC,EAAE,IAAI,CAACF,KAAK,CAACY,CAAC,GAAGsD,OAAO,GAAG,IAAI,CAAC9D,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG,CAAC,CAAC;IAC1G;IACA,OAAO,CAAC,IAAI,CAACJ,KAAK,CAACmE,CAAC,GAAGD,OAAO,GAAG,IAAI,CAAChE,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG,CAAC,EAAE,IAAI,CAACF,KAAK,CAACY,CAAC,GAAGqD,IAAI,GAAG,IAAI,CAAC7D,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG,CAAC,CAAC;EAC1G,CAAC;EACD;AACF;AACA;EACElB,QAAQ,CAACS,SAAS,CAACyE,WAAW,GAAG,UAAUC,KAAK,EAAE;IAChD,IAAI3D,IAAI,GAAG,IAAI,CAAC4D,WAAW,CAACD,KAAK,CAAC;IAClC,OAAO3D,IAAI,IAAIA,IAAI,CAACc,IAAI;EAC1B,CAAC;EACD;AACF;AACA;EACEtC,QAAQ,CAACS,SAAS,CAAC4E,UAAU,GAAG,UAAUd,IAAI,EAAEC,KAAK,EAAE;IACrD,IAAIW,KAAK,GAAG,IAAI,CAACb,WAAW,CAACC,IAAI,EAAEC,KAAK,CAAC;IACzC,OAAO;MACLc,YAAY,EAAE;QACZL,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAACnE,GAAG,GAAG,IAAI,CAAC+B,UAAU,IAAI,CAAC;QAC9CrB,CAAC,EAAEyD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAACjE,GAAG,GAAG,IAAI,CAAC6B,UAAU,IAAI,CAAC;QAC9CiB,KAAK,EAAE,IAAI,CAAChD,GAAG,GAAG,IAAI,CAAC+B,UAAU;QACjCmB,MAAM,EAAE,IAAI,CAAChD,GAAG,GAAG,IAAI,CAAC6B;MAC1B,CAAC;MACDwC,MAAM,EAAEJ,KAAK;MACbK,EAAE,EAAE,CAACL,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACnE,GAAG,GAAG,CAAC,EAAEmE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACjE,GAAG,GAAG,CAAC,CAAC;MACtDuE,EAAE,EAAE,CAACN,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACnE,GAAG,GAAG,CAAC,EAAEmE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACjE,GAAG,GAAG,CAAC,CAAC;MACtDwE,EAAE,EAAE,CAACP,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACnE,GAAG,GAAG,CAAC,EAAEmE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACjE,GAAG,GAAG,CAAC,CAAC;MACtDyE,EAAE,EAAE,CAACR,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACnE,GAAG,GAAG,CAAC,EAAEmE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACjE,GAAG,GAAG,CAAC;IACvD,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACElB,QAAQ,CAACS,SAAS,CAAC2E,WAAW,GAAG,UAAUD,KAAK,EAAE;IAChD,IAAIS,IAAI,GAAGxD,IAAI,CAACyD,KAAK,CAAC,CAACV,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACrE,KAAK,CAACmE,CAAC,IAAI,IAAI,CAACjE,GAAG,CAAC,GAAG,CAAC;IAC/D,IAAI8E,IAAI,GAAG1D,IAAI,CAACyD,KAAK,CAAC,CAACV,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACrE,KAAK,CAACY,CAAC,IAAI,IAAI,CAACR,GAAG,CAAC,GAAG,CAAC;IAC/D,IAAIyD,KAAK,GAAG,IAAI,CAAChE,UAAU,CAACgE,KAAK;IACjC,IAAI,IAAI,CAACvD,OAAO,KAAK,UAAU,EAAE;MAC/B,OAAO,IAAI,CAAC2E,qBAAqB,CAACD,IAAI,EAAEF,IAAI,GAAG,CAAC,EAAEjB,KAAK,CAAC;IAC1D;IACA,OAAO,IAAI,CAACoB,qBAAqB,CAACH,IAAI,EAAEE,IAAI,GAAG,CAAC,EAAEnB,KAAK,CAAC;EAC1D,CAAC;EACD3E,QAAQ,CAACS,SAAS,CAACuF,cAAc,GAAG,UAAU9F,OAAO,EAAE+F,MAAM,EAAEC,KAAK,EAAE;IACpE,IAAIC,QAAQ,GAAGC,WAAW,CAACH,MAAM,CAAC;IAClC,OAAOE,QAAQ,KAAK,IAAI,GAAGA,QAAQ,CAAC7B,WAAW,CAAC4B,KAAK,CAAC,GAAG,IAAI;EAC/D,CAAC;EACDlG,QAAQ,CAACS,SAAS,CAAC4F,gBAAgB,GAAG,UAAUnG,OAAO,EAAE+F,MAAM,EAAEK,KAAK,EAAE;IACtE,IAAIH,QAAQ,GAAGC,WAAW,CAACH,MAAM,CAAC;IAClC,OAAOE,QAAQ,KAAK,IAAI,GAAGA,QAAQ,CAACjB,WAAW,CAACoB,KAAK,CAAC,GAAG,IAAI;EAC/D,CAAC;EACDtG,QAAQ,CAACS,SAAS,CAAC8F,YAAY,GAAG,UAAUpB,KAAK,EAAE;IACjDqB,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IAChC,OAAO,KAAK;EACd,CAAC;EACD;AACF;AACA;AACA;EACEzG,QAAQ,CAACS,SAAS,CAAC0C,gBAAgB,GAAG,YAAY;IAChD,IAAIwB,KAAK,GAAG,IAAI,CAACpE,MAAM,CAACuC,GAAG,CAAC,OAAO,CAAC;IACpC,IAAI4D,eAAe;IACnB;IACA,IAAI9G,MAAM,CAAC6E,OAAO,CAACE,KAAK,CAAC,IAAIA,KAAK,CAACgC,MAAM,KAAK,CAAC,EAAE;MAC/ChC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;IAClB;IACA,IAAI,CAAC/E,MAAM,CAAC6E,OAAO,CAACE,KAAK,CAAC,EAAE;MAC1B,IAAIiC,QAAQ,GAAGjC,KAAK,CAACkC,QAAQ,CAAC,CAAC;MAC/B;MACA,IAAI,SAAS,CAACC,IAAI,CAACF,QAAQ,CAAC,EAAE;QAC5BF,eAAe,GAAG,CAACE,QAAQ,GAAG,QAAQ,EAAEA,QAAQ,GAAG,QAAQ,CAAC;MAC9D;MACA;MACA,IAAI,sBAAsB,CAACE,IAAI,CAACF,QAAQ,CAAC,EAAE;QACzC,IAAIhC,KAAK,GAAG,IAAI,CAACrD,WAAW,CAACqF,QAAQ,CAAC;QACtC,IAAIG,QAAQ,GAAGnC,KAAK,CAACpD,IAAI;QACzBuF,QAAQ,CAACC,QAAQ,CAACD,QAAQ,CAAClF,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAIgD,GAAG,GAAG,IAAI,CAACpC,WAAW,CAACsE,QAAQ,EAAE,CAAC,CAAC,CAAC;QACxCL,eAAe,GAAG,CAAC9B,KAAK,CAACpC,YAAY,EAAEqC,GAAG,CAACrC,YAAY,CAAC;MAC1D;MACA;MACA,IAAI,mCAAmC,CAACsE,IAAI,CAACF,QAAQ,CAAC,EAAE;QACtDF,eAAe,GAAG,CAACE,QAAQ,EAAEA,QAAQ,CAAC;MACxC;IACF,CAAC,MAAM;MACLF,eAAe,GAAG/B,KAAK;IACzB;IACA,IAAI,CAAC+B,eAAe,EAAE;MACpB,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCvH,MAAM,CAACwH,QAAQ,CAAC,qBAAqB,CAAC;MACxC;MACA;MACA,OAAOzC,KAAK;IACd;IACA,IAAI0C,GAAG,GAAG,IAAI,CAACnE,aAAa,CAACwD,eAAe,CAAC;IAC7C,IAAIW,GAAG,CAACzC,KAAK,CAACtC,IAAI,GAAG+E,GAAG,CAACxC,GAAG,CAACvC,IAAI,EAAE;MACjCoE,eAAe,CAACY,OAAO,CAAC,CAAC;IAC3B;IACA,OAAOZ,eAAe;EACxB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE1G,QAAQ,CAACS,SAAS,CAACyC,aAAa,GAAG,UAAUyB,KAAK,EAAE;IAClD,IAAI4C,WAAW,GAAG,CAAC,IAAI,CAAChG,WAAW,CAACoD,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACpD,WAAW,CAACoD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,IAAI6C,QAAQ;IACZ,IAAID,WAAW,CAAC,CAAC,CAAC,CAACjF,IAAI,GAAGiF,WAAW,CAAC,CAAC,CAAC,CAACjF,IAAI,EAAE;MAC7CkF,QAAQ,GAAG,IAAI;MACfD,WAAW,CAACD,OAAO,CAAC,CAAC;IACvB;IACA,IAAIG,MAAM,GAAGrF,IAAI,CAACyD,KAAK,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACjF,IAAI,GAAGvC,iBAAiB,CAAC,GAAGqC,IAAI,CAACyD,KAAK,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACjF,IAAI,GAAGvC,iBAAiB,CAAC,GAAG,CAAC;IAC1H;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIyB,IAAI,GAAG,IAAImB,IAAI,CAAC4E,WAAW,CAAC,CAAC,CAAC,CAACjF,IAAI,CAAC;IACxC,IAAIoF,YAAY,GAAGlG,IAAI,CAACQ,OAAO,CAAC,CAAC;IACjC,IAAI2F,UAAU,GAAGJ,WAAW,CAAC,CAAC,CAAC,CAAC/F,IAAI,CAACQ,OAAO,CAAC,CAAC;IAC9CR,IAAI,CAACoB,OAAO,CAAC8E,YAAY,GAAGD,MAAM,GAAG,CAAC,CAAC;IACvC;IACA,IAAIG,OAAO,GAAGpG,IAAI,CAACQ,OAAO,CAAC,CAAC;IAC5B,IAAI4F,OAAO,KAAKD,UAAU,EAAE;MAC1B,IAAIE,IAAI,GAAGrG,IAAI,CAACe,OAAO,CAAC,CAAC,GAAGgF,WAAW,CAAC,CAAC,CAAC,CAACjF,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5D,OAAO,CAACsF,OAAO,GAAGpG,IAAI,CAACQ,OAAO,CAAC,CAAC,MAAM2F,UAAU,IAAI,CAACnG,IAAI,CAACe,OAAO,CAAC,CAAC,GAAGgF,WAAW,CAAC,CAAC,CAAC,CAACjF,IAAI,IAAIuF,IAAI,GAAG,CAAC,EAAE;QACrGJ,MAAM,IAAII,IAAI;QACdrG,IAAI,CAACoB,OAAO,CAACgF,OAAO,GAAGC,IAAI,CAAC;MAC9B;IACF;IACA,IAAIzE,KAAK,GAAGhB,IAAI,CAACyD,KAAK,CAAC,CAAC4B,MAAM,GAAGF,WAAW,CAAC,CAAC,CAAC,CAACrF,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;IAC7D,IAAI8C,OAAO,GAAGwC,QAAQ,GAAG,CAACpE,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;IAC/CoE,QAAQ,IAAID,WAAW,CAACD,OAAO,CAAC,CAAC;IACjC,OAAO;MACL3C,KAAK,EAAE,CAAC4C,WAAW,CAAC,CAAC,CAAC,CAAC/E,YAAY,EAAE+E,WAAW,CAAC,CAAC,CAAC,CAAC/E,YAAY,CAAC;MACjEoC,KAAK,EAAE2C,WAAW,CAAC,CAAC,CAAC;MACrB1C,GAAG,EAAE0C,WAAW,CAAC,CAAC,CAAC;MACnBE,MAAM,EAAEA,MAAM;MACdrE,KAAK,EAAEA,KAAK;MACZ;MACA4B,OAAO,EAAEA,OAAO;MAChB8C,KAAK,EAAEP,WAAW,CAAC,CAAC,CAAC,CAACrF,GAAG;MACzB6F,KAAK,EAAER,WAAW,CAAC,CAAC,CAAC,CAACrF;IACxB,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElC,QAAQ,CAACS,SAAS,CAACsF,qBAAqB,GAAG,UAAUf,OAAO,EAAE9C,GAAG,EAAEyC,KAAK,EAAE;IACxE,IAAIqD,SAAS,GAAG,IAAI,CAAC9E,aAAa,CAACyB,KAAK,CAAC;IACzC,IAAIK,OAAO,GAAGgD,SAAS,CAAC5E,KAAK,IAAI4B,OAAO,KAAK,CAAC,IAAI9C,GAAG,GAAG8F,SAAS,CAACF,KAAK,IAAI9C,OAAO,KAAKgD,SAAS,CAAC5E,KAAK,IAAIlB,GAAG,GAAG8F,SAAS,CAACD,KAAK,EAAE;MAC/H,OAAO,IAAI;IACb;IACA,IAAIE,MAAM,GAAG,CAACjD,OAAO,GAAG,CAAC,IAAI,CAAC,GAAGgD,SAAS,CAACF,KAAK,GAAG5F,GAAG;IACtD,IAAIV,IAAI,GAAG,IAAImB,IAAI,CAACqF,SAAS,CAACpD,KAAK,CAACtC,IAAI,CAAC;IACzCd,IAAI,CAACoB,OAAO,CAAC,CAACoF,SAAS,CAACpD,KAAK,CAAC7C,CAAC,GAAGkG,MAAM,CAAC;IACzC,OAAO,IAAI,CAAC1G,WAAW,CAACC,IAAI,CAAC;EAC/B,CAAC;EACDxB,QAAQ,CAACkI,MAAM,GAAG,UAAUhI,OAAO,EAAEC,GAAG,EAAE;IACxC,IAAIgI,YAAY,GAAG,EAAE;IACrBjI,OAAO,CAACkI,aAAa,CAAC,UAAU,EAAE,UAAUnI,aAAa,EAAE;MACzD,IAAIoI,QAAQ,GAAG,IAAIrI,QAAQ,CAACC,aAAa,EAAEC,OAAO,EAAEC,GAAG,CAAC;MACxDgI,YAAY,CAACG,IAAI,CAACD,QAAQ,CAAC;MAC3BpI,aAAa,CAACsI,gBAAgB,GAAGF,QAAQ;IAC3C,CAAC,CAAC;IACFnI,OAAO,CAACsI,UAAU,CAAC,UAAUC,cAAc,EAAE;MAC3C,IAAIA,cAAc,CAAC3F,GAAG,CAAC,kBAAkB,CAAC,KAAK,UAAU,EAAE;QACzD;QACA2F,cAAc,CAACF,gBAAgB,GAAGJ,YAAY,CAACM,cAAc,CAAC3F,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;MAC1F;IACF,CAAC,CAAC;IACF,OAAOqF,YAAY;EACrB,CAAC;EACDnI,QAAQ,CAACK,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;EACvC,OAAOL,QAAQ;AACjB,CAAC,CAAC,CAAC;AACH,SAASoG,WAAWA,CAACH,MAAM,EAAE;EAC3B,IAAIhG,aAAa,GAAGgG,MAAM,CAAChG,aAAa;EACxC,IAAIyI,WAAW,GAAGzC,MAAM,CAACyC,WAAW;EACpC,IAAIvC,QAAQ,GAAGlG,aAAa,GAAGA,aAAa,CAACsI,gBAAgB,GAAGG,WAAW,GAAGA,WAAW,CAACH,gBAAgB,GAAG,IAAI;EACjH,OAAOpC,QAAQ;AACjB;AACA,eAAenG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}