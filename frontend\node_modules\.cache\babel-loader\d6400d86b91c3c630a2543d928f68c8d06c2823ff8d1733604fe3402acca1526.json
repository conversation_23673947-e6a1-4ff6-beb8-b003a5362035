{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// FIXME emphasis label position is not same with normal label position\nimport { parsePercent } from '../../util/number.js';\nimport { Point } from '../../util/graphic.js';\nimport { each, isNumber } from 'zrender/lib/core/util.js';\nimport { limitTurnAngle, limitSurfaceAngle } from '../../label/labelGuideHelper.js';\nimport { shiftLayoutOnY } from '../../label/labelLayoutHelper.js';\nvar RADIAN = Math.PI / 180;\nfunction adjustSingleSide(list, cx, cy, r, dir, viewWidth, viewHeight, viewLeft, viewTop, farthestX) {\n  if (list.length < 2) {\n    return;\n  }\n  ;\n  function recalculateXOnSemiToAlignOnEllipseCurve(semi) {\n    var rB = semi.rB;\n    var rB2 = rB * rB;\n    for (var i = 0; i < semi.list.length; i++) {\n      var item = semi.list[i];\n      var dy = Math.abs(item.label.y - cy);\n      // horizontal r is always same with original r because x is not changed.\n      var rA = r + item.len;\n      var rA2 = rA * rA;\n      // Use ellipse implicit function to calculate x\n      var dx = Math.sqrt(Math.abs((1 - dy * dy / rB2) * rA2));\n      var newX = cx + (dx + item.len2) * dir;\n      var deltaX = newX - item.label.x;\n      var newTargetWidth = item.targetTextWidth - deltaX * dir;\n      // text x is changed, so need to recalculate width.\n      constrainTextWidth(item, newTargetWidth, true);\n      item.label.x = newX;\n    }\n  }\n  // Adjust X based on the shifted y. Make tight labels aligned on an ellipse curve.\n  function recalculateX(items) {\n    // Extremes of\n    var topSemi = {\n      list: [],\n      maxY: 0\n    };\n    var bottomSemi = {\n      list: [],\n      maxY: 0\n    };\n    for (var i = 0; i < items.length; i++) {\n      if (items[i].labelAlignTo !== 'none') {\n        continue;\n      }\n      var item = items[i];\n      var semi = item.label.y > cy ? bottomSemi : topSemi;\n      var dy = Math.abs(item.label.y - cy);\n      if (dy >= semi.maxY) {\n        var dx = item.label.x - cx - item.len2 * dir;\n        // horizontal r is always same with original r because x is not changed.\n        var rA = r + item.len;\n        // Canculate rB based on the topest / bottemest label.\n        var rB = Math.abs(dx) < rA ? Math.sqrt(dy * dy / (1 - dx * dx / rA / rA)) : rA;\n        semi.rB = rB;\n        semi.maxY = dy;\n      }\n      semi.list.push(item);\n    }\n    recalculateXOnSemiToAlignOnEllipseCurve(topSemi);\n    recalculateXOnSemiToAlignOnEllipseCurve(bottomSemi);\n  }\n  var len = list.length;\n  for (var i = 0; i < len; i++) {\n    if (list[i].position === 'outer' && list[i].labelAlignTo === 'labelLine') {\n      var dx = list[i].label.x - farthestX;\n      list[i].linePoints[1][0] += dx;\n      list[i].label.x = farthestX;\n    }\n  }\n  if (shiftLayoutOnY(list, viewTop, viewTop + viewHeight)) {\n    recalculateX(list);\n  }\n}\nfunction avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop) {\n  var leftList = [];\n  var rightList = [];\n  var leftmostX = Number.MAX_VALUE;\n  var rightmostX = -Number.MAX_VALUE;\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var label = labelLayoutList[i].label;\n    if (isPositionCenter(labelLayoutList[i])) {\n      continue;\n    }\n    if (label.x < cx) {\n      leftmostX = Math.min(leftmostX, label.x);\n      leftList.push(labelLayoutList[i]);\n    } else {\n      rightmostX = Math.max(rightmostX, label.x);\n      rightList.push(labelLayoutList[i]);\n    }\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      if (layout.labelStyleWidth != null) {\n        continue;\n      }\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var targetTextWidth = void 0;\n      if (layout.labelAlignTo === 'edge') {\n        if (label.x < cx) {\n          targetTextWidth = linePoints[2][0] - layout.labelDistance - viewLeft - layout.edgeDistance;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - layout.edgeDistance - linePoints[2][0] - layout.labelDistance;\n        }\n      } else if (layout.labelAlignTo === 'labelLine') {\n        if (label.x < cx) {\n          targetTextWidth = leftmostX - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - rightmostX - layout.bleedMargin;\n        }\n      } else {\n        if (label.x < cx) {\n          targetTextWidth = label.x - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - label.x - layout.bleedMargin;\n        }\n      }\n      layout.targetTextWidth = targetTextWidth;\n      constrainTextWidth(layout, targetTextWidth);\n    }\n  }\n  adjustSingleSide(rightList, cx, cy, r, 1, viewWidth, viewHeight, viewLeft, viewTop, rightmostX);\n  adjustSingleSide(leftList, cx, cy, r, -1, viewWidth, viewHeight, viewLeft, viewTop, leftmostX);\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var isAlignToEdge = layout.labelAlignTo === 'edge';\n      var padding = label.style.padding;\n      var paddingH = padding ? padding[1] + padding[3] : 0;\n      // textRect.width already contains paddingH if bgColor is set\n      var extraPaddingH = label.style.backgroundColor ? 0 : paddingH;\n      var realTextWidth = layout.rect.width + extraPaddingH;\n      var dist = linePoints[1][0] - linePoints[2][0];\n      if (isAlignToEdge) {\n        if (label.x < cx) {\n          linePoints[2][0] = viewLeft + layout.edgeDistance + realTextWidth + layout.labelDistance;\n        } else {\n          linePoints[2][0] = viewLeft + viewWidth - layout.edgeDistance - realTextWidth - layout.labelDistance;\n        }\n      } else {\n        if (label.x < cx) {\n          linePoints[2][0] = label.x + layout.labelDistance;\n        } else {\n          linePoints[2][0] = label.x - layout.labelDistance;\n        }\n        linePoints[1][0] = linePoints[2][0] + dist;\n      }\n      linePoints[1][1] = linePoints[2][1] = label.y;\n    }\n  }\n}\n/**\r\n * Set max width of each label, and then wrap each label to the max width.\r\n *\r\n * @param layout label layout\r\n * @param availableWidth max width for the label to display\r\n * @param forceRecalculate recaculate the text layout even if the current width\r\n * is smaller than `availableWidth`. This is useful when the text was previously\r\n * wrapped by calling `constrainTextWidth` but now `availableWidth` changed, in\r\n * which case, previous wrapping should be redo.\r\n */\nfunction constrainTextWidth(layout, availableWidth, forceRecalculate) {\n  if (forceRecalculate === void 0) {\n    forceRecalculate = false;\n  }\n  if (layout.labelStyleWidth != null) {\n    // User-defined style.width has the highest priority.\n    return;\n  }\n  var label = layout.label;\n  var style = label.style;\n  var textRect = layout.rect;\n  var bgColor = style.backgroundColor;\n  var padding = style.padding;\n  var paddingH = padding ? padding[1] + padding[3] : 0;\n  var overflow = style.overflow;\n  // textRect.width already contains paddingH if bgColor is set\n  var oldOuterWidth = textRect.width + (bgColor ? 0 : paddingH);\n  if (availableWidth < oldOuterWidth || forceRecalculate) {\n    var oldHeight = textRect.height;\n    if (overflow && overflow.match('break')) {\n      // Temporarily set background to be null to calculate\n      // the bounding box without background.\n      label.setStyle('backgroundColor', null);\n      // Set constraining width\n      label.setStyle('width', availableWidth - paddingH);\n      // This is the real bounding box of the text without padding.\n      var innerRect = label.getBoundingRect();\n      label.setStyle('width', Math.ceil(innerRect.width));\n      label.setStyle('backgroundColor', bgColor);\n    } else {\n      var availableInnerWidth = availableWidth - paddingH;\n      var newWidth = availableWidth < oldOuterWidth\n      // Current text is too wide, use `availableWidth` as max width.\n      ? availableInnerWidth :\n      // Current available width is enough, but the text may have\n      // already been wrapped with a smaller available width.\n      forceRecalculate ? availableInnerWidth > layout.unconstrainedWidth\n      // Current available is larger than text width,\n      // so don't constrain width (otherwise it may have\n      // empty space in the background).\n      ? null\n      // Current available is smaller than text width, so\n      // use the current available width as constraining\n      // width.\n      : availableInnerWidth\n      // Current available width is enough, so no need to\n      // constrain.\n      : null;\n      label.setStyle('width', newWidth);\n    }\n    var newRect = label.getBoundingRect();\n    textRect.width = newRect.width;\n    var margin = (label.style.margin || 0) + 2.1;\n    textRect.height = newRect.height + margin;\n    textRect.y -= (textRect.height - oldHeight) / 2;\n  }\n}\nfunction isPositionCenter(sectorShape) {\n  // Not change x for center label\n  return sectorShape.position === 'center';\n}\nexport default function pieLabelLayout(seriesModel) {\n  var data = seriesModel.getData();\n  var labelLayoutList = [];\n  var cx;\n  var cy;\n  var hasLabelRotate = false;\n  var minShowLabelRadian = (seriesModel.get('minShowLabelAngle') || 0) * RADIAN;\n  var viewRect = data.getLayout('viewRect');\n  var r = data.getLayout('r');\n  var viewWidth = viewRect.width;\n  var viewLeft = viewRect.x;\n  var viewTop = viewRect.y;\n  var viewHeight = viewRect.height;\n  function setNotShow(el) {\n    el.ignore = true;\n  }\n  function isLabelShown(label) {\n    if (!label.ignore) {\n      return true;\n    }\n    for (var key in label.states) {\n      if (label.states[key].ignore === false) {\n        return true;\n      }\n    }\n    return false;\n  }\n  data.each(function (idx) {\n    var sector = data.getItemGraphicEl(idx);\n    var sectorShape = sector.shape;\n    var label = sector.getTextContent();\n    var labelLine = sector.getTextGuideLine();\n    var itemModel = data.getItemModel(idx);\n    var labelModel = itemModel.getModel('label');\n    // Use position in normal or emphasis\n    var labelPosition = labelModel.get('position') || itemModel.get(['emphasis', 'label', 'position']);\n    var labelDistance = labelModel.get('distanceToLabelLine');\n    var labelAlignTo = labelModel.get('alignTo');\n    var edgeDistance = parsePercent(labelModel.get('edgeDistance'), viewWidth);\n    var bleedMargin = labelModel.get('bleedMargin');\n    var labelLineModel = itemModel.getModel('labelLine');\n    var labelLineLen = labelLineModel.get('length');\n    labelLineLen = parsePercent(labelLineLen, viewWidth);\n    var labelLineLen2 = labelLineModel.get('length2');\n    labelLineLen2 = parsePercent(labelLineLen2, viewWidth);\n    if (Math.abs(sectorShape.endAngle - sectorShape.startAngle) < minShowLabelRadian) {\n      each(label.states, setNotShow);\n      label.ignore = true;\n      if (labelLine) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      }\n      return;\n    }\n    if (!isLabelShown(label)) {\n      return;\n    }\n    var midAngle = (sectorShape.startAngle + sectorShape.endAngle) / 2;\n    var nx = Math.cos(midAngle);\n    var ny = Math.sin(midAngle);\n    var textX;\n    var textY;\n    var linePoints;\n    var textAlign;\n    cx = sectorShape.cx;\n    cy = sectorShape.cy;\n    var isLabelInside = labelPosition === 'inside' || labelPosition === 'inner';\n    if (labelPosition === 'center') {\n      textX = sectorShape.cx;\n      textY = sectorShape.cy;\n      textAlign = 'center';\n    } else {\n      var x1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * nx : sectorShape.r * nx) + cx;\n      var y1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * ny : sectorShape.r * ny) + cy;\n      textX = x1 + nx * 3;\n      textY = y1 + ny * 3;\n      if (!isLabelInside) {\n        // For roseType\n        var x2 = x1 + nx * (labelLineLen + r - sectorShape.r);\n        var y2 = y1 + ny * (labelLineLen + r - sectorShape.r);\n        var x3 = x2 + (nx < 0 ? -1 : 1) * labelLineLen2;\n        var y3 = y2;\n        if (labelAlignTo === 'edge') {\n          // Adjust textX because text align of edge is opposite\n          textX = nx < 0 ? viewLeft + edgeDistance : viewLeft + viewWidth - edgeDistance;\n        } else {\n          textX = x3 + (nx < 0 ? -labelDistance : labelDistance);\n        }\n        textY = y3;\n        linePoints = [[x1, y1], [x2, y2], [x3, y3]];\n      }\n      textAlign = isLabelInside ? 'center' : labelAlignTo === 'edge' ? nx > 0 ? 'right' : 'left' : nx > 0 ? 'left' : 'right';\n    }\n    var PI = Math.PI;\n    var labelRotate = 0;\n    var rotate = labelModel.get('rotate');\n    if (isNumber(rotate)) {\n      labelRotate = rotate * (PI / 180);\n    } else if (labelPosition === 'center') {\n      labelRotate = 0;\n    } else if (rotate === 'radial' || rotate === true) {\n      var radialAngle = nx < 0 ? -midAngle + PI : -midAngle;\n      labelRotate = radialAngle;\n    } else if (rotate === 'tangential' && labelPosition !== 'outside' && labelPosition !== 'outer') {\n      var rad = Math.atan2(nx, ny);\n      if (rad < 0) {\n        rad = PI * 2 + rad;\n      }\n      var isDown = ny > 0;\n      if (isDown) {\n        rad = PI + rad;\n      }\n      labelRotate = rad - PI;\n    }\n    hasLabelRotate = !!labelRotate;\n    label.x = textX;\n    label.y = textY;\n    label.rotation = labelRotate;\n    label.setStyle({\n      verticalAlign: 'middle'\n    });\n    // Not sectorShape the inside label\n    if (!isLabelInside) {\n      var textRect = label.getBoundingRect().clone();\n      textRect.applyTransform(label.getComputedTransform());\n      // Text has a default 1px stroke. Exclude this.\n      var margin = (label.style.margin || 0) + 2.1;\n      textRect.y -= margin / 2;\n      textRect.height += margin;\n      labelLayoutList.push({\n        label: label,\n        labelLine: labelLine,\n        position: labelPosition,\n        len: labelLineLen,\n        len2: labelLineLen2,\n        minTurnAngle: labelLineModel.get('minTurnAngle'),\n        maxSurfaceAngle: labelLineModel.get('maxSurfaceAngle'),\n        surfaceNormal: new Point(nx, ny),\n        linePoints: linePoints,\n        textAlign: textAlign,\n        labelDistance: labelDistance,\n        labelAlignTo: labelAlignTo,\n        edgeDistance: edgeDistance,\n        bleedMargin: bleedMargin,\n        rect: textRect,\n        unconstrainedWidth: textRect.width,\n        labelStyleWidth: label.style.width\n      });\n    } else {\n      label.setStyle({\n        align: textAlign\n      });\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    sector.setTextConfig({\n      inside: isLabelInside\n    });\n  });\n  if (!hasLabelRotate && seriesModel.get('avoidLabelOverlap')) {\n    avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop);\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    var label = layout.label;\n    var labelLine = layout.labelLine;\n    var notShowLabel = isNaN(label.x) || isNaN(label.y);\n    if (label) {\n      label.setStyle({\n        align: layout.textAlign\n      });\n      if (notShowLabel) {\n        each(label.states, setNotShow);\n        label.ignore = true;\n      }\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    if (labelLine) {\n      var linePoints = layout.linePoints;\n      if (notShowLabel || !linePoints) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      } else {\n        limitTurnAngle(linePoints, layout.minTurnAngle);\n        limitSurfaceAngle(linePoints, layout.surfaceNormal, layout.maxSurfaceAngle);\n        labelLine.setShape({\n          points: linePoints\n        });\n        // Set the anchor to the midpoint of sector\n        label.__hostTarget.textGuideLineConfig = {\n          anchor: new Point(linePoints[0][0], linePoints[0][1])\n        };\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["parsePercent", "Point", "each", "isNumber", "limitTurnAngle", "limitSurfaceAngle", "shiftLayoutOnY", "RADIAN", "Math", "PI", "adjustSingleSide", "list", "cx", "cy", "r", "dir", "viewWidth", "viewHeight", "viewLeft", "viewTop", "farthestX", "length", "recalculateXOnSemiToAlignOnEllipseCurve", "semi", "rB", "rB2", "i", "item", "dy", "abs", "label", "y", "rA", "len", "rA2", "dx", "sqrt", "newX", "len2", "deltaX", "x", "newTargetWidth", "targetTextWidth", "constrainTextWidth", "recalculateX", "items", "topSemi", "maxY", "bottomSemi", "labelAlignTo", "push", "position", "linePoints", "avoidOverlap", "labelLayoutList", "leftList", "rightList", "leftmostX", "Number", "MAX_VALUE", "rightmostX", "isPositionCenter", "min", "max", "layout", "labelStyleWidth", "labelDistance", "edgeDistance", "<PERSON><PERSON><PERSON><PERSON>", "isAlignToEdge", "padding", "style", "paddingH", "extraPaddingH", "backgroundColor", "realTextWidth", "rect", "width", "dist", "availableWidth", "forceRecalculate", "textRect", "bgColor", "overflow", "oldOuterWidth", "oldHeight", "height", "match", "setStyle", "innerRect", "getBoundingRect", "ceil", "availableInnerWidth", "newWidth", "unconstrainedWidth", "newRect", "margin", "sectorShape", "pieLabelLayout", "seriesModel", "data", "getData", "hasLabelRotate", "minShowLabelRadian", "get", "viewRect", "getLayout", "setNotShow", "el", "ignore", "isLabelShown", "key", "states", "idx", "sector", "getItemGraphicEl", "shape", "getTextContent", "labelLine", "getTextGuideLine", "itemModel", "getItemModel", "labelModel", "getModel", "labelPosition", "labelLineModel", "labelLineLen", "labelLineLen2", "endAngle", "startAngle", "midAngle", "nx", "cos", "ny", "sin", "textX", "textY", "textAlign", "isLabelInside", "x1", "r0", "y1", "x2", "y2", "x3", "y3", "labelRotate", "rotate", "radialAngle", "rad", "atan2", "isDown", "rotation", "verticalAlign", "clone", "applyTransform", "getComputedTransform", "minTurnAngle", "maxSurfaceAngle", "surfaceNormal", "align", "selectState", "select", "setTextConfig", "inside", "notShowLabel", "isNaN", "setShape", "points", "__host<PERSON><PERSON>get", "textGuideLineConfig", "anchor"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/chart/pie/labelLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// FIXME emphasis label position is not same with normal label position\nimport { parsePercent } from '../../util/number.js';\nimport { Point } from '../../util/graphic.js';\nimport { each, isNumber } from 'zrender/lib/core/util.js';\nimport { limitTurnAngle, limitSurfaceAngle } from '../../label/labelGuideHelper.js';\nimport { shiftLayoutOnY } from '../../label/labelLayoutHelper.js';\nvar RADIAN = Math.PI / 180;\nfunction adjustSingleSide(list, cx, cy, r, dir, viewWidth, viewHeight, viewLeft, viewTop, farthestX) {\n  if (list.length < 2) {\n    return;\n  }\n  ;\n  function recalculateXOnSemiToAlignOnEllipseCurve(semi) {\n    var rB = semi.rB;\n    var rB2 = rB * rB;\n    for (var i = 0; i < semi.list.length; i++) {\n      var item = semi.list[i];\n      var dy = Math.abs(item.label.y - cy);\n      // horizontal r is always same with original r because x is not changed.\n      var rA = r + item.len;\n      var rA2 = rA * rA;\n      // Use ellipse implicit function to calculate x\n      var dx = Math.sqrt(Math.abs((1 - dy * dy / rB2) * rA2));\n      var newX = cx + (dx + item.len2) * dir;\n      var deltaX = newX - item.label.x;\n      var newTargetWidth = item.targetTextWidth - deltaX * dir;\n      // text x is changed, so need to recalculate width.\n      constrainTextWidth(item, newTargetWidth, true);\n      item.label.x = newX;\n    }\n  }\n  // Adjust X based on the shifted y. Make tight labels aligned on an ellipse curve.\n  function recalculateX(items) {\n    // Extremes of\n    var topSemi = {\n      list: [],\n      maxY: 0\n    };\n    var bottomSemi = {\n      list: [],\n      maxY: 0\n    };\n    for (var i = 0; i < items.length; i++) {\n      if (items[i].labelAlignTo !== 'none') {\n        continue;\n      }\n      var item = items[i];\n      var semi = item.label.y > cy ? bottomSemi : topSemi;\n      var dy = Math.abs(item.label.y - cy);\n      if (dy >= semi.maxY) {\n        var dx = item.label.x - cx - item.len2 * dir;\n        // horizontal r is always same with original r because x is not changed.\n        var rA = r + item.len;\n        // Canculate rB based on the topest / bottemest label.\n        var rB = Math.abs(dx) < rA ? Math.sqrt(dy * dy / (1 - dx * dx / rA / rA)) : rA;\n        semi.rB = rB;\n        semi.maxY = dy;\n      }\n      semi.list.push(item);\n    }\n    recalculateXOnSemiToAlignOnEllipseCurve(topSemi);\n    recalculateXOnSemiToAlignOnEllipseCurve(bottomSemi);\n  }\n  var len = list.length;\n  for (var i = 0; i < len; i++) {\n    if (list[i].position === 'outer' && list[i].labelAlignTo === 'labelLine') {\n      var dx = list[i].label.x - farthestX;\n      list[i].linePoints[1][0] += dx;\n      list[i].label.x = farthestX;\n    }\n  }\n  if (shiftLayoutOnY(list, viewTop, viewTop + viewHeight)) {\n    recalculateX(list);\n  }\n}\nfunction avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop) {\n  var leftList = [];\n  var rightList = [];\n  var leftmostX = Number.MAX_VALUE;\n  var rightmostX = -Number.MAX_VALUE;\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var label = labelLayoutList[i].label;\n    if (isPositionCenter(labelLayoutList[i])) {\n      continue;\n    }\n    if (label.x < cx) {\n      leftmostX = Math.min(leftmostX, label.x);\n      leftList.push(labelLayoutList[i]);\n    } else {\n      rightmostX = Math.max(rightmostX, label.x);\n      rightList.push(labelLayoutList[i]);\n    }\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      if (layout.labelStyleWidth != null) {\n        continue;\n      }\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var targetTextWidth = void 0;\n      if (layout.labelAlignTo === 'edge') {\n        if (label.x < cx) {\n          targetTextWidth = linePoints[2][0] - layout.labelDistance - viewLeft - layout.edgeDistance;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - layout.edgeDistance - linePoints[2][0] - layout.labelDistance;\n        }\n      } else if (layout.labelAlignTo === 'labelLine') {\n        if (label.x < cx) {\n          targetTextWidth = leftmostX - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - rightmostX - layout.bleedMargin;\n        }\n      } else {\n        if (label.x < cx) {\n          targetTextWidth = label.x - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - label.x - layout.bleedMargin;\n        }\n      }\n      layout.targetTextWidth = targetTextWidth;\n      constrainTextWidth(layout, targetTextWidth);\n    }\n  }\n  adjustSingleSide(rightList, cx, cy, r, 1, viewWidth, viewHeight, viewLeft, viewTop, rightmostX);\n  adjustSingleSide(leftList, cx, cy, r, -1, viewWidth, viewHeight, viewLeft, viewTop, leftmostX);\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var isAlignToEdge = layout.labelAlignTo === 'edge';\n      var padding = label.style.padding;\n      var paddingH = padding ? padding[1] + padding[3] : 0;\n      // textRect.width already contains paddingH if bgColor is set\n      var extraPaddingH = label.style.backgroundColor ? 0 : paddingH;\n      var realTextWidth = layout.rect.width + extraPaddingH;\n      var dist = linePoints[1][0] - linePoints[2][0];\n      if (isAlignToEdge) {\n        if (label.x < cx) {\n          linePoints[2][0] = viewLeft + layout.edgeDistance + realTextWidth + layout.labelDistance;\n        } else {\n          linePoints[2][0] = viewLeft + viewWidth - layout.edgeDistance - realTextWidth - layout.labelDistance;\n        }\n      } else {\n        if (label.x < cx) {\n          linePoints[2][0] = label.x + layout.labelDistance;\n        } else {\n          linePoints[2][0] = label.x - layout.labelDistance;\n        }\n        linePoints[1][0] = linePoints[2][0] + dist;\n      }\n      linePoints[1][1] = linePoints[2][1] = label.y;\n    }\n  }\n}\n/**\r\n * Set max width of each label, and then wrap each label to the max width.\r\n *\r\n * @param layout label layout\r\n * @param availableWidth max width for the label to display\r\n * @param forceRecalculate recaculate the text layout even if the current width\r\n * is smaller than `availableWidth`. This is useful when the text was previously\r\n * wrapped by calling `constrainTextWidth` but now `availableWidth` changed, in\r\n * which case, previous wrapping should be redo.\r\n */\nfunction constrainTextWidth(layout, availableWidth, forceRecalculate) {\n  if (forceRecalculate === void 0) {\n    forceRecalculate = false;\n  }\n  if (layout.labelStyleWidth != null) {\n    // User-defined style.width has the highest priority.\n    return;\n  }\n  var label = layout.label;\n  var style = label.style;\n  var textRect = layout.rect;\n  var bgColor = style.backgroundColor;\n  var padding = style.padding;\n  var paddingH = padding ? padding[1] + padding[3] : 0;\n  var overflow = style.overflow;\n  // textRect.width already contains paddingH if bgColor is set\n  var oldOuterWidth = textRect.width + (bgColor ? 0 : paddingH);\n  if (availableWidth < oldOuterWidth || forceRecalculate) {\n    var oldHeight = textRect.height;\n    if (overflow && overflow.match('break')) {\n      // Temporarily set background to be null to calculate\n      // the bounding box without background.\n      label.setStyle('backgroundColor', null);\n      // Set constraining width\n      label.setStyle('width', availableWidth - paddingH);\n      // This is the real bounding box of the text without padding.\n      var innerRect = label.getBoundingRect();\n      label.setStyle('width', Math.ceil(innerRect.width));\n      label.setStyle('backgroundColor', bgColor);\n    } else {\n      var availableInnerWidth = availableWidth - paddingH;\n      var newWidth = availableWidth < oldOuterWidth\n      // Current text is too wide, use `availableWidth` as max width.\n      ? availableInnerWidth :\n      // Current available width is enough, but the text may have\n      // already been wrapped with a smaller available width.\n      forceRecalculate ? availableInnerWidth > layout.unconstrainedWidth\n      // Current available is larger than text width,\n      // so don't constrain width (otherwise it may have\n      // empty space in the background).\n      ? null\n      // Current available is smaller than text width, so\n      // use the current available width as constraining\n      // width.\n      : availableInnerWidth\n      // Current available width is enough, so no need to\n      // constrain.\n      : null;\n      label.setStyle('width', newWidth);\n    }\n    var newRect = label.getBoundingRect();\n    textRect.width = newRect.width;\n    var margin = (label.style.margin || 0) + 2.1;\n    textRect.height = newRect.height + margin;\n    textRect.y -= (textRect.height - oldHeight) / 2;\n  }\n}\nfunction isPositionCenter(sectorShape) {\n  // Not change x for center label\n  return sectorShape.position === 'center';\n}\nexport default function pieLabelLayout(seriesModel) {\n  var data = seriesModel.getData();\n  var labelLayoutList = [];\n  var cx;\n  var cy;\n  var hasLabelRotate = false;\n  var minShowLabelRadian = (seriesModel.get('minShowLabelAngle') || 0) * RADIAN;\n  var viewRect = data.getLayout('viewRect');\n  var r = data.getLayout('r');\n  var viewWidth = viewRect.width;\n  var viewLeft = viewRect.x;\n  var viewTop = viewRect.y;\n  var viewHeight = viewRect.height;\n  function setNotShow(el) {\n    el.ignore = true;\n  }\n  function isLabelShown(label) {\n    if (!label.ignore) {\n      return true;\n    }\n    for (var key in label.states) {\n      if (label.states[key].ignore === false) {\n        return true;\n      }\n    }\n    return false;\n  }\n  data.each(function (idx) {\n    var sector = data.getItemGraphicEl(idx);\n    var sectorShape = sector.shape;\n    var label = sector.getTextContent();\n    var labelLine = sector.getTextGuideLine();\n    var itemModel = data.getItemModel(idx);\n    var labelModel = itemModel.getModel('label');\n    // Use position in normal or emphasis\n    var labelPosition = labelModel.get('position') || itemModel.get(['emphasis', 'label', 'position']);\n    var labelDistance = labelModel.get('distanceToLabelLine');\n    var labelAlignTo = labelModel.get('alignTo');\n    var edgeDistance = parsePercent(labelModel.get('edgeDistance'), viewWidth);\n    var bleedMargin = labelModel.get('bleedMargin');\n    var labelLineModel = itemModel.getModel('labelLine');\n    var labelLineLen = labelLineModel.get('length');\n    labelLineLen = parsePercent(labelLineLen, viewWidth);\n    var labelLineLen2 = labelLineModel.get('length2');\n    labelLineLen2 = parsePercent(labelLineLen2, viewWidth);\n    if (Math.abs(sectorShape.endAngle - sectorShape.startAngle) < minShowLabelRadian) {\n      each(label.states, setNotShow);\n      label.ignore = true;\n      if (labelLine) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      }\n      return;\n    }\n    if (!isLabelShown(label)) {\n      return;\n    }\n    var midAngle = (sectorShape.startAngle + sectorShape.endAngle) / 2;\n    var nx = Math.cos(midAngle);\n    var ny = Math.sin(midAngle);\n    var textX;\n    var textY;\n    var linePoints;\n    var textAlign;\n    cx = sectorShape.cx;\n    cy = sectorShape.cy;\n    var isLabelInside = labelPosition === 'inside' || labelPosition === 'inner';\n    if (labelPosition === 'center') {\n      textX = sectorShape.cx;\n      textY = sectorShape.cy;\n      textAlign = 'center';\n    } else {\n      var x1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * nx : sectorShape.r * nx) + cx;\n      var y1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * ny : sectorShape.r * ny) + cy;\n      textX = x1 + nx * 3;\n      textY = y1 + ny * 3;\n      if (!isLabelInside) {\n        // For roseType\n        var x2 = x1 + nx * (labelLineLen + r - sectorShape.r);\n        var y2 = y1 + ny * (labelLineLen + r - sectorShape.r);\n        var x3 = x2 + (nx < 0 ? -1 : 1) * labelLineLen2;\n        var y3 = y2;\n        if (labelAlignTo === 'edge') {\n          // Adjust textX because text align of edge is opposite\n          textX = nx < 0 ? viewLeft + edgeDistance : viewLeft + viewWidth - edgeDistance;\n        } else {\n          textX = x3 + (nx < 0 ? -labelDistance : labelDistance);\n        }\n        textY = y3;\n        linePoints = [[x1, y1], [x2, y2], [x3, y3]];\n      }\n      textAlign = isLabelInside ? 'center' : labelAlignTo === 'edge' ? nx > 0 ? 'right' : 'left' : nx > 0 ? 'left' : 'right';\n    }\n    var PI = Math.PI;\n    var labelRotate = 0;\n    var rotate = labelModel.get('rotate');\n    if (isNumber(rotate)) {\n      labelRotate = rotate * (PI / 180);\n    } else if (labelPosition === 'center') {\n      labelRotate = 0;\n    } else if (rotate === 'radial' || rotate === true) {\n      var radialAngle = nx < 0 ? -midAngle + PI : -midAngle;\n      labelRotate = radialAngle;\n    } else if (rotate === 'tangential' && labelPosition !== 'outside' && labelPosition !== 'outer') {\n      var rad = Math.atan2(nx, ny);\n      if (rad < 0) {\n        rad = PI * 2 + rad;\n      }\n      var isDown = ny > 0;\n      if (isDown) {\n        rad = PI + rad;\n      }\n      labelRotate = rad - PI;\n    }\n    hasLabelRotate = !!labelRotate;\n    label.x = textX;\n    label.y = textY;\n    label.rotation = labelRotate;\n    label.setStyle({\n      verticalAlign: 'middle'\n    });\n    // Not sectorShape the inside label\n    if (!isLabelInside) {\n      var textRect = label.getBoundingRect().clone();\n      textRect.applyTransform(label.getComputedTransform());\n      // Text has a default 1px stroke. Exclude this.\n      var margin = (label.style.margin || 0) + 2.1;\n      textRect.y -= margin / 2;\n      textRect.height += margin;\n      labelLayoutList.push({\n        label: label,\n        labelLine: labelLine,\n        position: labelPosition,\n        len: labelLineLen,\n        len2: labelLineLen2,\n        minTurnAngle: labelLineModel.get('minTurnAngle'),\n        maxSurfaceAngle: labelLineModel.get('maxSurfaceAngle'),\n        surfaceNormal: new Point(nx, ny),\n        linePoints: linePoints,\n        textAlign: textAlign,\n        labelDistance: labelDistance,\n        labelAlignTo: labelAlignTo,\n        edgeDistance: edgeDistance,\n        bleedMargin: bleedMargin,\n        rect: textRect,\n        unconstrainedWidth: textRect.width,\n        labelStyleWidth: label.style.width\n      });\n    } else {\n      label.setStyle({\n        align: textAlign\n      });\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    sector.setTextConfig({\n      inside: isLabelInside\n    });\n  });\n  if (!hasLabelRotate && seriesModel.get('avoidLabelOverlap')) {\n    avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop);\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    var label = layout.label;\n    var labelLine = layout.labelLine;\n    var notShowLabel = isNaN(label.x) || isNaN(label.y);\n    if (label) {\n      label.setStyle({\n        align: layout.textAlign\n      });\n      if (notShowLabel) {\n        each(label.states, setNotShow);\n        label.ignore = true;\n      }\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    if (labelLine) {\n      var linePoints = layout.linePoints;\n      if (notShowLabel || !linePoints) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      } else {\n        limitTurnAngle(linePoints, layout.minTurnAngle);\n        limitSurfaceAngle(linePoints, layout.surfaceNormal, layout.maxSurfaceAngle);\n        labelLine.setShape({\n          points: linePoints\n        });\n        // Set the anchor to the midpoint of sector\n        label.__hostTarget.textGuideLineConfig = {\n          anchor: new Point(linePoints[0][0], linePoints[0][1])\n        };\n      }\n    }\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,sBAAsB;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,IAAI,EAAEC,QAAQ,QAAQ,0BAA0B;AACzD,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,iCAAiC;AACnF,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAC1B,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,GAAG,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;EACnG,IAAIT,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;IACnB;EACF;EACA;EACA,SAASC,uCAAuCA,CAACC,IAAI,EAAE;IACrD,IAAIC,EAAE,GAAGD,IAAI,CAACC,EAAE;IAChB,IAAIC,GAAG,GAAGD,EAAE,GAAGA,EAAE;IACjB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACZ,IAAI,CAACU,MAAM,EAAEK,CAAC,EAAE,EAAE;MACzC,IAAIC,IAAI,GAAGJ,IAAI,CAACZ,IAAI,CAACe,CAAC,CAAC;MACvB,IAAIE,EAAE,GAAGpB,IAAI,CAACqB,GAAG,CAACF,IAAI,CAACG,KAAK,CAACC,CAAC,GAAGlB,EAAE,CAAC;MACpC;MACA,IAAImB,EAAE,GAAGlB,CAAC,GAAGa,IAAI,CAACM,GAAG;MACrB,IAAIC,GAAG,GAAGF,EAAE,GAAGA,EAAE;MACjB;MACA,IAAIG,EAAE,GAAG3B,IAAI,CAAC4B,IAAI,CAAC5B,IAAI,CAACqB,GAAG,CAAC,CAAC,CAAC,GAAGD,EAAE,GAAGA,EAAE,GAAGH,GAAG,IAAIS,GAAG,CAAC,CAAC;MACvD,IAAIG,IAAI,GAAGzB,EAAE,GAAG,CAACuB,EAAE,GAAGR,IAAI,CAACW,IAAI,IAAIvB,GAAG;MACtC,IAAIwB,MAAM,GAAGF,IAAI,GAAGV,IAAI,CAACG,KAAK,CAACU,CAAC;MAChC,IAAIC,cAAc,GAAGd,IAAI,CAACe,eAAe,GAAGH,MAAM,GAAGxB,GAAG;MACxD;MACA4B,kBAAkB,CAAChB,IAAI,EAAEc,cAAc,EAAE,IAAI,CAAC;MAC9Cd,IAAI,CAACG,KAAK,CAACU,CAAC,GAAGH,IAAI;IACrB;EACF;EACA;EACA,SAASO,YAAYA,CAACC,KAAK,EAAE;IAC3B;IACA,IAAIC,OAAO,GAAG;MACZnC,IAAI,EAAE,EAAE;MACRoC,IAAI,EAAE;IACR,CAAC;IACD,IAAIC,UAAU,GAAG;MACfrC,IAAI,EAAE,EAAE;MACRoC,IAAI,EAAE;IACR,CAAC;IACD,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,KAAK,CAACxB,MAAM,EAAEK,CAAC,EAAE,EAAE;MACrC,IAAImB,KAAK,CAACnB,CAAC,CAAC,CAACuB,YAAY,KAAK,MAAM,EAAE;QACpC;MACF;MACA,IAAItB,IAAI,GAAGkB,KAAK,CAACnB,CAAC,CAAC;MACnB,IAAIH,IAAI,GAAGI,IAAI,CAACG,KAAK,CAACC,CAAC,GAAGlB,EAAE,GAAGmC,UAAU,GAAGF,OAAO;MACnD,IAAIlB,EAAE,GAAGpB,IAAI,CAACqB,GAAG,CAACF,IAAI,CAACG,KAAK,CAACC,CAAC,GAAGlB,EAAE,CAAC;MACpC,IAAIe,EAAE,IAAIL,IAAI,CAACwB,IAAI,EAAE;QACnB,IAAIZ,EAAE,GAAGR,IAAI,CAACG,KAAK,CAACU,CAAC,GAAG5B,EAAE,GAAGe,IAAI,CAACW,IAAI,GAAGvB,GAAG;QAC5C;QACA,IAAIiB,EAAE,GAAGlB,CAAC,GAAGa,IAAI,CAACM,GAAG;QACrB;QACA,IAAIT,EAAE,GAAGhB,IAAI,CAACqB,GAAG,CAACM,EAAE,CAAC,GAAGH,EAAE,GAAGxB,IAAI,CAAC4B,IAAI,CAACR,EAAE,GAAGA,EAAE,IAAI,CAAC,GAAGO,EAAE,GAAGA,EAAE,GAAGH,EAAE,GAAGA,EAAE,CAAC,CAAC,GAAGA,EAAE;QAC9ET,IAAI,CAACC,EAAE,GAAGA,EAAE;QACZD,IAAI,CAACwB,IAAI,GAAGnB,EAAE;MAChB;MACAL,IAAI,CAACZ,IAAI,CAACuC,IAAI,CAACvB,IAAI,CAAC;IACtB;IACAL,uCAAuC,CAACwB,OAAO,CAAC;IAChDxB,uCAAuC,CAAC0B,UAAU,CAAC;EACrD;EACA,IAAIf,GAAG,GAAGtB,IAAI,CAACU,MAAM;EACrB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;IAC5B,IAAIf,IAAI,CAACe,CAAC,CAAC,CAACyB,QAAQ,KAAK,OAAO,IAAIxC,IAAI,CAACe,CAAC,CAAC,CAACuB,YAAY,KAAK,WAAW,EAAE;MACxE,IAAId,EAAE,GAAGxB,IAAI,CAACe,CAAC,CAAC,CAACI,KAAK,CAACU,CAAC,GAAGpB,SAAS;MACpCT,IAAI,CAACe,CAAC,CAAC,CAAC0B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIjB,EAAE;MAC9BxB,IAAI,CAACe,CAAC,CAAC,CAACI,KAAK,CAACU,CAAC,GAAGpB,SAAS;IAC7B;EACF;EACA,IAAId,cAAc,CAACK,IAAI,EAAEQ,OAAO,EAAEA,OAAO,GAAGF,UAAU,CAAC,EAAE;IACvD2B,YAAY,CAACjC,IAAI,CAAC;EACpB;AACF;AACA,SAAS0C,YAAYA,CAACC,eAAe,EAAE1C,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEE,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC1F,IAAIoC,QAAQ,GAAG,EAAE;EACjB,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIC,SAAS,GAAGC,MAAM,CAACC,SAAS;EAChC,IAAIC,UAAU,GAAG,CAACF,MAAM,CAACC,SAAS;EAClC,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,eAAe,CAACjC,MAAM,EAAEK,CAAC,EAAE,EAAE;IAC/C,IAAII,KAAK,GAAGwB,eAAe,CAAC5B,CAAC,CAAC,CAACI,KAAK;IACpC,IAAI+B,gBAAgB,CAACP,eAAe,CAAC5B,CAAC,CAAC,CAAC,EAAE;MACxC;IACF;IACA,IAAII,KAAK,CAACU,CAAC,GAAG5B,EAAE,EAAE;MAChB6C,SAAS,GAAGjD,IAAI,CAACsD,GAAG,CAACL,SAAS,EAAE3B,KAAK,CAACU,CAAC,CAAC;MACxCe,QAAQ,CAACL,IAAI,CAACI,eAAe,CAAC5B,CAAC,CAAC,CAAC;IACnC,CAAC,MAAM;MACLkC,UAAU,GAAGpD,IAAI,CAACuD,GAAG,CAACH,UAAU,EAAE9B,KAAK,CAACU,CAAC,CAAC;MAC1CgB,SAAS,CAACN,IAAI,CAACI,eAAe,CAAC5B,CAAC,CAAC,CAAC;IACpC;EACF;EACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,eAAe,CAACjC,MAAM,EAAEK,CAAC,EAAE,EAAE;IAC/C,IAAIsC,MAAM,GAAGV,eAAe,CAAC5B,CAAC,CAAC;IAC/B,IAAI,CAACmC,gBAAgB,CAACG,MAAM,CAAC,IAAIA,MAAM,CAACZ,UAAU,EAAE;MAClD,IAAIY,MAAM,CAACC,eAAe,IAAI,IAAI,EAAE;QAClC;MACF;MACA,IAAInC,KAAK,GAAGkC,MAAM,CAAClC,KAAK;MACxB,IAAIsB,UAAU,GAAGY,MAAM,CAACZ,UAAU;MAClC,IAAIV,eAAe,GAAG,KAAK,CAAC;MAC5B,IAAIsB,MAAM,CAACf,YAAY,KAAK,MAAM,EAAE;QAClC,IAAInB,KAAK,CAACU,CAAC,GAAG5B,EAAE,EAAE;UAChB8B,eAAe,GAAGU,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGY,MAAM,CAACE,aAAa,GAAGhD,QAAQ,GAAG8C,MAAM,CAACG,YAAY;QAC5F,CAAC,MAAM;UACLzB,eAAe,GAAGxB,QAAQ,GAAGF,SAAS,GAAGgD,MAAM,CAACG,YAAY,GAAGf,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGY,MAAM,CAACE,aAAa;QACxG;MACF,CAAC,MAAM,IAAIF,MAAM,CAACf,YAAY,KAAK,WAAW,EAAE;QAC9C,IAAInB,KAAK,CAACU,CAAC,GAAG5B,EAAE,EAAE;UAChB8B,eAAe,GAAGe,SAAS,GAAGvC,QAAQ,GAAG8C,MAAM,CAACI,WAAW;QAC7D,CAAC,MAAM;UACL1B,eAAe,GAAGxB,QAAQ,GAAGF,SAAS,GAAG4C,UAAU,GAAGI,MAAM,CAACI,WAAW;QAC1E;MACF,CAAC,MAAM;QACL,IAAItC,KAAK,CAACU,CAAC,GAAG5B,EAAE,EAAE;UAChB8B,eAAe,GAAGZ,KAAK,CAACU,CAAC,GAAGtB,QAAQ,GAAG8C,MAAM,CAACI,WAAW;QAC3D,CAAC,MAAM;UACL1B,eAAe,GAAGxB,QAAQ,GAAGF,SAAS,GAAGc,KAAK,CAACU,CAAC,GAAGwB,MAAM,CAACI,WAAW;QACvE;MACF;MACAJ,MAAM,CAACtB,eAAe,GAAGA,eAAe;MACxCC,kBAAkB,CAACqB,MAAM,EAAEtB,eAAe,CAAC;IAC7C;EACF;EACAhC,gBAAgB,CAAC8C,SAAS,EAAE5C,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE,CAAC,EAAEE,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEyC,UAAU,CAAC;EAC/FlD,gBAAgB,CAAC6C,QAAQ,EAAE3C,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE,CAAC,CAAC,EAAEE,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEsC,SAAS,CAAC;EAC9F,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,eAAe,CAACjC,MAAM,EAAEK,CAAC,EAAE,EAAE;IAC/C,IAAIsC,MAAM,GAAGV,eAAe,CAAC5B,CAAC,CAAC;IAC/B,IAAI,CAACmC,gBAAgB,CAACG,MAAM,CAAC,IAAIA,MAAM,CAACZ,UAAU,EAAE;MAClD,IAAItB,KAAK,GAAGkC,MAAM,CAAClC,KAAK;MACxB,IAAIsB,UAAU,GAAGY,MAAM,CAACZ,UAAU;MAClC,IAAIiB,aAAa,GAAGL,MAAM,CAACf,YAAY,KAAK,MAAM;MAClD,IAAIqB,OAAO,GAAGxC,KAAK,CAACyC,KAAK,CAACD,OAAO;MACjC,IAAIE,QAAQ,GAAGF,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;MACpD;MACA,IAAIG,aAAa,GAAG3C,KAAK,CAACyC,KAAK,CAACG,eAAe,GAAG,CAAC,GAAGF,QAAQ;MAC9D,IAAIG,aAAa,GAAGX,MAAM,CAACY,IAAI,CAACC,KAAK,GAAGJ,aAAa;MACrD,IAAIK,IAAI,GAAG1B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,IAAIiB,aAAa,EAAE;QACjB,IAAIvC,KAAK,CAACU,CAAC,GAAG5B,EAAE,EAAE;UAChBwC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGlC,QAAQ,GAAG8C,MAAM,CAACG,YAAY,GAAGQ,aAAa,GAAGX,MAAM,CAACE,aAAa;QAC1F,CAAC,MAAM;UACLd,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGlC,QAAQ,GAAGF,SAAS,GAAGgD,MAAM,CAACG,YAAY,GAAGQ,aAAa,GAAGX,MAAM,CAACE,aAAa;QACtG;MACF,CAAC,MAAM;QACL,IAAIpC,KAAK,CAACU,CAAC,GAAG5B,EAAE,EAAE;UAChBwC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGtB,KAAK,CAACU,CAAC,GAAGwB,MAAM,CAACE,aAAa;QACnD,CAAC,MAAM;UACLd,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGtB,KAAK,CAACU,CAAC,GAAGwB,MAAM,CAACE,aAAa;QACnD;QACAd,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG0B,IAAI;MAC5C;MACA1B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGtB,KAAK,CAACC,CAAC;IAC/C;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,kBAAkBA,CAACqB,MAAM,EAAEe,cAAc,EAAEC,gBAAgB,EAAE;EACpE,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;IAC/BA,gBAAgB,GAAG,KAAK;EAC1B;EACA,IAAIhB,MAAM,CAACC,eAAe,IAAI,IAAI,EAAE;IAClC;IACA;EACF;EACA,IAAInC,KAAK,GAAGkC,MAAM,CAAClC,KAAK;EACxB,IAAIyC,KAAK,GAAGzC,KAAK,CAACyC,KAAK;EACvB,IAAIU,QAAQ,GAAGjB,MAAM,CAACY,IAAI;EAC1B,IAAIM,OAAO,GAAGX,KAAK,CAACG,eAAe;EACnC,IAAIJ,OAAO,GAAGC,KAAK,CAACD,OAAO;EAC3B,IAAIE,QAAQ,GAAGF,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACpD,IAAIa,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;EAC7B;EACA,IAAIC,aAAa,GAAGH,QAAQ,CAACJ,KAAK,IAAIK,OAAO,GAAG,CAAC,GAAGV,QAAQ,CAAC;EAC7D,IAAIO,cAAc,GAAGK,aAAa,IAAIJ,gBAAgB,EAAE;IACtD,IAAIK,SAAS,GAAGJ,QAAQ,CAACK,MAAM;IAC/B,IAAIH,QAAQ,IAAIA,QAAQ,CAACI,KAAK,CAAC,OAAO,CAAC,EAAE;MACvC;MACA;MACAzD,KAAK,CAAC0D,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC;MACvC;MACA1D,KAAK,CAAC0D,QAAQ,CAAC,OAAO,EAAET,cAAc,GAAGP,QAAQ,CAAC;MAClD;MACA,IAAIiB,SAAS,GAAG3D,KAAK,CAAC4D,eAAe,CAAC,CAAC;MACvC5D,KAAK,CAAC0D,QAAQ,CAAC,OAAO,EAAEhF,IAAI,CAACmF,IAAI,CAACF,SAAS,CAACZ,KAAK,CAAC,CAAC;MACnD/C,KAAK,CAAC0D,QAAQ,CAAC,iBAAiB,EAAEN,OAAO,CAAC;IAC5C,CAAC,MAAM;MACL,IAAIU,mBAAmB,GAAGb,cAAc,GAAGP,QAAQ;MACnD,IAAIqB,QAAQ,GAAGd,cAAc,GAAGK;MAChC;MAAA,EACEQ,mBAAmB;MACrB;MACA;MACAZ,gBAAgB,GAAGY,mBAAmB,GAAG5B,MAAM,CAAC8B;MAChD;MACA;MACA;MAAA,EACE;MACF;MACA;MACA;MAAA,EACEF;MACF;MACA;MAAA,EACE,IAAI;MACN9D,KAAK,CAAC0D,QAAQ,CAAC,OAAO,EAAEK,QAAQ,CAAC;IACnC;IACA,IAAIE,OAAO,GAAGjE,KAAK,CAAC4D,eAAe,CAAC,CAAC;IACrCT,QAAQ,CAACJ,KAAK,GAAGkB,OAAO,CAAClB,KAAK;IAC9B,IAAImB,MAAM,GAAG,CAAClE,KAAK,CAACyC,KAAK,CAACyB,MAAM,IAAI,CAAC,IAAI,GAAG;IAC5Cf,QAAQ,CAACK,MAAM,GAAGS,OAAO,CAACT,MAAM,GAAGU,MAAM;IACzCf,QAAQ,CAAClD,CAAC,IAAI,CAACkD,QAAQ,CAACK,MAAM,GAAGD,SAAS,IAAI,CAAC;EACjD;AACF;AACA,SAASxB,gBAAgBA,CAACoC,WAAW,EAAE;EACrC;EACA,OAAOA,WAAW,CAAC9C,QAAQ,KAAK,QAAQ;AAC1C;AACA,eAAe,SAAS+C,cAAcA,CAACC,WAAW,EAAE;EAClD,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;EAChC,IAAI/C,eAAe,GAAG,EAAE;EACxB,IAAI1C,EAAE;EACN,IAAIC,EAAE;EACN,IAAIyF,cAAc,GAAG,KAAK;EAC1B,IAAIC,kBAAkB,GAAG,CAACJ,WAAW,CAACK,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAIjG,MAAM;EAC7E,IAAIkG,QAAQ,GAAGL,IAAI,CAACM,SAAS,CAAC,UAAU,CAAC;EACzC,IAAI5F,CAAC,GAAGsF,IAAI,CAACM,SAAS,CAAC,GAAG,CAAC;EAC3B,IAAI1F,SAAS,GAAGyF,QAAQ,CAAC5B,KAAK;EAC9B,IAAI3D,QAAQ,GAAGuF,QAAQ,CAACjE,CAAC;EACzB,IAAIrB,OAAO,GAAGsF,QAAQ,CAAC1E,CAAC;EACxB,IAAId,UAAU,GAAGwF,QAAQ,CAACnB,MAAM;EAChC,SAASqB,UAAUA,CAACC,EAAE,EAAE;IACtBA,EAAE,CAACC,MAAM,GAAG,IAAI;EAClB;EACA,SAASC,YAAYA,CAAChF,KAAK,EAAE;IAC3B,IAAI,CAACA,KAAK,CAAC+E,MAAM,EAAE;MACjB,OAAO,IAAI;IACb;IACA,KAAK,IAAIE,GAAG,IAAIjF,KAAK,CAACkF,MAAM,EAAE;MAC5B,IAAIlF,KAAK,CAACkF,MAAM,CAACD,GAAG,CAAC,CAACF,MAAM,KAAK,KAAK,EAAE;QACtC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EACAT,IAAI,CAAClG,IAAI,CAAC,UAAU+G,GAAG,EAAE;IACvB,IAAIC,MAAM,GAAGd,IAAI,CAACe,gBAAgB,CAACF,GAAG,CAAC;IACvC,IAAIhB,WAAW,GAAGiB,MAAM,CAACE,KAAK;IAC9B,IAAItF,KAAK,GAAGoF,MAAM,CAACG,cAAc,CAAC,CAAC;IACnC,IAAIC,SAAS,GAAGJ,MAAM,CAACK,gBAAgB,CAAC,CAAC;IACzC,IAAIC,SAAS,GAAGpB,IAAI,CAACqB,YAAY,CAACR,GAAG,CAAC;IACtC,IAAIS,UAAU,GAAGF,SAAS,CAACG,QAAQ,CAAC,OAAO,CAAC;IAC5C;IACA,IAAIC,aAAa,GAAGF,UAAU,CAAClB,GAAG,CAAC,UAAU,CAAC,IAAIgB,SAAS,CAAChB,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IAClG,IAAItC,aAAa,GAAGwD,UAAU,CAAClB,GAAG,CAAC,qBAAqB,CAAC;IACzD,IAAIvD,YAAY,GAAGyE,UAAU,CAAClB,GAAG,CAAC,SAAS,CAAC;IAC5C,IAAIrC,YAAY,GAAGnE,YAAY,CAAC0H,UAAU,CAAClB,GAAG,CAAC,cAAc,CAAC,EAAExF,SAAS,CAAC;IAC1E,IAAIoD,WAAW,GAAGsD,UAAU,CAAClB,GAAG,CAAC,aAAa,CAAC;IAC/C,IAAIqB,cAAc,GAAGL,SAAS,CAACG,QAAQ,CAAC,WAAW,CAAC;IACpD,IAAIG,YAAY,GAAGD,cAAc,CAACrB,GAAG,CAAC,QAAQ,CAAC;IAC/CsB,YAAY,GAAG9H,YAAY,CAAC8H,YAAY,EAAE9G,SAAS,CAAC;IACpD,IAAI+G,aAAa,GAAGF,cAAc,CAACrB,GAAG,CAAC,SAAS,CAAC;IACjDuB,aAAa,GAAG/H,YAAY,CAAC+H,aAAa,EAAE/G,SAAS,CAAC;IACtD,IAAIR,IAAI,CAACqB,GAAG,CAACoE,WAAW,CAAC+B,QAAQ,GAAG/B,WAAW,CAACgC,UAAU,CAAC,GAAG1B,kBAAkB,EAAE;MAChFrG,IAAI,CAAC4B,KAAK,CAACkF,MAAM,EAAEL,UAAU,CAAC;MAC9B7E,KAAK,CAAC+E,MAAM,GAAG,IAAI;MACnB,IAAIS,SAAS,EAAE;QACbpH,IAAI,CAACoH,SAAS,CAACN,MAAM,EAAEL,UAAU,CAAC;QAClCW,SAAS,CAACT,MAAM,GAAG,IAAI;MACzB;MACA;IACF;IACA,IAAI,CAACC,YAAY,CAAChF,KAAK,CAAC,EAAE;MACxB;IACF;IACA,IAAIoG,QAAQ,GAAG,CAACjC,WAAW,CAACgC,UAAU,GAAGhC,WAAW,CAAC+B,QAAQ,IAAI,CAAC;IAClE,IAAIG,EAAE,GAAG3H,IAAI,CAAC4H,GAAG,CAACF,QAAQ,CAAC;IAC3B,IAAIG,EAAE,GAAG7H,IAAI,CAAC8H,GAAG,CAACJ,QAAQ,CAAC;IAC3B,IAAIK,KAAK;IACT,IAAIC,KAAK;IACT,IAAIpF,UAAU;IACd,IAAIqF,SAAS;IACb7H,EAAE,GAAGqF,WAAW,CAACrF,EAAE;IACnBC,EAAE,GAAGoF,WAAW,CAACpF,EAAE;IACnB,IAAI6H,aAAa,GAAGd,aAAa,KAAK,QAAQ,IAAIA,aAAa,KAAK,OAAO;IAC3E,IAAIA,aAAa,KAAK,QAAQ,EAAE;MAC9BW,KAAK,GAAGtC,WAAW,CAACrF,EAAE;MACtB4H,KAAK,GAAGvC,WAAW,CAACpF,EAAE;MACtB4H,SAAS,GAAG,QAAQ;IACtB,CAAC,MAAM;MACL,IAAIE,EAAE,GAAG,CAACD,aAAa,GAAG,CAACzC,WAAW,CAACnF,CAAC,GAAGmF,WAAW,CAAC2C,EAAE,IAAI,CAAC,GAAGT,EAAE,GAAGlC,WAAW,CAACnF,CAAC,GAAGqH,EAAE,IAAIvH,EAAE;MAC9F,IAAIiI,EAAE,GAAG,CAACH,aAAa,GAAG,CAACzC,WAAW,CAACnF,CAAC,GAAGmF,WAAW,CAAC2C,EAAE,IAAI,CAAC,GAAGP,EAAE,GAAGpC,WAAW,CAACnF,CAAC,GAAGuH,EAAE,IAAIxH,EAAE;MAC9F0H,KAAK,GAAGI,EAAE,GAAGR,EAAE,GAAG,CAAC;MACnBK,KAAK,GAAGK,EAAE,GAAGR,EAAE,GAAG,CAAC;MACnB,IAAI,CAACK,aAAa,EAAE;QAClB;QACA,IAAII,EAAE,GAAGH,EAAE,GAAGR,EAAE,IAAIL,YAAY,GAAGhH,CAAC,GAAGmF,WAAW,CAACnF,CAAC,CAAC;QACrD,IAAIiI,EAAE,GAAGF,EAAE,GAAGR,EAAE,IAAIP,YAAY,GAAGhH,CAAC,GAAGmF,WAAW,CAACnF,CAAC,CAAC;QACrD,IAAIkI,EAAE,GAAGF,EAAE,GAAG,CAACX,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIJ,aAAa;QAC/C,IAAIkB,EAAE,GAAGF,EAAE;QACX,IAAI9F,YAAY,KAAK,MAAM,EAAE;UAC3B;UACAsF,KAAK,GAAGJ,EAAE,GAAG,CAAC,GAAGjH,QAAQ,GAAGiD,YAAY,GAAGjD,QAAQ,GAAGF,SAAS,GAAGmD,YAAY;QAChF,CAAC,MAAM;UACLoE,KAAK,GAAGS,EAAE,IAAIb,EAAE,GAAG,CAAC,GAAG,CAACjE,aAAa,GAAGA,aAAa,CAAC;QACxD;QACAsE,KAAK,GAAGS,EAAE;QACV7F,UAAU,GAAG,CAAC,CAACuF,EAAE,EAAEE,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC;MAC7C;MACAR,SAAS,GAAGC,aAAa,GAAG,QAAQ,GAAGzF,YAAY,KAAK,MAAM,GAAGkF,EAAE,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM,GAAGA,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;IACxH;IACA,IAAI1H,EAAE,GAAGD,IAAI,CAACC,EAAE;IAChB,IAAIyI,WAAW,GAAG,CAAC;IACnB,IAAIC,MAAM,GAAGzB,UAAU,CAAClB,GAAG,CAAC,QAAQ,CAAC;IACrC,IAAIrG,QAAQ,CAACgJ,MAAM,CAAC,EAAE;MACpBD,WAAW,GAAGC,MAAM,IAAI1I,EAAE,GAAG,GAAG,CAAC;IACnC,CAAC,MAAM,IAAImH,aAAa,KAAK,QAAQ,EAAE;MACrCsB,WAAW,GAAG,CAAC;IACjB,CAAC,MAAM,IAAIC,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;MACjD,IAAIC,WAAW,GAAGjB,EAAE,GAAG,CAAC,GAAG,CAACD,QAAQ,GAAGzH,EAAE,GAAG,CAACyH,QAAQ;MACrDgB,WAAW,GAAGE,WAAW;IAC3B,CAAC,MAAM,IAAID,MAAM,KAAK,YAAY,IAAIvB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC9F,IAAIyB,GAAG,GAAG7I,IAAI,CAAC8I,KAAK,CAACnB,EAAE,EAAEE,EAAE,CAAC;MAC5B,IAAIgB,GAAG,GAAG,CAAC,EAAE;QACXA,GAAG,GAAG5I,EAAE,GAAG,CAAC,GAAG4I,GAAG;MACpB;MACA,IAAIE,MAAM,GAAGlB,EAAE,GAAG,CAAC;MACnB,IAAIkB,MAAM,EAAE;QACVF,GAAG,GAAG5I,EAAE,GAAG4I,GAAG;MAChB;MACAH,WAAW,GAAGG,GAAG,GAAG5I,EAAE;IACxB;IACA6F,cAAc,GAAG,CAAC,CAAC4C,WAAW;IAC9BpH,KAAK,CAACU,CAAC,GAAG+F,KAAK;IACfzG,KAAK,CAACC,CAAC,GAAGyG,KAAK;IACf1G,KAAK,CAAC0H,QAAQ,GAAGN,WAAW;IAC5BpH,KAAK,CAAC0D,QAAQ,CAAC;MACbiE,aAAa,EAAE;IACjB,CAAC,CAAC;IACF;IACA,IAAI,CAACf,aAAa,EAAE;MAClB,IAAIzD,QAAQ,GAAGnD,KAAK,CAAC4D,eAAe,CAAC,CAAC,CAACgE,KAAK,CAAC,CAAC;MAC9CzE,QAAQ,CAAC0E,cAAc,CAAC7H,KAAK,CAAC8H,oBAAoB,CAAC,CAAC,CAAC;MACrD;MACA,IAAI5D,MAAM,GAAG,CAAClE,KAAK,CAACyC,KAAK,CAACyB,MAAM,IAAI,CAAC,IAAI,GAAG;MAC5Cf,QAAQ,CAAClD,CAAC,IAAIiE,MAAM,GAAG,CAAC;MACxBf,QAAQ,CAACK,MAAM,IAAIU,MAAM;MACzB1C,eAAe,CAACJ,IAAI,CAAC;QACnBpB,KAAK,EAAEA,KAAK;QACZwF,SAAS,EAAEA,SAAS;QACpBnE,QAAQ,EAAEyE,aAAa;QACvB3F,GAAG,EAAE6F,YAAY;QACjBxF,IAAI,EAAEyF,aAAa;QACnB8B,YAAY,EAAEhC,cAAc,CAACrB,GAAG,CAAC,cAAc,CAAC;QAChDsD,eAAe,EAAEjC,cAAc,CAACrB,GAAG,CAAC,iBAAiB,CAAC;QACtDuD,aAAa,EAAE,IAAI9J,KAAK,CAACkI,EAAE,EAAEE,EAAE,CAAC;QAChCjF,UAAU,EAAEA,UAAU;QACtBqF,SAAS,EAAEA,SAAS;QACpBvE,aAAa,EAAEA,aAAa;QAC5BjB,YAAY,EAAEA,YAAY;QAC1BkB,YAAY,EAAEA,YAAY;QAC1BC,WAAW,EAAEA,WAAW;QACxBQ,IAAI,EAAEK,QAAQ;QACda,kBAAkB,EAAEb,QAAQ,CAACJ,KAAK;QAClCZ,eAAe,EAAEnC,KAAK,CAACyC,KAAK,CAACM;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL/C,KAAK,CAAC0D,QAAQ,CAAC;QACbwE,KAAK,EAAEvB;MACT,CAAC,CAAC;MACF,IAAIwB,WAAW,GAAGnI,KAAK,CAACkF,MAAM,CAACkD,MAAM;MACrC,IAAID,WAAW,EAAE;QACfA,WAAW,CAACzH,CAAC,IAAIV,KAAK,CAACU,CAAC;QACxByH,WAAW,CAAClI,CAAC,IAAID,KAAK,CAACC,CAAC;MAC1B;IACF;IACAmF,MAAM,CAACiD,aAAa,CAAC;MACnBC,MAAM,EAAE1B;IACV,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI,CAACpC,cAAc,IAAIH,WAAW,CAACK,GAAG,CAAC,mBAAmB,CAAC,EAAE;IAC3DnD,YAAY,CAACC,eAAe,EAAE1C,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEE,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACpF;EACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,eAAe,CAACjC,MAAM,EAAEK,CAAC,EAAE,EAAE;IAC/C,IAAIsC,MAAM,GAAGV,eAAe,CAAC5B,CAAC,CAAC;IAC/B,IAAII,KAAK,GAAGkC,MAAM,CAAClC,KAAK;IACxB,IAAIwF,SAAS,GAAGtD,MAAM,CAACsD,SAAS;IAChC,IAAI+C,YAAY,GAAGC,KAAK,CAACxI,KAAK,CAACU,CAAC,CAAC,IAAI8H,KAAK,CAACxI,KAAK,CAACC,CAAC,CAAC;IACnD,IAAID,KAAK,EAAE;MACTA,KAAK,CAAC0D,QAAQ,CAAC;QACbwE,KAAK,EAAEhG,MAAM,CAACyE;MAChB,CAAC,CAAC;MACF,IAAI4B,YAAY,EAAE;QAChBnK,IAAI,CAAC4B,KAAK,CAACkF,MAAM,EAAEL,UAAU,CAAC;QAC9B7E,KAAK,CAAC+E,MAAM,GAAG,IAAI;MACrB;MACA,IAAIoD,WAAW,GAAGnI,KAAK,CAACkF,MAAM,CAACkD,MAAM;MACrC,IAAID,WAAW,EAAE;QACfA,WAAW,CAACzH,CAAC,IAAIV,KAAK,CAACU,CAAC;QACxByH,WAAW,CAAClI,CAAC,IAAID,KAAK,CAACC,CAAC;MAC1B;IACF;IACA,IAAIuF,SAAS,EAAE;MACb,IAAIlE,UAAU,GAAGY,MAAM,CAACZ,UAAU;MAClC,IAAIiH,YAAY,IAAI,CAACjH,UAAU,EAAE;QAC/BlD,IAAI,CAACoH,SAAS,CAACN,MAAM,EAAEL,UAAU,CAAC;QAClCW,SAAS,CAACT,MAAM,GAAG,IAAI;MACzB,CAAC,MAAM;QACLzG,cAAc,CAACgD,UAAU,EAAEY,MAAM,CAAC6F,YAAY,CAAC;QAC/CxJ,iBAAiB,CAAC+C,UAAU,EAAEY,MAAM,CAAC+F,aAAa,EAAE/F,MAAM,CAAC8F,eAAe,CAAC;QAC3ExC,SAAS,CAACiD,QAAQ,CAAC;UACjBC,MAAM,EAAEpH;QACV,CAAC,CAAC;QACF;QACAtB,KAAK,CAAC2I,YAAY,CAACC,mBAAmB,GAAG;UACvCC,MAAM,EAAE,IAAI1K,KAAK,CAACmD,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;MACH;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}