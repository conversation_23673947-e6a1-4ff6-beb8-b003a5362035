import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Tooltip,
  Badge
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
  ReloadOutlined,
  ExportOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';

import {
  selectFilteredOrders,
  selectOrderLoading,
  selectOrderFilters,
  selectOrderPagination,
  setSearchKeyword,
  setFilters,
  setPagination,
  resetFilters
} from '../../store/slices/orderSlice';

const { Title, Text } = Typography;
const { Option } = Select;

const OrderList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const orders = useSelector(selectFilteredOrders);
  const loading = useSelector(selectOrderLoading);
  const filters = useSelector(selectOrderFilters);
  const pagination = useSelector(selectOrderPagination);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'orange',
      'in_progress': 'blue',
      'completed': 'green',
      'cancelled': 'red'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      'pending': '待处理',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return texts[status] || status;
  };

  const getPaymentStatusColor = (status) => {
    const colors = {
      'unpaid': 'red',
      'partial': 'orange',
      'paid': 'green'
    };
    return colors[status] || 'default';
  };

  const getPaymentStatusText = (status) => {
    const texts = {
      'unpaid': '未付款',
      'partial': '部分付款',
      'paid': '已付款'
    };
    return texts[status] || status;
  };

  const columns = [
    {
      title: '订单编号',
      dataIndex: 'id',
      key: 'id',
      width: 150,
      fixed: 'left',
      render: (text) => (
        <Button 
          type="link" 
          style={{ padding: 0, fontWeight: 'bold' }}
          onClick={() => navigate(`/orders/${text}`)}
        >
          {text}
        </Button>
      )
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200
    },
    {
      title: '订单标题',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      ellipsis: true
    },
    {
      title: '订单金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount) => (
        <Text strong style={{ color: '#1890ff' }}>
          {formatCurrency(amount)}
        </Text>
      )
    },
    {
      title: '关联BOM',
      key: 'bom',
      width: 150,
      render: (_, record) => (
        record.bomId ? (
          <Space direction="vertical" size={0}>
            <Text style={{ fontSize: '12px', color: '#1890ff' }}>
              {record.bomId}
            </Text>
            <Text style={{ fontSize: '11px', color: '#8c8c8c' }}>
              {record.bomName}
            </Text>
          </Space>
        ) : (
          <Text type="secondary" style={{ fontSize: '12px' }}>
            未关联BOM
          </Text>
        )
      )
    },
    {
      title: '利润情况',
      key: 'profit',
      width: 150,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text style={{ fontSize: '12px', color: '#52c41a' }}>
            利润: {formatCurrency(record.actualProfit || 0)}
          </Text>
          <Text style={{ fontSize: '12px' }}>
            利润率: {record.actualProfitMargin || 0}%
          </Text>
        </Space>
      )
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '付款状态',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      width: 100,
      render: (status) => (
        <Tag color={getPaymentStatusColor(status)}>
          {getPaymentStatusText(status)}
        </Tag>
      )
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress) => (
        <Progress 
          percent={progress} 
          size="small" 
          strokeColor={progress === 100 ? '#52c41a' : '#1890ff'}
        />
      )
    },
    {
      title: '业务员',
      dataIndex: 'salesperson',
      key: 'salesperson',
      width: 100
    },
    {
      title: '交付日期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      width: 100
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => navigate(`/orders/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 计算统计数据
  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    inProgress: orders.filter(o => o.status === 'in_progress').length,
    completed: orders.filter(o => o.status === 'completed').length,
    totalAmount: orders.reduce((sum, o) => sum + o.amount, 0),
    totalProfit: orders.reduce((sum, o) => sum + (o.actualProfit || 0), 0)
  };

  return (
    <div>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ margin: 0 }}>订单管理</Title>
        <Space>
          <Button icon={<ReloadOutlined />}>刷新</Button>
          <Button icon={<ExportOutlined />}>导出</Button>
          <Button type="primary" icon={<PlusOutlined />}>
            新建订单
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="订单总数" 
              value={stats.total} 
              suffix="个"
              prefix={<ShoppingCartOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="待处理" value={stats.pending} suffix="个" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="进行中" value={stats.inProgress} suffix="个" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="已完成" value={stats.completed} suffix="个" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="订单总额" 
              value={stats.totalAmount} 
              formatter={(value) => formatCurrency(value)}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="总利润" 
              value={stats.totalProfit} 
              formatter={(value) => formatCurrency(value)}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索订单号、客户名称、标题"
              allowClear
              onSearch={(value) => dispatch(setSearchKeyword(value))}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="订单状态"
              allowClear
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => dispatch(setFilters({ status: value }))}
            >
              <Option value="pending">待处理</Option>
              <Option value="in_progress">进行中</Option>
              <Option value="completed">已完成</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="付款状态"
              allowClear
              style={{ width: '100%' }}
              value={filters.paymentStatus}
              onChange={(value) => dispatch(setFilters({ paymentStatus: value }))}
            >
              <Option value="unpaid">未付款</Option>
              <Option value="partial">部分付款</Option>
              <Option value="paid">已付款</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Button onClick={() => dispatch(resetFilters())}>重置</Button>
          </Col>
        </Row>
      </Card>

      {/* 订单列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
          }}
          onChange={(paginationConfig) => dispatch(setPagination({
            current: paginationConfig.current,
            pageSize: paginationConfig.pageSize
          }))}
          scroll={{ x: 1400 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default OrderList;
