{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport Model from '../../model/Model.js';\nimport { each, curry, clone, defaults, isArray, indexOf } from 'zrender/lib/core/util.js';\n// Build axisPointerModel, mergin tooltip.axisPointer model for each axis.\n// allAxesInfo should be updated when setOption performed.\nexport function collect(ecModel, api) {\n  var result = {\n    /**\r\n     * key: makeKey(axis.model)\r\n     * value: {\r\n     *      axis,\r\n     *      coordSys,\r\n     *      axisPointerModel,\r\n     *      triggerTooltip,\r\n     *      triggerEmphasis,\r\n     *      involveSeries,\r\n     *      snap,\r\n     *      seriesModels,\r\n     *      seriesDataCount\r\n     * }\r\n     */\n    axesInfo: {},\n    seriesInvolved: false,\n    /**\r\n     * key: makeKey(coordSys.model)\r\n     * value: Object: key makeKey(axis.model), value: axisInfo\r\n     */\n    coordSysAxesInfo: {},\n    coordSysMap: {}\n  };\n  collectAxesInfo(result, ecModel, api);\n  // Check seriesInvolved for performance, in case too many series in some chart.\n  result.seriesInvolved && collectSeriesInfo(result, ecModel);\n  return result;\n}\nfunction collectAxesInfo(result, ecModel, api) {\n  var globalTooltipModel = ecModel.getComponent('tooltip');\n  var globalAxisPointerModel = ecModel.getComponent('axisPointer');\n  // links can only be set on global.\n  var linksOption = globalAxisPointerModel.get('link', true) || [];\n  var linkGroups = [];\n  // Collect axes info.\n  each(api.getCoordinateSystems(), function (coordSys) {\n    // Some coordinate system do not support axes, like geo.\n    if (!coordSys.axisPointerEnabled) {\n      return;\n    }\n    var coordSysKey = makeKey(coordSys.model);\n    var axesInfoInCoordSys = result.coordSysAxesInfo[coordSysKey] = {};\n    result.coordSysMap[coordSysKey] = coordSys;\n    // Set tooltip (like 'cross') is a convenient way to show axisPointer\n    // for user. So we enable setting tooltip on coordSys model.\n    var coordSysModel = coordSys.model;\n    var baseTooltipModel = coordSysModel.getModel('tooltip', globalTooltipModel);\n    each(coordSys.getAxes(), curry(saveTooltipAxisInfo, false, null));\n    // If axis tooltip used, choose tooltip axis for each coordSys.\n    // Notice this case: coordSys is `grid` but not `cartesian2D` here.\n    if (coordSys.getTooltipAxes && globalTooltipModel\n    // If tooltip.showContent is set as false, tooltip will not\n    // show but axisPointer will show as normal.\n    && baseTooltipModel.get('show')) {\n      // Compatible with previous logic. But series.tooltip.trigger: 'axis'\n      // or series.data[n].tooltip.trigger: 'axis' are not support any more.\n      var triggerAxis = baseTooltipModel.get('trigger') === 'axis';\n      var cross = baseTooltipModel.get(['axisPointer', 'type']) === 'cross';\n      var tooltipAxes = coordSys.getTooltipAxes(baseTooltipModel.get(['axisPointer', 'axis']));\n      if (triggerAxis || cross) {\n        each(tooltipAxes.baseAxes, curry(saveTooltipAxisInfo, cross ? 'cross' : true, triggerAxis));\n      }\n      if (cross) {\n        each(tooltipAxes.otherAxes, curry(saveTooltipAxisInfo, 'cross', false));\n      }\n    }\n    // fromTooltip: true | false | 'cross'\n    // triggerTooltip: true | false | null\n    function saveTooltipAxisInfo(fromTooltip, triggerTooltip, axis) {\n      var axisPointerModel = axis.model.getModel('axisPointer', globalAxisPointerModel);\n      var axisPointerShow = axisPointerModel.get('show');\n      if (!axisPointerShow || axisPointerShow === 'auto' && !fromTooltip && !isHandleTrigger(axisPointerModel)) {\n        return;\n      }\n      if (triggerTooltip == null) {\n        triggerTooltip = axisPointerModel.get('triggerTooltip');\n      }\n      axisPointerModel = fromTooltip ? makeAxisPointerModel(axis, baseTooltipModel, globalAxisPointerModel, ecModel, fromTooltip, triggerTooltip) : axisPointerModel;\n      var snap = axisPointerModel.get('snap');\n      var triggerEmphasis = axisPointerModel.get('triggerEmphasis');\n      var axisKey = makeKey(axis.model);\n      var involveSeries = triggerTooltip || snap || axis.type === 'category';\n      // If result.axesInfo[key] exist, override it (tooltip has higher priority).\n      var axisInfo = result.axesInfo[axisKey] = {\n        key: axisKey,\n        axis: axis,\n        coordSys: coordSys,\n        axisPointerModel: axisPointerModel,\n        triggerTooltip: triggerTooltip,\n        triggerEmphasis: triggerEmphasis,\n        involveSeries: involveSeries,\n        snap: snap,\n        useHandle: isHandleTrigger(axisPointerModel),\n        seriesModels: [],\n        linkGroup: null\n      };\n      axesInfoInCoordSys[axisKey] = axisInfo;\n      result.seriesInvolved = result.seriesInvolved || involveSeries;\n      var groupIndex = getLinkGroupIndex(linksOption, axis);\n      if (groupIndex != null) {\n        var linkGroup = linkGroups[groupIndex] || (linkGroups[groupIndex] = {\n          axesInfo: {}\n        });\n        linkGroup.axesInfo[axisKey] = axisInfo;\n        linkGroup.mapper = linksOption[groupIndex].mapper;\n        axisInfo.linkGroup = linkGroup;\n      }\n    }\n  });\n}\nfunction makeAxisPointerModel(axis, baseTooltipModel, globalAxisPointerModel, ecModel, fromTooltip, triggerTooltip) {\n  var tooltipAxisPointerModel = baseTooltipModel.getModel('axisPointer');\n  var fields = ['type', 'snap', 'lineStyle', 'shadowStyle', 'label', 'animation', 'animationDurationUpdate', 'animationEasingUpdate', 'z'];\n  var volatileOption = {};\n  each(fields, function (field) {\n    volatileOption[field] = clone(tooltipAxisPointerModel.get(field));\n  });\n  // category axis do not auto snap, otherwise some tick that do not\n  // has value can not be hovered. value/time/log axis default snap if\n  // triggered from tooltip and trigger tooltip.\n  volatileOption.snap = axis.type !== 'category' && !!triggerTooltip;\n  // Compatible with previous behavior, tooltip axis does not show label by default.\n  // Only these properties can be overridden from tooltip to axisPointer.\n  if (tooltipAxisPointerModel.get('type') === 'cross') {\n    volatileOption.type = 'line';\n  }\n  var labelOption = volatileOption.label || (volatileOption.label = {});\n  // Follow the convention, do not show label when triggered by tooltip by default.\n  labelOption.show == null && (labelOption.show = false);\n  if (fromTooltip === 'cross') {\n    // When 'cross', both axes show labels.\n    var tooltipAxisPointerLabelShow = tooltipAxisPointerModel.get(['label', 'show']);\n    labelOption.show = tooltipAxisPointerLabelShow != null ? tooltipAxisPointerLabelShow : true;\n    // If triggerTooltip, this is a base axis, which should better not use cross style\n    // (cross style is dashed by default)\n    if (!triggerTooltip) {\n      var crossStyle = volatileOption.lineStyle = tooltipAxisPointerModel.get('crossStyle');\n      crossStyle && defaults(labelOption, crossStyle.textStyle);\n    }\n  }\n  return axis.model.getModel('axisPointer', new Model(volatileOption, globalAxisPointerModel, ecModel));\n}\nfunction collectSeriesInfo(result, ecModel) {\n  // Prepare data for axis trigger\n  ecModel.eachSeries(function (seriesModel) {\n    // Notice this case: this coordSys is `cartesian2D` but not `grid`.\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesTooltipTrigger = seriesModel.get(['tooltip', 'trigger'], true);\n    var seriesTooltipShow = seriesModel.get(['tooltip', 'show'], true);\n    if (!coordSys || seriesTooltipTrigger === 'none' || seriesTooltipTrigger === false || seriesTooltipTrigger === 'item' || seriesTooltipShow === false || seriesModel.get(['axisPointer', 'show'], true) === false) {\n      return;\n    }\n    each(result.coordSysAxesInfo[makeKey(coordSys.model)], function (axisInfo) {\n      var axis = axisInfo.axis;\n      if (coordSys.getAxis(axis.dim) === axis) {\n        axisInfo.seriesModels.push(seriesModel);\n        axisInfo.seriesDataCount == null && (axisInfo.seriesDataCount = 0);\n        axisInfo.seriesDataCount += seriesModel.getData().count();\n      }\n    });\n  });\n}\n/**\r\n * For example:\r\n * {\r\n *     axisPointer: {\r\n *         links: [{\r\n *             xAxisIndex: [2, 4],\r\n *             yAxisIndex: 'all'\r\n *         }, {\r\n *             xAxisId: ['a5', 'a7'],\r\n *             xAxisName: 'xxx'\r\n *         }]\r\n *     }\r\n * }\r\n */\nfunction getLinkGroupIndex(linksOption, axis) {\n  var axisModel = axis.model;\n  var dim = axis.dim;\n  for (var i = 0; i < linksOption.length; i++) {\n    var linkOption = linksOption[i] || {};\n    if (checkPropInLink(linkOption[dim + 'AxisId'], axisModel.id) || checkPropInLink(linkOption[dim + 'AxisIndex'], axisModel.componentIndex) || checkPropInLink(linkOption[dim + 'AxisName'], axisModel.name)) {\n      return i;\n    }\n  }\n}\nfunction checkPropInLink(linkPropValue, axisPropValue) {\n  return linkPropValue === 'all' || isArray(linkPropValue) && indexOf(linkPropValue, axisPropValue) >= 0 || linkPropValue === axisPropValue;\n}\nexport function fixValue(axisModel) {\n  var axisInfo = getAxisInfo(axisModel);\n  if (!axisInfo) {\n    return;\n  }\n  var axisPointerModel = axisInfo.axisPointerModel;\n  var scale = axisInfo.axis.scale;\n  var option = axisPointerModel.option;\n  var status = axisPointerModel.get('status');\n  var value = axisPointerModel.get('value');\n  // Parse init value for category and time axis.\n  if (value != null) {\n    value = scale.parse(value);\n  }\n  var useHandle = isHandleTrigger(axisPointerModel);\n  // If `handle` used, `axisPointer` will always be displayed, so value\n  // and status should be initialized.\n  if (status == null) {\n    option.status = useHandle ? 'show' : 'hide';\n  }\n  var extent = scale.getExtent().slice();\n  extent[0] > extent[1] && extent.reverse();\n  if (\n  // Pick a value on axis when initializing.\n  value == null\n  // If both `handle` and `dataZoom` are used, value may be out of axis extent,\n  // where we should re-pick a value to keep `handle` displaying normally.\n  || value > extent[1]) {\n    // Make handle displayed on the end of the axis when init, which looks better.\n    value = extent[1];\n  }\n  if (value < extent[0]) {\n    value = extent[0];\n  }\n  option.value = value;\n  if (useHandle) {\n    option.status = axisInfo.axis.scale.isBlank() ? 'hide' : 'show';\n  }\n}\nexport function getAxisInfo(axisModel) {\n  var coordSysAxesInfo = (axisModel.ecModel.getComponent('axisPointer') || {}).coordSysAxesInfo;\n  return coordSysAxesInfo && coordSysAxesInfo.axesInfo[makeKey(axisModel)];\n}\nexport function getAxisPointerModel(axisModel) {\n  var axisInfo = getAxisInfo(axisModel);\n  return axisInfo && axisInfo.axisPointerModel;\n}\nfunction isHandleTrigger(axisPointerModel) {\n  return !!axisPointerModel.get(['handle', 'show']);\n}\n/**\r\n * @param {module:echarts/model/Model} model\r\n * @return {string} unique key\r\n */\nexport function makeKey(model) {\n  return model.type + '||' + model.id;\n}", "map": {"version": 3, "names": ["Model", "each", "curry", "clone", "defaults", "isArray", "indexOf", "collect", "ecModel", "api", "result", "axesInfo", "seriesInvolved", "coordSysAxesInfo", "coordSysMap", "collectAxesInfo", "collectSeriesInfo", "globalTooltipModel", "getComponent", "globalAxisPointerModel", "linksOption", "get", "linkGroups", "getCoordinateSystems", "coordSys", "axisPointerEnabled", "coordSysKey", "<PERSON><PERSON><PERSON>", "model", "axesInfoInCoordSys", "coordSysModel", "baseTooltipModel", "getModel", "getAxes", "saveTooltipAxisInfo", "getTooltipAxes", "triggerAxis", "cross", "tooltipAxes", "baseAxes", "otherAxes", "fromTooltip", "triggerTooltip", "axis", "axisPointerModel", "axisPointerShow", "isHandleTrigger", "makeAxisPointerModel", "snap", "triggerEmphasis", "axisKey", "involveSeries", "type", "axisInfo", "key", "useHandle", "seriesModels", "linkGroup", "groupIndex", "getLinkGroupIndex", "mapper", "tooltipAxisPointerModel", "fields", "volatileOption", "field", "labelOption", "label", "show", "tooltipAxisPointerLabelShow", "crossStyle", "lineStyle", "textStyle", "eachSeries", "seriesModel", "coordinateSystem", "seriesTooltipTrigger", "seriesTooltipShow", "getAxis", "dim", "push", "seriesDataCount", "getData", "count", "axisModel", "i", "length", "linkOption", "checkPropInLink", "id", "componentIndex", "name", "linkPropValue", "axisPropValue", "fixValue", "getAxisInfo", "scale", "option", "status", "value", "parse", "extent", "getExtent", "slice", "reverse", "isBlank", "getAxisPointerModel"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/component/axisPointer/modelHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport Model from '../../model/Model.js';\nimport { each, curry, clone, defaults, isArray, indexOf } from 'zrender/lib/core/util.js';\n// Build axisPointerModel, mergin tooltip.axisPointer model for each axis.\n// allAxesInfo should be updated when setOption performed.\nexport function collect(ecModel, api) {\n  var result = {\n    /**\r\n     * key: makeKey(axis.model)\r\n     * value: {\r\n     *      axis,\r\n     *      coordSys,\r\n     *      axisPointerModel,\r\n     *      triggerTooltip,\r\n     *      triggerEmphasis,\r\n     *      involveSeries,\r\n     *      snap,\r\n     *      seriesModels,\r\n     *      seriesDataCount\r\n     * }\r\n     */\n    axesInfo: {},\n    seriesInvolved: false,\n    /**\r\n     * key: makeKey(coordSys.model)\r\n     * value: Object: key makeKey(axis.model), value: axisInfo\r\n     */\n    coordSysAxesInfo: {},\n    coordSysMap: {}\n  };\n  collectAxesInfo(result, ecModel, api);\n  // Check seriesInvolved for performance, in case too many series in some chart.\n  result.seriesInvolved && collectSeriesInfo(result, ecModel);\n  return result;\n}\nfunction collectAxesInfo(result, ecModel, api) {\n  var globalTooltipModel = ecModel.getComponent('tooltip');\n  var globalAxisPointerModel = ecModel.getComponent('axisPointer');\n  // links can only be set on global.\n  var linksOption = globalAxisPointerModel.get('link', true) || [];\n  var linkGroups = [];\n  // Collect axes info.\n  each(api.getCoordinateSystems(), function (coordSys) {\n    // Some coordinate system do not support axes, like geo.\n    if (!coordSys.axisPointerEnabled) {\n      return;\n    }\n    var coordSysKey = makeKey(coordSys.model);\n    var axesInfoInCoordSys = result.coordSysAxesInfo[coordSysKey] = {};\n    result.coordSysMap[coordSysKey] = coordSys;\n    // Set tooltip (like 'cross') is a convenient way to show axisPointer\n    // for user. So we enable setting tooltip on coordSys model.\n    var coordSysModel = coordSys.model;\n    var baseTooltipModel = coordSysModel.getModel('tooltip', globalTooltipModel);\n    each(coordSys.getAxes(), curry(saveTooltipAxisInfo, false, null));\n    // If axis tooltip used, choose tooltip axis for each coordSys.\n    // Notice this case: coordSys is `grid` but not `cartesian2D` here.\n    if (coordSys.getTooltipAxes && globalTooltipModel\n    // If tooltip.showContent is set as false, tooltip will not\n    // show but axisPointer will show as normal.\n    && baseTooltipModel.get('show')) {\n      // Compatible with previous logic. But series.tooltip.trigger: 'axis'\n      // or series.data[n].tooltip.trigger: 'axis' are not support any more.\n      var triggerAxis = baseTooltipModel.get('trigger') === 'axis';\n      var cross = baseTooltipModel.get(['axisPointer', 'type']) === 'cross';\n      var tooltipAxes = coordSys.getTooltipAxes(baseTooltipModel.get(['axisPointer', 'axis']));\n      if (triggerAxis || cross) {\n        each(tooltipAxes.baseAxes, curry(saveTooltipAxisInfo, cross ? 'cross' : true, triggerAxis));\n      }\n      if (cross) {\n        each(tooltipAxes.otherAxes, curry(saveTooltipAxisInfo, 'cross', false));\n      }\n    }\n    // fromTooltip: true | false | 'cross'\n    // triggerTooltip: true | false | null\n    function saveTooltipAxisInfo(fromTooltip, triggerTooltip, axis) {\n      var axisPointerModel = axis.model.getModel('axisPointer', globalAxisPointerModel);\n      var axisPointerShow = axisPointerModel.get('show');\n      if (!axisPointerShow || axisPointerShow === 'auto' && !fromTooltip && !isHandleTrigger(axisPointerModel)) {\n        return;\n      }\n      if (triggerTooltip == null) {\n        triggerTooltip = axisPointerModel.get('triggerTooltip');\n      }\n      axisPointerModel = fromTooltip ? makeAxisPointerModel(axis, baseTooltipModel, globalAxisPointerModel, ecModel, fromTooltip, triggerTooltip) : axisPointerModel;\n      var snap = axisPointerModel.get('snap');\n      var triggerEmphasis = axisPointerModel.get('triggerEmphasis');\n      var axisKey = makeKey(axis.model);\n      var involveSeries = triggerTooltip || snap || axis.type === 'category';\n      // If result.axesInfo[key] exist, override it (tooltip has higher priority).\n      var axisInfo = result.axesInfo[axisKey] = {\n        key: axisKey,\n        axis: axis,\n        coordSys: coordSys,\n        axisPointerModel: axisPointerModel,\n        triggerTooltip: triggerTooltip,\n        triggerEmphasis: triggerEmphasis,\n        involveSeries: involveSeries,\n        snap: snap,\n        useHandle: isHandleTrigger(axisPointerModel),\n        seriesModels: [],\n        linkGroup: null\n      };\n      axesInfoInCoordSys[axisKey] = axisInfo;\n      result.seriesInvolved = result.seriesInvolved || involveSeries;\n      var groupIndex = getLinkGroupIndex(linksOption, axis);\n      if (groupIndex != null) {\n        var linkGroup = linkGroups[groupIndex] || (linkGroups[groupIndex] = {\n          axesInfo: {}\n        });\n        linkGroup.axesInfo[axisKey] = axisInfo;\n        linkGroup.mapper = linksOption[groupIndex].mapper;\n        axisInfo.linkGroup = linkGroup;\n      }\n    }\n  });\n}\nfunction makeAxisPointerModel(axis, baseTooltipModel, globalAxisPointerModel, ecModel, fromTooltip, triggerTooltip) {\n  var tooltipAxisPointerModel = baseTooltipModel.getModel('axisPointer');\n  var fields = ['type', 'snap', 'lineStyle', 'shadowStyle', 'label', 'animation', 'animationDurationUpdate', 'animationEasingUpdate', 'z'];\n  var volatileOption = {};\n  each(fields, function (field) {\n    volatileOption[field] = clone(tooltipAxisPointerModel.get(field));\n  });\n  // category axis do not auto snap, otherwise some tick that do not\n  // has value can not be hovered. value/time/log axis default snap if\n  // triggered from tooltip and trigger tooltip.\n  volatileOption.snap = axis.type !== 'category' && !!triggerTooltip;\n  // Compatible with previous behavior, tooltip axis does not show label by default.\n  // Only these properties can be overridden from tooltip to axisPointer.\n  if (tooltipAxisPointerModel.get('type') === 'cross') {\n    volatileOption.type = 'line';\n  }\n  var labelOption = volatileOption.label || (volatileOption.label = {});\n  // Follow the convention, do not show label when triggered by tooltip by default.\n  labelOption.show == null && (labelOption.show = false);\n  if (fromTooltip === 'cross') {\n    // When 'cross', both axes show labels.\n    var tooltipAxisPointerLabelShow = tooltipAxisPointerModel.get(['label', 'show']);\n    labelOption.show = tooltipAxisPointerLabelShow != null ? tooltipAxisPointerLabelShow : true;\n    // If triggerTooltip, this is a base axis, which should better not use cross style\n    // (cross style is dashed by default)\n    if (!triggerTooltip) {\n      var crossStyle = volatileOption.lineStyle = tooltipAxisPointerModel.get('crossStyle');\n      crossStyle && defaults(labelOption, crossStyle.textStyle);\n    }\n  }\n  return axis.model.getModel('axisPointer', new Model(volatileOption, globalAxisPointerModel, ecModel));\n}\nfunction collectSeriesInfo(result, ecModel) {\n  // Prepare data for axis trigger\n  ecModel.eachSeries(function (seriesModel) {\n    // Notice this case: this coordSys is `cartesian2D` but not `grid`.\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesTooltipTrigger = seriesModel.get(['tooltip', 'trigger'], true);\n    var seriesTooltipShow = seriesModel.get(['tooltip', 'show'], true);\n    if (!coordSys || seriesTooltipTrigger === 'none' || seriesTooltipTrigger === false || seriesTooltipTrigger === 'item' || seriesTooltipShow === false || seriesModel.get(['axisPointer', 'show'], true) === false) {\n      return;\n    }\n    each(result.coordSysAxesInfo[makeKey(coordSys.model)], function (axisInfo) {\n      var axis = axisInfo.axis;\n      if (coordSys.getAxis(axis.dim) === axis) {\n        axisInfo.seriesModels.push(seriesModel);\n        axisInfo.seriesDataCount == null && (axisInfo.seriesDataCount = 0);\n        axisInfo.seriesDataCount += seriesModel.getData().count();\n      }\n    });\n  });\n}\n/**\r\n * For example:\r\n * {\r\n *     axisPointer: {\r\n *         links: [{\r\n *             xAxisIndex: [2, 4],\r\n *             yAxisIndex: 'all'\r\n *         }, {\r\n *             xAxisId: ['a5', 'a7'],\r\n *             xAxisName: 'xxx'\r\n *         }]\r\n *     }\r\n * }\r\n */\nfunction getLinkGroupIndex(linksOption, axis) {\n  var axisModel = axis.model;\n  var dim = axis.dim;\n  for (var i = 0; i < linksOption.length; i++) {\n    var linkOption = linksOption[i] || {};\n    if (checkPropInLink(linkOption[dim + 'AxisId'], axisModel.id) || checkPropInLink(linkOption[dim + 'AxisIndex'], axisModel.componentIndex) || checkPropInLink(linkOption[dim + 'AxisName'], axisModel.name)) {\n      return i;\n    }\n  }\n}\nfunction checkPropInLink(linkPropValue, axisPropValue) {\n  return linkPropValue === 'all' || isArray(linkPropValue) && indexOf(linkPropValue, axisPropValue) >= 0 || linkPropValue === axisPropValue;\n}\nexport function fixValue(axisModel) {\n  var axisInfo = getAxisInfo(axisModel);\n  if (!axisInfo) {\n    return;\n  }\n  var axisPointerModel = axisInfo.axisPointerModel;\n  var scale = axisInfo.axis.scale;\n  var option = axisPointerModel.option;\n  var status = axisPointerModel.get('status');\n  var value = axisPointerModel.get('value');\n  // Parse init value for category and time axis.\n  if (value != null) {\n    value = scale.parse(value);\n  }\n  var useHandle = isHandleTrigger(axisPointerModel);\n  // If `handle` used, `axisPointer` will always be displayed, so value\n  // and status should be initialized.\n  if (status == null) {\n    option.status = useHandle ? 'show' : 'hide';\n  }\n  var extent = scale.getExtent().slice();\n  extent[0] > extent[1] && extent.reverse();\n  if (\n  // Pick a value on axis when initializing.\n  value == null\n  // If both `handle` and `dataZoom` are used, value may be out of axis extent,\n  // where we should re-pick a value to keep `handle` displaying normally.\n  || value > extent[1]) {\n    // Make handle displayed on the end of the axis when init, which looks better.\n    value = extent[1];\n  }\n  if (value < extent[0]) {\n    value = extent[0];\n  }\n  option.value = value;\n  if (useHandle) {\n    option.status = axisInfo.axis.scale.isBlank() ? 'hide' : 'show';\n  }\n}\nexport function getAxisInfo(axisModel) {\n  var coordSysAxesInfo = (axisModel.ecModel.getComponent('axisPointer') || {}).coordSysAxesInfo;\n  return coordSysAxesInfo && coordSysAxesInfo.axesInfo[makeKey(axisModel)];\n}\nexport function getAxisPointerModel(axisModel) {\n  var axisInfo = getAxisInfo(axisModel);\n  return axisInfo && axisInfo.axisPointerModel;\n}\nfunction isHandleTrigger(axisPointerModel) {\n  return !!axisPointerModel.get(['handle', 'show']);\n}\n/**\r\n * @param {module:echarts/model/Model} model\r\n * @return {string} unique key\r\n */\nexport function makeKey(model) {\n  return model.type + '||' + model.id;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,sBAAsB;AACxC,SAASC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,QAAQ,0BAA0B;AACzF;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,OAAO,EAAEC,GAAG,EAAE;EACpC,IAAIC,MAAM,GAAG;IACX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,QAAQ,EAAE,CAAC,CAAC;IACZC,cAAc,EAAE,KAAK;IACrB;AACJ;AACA;AACA;IACIC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,WAAW,EAAE,CAAC;EAChB,CAAC;EACDC,eAAe,CAACL,MAAM,EAAEF,OAAO,EAAEC,GAAG,CAAC;EACrC;EACAC,MAAM,CAACE,cAAc,IAAII,iBAAiB,CAACN,MAAM,EAAEF,OAAO,CAAC;EAC3D,OAAOE,MAAM;AACf;AACA,SAASK,eAAeA,CAACL,MAAM,EAAEF,OAAO,EAAEC,GAAG,EAAE;EAC7C,IAAIQ,kBAAkB,GAAGT,OAAO,CAACU,YAAY,CAAC,SAAS,CAAC;EACxD,IAAIC,sBAAsB,GAAGX,OAAO,CAACU,YAAY,CAAC,aAAa,CAAC;EAChE;EACA,IAAIE,WAAW,GAAGD,sBAAsB,CAACE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE;EAChE,IAAIC,UAAU,GAAG,EAAE;EACnB;EACArB,IAAI,CAACQ,GAAG,CAACc,oBAAoB,CAAC,CAAC,EAAE,UAAUC,QAAQ,EAAE;IACnD;IACA,IAAI,CAACA,QAAQ,CAACC,kBAAkB,EAAE;MAChC;IACF;IACA,IAAIC,WAAW,GAAGC,OAAO,CAACH,QAAQ,CAACI,KAAK,CAAC;IACzC,IAAIC,kBAAkB,GAAGnB,MAAM,CAACG,gBAAgB,CAACa,WAAW,CAAC,GAAG,CAAC,CAAC;IAClEhB,MAAM,CAACI,WAAW,CAACY,WAAW,CAAC,GAAGF,QAAQ;IAC1C;IACA;IACA,IAAIM,aAAa,GAAGN,QAAQ,CAACI,KAAK;IAClC,IAAIG,gBAAgB,GAAGD,aAAa,CAACE,QAAQ,CAAC,SAAS,EAAEf,kBAAkB,CAAC;IAC5EhB,IAAI,CAACuB,QAAQ,CAACS,OAAO,CAAC,CAAC,EAAE/B,KAAK,CAACgC,mBAAmB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IACjE;IACA;IACA,IAAIV,QAAQ,CAACW,cAAc,IAAIlB;IAC/B;IACA;IAAA,GACGc,gBAAgB,CAACV,GAAG,CAAC,MAAM,CAAC,EAAE;MAC/B;MACA;MACA,IAAIe,WAAW,GAAGL,gBAAgB,CAACV,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM;MAC5D,IAAIgB,KAAK,GAAGN,gBAAgB,CAACV,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,KAAK,OAAO;MACrE,IAAIiB,WAAW,GAAGd,QAAQ,CAACW,cAAc,CAACJ,gBAAgB,CAACV,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;MACxF,IAAIe,WAAW,IAAIC,KAAK,EAAE;QACxBpC,IAAI,CAACqC,WAAW,CAACC,QAAQ,EAAErC,KAAK,CAACgC,mBAAmB,EAAEG,KAAK,GAAG,OAAO,GAAG,IAAI,EAAED,WAAW,CAAC,CAAC;MAC7F;MACA,IAAIC,KAAK,EAAE;QACTpC,IAAI,CAACqC,WAAW,CAACE,SAAS,EAAEtC,KAAK,CAACgC,mBAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;MACzE;IACF;IACA;IACA;IACA,SAASA,mBAAmBA,CAACO,WAAW,EAAEC,cAAc,EAAEC,IAAI,EAAE;MAC9D,IAAIC,gBAAgB,GAAGD,IAAI,CAACf,KAAK,CAACI,QAAQ,CAAC,aAAa,EAAEb,sBAAsB,CAAC;MACjF,IAAI0B,eAAe,GAAGD,gBAAgB,CAACvB,GAAG,CAAC,MAAM,CAAC;MAClD,IAAI,CAACwB,eAAe,IAAIA,eAAe,KAAK,MAAM,IAAI,CAACJ,WAAW,IAAI,CAACK,eAAe,CAACF,gBAAgB,CAAC,EAAE;QACxG;MACF;MACA,IAAIF,cAAc,IAAI,IAAI,EAAE;QAC1BA,cAAc,GAAGE,gBAAgB,CAACvB,GAAG,CAAC,gBAAgB,CAAC;MACzD;MACAuB,gBAAgB,GAAGH,WAAW,GAAGM,oBAAoB,CAACJ,IAAI,EAAEZ,gBAAgB,EAAEZ,sBAAsB,EAAEX,OAAO,EAAEiC,WAAW,EAAEC,cAAc,CAAC,GAAGE,gBAAgB;MAC9J,IAAII,IAAI,GAAGJ,gBAAgB,CAACvB,GAAG,CAAC,MAAM,CAAC;MACvC,IAAI4B,eAAe,GAAGL,gBAAgB,CAACvB,GAAG,CAAC,iBAAiB,CAAC;MAC7D,IAAI6B,OAAO,GAAGvB,OAAO,CAACgB,IAAI,CAACf,KAAK,CAAC;MACjC,IAAIuB,aAAa,GAAGT,cAAc,IAAIM,IAAI,IAAIL,IAAI,CAACS,IAAI,KAAK,UAAU;MACtE;MACA,IAAIC,QAAQ,GAAG3C,MAAM,CAACC,QAAQ,CAACuC,OAAO,CAAC,GAAG;QACxCI,GAAG,EAAEJ,OAAO;QACZP,IAAI,EAAEA,IAAI;QACVnB,QAAQ,EAAEA,QAAQ;QAClBoB,gBAAgB,EAAEA,gBAAgB;QAClCF,cAAc,EAAEA,cAAc;QAC9BO,eAAe,EAAEA,eAAe;QAChCE,aAAa,EAAEA,aAAa;QAC5BH,IAAI,EAAEA,IAAI;QACVO,SAAS,EAAET,eAAe,CAACF,gBAAgB,CAAC;QAC5CY,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC;MACD5B,kBAAkB,CAACqB,OAAO,CAAC,GAAGG,QAAQ;MACtC3C,MAAM,CAACE,cAAc,GAAGF,MAAM,CAACE,cAAc,IAAIuC,aAAa;MAC9D,IAAIO,UAAU,GAAGC,iBAAiB,CAACvC,WAAW,EAAEuB,IAAI,CAAC;MACrD,IAAIe,UAAU,IAAI,IAAI,EAAE;QACtB,IAAID,SAAS,GAAGnC,UAAU,CAACoC,UAAU,CAAC,KAAKpC,UAAU,CAACoC,UAAU,CAAC,GAAG;UAClE/C,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;QACF8C,SAAS,CAAC9C,QAAQ,CAACuC,OAAO,CAAC,GAAGG,QAAQ;QACtCI,SAAS,CAACG,MAAM,GAAGxC,WAAW,CAACsC,UAAU,CAAC,CAACE,MAAM;QACjDP,QAAQ,CAACI,SAAS,GAAGA,SAAS;MAChC;IACF;EACF,CAAC,CAAC;AACJ;AACA,SAASV,oBAAoBA,CAACJ,IAAI,EAAEZ,gBAAgB,EAAEZ,sBAAsB,EAAEX,OAAO,EAAEiC,WAAW,EAAEC,cAAc,EAAE;EAClH,IAAImB,uBAAuB,GAAG9B,gBAAgB,CAACC,QAAQ,CAAC,aAAa,CAAC;EACtE,IAAI8B,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,yBAAyB,EAAE,uBAAuB,EAAE,GAAG,CAAC;EACxI,IAAIC,cAAc,GAAG,CAAC,CAAC;EACvB9D,IAAI,CAAC6D,MAAM,EAAE,UAAUE,KAAK,EAAE;IAC5BD,cAAc,CAACC,KAAK,CAAC,GAAG7D,KAAK,CAAC0D,uBAAuB,CAACxC,GAAG,CAAC2C,KAAK,CAAC,CAAC;EACnE,CAAC,CAAC;EACF;EACA;EACA;EACAD,cAAc,CAACf,IAAI,GAAGL,IAAI,CAACS,IAAI,KAAK,UAAU,IAAI,CAAC,CAACV,cAAc;EAClE;EACA;EACA,IAAImB,uBAAuB,CAACxC,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO,EAAE;IACnD0C,cAAc,CAACX,IAAI,GAAG,MAAM;EAC9B;EACA,IAAIa,WAAW,GAAGF,cAAc,CAACG,KAAK,KAAKH,cAAc,CAACG,KAAK,GAAG,CAAC,CAAC,CAAC;EACrE;EACAD,WAAW,CAACE,IAAI,IAAI,IAAI,KAAKF,WAAW,CAACE,IAAI,GAAG,KAAK,CAAC;EACtD,IAAI1B,WAAW,KAAK,OAAO,EAAE;IAC3B;IACA,IAAI2B,2BAA2B,GAAGP,uBAAuB,CAACxC,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAChF4C,WAAW,CAACE,IAAI,GAAGC,2BAA2B,IAAI,IAAI,GAAGA,2BAA2B,GAAG,IAAI;IAC3F;IACA;IACA,IAAI,CAAC1B,cAAc,EAAE;MACnB,IAAI2B,UAAU,GAAGN,cAAc,CAACO,SAAS,GAAGT,uBAAuB,CAACxC,GAAG,CAAC,YAAY,CAAC;MACrFgD,UAAU,IAAIjE,QAAQ,CAAC6D,WAAW,EAAEI,UAAU,CAACE,SAAS,CAAC;IAC3D;EACF;EACA,OAAO5B,IAAI,CAACf,KAAK,CAACI,QAAQ,CAAC,aAAa,EAAE,IAAIhC,KAAK,CAAC+D,cAAc,EAAE5C,sBAAsB,EAAEX,OAAO,CAAC,CAAC;AACvG;AACA,SAASQ,iBAAiBA,CAACN,MAAM,EAAEF,OAAO,EAAE;EAC1C;EACAA,OAAO,CAACgE,UAAU,CAAC,UAAUC,WAAW,EAAE;IACxC;IACA,IAAIjD,QAAQ,GAAGiD,WAAW,CAACC,gBAAgB;IAC3C,IAAIC,oBAAoB,GAAGF,WAAW,CAACpD,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC;IACxE,IAAIuD,iBAAiB,GAAGH,WAAW,CAACpD,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;IAClE,IAAI,CAACG,QAAQ,IAAImD,oBAAoB,KAAK,MAAM,IAAIA,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,MAAM,IAAIC,iBAAiB,KAAK,KAAK,IAAIH,WAAW,CAACpD,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE;MAChN;IACF;IACApB,IAAI,CAACS,MAAM,CAACG,gBAAgB,CAACc,OAAO,CAACH,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAE,UAAUyB,QAAQ,EAAE;MACzE,IAAIV,IAAI,GAAGU,QAAQ,CAACV,IAAI;MACxB,IAAInB,QAAQ,CAACqD,OAAO,CAAClC,IAAI,CAACmC,GAAG,CAAC,KAAKnC,IAAI,EAAE;QACvCU,QAAQ,CAACG,YAAY,CAACuB,IAAI,CAACN,WAAW,CAAC;QACvCpB,QAAQ,CAAC2B,eAAe,IAAI,IAAI,KAAK3B,QAAQ,CAAC2B,eAAe,GAAG,CAAC,CAAC;QAClE3B,QAAQ,CAAC2B,eAAe,IAAIP,WAAW,CAACQ,OAAO,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MAC3D;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASvB,iBAAiBA,CAACvC,WAAW,EAAEuB,IAAI,EAAE;EAC5C,IAAIwC,SAAS,GAAGxC,IAAI,CAACf,KAAK;EAC1B,IAAIkD,GAAG,GAAGnC,IAAI,CAACmC,GAAG;EAClB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhE,WAAW,CAACiE,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,IAAIE,UAAU,GAAGlE,WAAW,CAACgE,CAAC,CAAC,IAAI,CAAC,CAAC;IACrC,IAAIG,eAAe,CAACD,UAAU,CAACR,GAAG,GAAG,QAAQ,CAAC,EAAEK,SAAS,CAACK,EAAE,CAAC,IAAID,eAAe,CAACD,UAAU,CAACR,GAAG,GAAG,WAAW,CAAC,EAAEK,SAAS,CAACM,cAAc,CAAC,IAAIF,eAAe,CAACD,UAAU,CAACR,GAAG,GAAG,UAAU,CAAC,EAAEK,SAAS,CAACO,IAAI,CAAC,EAAE;MAC1M,OAAON,CAAC;IACV;EACF;AACF;AACA,SAASG,eAAeA,CAACI,aAAa,EAAEC,aAAa,EAAE;EACrD,OAAOD,aAAa,KAAK,KAAK,IAAItF,OAAO,CAACsF,aAAa,CAAC,IAAIrF,OAAO,CAACqF,aAAa,EAAEC,aAAa,CAAC,IAAI,CAAC,IAAID,aAAa,KAAKC,aAAa;AAC3I;AACA,OAAO,SAASC,QAAQA,CAACV,SAAS,EAAE;EAClC,IAAI9B,QAAQ,GAAGyC,WAAW,CAACX,SAAS,CAAC;EACrC,IAAI,CAAC9B,QAAQ,EAAE;IACb;EACF;EACA,IAAIT,gBAAgB,GAAGS,QAAQ,CAACT,gBAAgB;EAChD,IAAImD,KAAK,GAAG1C,QAAQ,CAACV,IAAI,CAACoD,KAAK;EAC/B,IAAIC,MAAM,GAAGpD,gBAAgB,CAACoD,MAAM;EACpC,IAAIC,MAAM,GAAGrD,gBAAgB,CAACvB,GAAG,CAAC,QAAQ,CAAC;EAC3C,IAAI6E,KAAK,GAAGtD,gBAAgB,CAACvB,GAAG,CAAC,OAAO,CAAC;EACzC;EACA,IAAI6E,KAAK,IAAI,IAAI,EAAE;IACjBA,KAAK,GAAGH,KAAK,CAACI,KAAK,CAACD,KAAK,CAAC;EAC5B;EACA,IAAI3C,SAAS,GAAGT,eAAe,CAACF,gBAAgB,CAAC;EACjD;EACA;EACA,IAAIqD,MAAM,IAAI,IAAI,EAAE;IAClBD,MAAM,CAACC,MAAM,GAAG1C,SAAS,GAAG,MAAM,GAAG,MAAM;EAC7C;EACA,IAAI6C,MAAM,GAAGL,KAAK,CAACM,SAAS,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACtCF,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAACG,OAAO,CAAC,CAAC;EACzC;EACA;EACAL,KAAK,IAAI;EACT;EACA;EAAA,GACGA,KAAK,GAAGE,MAAM,CAAC,CAAC,CAAC,EAAE;IACpB;IACAF,KAAK,GAAGE,MAAM,CAAC,CAAC,CAAC;EACnB;EACA,IAAIF,KAAK,GAAGE,MAAM,CAAC,CAAC,CAAC,EAAE;IACrBF,KAAK,GAAGE,MAAM,CAAC,CAAC,CAAC;EACnB;EACAJ,MAAM,CAACE,KAAK,GAAGA,KAAK;EACpB,IAAI3C,SAAS,EAAE;IACbyC,MAAM,CAACC,MAAM,GAAG5C,QAAQ,CAACV,IAAI,CAACoD,KAAK,CAACS,OAAO,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;EACjE;AACF;AACA,OAAO,SAASV,WAAWA,CAACX,SAAS,EAAE;EACrC,IAAItE,gBAAgB,GAAG,CAACsE,SAAS,CAAC3E,OAAO,CAACU,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAEL,gBAAgB;EAC7F,OAAOA,gBAAgB,IAAIA,gBAAgB,CAACF,QAAQ,CAACgB,OAAO,CAACwD,SAAS,CAAC,CAAC;AAC1E;AACA,OAAO,SAASsB,mBAAmBA,CAACtB,SAAS,EAAE;EAC7C,IAAI9B,QAAQ,GAAGyC,WAAW,CAACX,SAAS,CAAC;EACrC,OAAO9B,QAAQ,IAAIA,QAAQ,CAACT,gBAAgB;AAC9C;AACA,SAASE,eAAeA,CAACF,gBAAgB,EAAE;EACzC,OAAO,CAAC,CAACA,gBAAgB,CAACvB,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAOA,KAAK,CAACwB,IAAI,GAAG,IAAI,GAAGxB,KAAK,CAAC4D,EAAE;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}