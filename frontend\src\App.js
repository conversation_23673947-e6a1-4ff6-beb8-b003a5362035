import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { ConfigProvider, App as AntApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import { initializeAuth, selectIsAuthenticated } from './store/slices/authSlice';
import Layout from './components/Layout/Layout';
import Login from './pages/Auth/Login';
import Dashboard from './pages/Dashboard/Dashboard';
import CustomerList from './pages/Customer/CustomerList';
import CustomerDetail from './pages/Customer/CustomerDetail';
import QuotationList from './pages/Quotation/QuotationList';
import QuotationDetail from './pages/Quotation/QuotationDetail';
import OrderList from './pages/Order/OrderList';
import OrderDetail from './pages/Order/OrderDetail';
import BOMList from './pages/BOM/BOMList';
import BOMDetail from './pages/BOM/BOMDetail';
import MaterialList from './pages/BOM/MaterialList';
import ReceivableList from './pages/Receivable/ReceivableList';
import ReceivableDetail from './pages/Receivable/ReceivableDetail';
import ProfitAnalysis from './pages/Analysis/ProfitAnalysis';
import PerformanceManagement from './pages/Analysis/PerformanceManagement';
import BusinessAnalysis from './pages/Analysis/BusinessAnalysis';
import FinancialReports from './pages/Analysis/FinancialReports';
import HelpCenter from './pages/Help/HelpCenter';
import Settings from './pages/Settings/Settings';

// 受保护的路由组件
const ProtectedRoute = ({ children }) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return children;
};

// 公共路由组件（已登录用户重定向到首页）
const PublicRoute = ({ children }) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return children;
};

function App() {
  const dispatch = useDispatch();
  
  useEffect(() => {
    // 初始化认证状态
    dispatch(initializeAuth());
  }, [dispatch]);

  return (
    <ConfigProvider 
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
          fontSize: 14,
        },
        components: {
          Layout: {
            headerBg: '#fff',
            siderBg: '#001529',
          },
          Menu: {
            darkItemBg: '#001529',
            darkSubMenuItemBg: '#000c17',
          }
        }
      }}
    >
      <AntApp>
        <div className="App">
          <Routes>
            {/* 公共路由 */}
            <Route 
              path="/login" 
              element={
                <PublicRoute>
                  <Login />
                </PublicRoute>
              } 
            />
            
            {/* 受保护的路由 */}
            <Route 
              path="/*" 
              element={
                <ProtectedRoute>
                  <Layout>
                    <Routes>
                      {/* 默认重定向到仪表板 */}
                      <Route path="/" element={<Navigate to="/dashboard" replace />} />
                      
                      {/* 仪表板 */}
                      <Route path="/dashboard" element={<Dashboard />} />
                      
                      {/* 客户管理 */}
                      <Route path="/customers" element={<CustomerList />} />
                      <Route path="/customers/:id" element={<CustomerDetail />} />
                      
                      {/* 报价管理 */}
                      <Route path="/quotations" element={<QuotationList />} />
                      <Route path="/quotations/:id" element={<QuotationDetail />} />
                      
                      {/* 订单管理 */}
                      <Route path="/orders" element={<OrderList />} />
                      <Route path="/orders/:id" element={<OrderDetail />} />
                      
                      {/* 应收款管理 */}
                      <Route path="/receivables" element={<ReceivableList />} />
                      <Route path="/receivables/:id" element={<ReceivableDetail />} />

                      {/* BOM管理 */}
                      <Route path="/bom" element={<BOMList />} />
                      <Route path="/bom/:id" element={<BOMDetail />} />
                      <Route path="/materials" element={<MaterialList />} />

                      {/* 分析报表 */}
                      <Route path="/analysis/profit" element={<ProfitAnalysis />} />
                      <Route path="/analysis/performance" element={<PerformanceManagement />} />
                      <Route path="/analysis/business" element={<BusinessAnalysis />} />
                      <Route path="/analysis/financial" element={<FinancialReports />} />
                      
                      {/* 帮助中心 */}
                      <Route path="/help" element={<HelpCenter />} />
                      
                      {/* 系统设置 */}
                      <Route path="/settings" element={<Settings />} />
                      
                      {/* 404页面 */}
                      <Route path="*" element={<Navigate to="/dashboard" replace />} />
                    </Routes>
                  </Layout>
                </ProtectedRoute>
              } 
            />
          </Routes>
        </div>
      </AntApp>
    </ConfigProvider>
  );
}

export default App;
