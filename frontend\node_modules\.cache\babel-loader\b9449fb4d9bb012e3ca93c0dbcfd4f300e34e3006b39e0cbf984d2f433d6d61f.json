{"ast": null, "code": "import Clip from './Clip.js';\nimport * as color from '../tool/color.js';\nimport { eqNaN, extend, isArrayLike, isFunction, isGradientObject, isNumber, isString, keys, logError, map } from '../core/util.js';\nimport easingFuncs from './easing.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nimport { isLinearGradient, isRadialGradient } from '../svg/helper.js';\n;\nvar arraySlice = Array.prototype.slice;\nfunction interpolateNumber(p0, p1, percent) {\n  return (p1 - p0) * percent + p0;\n}\nfunction interpolate1DArray(out, p0, p1, percent) {\n  var len = p0.length;\n  for (var i = 0; i < len; i++) {\n    out[i] = interpolateNumber(p0[i], p1[i], percent);\n  }\n  return out;\n}\nfunction interpolate2DArray(out, p0, p1, percent) {\n  var len = p0.length;\n  var len2 = len && p0[0].length;\n  for (var i = 0; i < len; i++) {\n    if (!out[i]) {\n      out[i] = [];\n    }\n    for (var j = 0; j < len2; j++) {\n      out[i][j] = interpolateNumber(p0[i][j], p1[i][j], percent);\n    }\n  }\n  return out;\n}\nfunction add1DArray(out, p0, p1, sign) {\n  var len = p0.length;\n  for (var i = 0; i < len; i++) {\n    out[i] = p0[i] + p1[i] * sign;\n  }\n  return out;\n}\nfunction add2DArray(out, p0, p1, sign) {\n  var len = p0.length;\n  var len2 = len && p0[0].length;\n  for (var i = 0; i < len; i++) {\n    if (!out[i]) {\n      out[i] = [];\n    }\n    for (var j = 0; j < len2; j++) {\n      out[i][j] = p0[i][j] + p1[i][j] * sign;\n    }\n  }\n  return out;\n}\nfunction fillColorStops(val0, val1) {\n  var len0 = val0.length;\n  var len1 = val1.length;\n  var shorterArr = len0 > len1 ? val1 : val0;\n  var shorterLen = Math.min(len0, len1);\n  var last = shorterArr[shorterLen - 1] || {\n    color: [0, 0, 0, 0],\n    offset: 0\n  };\n  for (var i = shorterLen; i < Math.max(len0, len1); i++) {\n    shorterArr.push({\n      offset: last.offset,\n      color: last.color.slice()\n    });\n  }\n}\nfunction fillArray(val0, val1, arrDim) {\n  var arr0 = val0;\n  var arr1 = val1;\n  if (!arr0.push || !arr1.push) {\n    return;\n  }\n  var arr0Len = arr0.length;\n  var arr1Len = arr1.length;\n  if (arr0Len !== arr1Len) {\n    var isPreviousLarger = arr0Len > arr1Len;\n    if (isPreviousLarger) {\n      arr0.length = arr1Len;\n    } else {\n      for (var i = arr0Len; i < arr1Len; i++) {\n        arr0.push(arrDim === 1 ? arr1[i] : arraySlice.call(arr1[i]));\n      }\n    }\n  }\n  var len2 = arr0[0] && arr0[0].length;\n  for (var i = 0; i < arr0.length; i++) {\n    if (arrDim === 1) {\n      if (isNaN(arr0[i])) {\n        arr0[i] = arr1[i];\n      }\n    } else {\n      for (var j = 0; j < len2; j++) {\n        if (isNaN(arr0[i][j])) {\n          arr0[i][j] = arr1[i][j];\n        }\n      }\n    }\n  }\n}\nexport function cloneValue(value) {\n  if (isArrayLike(value)) {\n    var len = value.length;\n    if (isArrayLike(value[0])) {\n      var ret = [];\n      for (var i = 0; i < len; i++) {\n        ret.push(arraySlice.call(value[i]));\n      }\n      return ret;\n    }\n    return arraySlice.call(value);\n  }\n  return value;\n}\nfunction rgba2String(rgba) {\n  rgba[0] = Math.floor(rgba[0]) || 0;\n  rgba[1] = Math.floor(rgba[1]) || 0;\n  rgba[2] = Math.floor(rgba[2]) || 0;\n  rgba[3] = rgba[3] == null ? 1 : rgba[3];\n  return 'rgba(' + rgba.join(',') + ')';\n}\nfunction guessArrayDim(value) {\n  return isArrayLike(value && value[0]) ? 2 : 1;\n}\nvar VALUE_TYPE_NUMBER = 0;\nvar VALUE_TYPE_1D_ARRAY = 1;\nvar VALUE_TYPE_2D_ARRAY = 2;\nvar VALUE_TYPE_COLOR = 3;\nvar VALUE_TYPE_LINEAR_GRADIENT = 4;\nvar VALUE_TYPE_RADIAL_GRADIENT = 5;\nvar VALUE_TYPE_UNKOWN = 6;\nfunction isGradientValueType(valType) {\n  return valType === VALUE_TYPE_LINEAR_GRADIENT || valType === VALUE_TYPE_RADIAL_GRADIENT;\n}\nfunction isArrayValueType(valType) {\n  return valType === VALUE_TYPE_1D_ARRAY || valType === VALUE_TYPE_2D_ARRAY;\n}\nvar tmpRgba = [0, 0, 0, 0];\nvar Track = function () {\n  function Track(propName) {\n    this.keyframes = [];\n    this.discrete = false;\n    this._invalid = false;\n    this._needsSort = false;\n    this._lastFr = 0;\n    this._lastFrP = 0;\n    this.propName = propName;\n  }\n  Track.prototype.isFinished = function () {\n    return this._finished;\n  };\n  Track.prototype.setFinished = function () {\n    this._finished = true;\n    if (this._additiveTrack) {\n      this._additiveTrack.setFinished();\n    }\n  };\n  Track.prototype.needsAnimate = function () {\n    return this.keyframes.length >= 1;\n  };\n  Track.prototype.getAdditiveTrack = function () {\n    return this._additiveTrack;\n  };\n  Track.prototype.addKeyframe = function (time, rawValue, easing) {\n    this._needsSort = true;\n    var keyframes = this.keyframes;\n    var len = keyframes.length;\n    var discrete = false;\n    var valType = VALUE_TYPE_UNKOWN;\n    var value = rawValue;\n    if (isArrayLike(rawValue)) {\n      var arrayDim = guessArrayDim(rawValue);\n      valType = arrayDim;\n      if (arrayDim === 1 && !isNumber(rawValue[0]) || arrayDim === 2 && !isNumber(rawValue[0][0])) {\n        discrete = true;\n      }\n    } else {\n      if (isNumber(rawValue) && !eqNaN(rawValue)) {\n        valType = VALUE_TYPE_NUMBER;\n      } else if (isString(rawValue)) {\n        if (!isNaN(+rawValue)) {\n          valType = VALUE_TYPE_NUMBER;\n        } else {\n          var colorArray = color.parse(rawValue);\n          if (colorArray) {\n            value = colorArray;\n            valType = VALUE_TYPE_COLOR;\n          }\n        }\n      } else if (isGradientObject(rawValue)) {\n        var parsedGradient = extend({}, value);\n        parsedGradient.colorStops = map(rawValue.colorStops, function (colorStop) {\n          return {\n            offset: colorStop.offset,\n            color: color.parse(colorStop.color)\n          };\n        });\n        if (isLinearGradient(rawValue)) {\n          valType = VALUE_TYPE_LINEAR_GRADIENT;\n        } else if (isRadialGradient(rawValue)) {\n          valType = VALUE_TYPE_RADIAL_GRADIENT;\n        }\n        value = parsedGradient;\n      }\n    }\n    if (len === 0) {\n      this.valType = valType;\n    } else if (valType !== this.valType || valType === VALUE_TYPE_UNKOWN) {\n      discrete = true;\n    }\n    this.discrete = this.discrete || discrete;\n    var kf = {\n      time: time,\n      value: value,\n      rawValue: rawValue,\n      percent: 0\n    };\n    if (easing) {\n      kf.easing = easing;\n      kf.easingFunc = isFunction(easing) ? easing : easingFuncs[easing] || createCubicEasingFunc(easing);\n    }\n    keyframes.push(kf);\n    return kf;\n  };\n  Track.prototype.prepare = function (maxTime, additiveTrack) {\n    var kfs = this.keyframes;\n    if (this._needsSort) {\n      kfs.sort(function (a, b) {\n        return a.time - b.time;\n      });\n    }\n    var valType = this.valType;\n    var kfsLen = kfs.length;\n    var lastKf = kfs[kfsLen - 1];\n    var isDiscrete = this.discrete;\n    var isArr = isArrayValueType(valType);\n    var isGradient = isGradientValueType(valType);\n    for (var i = 0; i < kfsLen; i++) {\n      var kf = kfs[i];\n      var value = kf.value;\n      var lastValue = lastKf.value;\n      kf.percent = kf.time / maxTime;\n      if (!isDiscrete) {\n        if (isArr && i !== kfsLen - 1) {\n          fillArray(value, lastValue, valType);\n        } else if (isGradient) {\n          fillColorStops(value.colorStops, lastValue.colorStops);\n        }\n      }\n    }\n    if (!isDiscrete && valType !== VALUE_TYPE_RADIAL_GRADIENT && additiveTrack && this.needsAnimate() && additiveTrack.needsAnimate() && valType === additiveTrack.valType && !additiveTrack._finished) {\n      this._additiveTrack = additiveTrack;\n      var startValue = kfs[0].value;\n      for (var i = 0; i < kfsLen; i++) {\n        if (valType === VALUE_TYPE_NUMBER) {\n          kfs[i].additiveValue = kfs[i].value - startValue;\n        } else if (valType === VALUE_TYPE_COLOR) {\n          kfs[i].additiveValue = add1DArray([], kfs[i].value, startValue, -1);\n        } else if (isArrayValueType(valType)) {\n          kfs[i].additiveValue = valType === VALUE_TYPE_1D_ARRAY ? add1DArray([], kfs[i].value, startValue, -1) : add2DArray([], kfs[i].value, startValue, -1);\n        }\n      }\n    }\n  };\n  Track.prototype.step = function (target, percent) {\n    if (this._finished) {\n      return;\n    }\n    if (this._additiveTrack && this._additiveTrack._finished) {\n      this._additiveTrack = null;\n    }\n    var isAdditive = this._additiveTrack != null;\n    var valueKey = isAdditive ? 'additiveValue' : 'value';\n    var valType = this.valType;\n    var keyframes = this.keyframes;\n    var kfsNum = keyframes.length;\n    var propName = this.propName;\n    var isValueColor = valType === VALUE_TYPE_COLOR;\n    var frameIdx;\n    var lastFrame = this._lastFr;\n    var mathMin = Math.min;\n    var frame;\n    var nextFrame;\n    if (kfsNum === 1) {\n      frame = nextFrame = keyframes[0];\n    } else {\n      if (percent < 0) {\n        frameIdx = 0;\n      } else if (percent < this._lastFrP) {\n        var start = mathMin(lastFrame + 1, kfsNum - 1);\n        for (frameIdx = start; frameIdx >= 0; frameIdx--) {\n          if (keyframes[frameIdx].percent <= percent) {\n            break;\n          }\n        }\n        frameIdx = mathMin(frameIdx, kfsNum - 2);\n      } else {\n        for (frameIdx = lastFrame; frameIdx < kfsNum; frameIdx++) {\n          if (keyframes[frameIdx].percent > percent) {\n            break;\n          }\n        }\n        frameIdx = mathMin(frameIdx - 1, kfsNum - 2);\n      }\n      nextFrame = keyframes[frameIdx + 1];\n      frame = keyframes[frameIdx];\n    }\n    if (!(frame && nextFrame)) {\n      return;\n    }\n    this._lastFr = frameIdx;\n    this._lastFrP = percent;\n    var interval = nextFrame.percent - frame.percent;\n    var w = interval === 0 ? 1 : mathMin((percent - frame.percent) / interval, 1);\n    if (nextFrame.easingFunc) {\n      w = nextFrame.easingFunc(w);\n    }\n    var targetArr = isAdditive ? this._additiveValue : isValueColor ? tmpRgba : target[propName];\n    if ((isArrayValueType(valType) || isValueColor) && !targetArr) {\n      targetArr = this._additiveValue = [];\n    }\n    if (this.discrete) {\n      target[propName] = w < 1 ? frame.rawValue : nextFrame.rawValue;\n    } else if (isArrayValueType(valType)) {\n      valType === VALUE_TYPE_1D_ARRAY ? interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w) : interpolate2DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n    } else if (isGradientValueType(valType)) {\n      var val = frame[valueKey];\n      var nextVal_1 = nextFrame[valueKey];\n      var isLinearGradient_1 = valType === VALUE_TYPE_LINEAR_GRADIENT;\n      target[propName] = {\n        type: isLinearGradient_1 ? 'linear' : 'radial',\n        x: interpolateNumber(val.x, nextVal_1.x, w),\n        y: interpolateNumber(val.y, nextVal_1.y, w),\n        colorStops: map(val.colorStops, function (colorStop, idx) {\n          var nextColorStop = nextVal_1.colorStops[idx];\n          return {\n            offset: interpolateNumber(colorStop.offset, nextColorStop.offset, w),\n            color: rgba2String(interpolate1DArray([], colorStop.color, nextColorStop.color, w))\n          };\n        }),\n        global: nextVal_1.global\n      };\n      if (isLinearGradient_1) {\n        target[propName].x2 = interpolateNumber(val.x2, nextVal_1.x2, w);\n        target[propName].y2 = interpolateNumber(val.y2, nextVal_1.y2, w);\n      } else {\n        target[propName].r = interpolateNumber(val.r, nextVal_1.r, w);\n      }\n    } else if (isValueColor) {\n      interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n      if (!isAdditive) {\n        target[propName] = rgba2String(targetArr);\n      }\n    } else {\n      var value = interpolateNumber(frame[valueKey], nextFrame[valueKey], w);\n      if (isAdditive) {\n        this._additiveValue = value;\n      } else {\n        target[propName] = value;\n      }\n    }\n    if (isAdditive) {\n      this._addToTarget(target);\n    }\n  };\n  Track.prototype._addToTarget = function (target) {\n    var valType = this.valType;\n    var propName = this.propName;\n    var additiveValue = this._additiveValue;\n    if (valType === VALUE_TYPE_NUMBER) {\n      target[propName] = target[propName] + additiveValue;\n    } else if (valType === VALUE_TYPE_COLOR) {\n      color.parse(target[propName], tmpRgba);\n      add1DArray(tmpRgba, tmpRgba, additiveValue, 1);\n      target[propName] = rgba2String(tmpRgba);\n    } else if (valType === VALUE_TYPE_1D_ARRAY) {\n      add1DArray(target[propName], target[propName], additiveValue, 1);\n    } else if (valType === VALUE_TYPE_2D_ARRAY) {\n      add2DArray(target[propName], target[propName], additiveValue, 1);\n    }\n  };\n  return Track;\n}();\nvar Animator = function () {\n  function Animator(target, loop, allowDiscreteAnimation, additiveTo) {\n    this._tracks = {};\n    this._trackKeys = [];\n    this._maxTime = 0;\n    this._started = 0;\n    this._clip = null;\n    this._target = target;\n    this._loop = loop;\n    if (loop && additiveTo) {\n      logError('Can\\' use additive animation on looped animation.');\n      return;\n    }\n    this._additiveAnimators = additiveTo;\n    this._allowDiscrete = allowDiscreteAnimation;\n  }\n  Animator.prototype.getMaxTime = function () {\n    return this._maxTime;\n  };\n  Animator.prototype.getDelay = function () {\n    return this._delay;\n  };\n  Animator.prototype.getLoop = function () {\n    return this._loop;\n  };\n  Animator.prototype.getTarget = function () {\n    return this._target;\n  };\n  Animator.prototype.changeTarget = function (target) {\n    this._target = target;\n  };\n  Animator.prototype.when = function (time, props, easing) {\n    return this.whenWithKeys(time, props, keys(props), easing);\n  };\n  Animator.prototype.whenWithKeys = function (time, props, propNames, easing) {\n    var tracks = this._tracks;\n    for (var i = 0; i < propNames.length; i++) {\n      var propName = propNames[i];\n      var track = tracks[propName];\n      if (!track) {\n        track = tracks[propName] = new Track(propName);\n        var initialValue = void 0;\n        var additiveTrack = this._getAdditiveTrack(propName);\n        if (additiveTrack) {\n          var addtiveTrackKfs = additiveTrack.keyframes;\n          var lastFinalKf = addtiveTrackKfs[addtiveTrackKfs.length - 1];\n          initialValue = lastFinalKf && lastFinalKf.value;\n          if (additiveTrack.valType === VALUE_TYPE_COLOR && initialValue) {\n            initialValue = rgba2String(initialValue);\n          }\n        } else {\n          initialValue = this._target[propName];\n        }\n        if (initialValue == null) {\n          continue;\n        }\n        if (time > 0) {\n          track.addKeyframe(0, cloneValue(initialValue), easing);\n        }\n        this._trackKeys.push(propName);\n      }\n      track.addKeyframe(time, cloneValue(props[propName]), easing);\n    }\n    this._maxTime = Math.max(this._maxTime, time);\n    return this;\n  };\n  Animator.prototype.pause = function () {\n    this._clip.pause();\n    this._paused = true;\n  };\n  Animator.prototype.resume = function () {\n    this._clip.resume();\n    this._paused = false;\n  };\n  Animator.prototype.isPaused = function () {\n    return !!this._paused;\n  };\n  Animator.prototype.duration = function (duration) {\n    this._maxTime = duration;\n    this._force = true;\n    return this;\n  };\n  Animator.prototype._doneCallback = function () {\n    this._setTracksFinished();\n    this._clip = null;\n    var doneList = this._doneCbs;\n    if (doneList) {\n      var len = doneList.length;\n      for (var i = 0; i < len; i++) {\n        doneList[i].call(this);\n      }\n    }\n  };\n  Animator.prototype._abortedCallback = function () {\n    this._setTracksFinished();\n    var animation = this.animation;\n    var abortedList = this._abortedCbs;\n    if (animation) {\n      animation.removeClip(this._clip);\n    }\n    this._clip = null;\n    if (abortedList) {\n      for (var i = 0; i < abortedList.length; i++) {\n        abortedList[i].call(this);\n      }\n    }\n  };\n  Animator.prototype._setTracksFinished = function () {\n    var tracks = this._tracks;\n    var tracksKeys = this._trackKeys;\n    for (var i = 0; i < tracksKeys.length; i++) {\n      tracks[tracksKeys[i]].setFinished();\n    }\n  };\n  Animator.prototype._getAdditiveTrack = function (trackName) {\n    var additiveTrack;\n    var additiveAnimators = this._additiveAnimators;\n    if (additiveAnimators) {\n      for (var i = 0; i < additiveAnimators.length; i++) {\n        var track = additiveAnimators[i].getTrack(trackName);\n        if (track) {\n          additiveTrack = track;\n        }\n      }\n    }\n    return additiveTrack;\n  };\n  Animator.prototype.start = function (easing) {\n    if (this._started > 0) {\n      return;\n    }\n    this._started = 1;\n    var self = this;\n    var tracks = [];\n    var maxTime = this._maxTime || 0;\n    for (var i = 0; i < this._trackKeys.length; i++) {\n      var propName = this._trackKeys[i];\n      var track = this._tracks[propName];\n      var additiveTrack = this._getAdditiveTrack(propName);\n      var kfs = track.keyframes;\n      var kfsNum = kfs.length;\n      track.prepare(maxTime, additiveTrack);\n      if (track.needsAnimate()) {\n        if (!this._allowDiscrete && track.discrete) {\n          var lastKf = kfs[kfsNum - 1];\n          if (lastKf) {\n            self._target[track.propName] = lastKf.rawValue;\n          }\n          track.setFinished();\n        } else {\n          tracks.push(track);\n        }\n      }\n    }\n    if (tracks.length || this._force) {\n      var clip = new Clip({\n        life: maxTime,\n        loop: this._loop,\n        delay: this._delay || 0,\n        onframe: function (percent) {\n          self._started = 2;\n          var additiveAnimators = self._additiveAnimators;\n          if (additiveAnimators) {\n            var stillHasAdditiveAnimator = false;\n            for (var i = 0; i < additiveAnimators.length; i++) {\n              if (additiveAnimators[i]._clip) {\n                stillHasAdditiveAnimator = true;\n                break;\n              }\n            }\n            if (!stillHasAdditiveAnimator) {\n              self._additiveAnimators = null;\n            }\n          }\n          for (var i = 0; i < tracks.length; i++) {\n            tracks[i].step(self._target, percent);\n          }\n          var onframeList = self._onframeCbs;\n          if (onframeList) {\n            for (var i = 0; i < onframeList.length; i++) {\n              onframeList[i](self._target, percent);\n            }\n          }\n        },\n        ondestroy: function () {\n          self._doneCallback();\n        }\n      });\n      this._clip = clip;\n      if (this.animation) {\n        this.animation.addClip(clip);\n      }\n      if (easing) {\n        clip.setEasing(easing);\n      }\n    } else {\n      this._doneCallback();\n    }\n    return this;\n  };\n  Animator.prototype.stop = function (forwardToLast) {\n    if (!this._clip) {\n      return;\n    }\n    var clip = this._clip;\n    if (forwardToLast) {\n      clip.onframe(1);\n    }\n    this._abortedCallback();\n  };\n  Animator.prototype.delay = function (time) {\n    this._delay = time;\n    return this;\n  };\n  Animator.prototype.during = function (cb) {\n    if (cb) {\n      if (!this._onframeCbs) {\n        this._onframeCbs = [];\n      }\n      this._onframeCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.done = function (cb) {\n    if (cb) {\n      if (!this._doneCbs) {\n        this._doneCbs = [];\n      }\n      this._doneCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.aborted = function (cb) {\n    if (cb) {\n      if (!this._abortedCbs) {\n        this._abortedCbs = [];\n      }\n      this._abortedCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.getClip = function () {\n    return this._clip;\n  };\n  Animator.prototype.getTrack = function (propName) {\n    return this._tracks[propName];\n  };\n  Animator.prototype.getTracks = function () {\n    var _this = this;\n    return map(this._trackKeys, function (key) {\n      return _this._tracks[key];\n    });\n  };\n  Animator.prototype.stopTracks = function (propNames, forwardToLast) {\n    if (!propNames.length || !this._clip) {\n      return true;\n    }\n    var tracks = this._tracks;\n    var tracksKeys = this._trackKeys;\n    for (var i = 0; i < propNames.length; i++) {\n      var track = tracks[propNames[i]];\n      if (track && !track.isFinished()) {\n        if (forwardToLast) {\n          track.step(this._target, 1);\n        } else if (this._started === 1) {\n          track.step(this._target, 0);\n        }\n        track.setFinished();\n      }\n    }\n    var allAborted = true;\n    for (var i = 0; i < tracksKeys.length; i++) {\n      if (!tracks[tracksKeys[i]].isFinished()) {\n        allAborted = false;\n        break;\n      }\n    }\n    if (allAborted) {\n      this._abortedCallback();\n    }\n    return allAborted;\n  };\n  Animator.prototype.saveTo = function (target, trackKeys, firstOrLast) {\n    if (!target) {\n      return;\n    }\n    trackKeys = trackKeys || this._trackKeys;\n    for (var i = 0; i < trackKeys.length; i++) {\n      var propName = trackKeys[i];\n      var track = this._tracks[propName];\n      if (!track || track.isFinished()) {\n        continue;\n      }\n      var kfs = track.keyframes;\n      var kf = kfs[firstOrLast ? 0 : kfs.length - 1];\n      if (kf) {\n        target[propName] = cloneValue(kf.rawValue);\n      }\n    }\n  };\n  Animator.prototype.__changeFinalValue = function (finalProps, trackKeys) {\n    trackKeys = trackKeys || keys(finalProps);\n    for (var i = 0; i < trackKeys.length; i++) {\n      var propName = trackKeys[i];\n      var track = this._tracks[propName];\n      if (!track) {\n        continue;\n      }\n      var kfs = track.keyframes;\n      if (kfs.length > 1) {\n        var lastKf = kfs.pop();\n        track.addKeyframe(lastKf.time, finalProps[propName]);\n        track.prepare(this._maxTime, track.getAdditiveTrack());\n      }\n    }\n  };\n  return Animator;\n}();\nexport default Animator;", "map": {"version": 3, "names": ["Clip", "color", "eqNaN", "extend", "isArrayLike", "isFunction", "isGradientObject", "isNumber", "isString", "keys", "logError", "map", "easingFuncs", "createCubicEasingFunc", "isLinearGradient", "isRadialGradient", "arraySlice", "Array", "prototype", "slice", "interpolateNumber", "p0", "p1", "percent", "interpolate1DArray", "out", "len", "length", "i", "interpolate2DArray", "len2", "j", "add1DArray", "sign", "add2DArray", "fillColorStops", "val0", "val1", "len0", "len1", "shorterArr", "shorter<PERSON>en", "Math", "min", "last", "offset", "max", "push", "fillA<PERSON>y", "arr<PERSON><PERSON>", "arr0", "arr1", "arr0Len", "arr1Len", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "call", "isNaN", "cloneValue", "value", "ret", "rgba2String", "rgba", "floor", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VALUE_TYPE_NUMBER", "VALUE_TYPE_1D_ARRAY", "VALUE_TYPE_2D_ARRAY", "VALUE_TYPE_COLOR", "VALUE_TYPE_LINEAR_GRADIENT", "VALUE_TYPE_RADIAL_GRADIENT", "VALUE_TYPE_UNKOWN", "isGradientValueType", "valType", "isArrayValueType", "tmpRgba", "Track", "propName", "keyframes", "discrete", "_invalid", "_needsSort", "_lastFr", "_lastFrP", "isFinished", "_finished", "setFinished", "_additiveTrack", "needsAnimate", "getAdditiveTrack", "addKeyframe", "time", "rawValue", "easing", "arrayDim", "colorArray", "parse", "parsedGradient", "colorStops", "colorStop", "kf", "easingFunc", "prepare", "maxTime", "additiveTrack", "kfs", "sort", "a", "b", "kfsLen", "lastKf", "isDiscrete", "isArr", "isGradient", "lastValue", "startValue", "additiveValue", "step", "target", "isAdditive", "valueKey", "kfsNum", "isValueColor", "frameIdx", "<PERSON><PERSON><PERSON><PERSON>", "mathMin", "frame", "next<PERSON><PERSON><PERSON>", "start", "interval", "w", "targetArr", "_additiveValue", "val", "nextVal_1", "isLinearGradient_1", "type", "x", "y", "idx", "nextColorStop", "global", "x2", "y2", "r", "_addToTarget", "Animator", "loop", "allowDiscreteAnimation", "additiveTo", "_tracks", "_trackKeys", "_maxTime", "_started", "_clip", "_target", "_loop", "_additiveAnimators", "_allowDiscrete", "getMaxTime", "get<PERSON>elay", "_delay", "getLoop", "get<PERSON><PERSON><PERSON>", "change<PERSON>arget", "when", "props", "when<PERSON>ith<PERSON>eys", "propNames", "tracks", "track", "initialValue", "_getAdditiveTrack", "addtiveTrackKfs", "lastFinalKf", "pause", "_paused", "resume", "isPaused", "duration", "_force", "_doneCallback", "_setTracksFinished", "doneList", "_doneCbs", "_abortedCallback", "animation", "abortedList", "_abortedCbs", "removeClip", "tracksKeys", "trackName", "additiveAnimators", "getTrack", "self", "clip", "life", "delay", "onframe", "stillHasAdditiveAnimator", "onframeList", "_onframeCbs", "ondestroy", "addClip", "setEasing", "stop", "forwardToLast", "during", "cb", "done", "aborted", "getClip", "getTracks", "_this", "key", "stopTracks", "allAborted", "saveTo", "trackKeys", "firstOrLast", "__changeFinalValue", "finalProps", "pop"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/zrender/lib/animation/Animator.js"], "sourcesContent": ["import Clip from './Clip.js';\nimport * as color from '../tool/color.js';\nimport { eqNaN, extend, isArrayLike, isFunction, isGradientObject, isNumber, isString, keys, logError, map } from '../core/util.js';\nimport easingFuncs from './easing.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nimport { isLinearGradient, isRadialGradient } from '../svg/helper.js';\n;\nvar arraySlice = Array.prototype.slice;\nfunction interpolateNumber(p0, p1, percent) {\n    return (p1 - p0) * percent + p0;\n}\nfunction interpolate1DArray(out, p0, p1, percent) {\n    var len = p0.length;\n    for (var i = 0; i < len; i++) {\n        out[i] = interpolateNumber(p0[i], p1[i], percent);\n    }\n    return out;\n}\nfunction interpolate2DArray(out, p0, p1, percent) {\n    var len = p0.length;\n    var len2 = len && p0[0].length;\n    for (var i = 0; i < len; i++) {\n        if (!out[i]) {\n            out[i] = [];\n        }\n        for (var j = 0; j < len2; j++) {\n            out[i][j] = interpolateNumber(p0[i][j], p1[i][j], percent);\n        }\n    }\n    return out;\n}\nfunction add1DArray(out, p0, p1, sign) {\n    var len = p0.length;\n    for (var i = 0; i < len; i++) {\n        out[i] = p0[i] + p1[i] * sign;\n    }\n    return out;\n}\nfunction add2DArray(out, p0, p1, sign) {\n    var len = p0.length;\n    var len2 = len && p0[0].length;\n    for (var i = 0; i < len; i++) {\n        if (!out[i]) {\n            out[i] = [];\n        }\n        for (var j = 0; j < len2; j++) {\n            out[i][j] = p0[i][j] + p1[i][j] * sign;\n        }\n    }\n    return out;\n}\nfunction fillColorStops(val0, val1) {\n    var len0 = val0.length;\n    var len1 = val1.length;\n    var shorterArr = len0 > len1 ? val1 : val0;\n    var shorterLen = Math.min(len0, len1);\n    var last = shorterArr[shorterLen - 1] || { color: [0, 0, 0, 0], offset: 0 };\n    for (var i = shorterLen; i < Math.max(len0, len1); i++) {\n        shorterArr.push({\n            offset: last.offset,\n            color: last.color.slice()\n        });\n    }\n}\nfunction fillArray(val0, val1, arrDim) {\n    var arr0 = val0;\n    var arr1 = val1;\n    if (!arr0.push || !arr1.push) {\n        return;\n    }\n    var arr0Len = arr0.length;\n    var arr1Len = arr1.length;\n    if (arr0Len !== arr1Len) {\n        var isPreviousLarger = arr0Len > arr1Len;\n        if (isPreviousLarger) {\n            arr0.length = arr1Len;\n        }\n        else {\n            for (var i = arr0Len; i < arr1Len; i++) {\n                arr0.push(arrDim === 1 ? arr1[i] : arraySlice.call(arr1[i]));\n            }\n        }\n    }\n    var len2 = arr0[0] && arr0[0].length;\n    for (var i = 0; i < arr0.length; i++) {\n        if (arrDim === 1) {\n            if (isNaN(arr0[i])) {\n                arr0[i] = arr1[i];\n            }\n        }\n        else {\n            for (var j = 0; j < len2; j++) {\n                if (isNaN(arr0[i][j])) {\n                    arr0[i][j] = arr1[i][j];\n                }\n            }\n        }\n    }\n}\nexport function cloneValue(value) {\n    if (isArrayLike(value)) {\n        var len = value.length;\n        if (isArrayLike(value[0])) {\n            var ret = [];\n            for (var i = 0; i < len; i++) {\n                ret.push(arraySlice.call(value[i]));\n            }\n            return ret;\n        }\n        return arraySlice.call(value);\n    }\n    return value;\n}\nfunction rgba2String(rgba) {\n    rgba[0] = Math.floor(rgba[0]) || 0;\n    rgba[1] = Math.floor(rgba[1]) || 0;\n    rgba[2] = Math.floor(rgba[2]) || 0;\n    rgba[3] = rgba[3] == null ? 1 : rgba[3];\n    return 'rgba(' + rgba.join(',') + ')';\n}\nfunction guessArrayDim(value) {\n    return isArrayLike(value && value[0]) ? 2 : 1;\n}\nvar VALUE_TYPE_NUMBER = 0;\nvar VALUE_TYPE_1D_ARRAY = 1;\nvar VALUE_TYPE_2D_ARRAY = 2;\nvar VALUE_TYPE_COLOR = 3;\nvar VALUE_TYPE_LINEAR_GRADIENT = 4;\nvar VALUE_TYPE_RADIAL_GRADIENT = 5;\nvar VALUE_TYPE_UNKOWN = 6;\nfunction isGradientValueType(valType) {\n    return valType === VALUE_TYPE_LINEAR_GRADIENT || valType === VALUE_TYPE_RADIAL_GRADIENT;\n}\nfunction isArrayValueType(valType) {\n    return valType === VALUE_TYPE_1D_ARRAY || valType === VALUE_TYPE_2D_ARRAY;\n}\nvar tmpRgba = [0, 0, 0, 0];\nvar Track = (function () {\n    function Track(propName) {\n        this.keyframes = [];\n        this.discrete = false;\n        this._invalid = false;\n        this._needsSort = false;\n        this._lastFr = 0;\n        this._lastFrP = 0;\n        this.propName = propName;\n    }\n    Track.prototype.isFinished = function () {\n        return this._finished;\n    };\n    Track.prototype.setFinished = function () {\n        this._finished = true;\n        if (this._additiveTrack) {\n            this._additiveTrack.setFinished();\n        }\n    };\n    Track.prototype.needsAnimate = function () {\n        return this.keyframes.length >= 1;\n    };\n    Track.prototype.getAdditiveTrack = function () {\n        return this._additiveTrack;\n    };\n    Track.prototype.addKeyframe = function (time, rawValue, easing) {\n        this._needsSort = true;\n        var keyframes = this.keyframes;\n        var len = keyframes.length;\n        var discrete = false;\n        var valType = VALUE_TYPE_UNKOWN;\n        var value = rawValue;\n        if (isArrayLike(rawValue)) {\n            var arrayDim = guessArrayDim(rawValue);\n            valType = arrayDim;\n            if (arrayDim === 1 && !isNumber(rawValue[0])\n                || arrayDim === 2 && !isNumber(rawValue[0][0])) {\n                discrete = true;\n            }\n        }\n        else {\n            if (isNumber(rawValue) && !eqNaN(rawValue)) {\n                valType = VALUE_TYPE_NUMBER;\n            }\n            else if (isString(rawValue)) {\n                if (!isNaN(+rawValue)) {\n                    valType = VALUE_TYPE_NUMBER;\n                }\n                else {\n                    var colorArray = color.parse(rawValue);\n                    if (colorArray) {\n                        value = colorArray;\n                        valType = VALUE_TYPE_COLOR;\n                    }\n                }\n            }\n            else if (isGradientObject(rawValue)) {\n                var parsedGradient = extend({}, value);\n                parsedGradient.colorStops = map(rawValue.colorStops, function (colorStop) { return ({\n                    offset: colorStop.offset,\n                    color: color.parse(colorStop.color)\n                }); });\n                if (isLinearGradient(rawValue)) {\n                    valType = VALUE_TYPE_LINEAR_GRADIENT;\n                }\n                else if (isRadialGradient(rawValue)) {\n                    valType = VALUE_TYPE_RADIAL_GRADIENT;\n                }\n                value = parsedGradient;\n            }\n        }\n        if (len === 0) {\n            this.valType = valType;\n        }\n        else if (valType !== this.valType || valType === VALUE_TYPE_UNKOWN) {\n            discrete = true;\n        }\n        this.discrete = this.discrete || discrete;\n        var kf = {\n            time: time,\n            value: value,\n            rawValue: rawValue,\n            percent: 0\n        };\n        if (easing) {\n            kf.easing = easing;\n            kf.easingFunc = isFunction(easing)\n                ? easing\n                : easingFuncs[easing] || createCubicEasingFunc(easing);\n        }\n        keyframes.push(kf);\n        return kf;\n    };\n    Track.prototype.prepare = function (maxTime, additiveTrack) {\n        var kfs = this.keyframes;\n        if (this._needsSort) {\n            kfs.sort(function (a, b) {\n                return a.time - b.time;\n            });\n        }\n        var valType = this.valType;\n        var kfsLen = kfs.length;\n        var lastKf = kfs[kfsLen - 1];\n        var isDiscrete = this.discrete;\n        var isArr = isArrayValueType(valType);\n        var isGradient = isGradientValueType(valType);\n        for (var i = 0; i < kfsLen; i++) {\n            var kf = kfs[i];\n            var value = kf.value;\n            var lastValue = lastKf.value;\n            kf.percent = kf.time / maxTime;\n            if (!isDiscrete) {\n                if (isArr && i !== kfsLen - 1) {\n                    fillArray(value, lastValue, valType);\n                }\n                else if (isGradient) {\n                    fillColorStops(value.colorStops, lastValue.colorStops);\n                }\n            }\n        }\n        if (!isDiscrete\n            && valType !== VALUE_TYPE_RADIAL_GRADIENT\n            && additiveTrack\n            && this.needsAnimate()\n            && additiveTrack.needsAnimate()\n            && valType === additiveTrack.valType\n            && !additiveTrack._finished) {\n            this._additiveTrack = additiveTrack;\n            var startValue = kfs[0].value;\n            for (var i = 0; i < kfsLen; i++) {\n                if (valType === VALUE_TYPE_NUMBER) {\n                    kfs[i].additiveValue = kfs[i].value - startValue;\n                }\n                else if (valType === VALUE_TYPE_COLOR) {\n                    kfs[i].additiveValue =\n                        add1DArray([], kfs[i].value, startValue, -1);\n                }\n                else if (isArrayValueType(valType)) {\n                    kfs[i].additiveValue = valType === VALUE_TYPE_1D_ARRAY\n                        ? add1DArray([], kfs[i].value, startValue, -1)\n                        : add2DArray([], kfs[i].value, startValue, -1);\n                }\n            }\n        }\n    };\n    Track.prototype.step = function (target, percent) {\n        if (this._finished) {\n            return;\n        }\n        if (this._additiveTrack && this._additiveTrack._finished) {\n            this._additiveTrack = null;\n        }\n        var isAdditive = this._additiveTrack != null;\n        var valueKey = isAdditive ? 'additiveValue' : 'value';\n        var valType = this.valType;\n        var keyframes = this.keyframes;\n        var kfsNum = keyframes.length;\n        var propName = this.propName;\n        var isValueColor = valType === VALUE_TYPE_COLOR;\n        var frameIdx;\n        var lastFrame = this._lastFr;\n        var mathMin = Math.min;\n        var frame;\n        var nextFrame;\n        if (kfsNum === 1) {\n            frame = nextFrame = keyframes[0];\n        }\n        else {\n            if (percent < 0) {\n                frameIdx = 0;\n            }\n            else if (percent < this._lastFrP) {\n                var start = mathMin(lastFrame + 1, kfsNum - 1);\n                for (frameIdx = start; frameIdx >= 0; frameIdx--) {\n                    if (keyframes[frameIdx].percent <= percent) {\n                        break;\n                    }\n                }\n                frameIdx = mathMin(frameIdx, kfsNum - 2);\n            }\n            else {\n                for (frameIdx = lastFrame; frameIdx < kfsNum; frameIdx++) {\n                    if (keyframes[frameIdx].percent > percent) {\n                        break;\n                    }\n                }\n                frameIdx = mathMin(frameIdx - 1, kfsNum - 2);\n            }\n            nextFrame = keyframes[frameIdx + 1];\n            frame = keyframes[frameIdx];\n        }\n        if (!(frame && nextFrame)) {\n            return;\n        }\n        this._lastFr = frameIdx;\n        this._lastFrP = percent;\n        var interval = (nextFrame.percent - frame.percent);\n        var w = interval === 0 ? 1 : mathMin((percent - frame.percent) / interval, 1);\n        if (nextFrame.easingFunc) {\n            w = nextFrame.easingFunc(w);\n        }\n        var targetArr = isAdditive ? this._additiveValue\n            : (isValueColor ? tmpRgba : target[propName]);\n        if ((isArrayValueType(valType) || isValueColor) && !targetArr) {\n            targetArr = this._additiveValue = [];\n        }\n        if (this.discrete) {\n            target[propName] = w < 1 ? frame.rawValue : nextFrame.rawValue;\n        }\n        else if (isArrayValueType(valType)) {\n            valType === VALUE_TYPE_1D_ARRAY\n                ? interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w)\n                : interpolate2DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n        }\n        else if (isGradientValueType(valType)) {\n            var val = frame[valueKey];\n            var nextVal_1 = nextFrame[valueKey];\n            var isLinearGradient_1 = valType === VALUE_TYPE_LINEAR_GRADIENT;\n            target[propName] = {\n                type: isLinearGradient_1 ? 'linear' : 'radial',\n                x: interpolateNumber(val.x, nextVal_1.x, w),\n                y: interpolateNumber(val.y, nextVal_1.y, w),\n                colorStops: map(val.colorStops, function (colorStop, idx) {\n                    var nextColorStop = nextVal_1.colorStops[idx];\n                    return {\n                        offset: interpolateNumber(colorStop.offset, nextColorStop.offset, w),\n                        color: rgba2String(interpolate1DArray([], colorStop.color, nextColorStop.color, w))\n                    };\n                }),\n                global: nextVal_1.global\n            };\n            if (isLinearGradient_1) {\n                target[propName].x2 = interpolateNumber(val.x2, nextVal_1.x2, w);\n                target[propName].y2 = interpolateNumber(val.y2, nextVal_1.y2, w);\n            }\n            else {\n                target[propName].r = interpolateNumber(val.r, nextVal_1.r, w);\n            }\n        }\n        else if (isValueColor) {\n            interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n            if (!isAdditive) {\n                target[propName] = rgba2String(targetArr);\n            }\n        }\n        else {\n            var value = interpolateNumber(frame[valueKey], nextFrame[valueKey], w);\n            if (isAdditive) {\n                this._additiveValue = value;\n            }\n            else {\n                target[propName] = value;\n            }\n        }\n        if (isAdditive) {\n            this._addToTarget(target);\n        }\n    };\n    Track.prototype._addToTarget = function (target) {\n        var valType = this.valType;\n        var propName = this.propName;\n        var additiveValue = this._additiveValue;\n        if (valType === VALUE_TYPE_NUMBER) {\n            target[propName] = target[propName] + additiveValue;\n        }\n        else if (valType === VALUE_TYPE_COLOR) {\n            color.parse(target[propName], tmpRgba);\n            add1DArray(tmpRgba, tmpRgba, additiveValue, 1);\n            target[propName] = rgba2String(tmpRgba);\n        }\n        else if (valType === VALUE_TYPE_1D_ARRAY) {\n            add1DArray(target[propName], target[propName], additiveValue, 1);\n        }\n        else if (valType === VALUE_TYPE_2D_ARRAY) {\n            add2DArray(target[propName], target[propName], additiveValue, 1);\n        }\n    };\n    return Track;\n}());\nvar Animator = (function () {\n    function Animator(target, loop, allowDiscreteAnimation, additiveTo) {\n        this._tracks = {};\n        this._trackKeys = [];\n        this._maxTime = 0;\n        this._started = 0;\n        this._clip = null;\n        this._target = target;\n        this._loop = loop;\n        if (loop && additiveTo) {\n            logError('Can\\' use additive animation on looped animation.');\n            return;\n        }\n        this._additiveAnimators = additiveTo;\n        this._allowDiscrete = allowDiscreteAnimation;\n    }\n    Animator.prototype.getMaxTime = function () {\n        return this._maxTime;\n    };\n    Animator.prototype.getDelay = function () {\n        return this._delay;\n    };\n    Animator.prototype.getLoop = function () {\n        return this._loop;\n    };\n    Animator.prototype.getTarget = function () {\n        return this._target;\n    };\n    Animator.prototype.changeTarget = function (target) {\n        this._target = target;\n    };\n    Animator.prototype.when = function (time, props, easing) {\n        return this.whenWithKeys(time, props, keys(props), easing);\n    };\n    Animator.prototype.whenWithKeys = function (time, props, propNames, easing) {\n        var tracks = this._tracks;\n        for (var i = 0; i < propNames.length; i++) {\n            var propName = propNames[i];\n            var track = tracks[propName];\n            if (!track) {\n                track = tracks[propName] = new Track(propName);\n                var initialValue = void 0;\n                var additiveTrack = this._getAdditiveTrack(propName);\n                if (additiveTrack) {\n                    var addtiveTrackKfs = additiveTrack.keyframes;\n                    var lastFinalKf = addtiveTrackKfs[addtiveTrackKfs.length - 1];\n                    initialValue = lastFinalKf && lastFinalKf.value;\n                    if (additiveTrack.valType === VALUE_TYPE_COLOR && initialValue) {\n                        initialValue = rgba2String(initialValue);\n                    }\n                }\n                else {\n                    initialValue = this._target[propName];\n                }\n                if (initialValue == null) {\n                    continue;\n                }\n                if (time > 0) {\n                    track.addKeyframe(0, cloneValue(initialValue), easing);\n                }\n                this._trackKeys.push(propName);\n            }\n            track.addKeyframe(time, cloneValue(props[propName]), easing);\n        }\n        this._maxTime = Math.max(this._maxTime, time);\n        return this;\n    };\n    Animator.prototype.pause = function () {\n        this._clip.pause();\n        this._paused = true;\n    };\n    Animator.prototype.resume = function () {\n        this._clip.resume();\n        this._paused = false;\n    };\n    Animator.prototype.isPaused = function () {\n        return !!this._paused;\n    };\n    Animator.prototype.duration = function (duration) {\n        this._maxTime = duration;\n        this._force = true;\n        return this;\n    };\n    Animator.prototype._doneCallback = function () {\n        this._setTracksFinished();\n        this._clip = null;\n        var doneList = this._doneCbs;\n        if (doneList) {\n            var len = doneList.length;\n            for (var i = 0; i < len; i++) {\n                doneList[i].call(this);\n            }\n        }\n    };\n    Animator.prototype._abortedCallback = function () {\n        this._setTracksFinished();\n        var animation = this.animation;\n        var abortedList = this._abortedCbs;\n        if (animation) {\n            animation.removeClip(this._clip);\n        }\n        this._clip = null;\n        if (abortedList) {\n            for (var i = 0; i < abortedList.length; i++) {\n                abortedList[i].call(this);\n            }\n        }\n    };\n    Animator.prototype._setTracksFinished = function () {\n        var tracks = this._tracks;\n        var tracksKeys = this._trackKeys;\n        for (var i = 0; i < tracksKeys.length; i++) {\n            tracks[tracksKeys[i]].setFinished();\n        }\n    };\n    Animator.prototype._getAdditiveTrack = function (trackName) {\n        var additiveTrack;\n        var additiveAnimators = this._additiveAnimators;\n        if (additiveAnimators) {\n            for (var i = 0; i < additiveAnimators.length; i++) {\n                var track = additiveAnimators[i].getTrack(trackName);\n                if (track) {\n                    additiveTrack = track;\n                }\n            }\n        }\n        return additiveTrack;\n    };\n    Animator.prototype.start = function (easing) {\n        if (this._started > 0) {\n            return;\n        }\n        this._started = 1;\n        var self = this;\n        var tracks = [];\n        var maxTime = this._maxTime || 0;\n        for (var i = 0; i < this._trackKeys.length; i++) {\n            var propName = this._trackKeys[i];\n            var track = this._tracks[propName];\n            var additiveTrack = this._getAdditiveTrack(propName);\n            var kfs = track.keyframes;\n            var kfsNum = kfs.length;\n            track.prepare(maxTime, additiveTrack);\n            if (track.needsAnimate()) {\n                if (!this._allowDiscrete && track.discrete) {\n                    var lastKf = kfs[kfsNum - 1];\n                    if (lastKf) {\n                        self._target[track.propName] = lastKf.rawValue;\n                    }\n                    track.setFinished();\n                }\n                else {\n                    tracks.push(track);\n                }\n            }\n        }\n        if (tracks.length || this._force) {\n            var clip = new Clip({\n                life: maxTime,\n                loop: this._loop,\n                delay: this._delay || 0,\n                onframe: function (percent) {\n                    self._started = 2;\n                    var additiveAnimators = self._additiveAnimators;\n                    if (additiveAnimators) {\n                        var stillHasAdditiveAnimator = false;\n                        for (var i = 0; i < additiveAnimators.length; i++) {\n                            if (additiveAnimators[i]._clip) {\n                                stillHasAdditiveAnimator = true;\n                                break;\n                            }\n                        }\n                        if (!stillHasAdditiveAnimator) {\n                            self._additiveAnimators = null;\n                        }\n                    }\n                    for (var i = 0; i < tracks.length; i++) {\n                        tracks[i].step(self._target, percent);\n                    }\n                    var onframeList = self._onframeCbs;\n                    if (onframeList) {\n                        for (var i = 0; i < onframeList.length; i++) {\n                            onframeList[i](self._target, percent);\n                        }\n                    }\n                },\n                ondestroy: function () {\n                    self._doneCallback();\n                }\n            });\n            this._clip = clip;\n            if (this.animation) {\n                this.animation.addClip(clip);\n            }\n            if (easing) {\n                clip.setEasing(easing);\n            }\n        }\n        else {\n            this._doneCallback();\n        }\n        return this;\n    };\n    Animator.prototype.stop = function (forwardToLast) {\n        if (!this._clip) {\n            return;\n        }\n        var clip = this._clip;\n        if (forwardToLast) {\n            clip.onframe(1);\n        }\n        this._abortedCallback();\n    };\n    Animator.prototype.delay = function (time) {\n        this._delay = time;\n        return this;\n    };\n    Animator.prototype.during = function (cb) {\n        if (cb) {\n            if (!this._onframeCbs) {\n                this._onframeCbs = [];\n            }\n            this._onframeCbs.push(cb);\n        }\n        return this;\n    };\n    Animator.prototype.done = function (cb) {\n        if (cb) {\n            if (!this._doneCbs) {\n                this._doneCbs = [];\n            }\n            this._doneCbs.push(cb);\n        }\n        return this;\n    };\n    Animator.prototype.aborted = function (cb) {\n        if (cb) {\n            if (!this._abortedCbs) {\n                this._abortedCbs = [];\n            }\n            this._abortedCbs.push(cb);\n        }\n        return this;\n    };\n    Animator.prototype.getClip = function () {\n        return this._clip;\n    };\n    Animator.prototype.getTrack = function (propName) {\n        return this._tracks[propName];\n    };\n    Animator.prototype.getTracks = function () {\n        var _this = this;\n        return map(this._trackKeys, function (key) { return _this._tracks[key]; });\n    };\n    Animator.prototype.stopTracks = function (propNames, forwardToLast) {\n        if (!propNames.length || !this._clip) {\n            return true;\n        }\n        var tracks = this._tracks;\n        var tracksKeys = this._trackKeys;\n        for (var i = 0; i < propNames.length; i++) {\n            var track = tracks[propNames[i]];\n            if (track && !track.isFinished()) {\n                if (forwardToLast) {\n                    track.step(this._target, 1);\n                }\n                else if (this._started === 1) {\n                    track.step(this._target, 0);\n                }\n                track.setFinished();\n            }\n        }\n        var allAborted = true;\n        for (var i = 0; i < tracksKeys.length; i++) {\n            if (!tracks[tracksKeys[i]].isFinished()) {\n                allAborted = false;\n                break;\n            }\n        }\n        if (allAborted) {\n            this._abortedCallback();\n        }\n        return allAborted;\n    };\n    Animator.prototype.saveTo = function (target, trackKeys, firstOrLast) {\n        if (!target) {\n            return;\n        }\n        trackKeys = trackKeys || this._trackKeys;\n        for (var i = 0; i < trackKeys.length; i++) {\n            var propName = trackKeys[i];\n            var track = this._tracks[propName];\n            if (!track || track.isFinished()) {\n                continue;\n            }\n            var kfs = track.keyframes;\n            var kf = kfs[firstOrLast ? 0 : kfs.length - 1];\n            if (kf) {\n                target[propName] = cloneValue(kf.rawValue);\n            }\n        }\n    };\n    Animator.prototype.__changeFinalValue = function (finalProps, trackKeys) {\n        trackKeys = trackKeys || keys(finalProps);\n        for (var i = 0; i < trackKeys.length; i++) {\n            var propName = trackKeys[i];\n            var track = this._tracks[propName];\n            if (!track) {\n                continue;\n            }\n            var kfs = track.keyframes;\n            if (kfs.length > 1) {\n                var lastKf = kfs.pop();\n                track.addKeyframe(lastKf.time, finalProps[propName]);\n                track.prepare(this._maxTime, track.getAdditiveTrack());\n            }\n        }\n    };\n    return Animator;\n}());\nexport default Animator;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAC5B,OAAO,KAAKC,KAAK,MAAM,kBAAkB;AACzC,SAASC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,iBAAiB;AACnI,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,qBAAqB,QAAQ,kBAAkB;AACxD,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,kBAAkB;AACrE;AACA,IAAIC,UAAU,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK;AACtC,SAASC,iBAAiBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAE;EACxC,OAAO,CAACD,EAAE,GAAGD,EAAE,IAAIE,OAAO,GAAGF,EAAE;AACnC;AACA,SAASG,kBAAkBA,CAACC,GAAG,EAAEJ,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAE;EAC9C,IAAIG,GAAG,GAAGL,EAAE,CAACM,MAAM;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAC1BH,GAAG,CAACG,CAAC,CAAC,GAAGR,iBAAiB,CAACC,EAAE,CAACO,CAAC,CAAC,EAAEN,EAAE,CAACM,CAAC,CAAC,EAAEL,OAAO,CAAC;EACrD;EACA,OAAOE,GAAG;AACd;AACA,SAASI,kBAAkBA,CAACJ,GAAG,EAAEJ,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAE;EAC9C,IAAIG,GAAG,GAAGL,EAAE,CAACM,MAAM;EACnB,IAAIG,IAAI,GAAGJ,GAAG,IAAIL,EAAE,CAAC,CAAC,CAAC,CAACM,MAAM;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAC1B,IAAI,CAACH,GAAG,CAACG,CAAC,CAAC,EAAE;MACTH,GAAG,CAACG,CAAC,CAAC,GAAG,EAAE;IACf;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,EAAEC,CAAC,EAAE,EAAE;MAC3BN,GAAG,CAACG,CAAC,CAAC,CAACG,CAAC,CAAC,GAAGX,iBAAiB,CAACC,EAAE,CAACO,CAAC,CAAC,CAACG,CAAC,CAAC,EAAET,EAAE,CAACM,CAAC,CAAC,CAACG,CAAC,CAAC,EAAER,OAAO,CAAC;IAC9D;EACJ;EACA,OAAOE,GAAG;AACd;AACA,SAASO,UAAUA,CAACP,GAAG,EAAEJ,EAAE,EAAEC,EAAE,EAAEW,IAAI,EAAE;EACnC,IAAIP,GAAG,GAAGL,EAAE,CAACM,MAAM;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAC1BH,GAAG,CAACG,CAAC,CAAC,GAAGP,EAAE,CAACO,CAAC,CAAC,GAAGN,EAAE,CAACM,CAAC,CAAC,GAAGK,IAAI;EACjC;EACA,OAAOR,GAAG;AACd;AACA,SAASS,UAAUA,CAACT,GAAG,EAAEJ,EAAE,EAAEC,EAAE,EAAEW,IAAI,EAAE;EACnC,IAAIP,GAAG,GAAGL,EAAE,CAACM,MAAM;EACnB,IAAIG,IAAI,GAAGJ,GAAG,IAAIL,EAAE,CAAC,CAAC,CAAC,CAACM,MAAM;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAC1B,IAAI,CAACH,GAAG,CAACG,CAAC,CAAC,EAAE;MACTH,GAAG,CAACG,CAAC,CAAC,GAAG,EAAE;IACf;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,EAAEC,CAAC,EAAE,EAAE;MAC3BN,GAAG,CAACG,CAAC,CAAC,CAACG,CAAC,CAAC,GAAGV,EAAE,CAACO,CAAC,CAAC,CAACG,CAAC,CAAC,GAAGT,EAAE,CAACM,CAAC,CAAC,CAACG,CAAC,CAAC,GAAGE,IAAI;IAC1C;EACJ;EACA,OAAOR,GAAG;AACd;AACA,SAASU,cAAcA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAChC,IAAIC,IAAI,GAAGF,IAAI,CAACT,MAAM;EACtB,IAAIY,IAAI,GAAGF,IAAI,CAACV,MAAM;EACtB,IAAIa,UAAU,GAAGF,IAAI,GAAGC,IAAI,GAAGF,IAAI,GAAGD,IAAI;EAC1C,IAAIK,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACL,IAAI,EAAEC,IAAI,CAAC;EACrC,IAAIK,IAAI,GAAGJ,UAAU,CAACC,UAAU,GAAG,CAAC,CAAC,IAAI;IAAExC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAE4C,MAAM,EAAE;EAAE,CAAC;EAC3E,KAAK,IAAIjB,CAAC,GAAGa,UAAU,EAAEb,CAAC,GAAGc,IAAI,CAACI,GAAG,CAACR,IAAI,EAAEC,IAAI,CAAC,EAAEX,CAAC,EAAE,EAAE;IACpDY,UAAU,CAACO,IAAI,CAAC;MACZF,MAAM,EAAED,IAAI,CAACC,MAAM;MACnB5C,KAAK,EAAE2C,IAAI,CAAC3C,KAAK,CAACkB,KAAK,CAAC;IAC5B,CAAC,CAAC;EACN;AACJ;AACA,SAAS6B,SAASA,CAACZ,IAAI,EAAEC,IAAI,EAAEY,MAAM,EAAE;EACnC,IAAIC,IAAI,GAAGd,IAAI;EACf,IAAIe,IAAI,GAAGd,IAAI;EACf,IAAI,CAACa,IAAI,CAACH,IAAI,IAAI,CAACI,IAAI,CAACJ,IAAI,EAAE;IAC1B;EACJ;EACA,IAAIK,OAAO,GAAGF,IAAI,CAACvB,MAAM;EACzB,IAAI0B,OAAO,GAAGF,IAAI,CAACxB,MAAM;EACzB,IAAIyB,OAAO,KAAKC,OAAO,EAAE;IACrB,IAAIC,gBAAgB,GAAGF,OAAO,GAAGC,OAAO;IACxC,IAAIC,gBAAgB,EAAE;MAClBJ,IAAI,CAACvB,MAAM,GAAG0B,OAAO;IACzB,CAAC,MACI;MACD,KAAK,IAAIzB,CAAC,GAAGwB,OAAO,EAAExB,CAAC,GAAGyB,OAAO,EAAEzB,CAAC,EAAE,EAAE;QACpCsB,IAAI,CAACH,IAAI,CAACE,MAAM,KAAK,CAAC,GAAGE,IAAI,CAACvB,CAAC,CAAC,GAAGZ,UAAU,CAACuC,IAAI,CAACJ,IAAI,CAACvB,CAAC,CAAC,CAAC,CAAC;MAChE;IACJ;EACJ;EACA,IAAIE,IAAI,GAAGoB,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACvB,MAAM;EACpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,IAAI,CAACvB,MAAM,EAAEC,CAAC,EAAE,EAAE;IAClC,IAAIqB,MAAM,KAAK,CAAC,EAAE;MACd,IAAIO,KAAK,CAACN,IAAI,CAACtB,CAAC,CAAC,CAAC,EAAE;QAChBsB,IAAI,CAACtB,CAAC,CAAC,GAAGuB,IAAI,CAACvB,CAAC,CAAC;MACrB;IACJ,CAAC,MACI;MACD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,EAAEC,CAAC,EAAE,EAAE;QAC3B,IAAIyB,KAAK,CAACN,IAAI,CAACtB,CAAC,CAAC,CAACG,CAAC,CAAC,CAAC,EAAE;UACnBmB,IAAI,CAACtB,CAAC,CAAC,CAACG,CAAC,CAAC,GAAGoB,IAAI,CAACvB,CAAC,CAAC,CAACG,CAAC,CAAC;QAC3B;MACJ;IACJ;EACJ;AACJ;AACA,OAAO,SAAS0B,UAAUA,CAACC,KAAK,EAAE;EAC9B,IAAItD,WAAW,CAACsD,KAAK,CAAC,EAAE;IACpB,IAAIhC,GAAG,GAAGgC,KAAK,CAAC/B,MAAM;IACtB,IAAIvB,WAAW,CAACsD,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,IAAIC,GAAG,GAAG,EAAE;MACZ,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAC1B+B,GAAG,CAACZ,IAAI,CAAC/B,UAAU,CAACuC,IAAI,CAACG,KAAK,CAAC9B,CAAC,CAAC,CAAC,CAAC;MACvC;MACA,OAAO+B,GAAG;IACd;IACA,OAAO3C,UAAU,CAACuC,IAAI,CAACG,KAAK,CAAC;EACjC;EACA,OAAOA,KAAK;AAChB;AACA,SAASE,WAAWA,CAACC,IAAI,EAAE;EACvBA,IAAI,CAAC,CAAC,CAAC,GAAGnB,IAAI,CAACoB,KAAK,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EAClCA,IAAI,CAAC,CAAC,CAAC,GAAGnB,IAAI,CAACoB,KAAK,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EAClCA,IAAI,CAAC,CAAC,CAAC,GAAGnB,IAAI,CAACoB,KAAK,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EAClCA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC;EACvC,OAAO,OAAO,GAAGA,IAAI,CAACE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;AACzC;AACA,SAASC,aAAaA,CAACN,KAAK,EAAE;EAC1B,OAAOtD,WAAW,CAACsD,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACjD;AACA,IAAIO,iBAAiB,GAAG,CAAC;AACzB,IAAIC,mBAAmB,GAAG,CAAC;AAC3B,IAAIC,mBAAmB,GAAG,CAAC;AAC3B,IAAIC,gBAAgB,GAAG,CAAC;AACxB,IAAIC,0BAA0B,GAAG,CAAC;AAClC,IAAIC,0BAA0B,GAAG,CAAC;AAClC,IAAIC,iBAAiB,GAAG,CAAC;AACzB,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAClC,OAAOA,OAAO,KAAKJ,0BAA0B,IAAII,OAAO,KAAKH,0BAA0B;AAC3F;AACA,SAASI,gBAAgBA,CAACD,OAAO,EAAE;EAC/B,OAAOA,OAAO,KAAKP,mBAAmB,IAAIO,OAAO,KAAKN,mBAAmB;AAC7E;AACA,IAAIQ,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1B,IAAIC,KAAK,GAAI,YAAY;EACrB,SAASA,KAAKA,CAACC,QAAQ,EAAE;IACrB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACN,QAAQ,GAAGA,QAAQ;EAC5B;EACAD,KAAK,CAAC1D,SAAS,CAACkE,UAAU,GAAG,YAAY;IACrC,OAAO,IAAI,CAACC,SAAS;EACzB,CAAC;EACDT,KAAK,CAAC1D,SAAS,CAACoE,WAAW,GAAG,YAAY;IACtC,IAAI,CAACD,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACE,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACD,WAAW,CAAC,CAAC;IACrC;EACJ,CAAC;EACDV,KAAK,CAAC1D,SAAS,CAACsE,YAAY,GAAG,YAAY;IACvC,OAAO,IAAI,CAACV,SAAS,CAACnD,MAAM,IAAI,CAAC;EACrC,CAAC;EACDiD,KAAK,CAAC1D,SAAS,CAACuE,gBAAgB,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACF,cAAc;EAC9B,CAAC;EACDX,KAAK,CAAC1D,SAAS,CAACwE,WAAW,GAAG,UAAUC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IAC5D,IAAI,CAACZ,UAAU,GAAG,IAAI;IACtB,IAAIH,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIpD,GAAG,GAAGoD,SAAS,CAACnD,MAAM;IAC1B,IAAIoD,QAAQ,GAAG,KAAK;IACpB,IAAIN,OAAO,GAAGF,iBAAiB;IAC/B,IAAIb,KAAK,GAAGkC,QAAQ;IACpB,IAAIxF,WAAW,CAACwF,QAAQ,CAAC,EAAE;MACvB,IAAIE,QAAQ,GAAG9B,aAAa,CAAC4B,QAAQ,CAAC;MACtCnB,OAAO,GAAGqB,QAAQ;MAClB,IAAIA,QAAQ,KAAK,CAAC,IAAI,CAACvF,QAAQ,CAACqF,QAAQ,CAAC,CAAC,CAAC,CAAC,IACrCE,QAAQ,KAAK,CAAC,IAAI,CAACvF,QAAQ,CAACqF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChDb,QAAQ,GAAG,IAAI;MACnB;IACJ,CAAC,MACI;MACD,IAAIxE,QAAQ,CAACqF,QAAQ,CAAC,IAAI,CAAC1F,KAAK,CAAC0F,QAAQ,CAAC,EAAE;QACxCnB,OAAO,GAAGR,iBAAiB;MAC/B,CAAC,MACI,IAAIzD,QAAQ,CAACoF,QAAQ,CAAC,EAAE;QACzB,IAAI,CAACpC,KAAK,CAAC,CAACoC,QAAQ,CAAC,EAAE;UACnBnB,OAAO,GAAGR,iBAAiB;QAC/B,CAAC,MACI;UACD,IAAI8B,UAAU,GAAG9F,KAAK,CAAC+F,KAAK,CAACJ,QAAQ,CAAC;UACtC,IAAIG,UAAU,EAAE;YACZrC,KAAK,GAAGqC,UAAU;YAClBtB,OAAO,GAAGL,gBAAgB;UAC9B;QACJ;MACJ,CAAC,MACI,IAAI9D,gBAAgB,CAACsF,QAAQ,CAAC,EAAE;QACjC,IAAIK,cAAc,GAAG9F,MAAM,CAAC,CAAC,CAAC,EAAEuD,KAAK,CAAC;QACtCuC,cAAc,CAACC,UAAU,GAAGvF,GAAG,CAACiF,QAAQ,CAACM,UAAU,EAAE,UAAUC,SAAS,EAAE;UAAE,OAAQ;YAChFtD,MAAM,EAAEsD,SAAS,CAACtD,MAAM;YACxB5C,KAAK,EAAEA,KAAK,CAAC+F,KAAK,CAACG,SAAS,CAAClG,KAAK;UACtC,CAAC;QAAG,CAAC,CAAC;QACN,IAAIa,gBAAgB,CAAC8E,QAAQ,CAAC,EAAE;UAC5BnB,OAAO,GAAGJ,0BAA0B;QACxC,CAAC,MACI,IAAItD,gBAAgB,CAAC6E,QAAQ,CAAC,EAAE;UACjCnB,OAAO,GAAGH,0BAA0B;QACxC;QACAZ,KAAK,GAAGuC,cAAc;MAC1B;IACJ;IACA,IAAIvE,GAAG,KAAK,CAAC,EAAE;MACX,IAAI,CAAC+C,OAAO,GAAGA,OAAO;IAC1B,CAAC,MACI,IAAIA,OAAO,KAAK,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAKF,iBAAiB,EAAE;MAChEQ,QAAQ,GAAG,IAAI;IACnB;IACA,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAIA,QAAQ;IACzC,IAAIqB,EAAE,GAAG;MACLT,IAAI,EAAEA,IAAI;MACVjC,KAAK,EAAEA,KAAK;MACZkC,QAAQ,EAAEA,QAAQ;MAClBrE,OAAO,EAAE;IACb,CAAC;IACD,IAAIsE,MAAM,EAAE;MACRO,EAAE,CAACP,MAAM,GAAGA,MAAM;MAClBO,EAAE,CAACC,UAAU,GAAGhG,UAAU,CAACwF,MAAM,CAAC,GAC5BA,MAAM,GACNjF,WAAW,CAACiF,MAAM,CAAC,IAAIhF,qBAAqB,CAACgF,MAAM,CAAC;IAC9D;IACAf,SAAS,CAAC/B,IAAI,CAACqD,EAAE,CAAC;IAClB,OAAOA,EAAE;EACb,CAAC;EACDxB,KAAK,CAAC1D,SAAS,CAACoF,OAAO,GAAG,UAAUC,OAAO,EAAEC,aAAa,EAAE;IACxD,IAAIC,GAAG,GAAG,IAAI,CAAC3B,SAAS;IACxB,IAAI,IAAI,CAACG,UAAU,EAAE;MACjBwB,GAAG,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QACrB,OAAOD,CAAC,CAAChB,IAAI,GAAGiB,CAAC,CAACjB,IAAI;MAC1B,CAAC,CAAC;IACN;IACA,IAAIlB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIoC,MAAM,GAAGJ,GAAG,CAAC9E,MAAM;IACvB,IAAImF,MAAM,GAAGL,GAAG,CAACI,MAAM,GAAG,CAAC,CAAC;IAC5B,IAAIE,UAAU,GAAG,IAAI,CAAChC,QAAQ;IAC9B,IAAIiC,KAAK,GAAGtC,gBAAgB,CAACD,OAAO,CAAC;IACrC,IAAIwC,UAAU,GAAGzC,mBAAmB,CAACC,OAAO,CAAC;IAC7C,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,MAAM,EAAEjF,CAAC,EAAE,EAAE;MAC7B,IAAIwE,EAAE,GAAGK,GAAG,CAAC7E,CAAC,CAAC;MACf,IAAI8B,KAAK,GAAG0C,EAAE,CAAC1C,KAAK;MACpB,IAAIwD,SAAS,GAAGJ,MAAM,CAACpD,KAAK;MAC5B0C,EAAE,CAAC7E,OAAO,GAAG6E,EAAE,CAACT,IAAI,GAAGY,OAAO;MAC9B,IAAI,CAACQ,UAAU,EAAE;QACb,IAAIC,KAAK,IAAIpF,CAAC,KAAKiF,MAAM,GAAG,CAAC,EAAE;UAC3B7D,SAAS,CAACU,KAAK,EAAEwD,SAAS,EAAEzC,OAAO,CAAC;QACxC,CAAC,MACI,IAAIwC,UAAU,EAAE;UACjB9E,cAAc,CAACuB,KAAK,CAACwC,UAAU,EAAEgB,SAAS,CAAChB,UAAU,CAAC;QAC1D;MACJ;IACJ;IACA,IAAI,CAACa,UAAU,IACRtC,OAAO,KAAKH,0BAA0B,IACtCkC,aAAa,IACb,IAAI,CAAChB,YAAY,CAAC,CAAC,IACnBgB,aAAa,CAAChB,YAAY,CAAC,CAAC,IAC5Bf,OAAO,KAAK+B,aAAa,CAAC/B,OAAO,IACjC,CAAC+B,aAAa,CAACnB,SAAS,EAAE;MAC7B,IAAI,CAACE,cAAc,GAAGiB,aAAa;MACnC,IAAIW,UAAU,GAAGV,GAAG,CAAC,CAAC,CAAC,CAAC/C,KAAK;MAC7B,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,MAAM,EAAEjF,CAAC,EAAE,EAAE;QAC7B,IAAI6C,OAAO,KAAKR,iBAAiB,EAAE;UAC/BwC,GAAG,CAAC7E,CAAC,CAAC,CAACwF,aAAa,GAAGX,GAAG,CAAC7E,CAAC,CAAC,CAAC8B,KAAK,GAAGyD,UAAU;QACpD,CAAC,MACI,IAAI1C,OAAO,KAAKL,gBAAgB,EAAE;UACnCqC,GAAG,CAAC7E,CAAC,CAAC,CAACwF,aAAa,GAChBpF,UAAU,CAAC,EAAE,EAAEyE,GAAG,CAAC7E,CAAC,CAAC,CAAC8B,KAAK,EAAEyD,UAAU,EAAE,CAAC,CAAC,CAAC;QACpD,CAAC,MACI,IAAIzC,gBAAgB,CAACD,OAAO,CAAC,EAAE;UAChCgC,GAAG,CAAC7E,CAAC,CAAC,CAACwF,aAAa,GAAG3C,OAAO,KAAKP,mBAAmB,GAChDlC,UAAU,CAAC,EAAE,EAAEyE,GAAG,CAAC7E,CAAC,CAAC,CAAC8B,KAAK,EAAEyD,UAAU,EAAE,CAAC,CAAC,CAAC,GAC5CjF,UAAU,CAAC,EAAE,EAAEuE,GAAG,CAAC7E,CAAC,CAAC,CAAC8B,KAAK,EAAEyD,UAAU,EAAE,CAAC,CAAC,CAAC;QACtD;MACJ;IACJ;EACJ,CAAC;EACDvC,KAAK,CAAC1D,SAAS,CAACmG,IAAI,GAAG,UAAUC,MAAM,EAAE/F,OAAO,EAAE;IAC9C,IAAI,IAAI,CAAC8D,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,IAAI,CAACE,cAAc,IAAI,IAAI,CAACA,cAAc,CAACF,SAAS,EAAE;MACtD,IAAI,CAACE,cAAc,GAAG,IAAI;IAC9B;IACA,IAAIgC,UAAU,GAAG,IAAI,CAAChC,cAAc,IAAI,IAAI;IAC5C,IAAIiC,QAAQ,GAAGD,UAAU,GAAG,eAAe,GAAG,OAAO;IACrD,IAAI9C,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIK,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI2C,MAAM,GAAG3C,SAAS,CAACnD,MAAM;IAC7B,IAAIkD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAI6C,YAAY,GAAGjD,OAAO,KAAKL,gBAAgB;IAC/C,IAAIuD,QAAQ;IACZ,IAAIC,SAAS,GAAG,IAAI,CAAC1C,OAAO;IAC5B,IAAI2C,OAAO,GAAGnF,IAAI,CAACC,GAAG;IACtB,IAAImF,KAAK;IACT,IAAIC,SAAS;IACb,IAAIN,MAAM,KAAK,CAAC,EAAE;MACdK,KAAK,GAAGC,SAAS,GAAGjD,SAAS,CAAC,CAAC,CAAC;IACpC,CAAC,MACI;MACD,IAAIvD,OAAO,GAAG,CAAC,EAAE;QACboG,QAAQ,GAAG,CAAC;MAChB,CAAC,MACI,IAAIpG,OAAO,GAAG,IAAI,CAAC4D,QAAQ,EAAE;QAC9B,IAAI6C,KAAK,GAAGH,OAAO,CAACD,SAAS,GAAG,CAAC,EAAEH,MAAM,GAAG,CAAC,CAAC;QAC9C,KAAKE,QAAQ,GAAGK,KAAK,EAAEL,QAAQ,IAAI,CAAC,EAAEA,QAAQ,EAAE,EAAE;UAC9C,IAAI7C,SAAS,CAAC6C,QAAQ,CAAC,CAACpG,OAAO,IAAIA,OAAO,EAAE;YACxC;UACJ;QACJ;QACAoG,QAAQ,GAAGE,OAAO,CAACF,QAAQ,EAAEF,MAAM,GAAG,CAAC,CAAC;MAC5C,CAAC,MACI;QACD,KAAKE,QAAQ,GAAGC,SAAS,EAAED,QAAQ,GAAGF,MAAM,EAAEE,QAAQ,EAAE,EAAE;UACtD,IAAI7C,SAAS,CAAC6C,QAAQ,CAAC,CAACpG,OAAO,GAAGA,OAAO,EAAE;YACvC;UACJ;QACJ;QACAoG,QAAQ,GAAGE,OAAO,CAACF,QAAQ,GAAG,CAAC,EAAEF,MAAM,GAAG,CAAC,CAAC;MAChD;MACAM,SAAS,GAAGjD,SAAS,CAAC6C,QAAQ,GAAG,CAAC,CAAC;MACnCG,KAAK,GAAGhD,SAAS,CAAC6C,QAAQ,CAAC;IAC/B;IACA,IAAI,EAAEG,KAAK,IAAIC,SAAS,CAAC,EAAE;MACvB;IACJ;IACA,IAAI,CAAC7C,OAAO,GAAGyC,QAAQ;IACvB,IAAI,CAACxC,QAAQ,GAAG5D,OAAO;IACvB,IAAI0G,QAAQ,GAAIF,SAAS,CAACxG,OAAO,GAAGuG,KAAK,CAACvG,OAAQ;IAClD,IAAI2G,CAAC,GAAGD,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAGJ,OAAO,CAAC,CAACtG,OAAO,GAAGuG,KAAK,CAACvG,OAAO,IAAI0G,QAAQ,EAAE,CAAC,CAAC;IAC7E,IAAIF,SAAS,CAAC1B,UAAU,EAAE;MACtB6B,CAAC,GAAGH,SAAS,CAAC1B,UAAU,CAAC6B,CAAC,CAAC;IAC/B;IACA,IAAIC,SAAS,GAAGZ,UAAU,GAAG,IAAI,CAACa,cAAc,GACzCV,YAAY,GAAG/C,OAAO,GAAG2C,MAAM,CAACzC,QAAQ,CAAE;IACjD,IAAI,CAACH,gBAAgB,CAACD,OAAO,CAAC,IAAIiD,YAAY,KAAK,CAACS,SAAS,EAAE;MAC3DA,SAAS,GAAG,IAAI,CAACC,cAAc,GAAG,EAAE;IACxC;IACA,IAAI,IAAI,CAACrD,QAAQ,EAAE;MACfuC,MAAM,CAACzC,QAAQ,CAAC,GAAGqD,CAAC,GAAG,CAAC,GAAGJ,KAAK,CAAClC,QAAQ,GAAGmC,SAAS,CAACnC,QAAQ;IAClE,CAAC,MACI,IAAIlB,gBAAgB,CAACD,OAAO,CAAC,EAAE;MAChCA,OAAO,KAAKP,mBAAmB,GACzB1C,kBAAkB,CAAC2G,SAAS,EAAEL,KAAK,CAACN,QAAQ,CAAC,EAAEO,SAAS,CAACP,QAAQ,CAAC,EAAEU,CAAC,CAAC,GACtErG,kBAAkB,CAACsG,SAAS,EAAEL,KAAK,CAACN,QAAQ,CAAC,EAAEO,SAAS,CAACP,QAAQ,CAAC,EAAEU,CAAC,CAAC;IAChF,CAAC,MACI,IAAI1D,mBAAmB,CAACC,OAAO,CAAC,EAAE;MACnC,IAAI4D,GAAG,GAAGP,KAAK,CAACN,QAAQ,CAAC;MACzB,IAAIc,SAAS,GAAGP,SAAS,CAACP,QAAQ,CAAC;MACnC,IAAIe,kBAAkB,GAAG9D,OAAO,KAAKJ,0BAA0B;MAC/DiD,MAAM,CAACzC,QAAQ,CAAC,GAAG;QACf2D,IAAI,EAAED,kBAAkB,GAAG,QAAQ,GAAG,QAAQ;QAC9CE,CAAC,EAAErH,iBAAiB,CAACiH,GAAG,CAACI,CAAC,EAAEH,SAAS,CAACG,CAAC,EAAEP,CAAC,CAAC;QAC3CQ,CAAC,EAAEtH,iBAAiB,CAACiH,GAAG,CAACK,CAAC,EAAEJ,SAAS,CAACI,CAAC,EAAER,CAAC,CAAC;QAC3ChC,UAAU,EAAEvF,GAAG,CAAC0H,GAAG,CAACnC,UAAU,EAAE,UAAUC,SAAS,EAAEwC,GAAG,EAAE;UACtD,IAAIC,aAAa,GAAGN,SAAS,CAACpC,UAAU,CAACyC,GAAG,CAAC;UAC7C,OAAO;YACH9F,MAAM,EAAEzB,iBAAiB,CAAC+E,SAAS,CAACtD,MAAM,EAAE+F,aAAa,CAAC/F,MAAM,EAAEqF,CAAC,CAAC;YACpEjI,KAAK,EAAE2D,WAAW,CAACpC,kBAAkB,CAAC,EAAE,EAAE2E,SAAS,CAAClG,KAAK,EAAE2I,aAAa,CAAC3I,KAAK,EAAEiI,CAAC,CAAC;UACtF,CAAC;QACL,CAAC,CAAC;QACFW,MAAM,EAAEP,SAAS,CAACO;MACtB,CAAC;MACD,IAAIN,kBAAkB,EAAE;QACpBjB,MAAM,CAACzC,QAAQ,CAAC,CAACiE,EAAE,GAAG1H,iBAAiB,CAACiH,GAAG,CAACS,EAAE,EAAER,SAAS,CAACQ,EAAE,EAAEZ,CAAC,CAAC;QAChEZ,MAAM,CAACzC,QAAQ,CAAC,CAACkE,EAAE,GAAG3H,iBAAiB,CAACiH,GAAG,CAACU,EAAE,EAAET,SAAS,CAACS,EAAE,EAAEb,CAAC,CAAC;MACpE,CAAC,MACI;QACDZ,MAAM,CAACzC,QAAQ,CAAC,CAACmE,CAAC,GAAG5H,iBAAiB,CAACiH,GAAG,CAACW,CAAC,EAAEV,SAAS,CAACU,CAAC,EAAEd,CAAC,CAAC;MACjE;IACJ,CAAC,MACI,IAAIR,YAAY,EAAE;MACnBlG,kBAAkB,CAAC2G,SAAS,EAAEL,KAAK,CAACN,QAAQ,CAAC,EAAEO,SAAS,CAACP,QAAQ,CAAC,EAAEU,CAAC,CAAC;MACtE,IAAI,CAACX,UAAU,EAAE;QACbD,MAAM,CAACzC,QAAQ,CAAC,GAAGjB,WAAW,CAACuE,SAAS,CAAC;MAC7C;IACJ,CAAC,MACI;MACD,IAAIzE,KAAK,GAAGtC,iBAAiB,CAAC0G,KAAK,CAACN,QAAQ,CAAC,EAAEO,SAAS,CAACP,QAAQ,CAAC,EAAEU,CAAC,CAAC;MACtE,IAAIX,UAAU,EAAE;QACZ,IAAI,CAACa,cAAc,GAAG1E,KAAK;MAC/B,CAAC,MACI;QACD4D,MAAM,CAACzC,QAAQ,CAAC,GAAGnB,KAAK;MAC5B;IACJ;IACA,IAAI6D,UAAU,EAAE;MACZ,IAAI,CAAC0B,YAAY,CAAC3B,MAAM,CAAC;IAC7B;EACJ,CAAC;EACD1C,KAAK,CAAC1D,SAAS,CAAC+H,YAAY,GAAG,UAAU3B,MAAM,EAAE;IAC7C,IAAI7C,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAII,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAIuC,aAAa,GAAG,IAAI,CAACgB,cAAc;IACvC,IAAI3D,OAAO,KAAKR,iBAAiB,EAAE;MAC/BqD,MAAM,CAACzC,QAAQ,CAAC,GAAGyC,MAAM,CAACzC,QAAQ,CAAC,GAAGuC,aAAa;IACvD,CAAC,MACI,IAAI3C,OAAO,KAAKL,gBAAgB,EAAE;MACnCnE,KAAK,CAAC+F,KAAK,CAACsB,MAAM,CAACzC,QAAQ,CAAC,EAAEF,OAAO,CAAC;MACtC3C,UAAU,CAAC2C,OAAO,EAAEA,OAAO,EAAEyC,aAAa,EAAE,CAAC,CAAC;MAC9CE,MAAM,CAACzC,QAAQ,CAAC,GAAGjB,WAAW,CAACe,OAAO,CAAC;IAC3C,CAAC,MACI,IAAIF,OAAO,KAAKP,mBAAmB,EAAE;MACtClC,UAAU,CAACsF,MAAM,CAACzC,QAAQ,CAAC,EAAEyC,MAAM,CAACzC,QAAQ,CAAC,EAAEuC,aAAa,EAAE,CAAC,CAAC;IACpE,CAAC,MACI,IAAI3C,OAAO,KAAKN,mBAAmB,EAAE;MACtCjC,UAAU,CAACoF,MAAM,CAACzC,QAAQ,CAAC,EAAEyC,MAAM,CAACzC,QAAQ,CAAC,EAAEuC,aAAa,EAAE,CAAC,CAAC;IACpE;EACJ,CAAC;EACD,OAAOxC,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,IAAIsE,QAAQ,GAAI,YAAY;EACxB,SAASA,QAAQA,CAAC5B,MAAM,EAAE6B,IAAI,EAAEC,sBAAsB,EAAEC,UAAU,EAAE;IAChE,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAGrC,MAAM;IACrB,IAAI,CAACsC,KAAK,GAAGT,IAAI;IACjB,IAAIA,IAAI,IAAIE,UAAU,EAAE;MACpB3I,QAAQ,CAAC,mDAAmD,CAAC;MAC7D;IACJ;IACA,IAAI,CAACmJ,kBAAkB,GAAGR,UAAU;IACpC,IAAI,CAACS,cAAc,GAAGV,sBAAsB;EAChD;EACAF,QAAQ,CAAChI,SAAS,CAAC6I,UAAU,GAAG,YAAY;IACxC,OAAO,IAAI,CAACP,QAAQ;EACxB,CAAC;EACDN,QAAQ,CAAChI,SAAS,CAAC8I,QAAQ,GAAG,YAAY;IACtC,OAAO,IAAI,CAACC,MAAM;EACtB,CAAC;EACDf,QAAQ,CAAChI,SAAS,CAACgJ,OAAO,GAAG,YAAY;IACrC,OAAO,IAAI,CAACN,KAAK;EACrB,CAAC;EACDV,QAAQ,CAAChI,SAAS,CAACiJ,SAAS,GAAG,YAAY;IACvC,OAAO,IAAI,CAACR,OAAO;EACvB,CAAC;EACDT,QAAQ,CAAChI,SAAS,CAACkJ,YAAY,GAAG,UAAU9C,MAAM,EAAE;IAChD,IAAI,CAACqC,OAAO,GAAGrC,MAAM;EACzB,CAAC;EACD4B,QAAQ,CAAChI,SAAS,CAACmJ,IAAI,GAAG,UAAU1E,IAAI,EAAE2E,KAAK,EAAEzE,MAAM,EAAE;IACrD,OAAO,IAAI,CAAC0E,YAAY,CAAC5E,IAAI,EAAE2E,KAAK,EAAE7J,IAAI,CAAC6J,KAAK,CAAC,EAAEzE,MAAM,CAAC;EAC9D,CAAC;EACDqD,QAAQ,CAAChI,SAAS,CAACqJ,YAAY,GAAG,UAAU5E,IAAI,EAAE2E,KAAK,EAAEE,SAAS,EAAE3E,MAAM,EAAE;IACxE,IAAI4E,MAAM,GAAG,IAAI,CAACnB,OAAO;IACzB,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,SAAS,CAAC7I,MAAM,EAAEC,CAAC,EAAE,EAAE;MACvC,IAAIiD,QAAQ,GAAG2F,SAAS,CAAC5I,CAAC,CAAC;MAC3B,IAAI8I,KAAK,GAAGD,MAAM,CAAC5F,QAAQ,CAAC;MAC5B,IAAI,CAAC6F,KAAK,EAAE;QACRA,KAAK,GAAGD,MAAM,CAAC5F,QAAQ,CAAC,GAAG,IAAID,KAAK,CAACC,QAAQ,CAAC;QAC9C,IAAI8F,YAAY,GAAG,KAAK,CAAC;QACzB,IAAInE,aAAa,GAAG,IAAI,CAACoE,iBAAiB,CAAC/F,QAAQ,CAAC;QACpD,IAAI2B,aAAa,EAAE;UACf,IAAIqE,eAAe,GAAGrE,aAAa,CAAC1B,SAAS;UAC7C,IAAIgG,WAAW,GAAGD,eAAe,CAACA,eAAe,CAAClJ,MAAM,GAAG,CAAC,CAAC;UAC7DgJ,YAAY,GAAGG,WAAW,IAAIA,WAAW,CAACpH,KAAK;UAC/C,IAAI8C,aAAa,CAAC/B,OAAO,KAAKL,gBAAgB,IAAIuG,YAAY,EAAE;YAC5DA,YAAY,GAAG/G,WAAW,CAAC+G,YAAY,CAAC;UAC5C;QACJ,CAAC,MACI;UACDA,YAAY,GAAG,IAAI,CAAChB,OAAO,CAAC9E,QAAQ,CAAC;QACzC;QACA,IAAI8F,YAAY,IAAI,IAAI,EAAE;UACtB;QACJ;QACA,IAAIhF,IAAI,GAAG,CAAC,EAAE;UACV+E,KAAK,CAAChF,WAAW,CAAC,CAAC,EAAEjC,UAAU,CAACkH,YAAY,CAAC,EAAE9E,MAAM,CAAC;QAC1D;QACA,IAAI,CAAC0D,UAAU,CAACxG,IAAI,CAAC8B,QAAQ,CAAC;MAClC;MACA6F,KAAK,CAAChF,WAAW,CAACC,IAAI,EAAElC,UAAU,CAAC6G,KAAK,CAACzF,QAAQ,CAAC,CAAC,EAAEgB,MAAM,CAAC;IAChE;IACA,IAAI,CAAC2D,QAAQ,GAAG9G,IAAI,CAACI,GAAG,CAAC,IAAI,CAAC0G,QAAQ,EAAE7D,IAAI,CAAC;IAC7C,OAAO,IAAI;EACf,CAAC;EACDuD,QAAQ,CAAChI,SAAS,CAAC6J,KAAK,GAAG,YAAY;IACnC,IAAI,CAACrB,KAAK,CAACqB,KAAK,CAAC,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB,CAAC;EACD9B,QAAQ,CAAChI,SAAS,CAAC+J,MAAM,GAAG,YAAY;IACpC,IAAI,CAACvB,KAAK,CAACuB,MAAM,CAAC,CAAC;IACnB,IAAI,CAACD,OAAO,GAAG,KAAK;EACxB,CAAC;EACD9B,QAAQ,CAAChI,SAAS,CAACgK,QAAQ,GAAG,YAAY;IACtC,OAAO,CAAC,CAAC,IAAI,CAACF,OAAO;EACzB,CAAC;EACD9B,QAAQ,CAAChI,SAAS,CAACiK,QAAQ,GAAG,UAAUA,QAAQ,EAAE;IAC9C,IAAI,CAAC3B,QAAQ,GAAG2B,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,OAAO,IAAI;EACf,CAAC;EACDlC,QAAQ,CAAChI,SAAS,CAACmK,aAAa,GAAG,YAAY;IAC3C,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAC5B,KAAK,GAAG,IAAI;IACjB,IAAI6B,QAAQ,GAAG,IAAI,CAACC,QAAQ;IAC5B,IAAID,QAAQ,EAAE;MACV,IAAI7J,GAAG,GAAG6J,QAAQ,CAAC5J,MAAM;MACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAC1B2J,QAAQ,CAAC3J,CAAC,CAAC,CAAC2B,IAAI,CAAC,IAAI,CAAC;MAC1B;IACJ;EACJ,CAAC;EACD2F,QAAQ,CAAChI,SAAS,CAACuK,gBAAgB,GAAG,YAAY;IAC9C,IAAI,CAACH,kBAAkB,CAAC,CAAC;IACzB,IAAII,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIC,WAAW,GAAG,IAAI,CAACC,WAAW;IAClC,IAAIF,SAAS,EAAE;MACXA,SAAS,CAACG,UAAU,CAAC,IAAI,CAACnC,KAAK,CAAC;IACpC;IACA,IAAI,CAACA,KAAK,GAAG,IAAI;IACjB,IAAIiC,WAAW,EAAE;MACb,KAAK,IAAI/J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+J,WAAW,CAAChK,MAAM,EAAEC,CAAC,EAAE,EAAE;QACzC+J,WAAW,CAAC/J,CAAC,CAAC,CAAC2B,IAAI,CAAC,IAAI,CAAC;MAC7B;IACJ;EACJ,CAAC;EACD2F,QAAQ,CAAChI,SAAS,CAACoK,kBAAkB,GAAG,YAAY;IAChD,IAAIb,MAAM,GAAG,IAAI,CAACnB,OAAO;IACzB,IAAIwC,UAAU,GAAG,IAAI,CAACvC,UAAU;IAChC,KAAK,IAAI3H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkK,UAAU,CAACnK,MAAM,EAAEC,CAAC,EAAE,EAAE;MACxC6I,MAAM,CAACqB,UAAU,CAAClK,CAAC,CAAC,CAAC,CAAC0D,WAAW,CAAC,CAAC;IACvC;EACJ,CAAC;EACD4D,QAAQ,CAAChI,SAAS,CAAC0J,iBAAiB,GAAG,UAAUmB,SAAS,EAAE;IACxD,IAAIvF,aAAa;IACjB,IAAIwF,iBAAiB,GAAG,IAAI,CAACnC,kBAAkB;IAC/C,IAAImC,iBAAiB,EAAE;MACnB,KAAK,IAAIpK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoK,iBAAiB,CAACrK,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC/C,IAAI8I,KAAK,GAAGsB,iBAAiB,CAACpK,CAAC,CAAC,CAACqK,QAAQ,CAACF,SAAS,CAAC;QACpD,IAAIrB,KAAK,EAAE;UACPlE,aAAa,GAAGkE,KAAK;QACzB;MACJ;IACJ;IACA,OAAOlE,aAAa;EACxB,CAAC;EACD0C,QAAQ,CAAChI,SAAS,CAAC8G,KAAK,GAAG,UAAUnC,MAAM,EAAE;IACzC,IAAI,IAAI,CAAC4D,QAAQ,GAAG,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,CAACA,QAAQ,GAAG,CAAC;IACjB,IAAIyC,IAAI,GAAG,IAAI;IACf,IAAIzB,MAAM,GAAG,EAAE;IACf,IAAIlE,OAAO,GAAG,IAAI,CAACiD,QAAQ,IAAI,CAAC;IAChC,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2H,UAAU,CAAC5H,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC7C,IAAIiD,QAAQ,GAAG,IAAI,CAAC0E,UAAU,CAAC3H,CAAC,CAAC;MACjC,IAAI8I,KAAK,GAAG,IAAI,CAACpB,OAAO,CAACzE,QAAQ,CAAC;MAClC,IAAI2B,aAAa,GAAG,IAAI,CAACoE,iBAAiB,CAAC/F,QAAQ,CAAC;MACpD,IAAI4B,GAAG,GAAGiE,KAAK,CAAC5F,SAAS;MACzB,IAAI2C,MAAM,GAAGhB,GAAG,CAAC9E,MAAM;MACvB+I,KAAK,CAACpE,OAAO,CAACC,OAAO,EAAEC,aAAa,CAAC;MACrC,IAAIkE,KAAK,CAAClF,YAAY,CAAC,CAAC,EAAE;QACtB,IAAI,CAAC,IAAI,CAACsE,cAAc,IAAIY,KAAK,CAAC3F,QAAQ,EAAE;UACxC,IAAI+B,MAAM,GAAGL,GAAG,CAACgB,MAAM,GAAG,CAAC,CAAC;UAC5B,IAAIX,MAAM,EAAE;YACRoF,IAAI,CAACvC,OAAO,CAACe,KAAK,CAAC7F,QAAQ,CAAC,GAAGiC,MAAM,CAAClB,QAAQ;UAClD;UACA8E,KAAK,CAACpF,WAAW,CAAC,CAAC;QACvB,CAAC,MACI;UACDmF,MAAM,CAAC1H,IAAI,CAAC2H,KAAK,CAAC;QACtB;MACJ;IACJ;IACA,IAAID,MAAM,CAAC9I,MAAM,IAAI,IAAI,CAACyJ,MAAM,EAAE;MAC9B,IAAIe,IAAI,GAAG,IAAInM,IAAI,CAAC;QAChBoM,IAAI,EAAE7F,OAAO;QACb4C,IAAI,EAAE,IAAI,CAACS,KAAK;QAChByC,KAAK,EAAE,IAAI,CAACpC,MAAM,IAAI,CAAC;QACvBqC,OAAO,EAAE,SAAAA,CAAU/K,OAAO,EAAE;UACxB2K,IAAI,CAACzC,QAAQ,GAAG,CAAC;UACjB,IAAIuC,iBAAiB,GAAGE,IAAI,CAACrC,kBAAkB;UAC/C,IAAImC,iBAAiB,EAAE;YACnB,IAAIO,wBAAwB,GAAG,KAAK;YACpC,KAAK,IAAI3K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoK,iBAAiB,CAACrK,MAAM,EAAEC,CAAC,EAAE,EAAE;cAC/C,IAAIoK,iBAAiB,CAACpK,CAAC,CAAC,CAAC8H,KAAK,EAAE;gBAC5B6C,wBAAwB,GAAG,IAAI;gBAC/B;cACJ;YACJ;YACA,IAAI,CAACA,wBAAwB,EAAE;cAC3BL,IAAI,CAACrC,kBAAkB,GAAG,IAAI;YAClC;UACJ;UACA,KAAK,IAAIjI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6I,MAAM,CAAC9I,MAAM,EAAEC,CAAC,EAAE,EAAE;YACpC6I,MAAM,CAAC7I,CAAC,CAAC,CAACyF,IAAI,CAAC6E,IAAI,CAACvC,OAAO,EAAEpI,OAAO,CAAC;UACzC;UACA,IAAIiL,WAAW,GAAGN,IAAI,CAACO,WAAW;UAClC,IAAID,WAAW,EAAE;YACb,KAAK,IAAI5K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4K,WAAW,CAAC7K,MAAM,EAAEC,CAAC,EAAE,EAAE;cACzC4K,WAAW,CAAC5K,CAAC,CAAC,CAACsK,IAAI,CAACvC,OAAO,EAAEpI,OAAO,CAAC;YACzC;UACJ;QACJ,CAAC;QACDmL,SAAS,EAAE,SAAAA,CAAA,EAAY;UACnBR,IAAI,CAACb,aAAa,CAAC,CAAC;QACxB;MACJ,CAAC,CAAC;MACF,IAAI,CAAC3B,KAAK,GAAGyC,IAAI;MACjB,IAAI,IAAI,CAACT,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACiB,OAAO,CAACR,IAAI,CAAC;MAChC;MACA,IAAItG,MAAM,EAAE;QACRsG,IAAI,CAACS,SAAS,CAAC/G,MAAM,CAAC;MAC1B;IACJ,CAAC,MACI;MACD,IAAI,CAACwF,aAAa,CAAC,CAAC;IACxB;IACA,OAAO,IAAI;EACf,CAAC;EACDnC,QAAQ,CAAChI,SAAS,CAAC2L,IAAI,GAAG,UAAUC,aAAa,EAAE;IAC/C,IAAI,CAAC,IAAI,CAACpD,KAAK,EAAE;MACb;IACJ;IACA,IAAIyC,IAAI,GAAG,IAAI,CAACzC,KAAK;IACrB,IAAIoD,aAAa,EAAE;MACfX,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC;IACnB;IACA,IAAI,CAACb,gBAAgB,CAAC,CAAC;EAC3B,CAAC;EACDvC,QAAQ,CAAChI,SAAS,CAACmL,KAAK,GAAG,UAAU1G,IAAI,EAAE;IACvC,IAAI,CAACsE,MAAM,GAAGtE,IAAI;IAClB,OAAO,IAAI;EACf,CAAC;EACDuD,QAAQ,CAAChI,SAAS,CAAC6L,MAAM,GAAG,UAAUC,EAAE,EAAE;IACtC,IAAIA,EAAE,EAAE;MACJ,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,EAAE;MACzB;MACA,IAAI,CAACA,WAAW,CAAC1J,IAAI,CAACiK,EAAE,CAAC;IAC7B;IACA,OAAO,IAAI;EACf,CAAC;EACD9D,QAAQ,CAAChI,SAAS,CAAC+L,IAAI,GAAG,UAAUD,EAAE,EAAE;IACpC,IAAIA,EAAE,EAAE;MACJ,IAAI,CAAC,IAAI,CAACxB,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAG,EAAE;MACtB;MACA,IAAI,CAACA,QAAQ,CAACzI,IAAI,CAACiK,EAAE,CAAC;IAC1B;IACA,OAAO,IAAI;EACf,CAAC;EACD9D,QAAQ,CAAChI,SAAS,CAACgM,OAAO,GAAG,UAAUF,EAAE,EAAE;IACvC,IAAIA,EAAE,EAAE;MACJ,IAAI,CAAC,IAAI,CAACpB,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,EAAE;MACzB;MACA,IAAI,CAACA,WAAW,CAAC7I,IAAI,CAACiK,EAAE,CAAC;IAC7B;IACA,OAAO,IAAI;EACf,CAAC;EACD9D,QAAQ,CAAChI,SAAS,CAACiM,OAAO,GAAG,YAAY;IACrC,OAAO,IAAI,CAACzD,KAAK;EACrB,CAAC;EACDR,QAAQ,CAAChI,SAAS,CAAC+K,QAAQ,GAAG,UAAUpH,QAAQ,EAAE;IAC9C,OAAO,IAAI,CAACyE,OAAO,CAACzE,QAAQ,CAAC;EACjC,CAAC;EACDqE,QAAQ,CAAChI,SAAS,CAACkM,SAAS,GAAG,YAAY;IACvC,IAAIC,KAAK,GAAG,IAAI;IAChB,OAAO1M,GAAG,CAAC,IAAI,CAAC4I,UAAU,EAAE,UAAU+D,GAAG,EAAE;MAAE,OAAOD,KAAK,CAAC/D,OAAO,CAACgE,GAAG,CAAC;IAAE,CAAC,CAAC;EAC9E,CAAC;EACDpE,QAAQ,CAAChI,SAAS,CAACqM,UAAU,GAAG,UAAU/C,SAAS,EAAEsC,aAAa,EAAE;IAChE,IAAI,CAACtC,SAAS,CAAC7I,MAAM,IAAI,CAAC,IAAI,CAAC+H,KAAK,EAAE;MAClC,OAAO,IAAI;IACf;IACA,IAAIe,MAAM,GAAG,IAAI,CAACnB,OAAO;IACzB,IAAIwC,UAAU,GAAG,IAAI,CAACvC,UAAU;IAChC,KAAK,IAAI3H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,SAAS,CAAC7I,MAAM,EAAEC,CAAC,EAAE,EAAE;MACvC,IAAI8I,KAAK,GAAGD,MAAM,CAACD,SAAS,CAAC5I,CAAC,CAAC,CAAC;MAChC,IAAI8I,KAAK,IAAI,CAACA,KAAK,CAACtF,UAAU,CAAC,CAAC,EAAE;QAC9B,IAAI0H,aAAa,EAAE;UACfpC,KAAK,CAACrD,IAAI,CAAC,IAAI,CAACsC,OAAO,EAAE,CAAC,CAAC;QAC/B,CAAC,MACI,IAAI,IAAI,CAACF,QAAQ,KAAK,CAAC,EAAE;UAC1BiB,KAAK,CAACrD,IAAI,CAAC,IAAI,CAACsC,OAAO,EAAE,CAAC,CAAC;QAC/B;QACAe,KAAK,CAACpF,WAAW,CAAC,CAAC;MACvB;IACJ;IACA,IAAIkI,UAAU,GAAG,IAAI;IACrB,KAAK,IAAI5L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkK,UAAU,CAACnK,MAAM,EAAEC,CAAC,EAAE,EAAE;MACxC,IAAI,CAAC6I,MAAM,CAACqB,UAAU,CAAClK,CAAC,CAAC,CAAC,CAACwD,UAAU,CAAC,CAAC,EAAE;QACrCoI,UAAU,GAAG,KAAK;QAClB;MACJ;IACJ;IACA,IAAIA,UAAU,EAAE;MACZ,IAAI,CAAC/B,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAO+B,UAAU;EACrB,CAAC;EACDtE,QAAQ,CAAChI,SAAS,CAACuM,MAAM,GAAG,UAAUnG,MAAM,EAAEoG,SAAS,EAAEC,WAAW,EAAE;IAClE,IAAI,CAACrG,MAAM,EAAE;MACT;IACJ;IACAoG,SAAS,GAAGA,SAAS,IAAI,IAAI,CAACnE,UAAU;IACxC,KAAK,IAAI3H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8L,SAAS,CAAC/L,MAAM,EAAEC,CAAC,EAAE,EAAE;MACvC,IAAIiD,QAAQ,GAAG6I,SAAS,CAAC9L,CAAC,CAAC;MAC3B,IAAI8I,KAAK,GAAG,IAAI,CAACpB,OAAO,CAACzE,QAAQ,CAAC;MAClC,IAAI,CAAC6F,KAAK,IAAIA,KAAK,CAACtF,UAAU,CAAC,CAAC,EAAE;QAC9B;MACJ;MACA,IAAIqB,GAAG,GAAGiE,KAAK,CAAC5F,SAAS;MACzB,IAAIsB,EAAE,GAAGK,GAAG,CAACkH,WAAW,GAAG,CAAC,GAAGlH,GAAG,CAAC9E,MAAM,GAAG,CAAC,CAAC;MAC9C,IAAIyE,EAAE,EAAE;QACJkB,MAAM,CAACzC,QAAQ,CAAC,GAAGpB,UAAU,CAAC2C,EAAE,CAACR,QAAQ,CAAC;MAC9C;IACJ;EACJ,CAAC;EACDsD,QAAQ,CAAChI,SAAS,CAAC0M,kBAAkB,GAAG,UAAUC,UAAU,EAAEH,SAAS,EAAE;IACrEA,SAAS,GAAGA,SAAS,IAAIjN,IAAI,CAACoN,UAAU,CAAC;IACzC,KAAK,IAAIjM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8L,SAAS,CAAC/L,MAAM,EAAEC,CAAC,EAAE,EAAE;MACvC,IAAIiD,QAAQ,GAAG6I,SAAS,CAAC9L,CAAC,CAAC;MAC3B,IAAI8I,KAAK,GAAG,IAAI,CAACpB,OAAO,CAACzE,QAAQ,CAAC;MAClC,IAAI,CAAC6F,KAAK,EAAE;QACR;MACJ;MACA,IAAIjE,GAAG,GAAGiE,KAAK,CAAC5F,SAAS;MACzB,IAAI2B,GAAG,CAAC9E,MAAM,GAAG,CAAC,EAAE;QAChB,IAAImF,MAAM,GAAGL,GAAG,CAACqH,GAAG,CAAC,CAAC;QACtBpD,KAAK,CAAChF,WAAW,CAACoB,MAAM,CAACnB,IAAI,EAAEkI,UAAU,CAAChJ,QAAQ,CAAC,CAAC;QACpD6F,KAAK,CAACpE,OAAO,CAAC,IAAI,CAACkD,QAAQ,EAAEkB,KAAK,CAACjF,gBAAgB,CAAC,CAAC,CAAC;MAC1D;IACJ;EACJ,CAAC;EACD,OAAOyD,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}