{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport View from '../View.js';\nimport geoSourceManager from './geoSourceManager.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nimport { warn } from '../../util/log.js';\nvar GEO_DEFAULT_PARAMS = {\n  'geoJSON': {\n    aspectScale: 0.75,\n    invertLongitute: true\n  },\n  'geoSVG': {\n    aspectScale: 1,\n    invertLongitute: false\n  }\n};\nexport var geo2DDimensions = ['lng', 'lat'];\nvar Geo = /** @class */function (_super) {\n  __extends(Geo, _super);\n  function Geo(name, map, opt) {\n    var _this = _super.call(this, name) || this;\n    _this.dimensions = geo2DDimensions;\n    _this.type = 'geo';\n    // Only store specified name coord via `addGeoCoord`.\n    _this._nameCoordMap = zrUtil.createHashMap();\n    _this.map = map;\n    var projection = opt.projection;\n    var source = geoSourceManager.load(map, opt.nameMap, opt.nameProperty);\n    var resource = geoSourceManager.getGeoResource(map);\n    var resourceType = _this.resourceType = resource ? resource.type : null;\n    var regions = _this.regions = source.regions;\n    var defaultParams = GEO_DEFAULT_PARAMS[resource.type];\n    _this._regionsMap = source.regionsMap;\n    _this.regions = source.regions;\n    if (process.env.NODE_ENV !== 'production' && projection) {\n      // Do some check\n      if (resourceType === 'geoSVG') {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\"Map \" + map + \" with SVG source can't use projection. Only GeoJSON source supports projection.\");\n        }\n        projection = null;\n      }\n      if (!(projection.project && projection.unproject)) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn('project and unproject must be both provided in the projeciton.');\n        }\n        projection = null;\n      }\n    }\n    _this.projection = projection;\n    var boundingRect;\n    if (projection) {\n      // Can't reuse the raw bounding rect\n      for (var i = 0; i < regions.length; i++) {\n        var regionRect = regions[i].getBoundingRect(projection);\n        boundingRect = boundingRect || regionRect.clone();\n        boundingRect.union(regionRect);\n      }\n    } else {\n      boundingRect = source.boundingRect;\n    }\n    _this.setBoundingRect(boundingRect.x, boundingRect.y, boundingRect.width, boundingRect.height);\n    // aspectScale and invertLongitute actually is the parameters default raw projection.\n    // So we ignore them if projection is given.\n    // Ignore default aspect scale if projection exits.\n    _this.aspectScale = projection ? 1 : zrUtil.retrieve2(opt.aspectScale, defaultParams.aspectScale);\n    // Not invert longitude if projection exits.\n    _this._invertLongitute = projection ? false : defaultParams.invertLongitute;\n    return _this;\n  }\n  Geo.prototype._transformTo = function (x, y, width, height) {\n    var rect = this.getBoundingRect();\n    var invertLongitute = this._invertLongitute;\n    rect = rect.clone();\n    if (invertLongitute) {\n      // Longitude is inverted.\n      rect.y = -rect.y - rect.height;\n    }\n    var rawTransformable = this._rawTransformable;\n    rawTransformable.transform = rect.calculateTransform(new BoundingRect(x, y, width, height));\n    var rawParent = rawTransformable.parent;\n    rawTransformable.parent = null;\n    rawTransformable.decomposeTransform();\n    rawTransformable.parent = rawParent;\n    if (invertLongitute) {\n      rawTransformable.scaleY = -rawTransformable.scaleY;\n    }\n    this._updateTransform();\n  };\n  Geo.prototype.getRegion = function (name) {\n    return this._regionsMap.get(name);\n  };\n  Geo.prototype.getRegionByCoord = function (coord) {\n    var regions = this.regions;\n    for (var i = 0; i < regions.length; i++) {\n      var region = regions[i];\n      if (region.type === 'geoJSON' && region.contain(coord)) {\n        return regions[i];\n      }\n    }\n  };\n  /**\r\n   * Add geoCoord for indexing by name\r\n   */\n  Geo.prototype.addGeoCoord = function (name, geoCoord) {\n    this._nameCoordMap.set(name, geoCoord);\n  };\n  /**\r\n   * Get geoCoord by name\r\n   */\n  Geo.prototype.getGeoCoord = function (name) {\n    var region = this._regionsMap.get(name);\n    // Calculate center only on demand.\n    return this._nameCoordMap.get(name) || region && region.getCenter();\n  };\n  Geo.prototype.dataToPoint = function (data, noRoam, out) {\n    if (zrUtil.isString(data)) {\n      // Map area name to geoCoord\n      data = this.getGeoCoord(data);\n    }\n    if (data) {\n      var projection = this.projection;\n      if (projection) {\n        // projection may return null point.\n        data = projection.project(data);\n      }\n      return data && this.projectedToPoint(data, noRoam, out);\n    }\n  };\n  Geo.prototype.pointToData = function (point) {\n    var projection = this.projection;\n    if (projection) {\n      // projection may return null point.\n      point = projection.unproject(point);\n    }\n    return point && this.pointToProjected(point);\n  };\n  /**\r\n   * Point to projected data. Same with pointToData when projection is used.\r\n   */\n  Geo.prototype.pointToProjected = function (point) {\n    return _super.prototype.pointToData.call(this, point);\n  };\n  Geo.prototype.projectedToPoint = function (projected, noRoam, out) {\n    return _super.prototype.dataToPoint.call(this, projected, noRoam, out);\n  };\n  Geo.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.dataToPoint(value) : null;\n  };\n  Geo.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.pointToData(pixel) : null;\n  };\n  return Geo;\n}(View);\n;\nzrUtil.mixin(Geo, View);\nfunction getCoordSys(finder) {\n  var geoModel = finder.geoModel;\n  var seriesModel = finder.seriesModel;\n  return geoModel ? geoModel.coordinateSystem : seriesModel ? seriesModel.coordinateSystem // For map series.\n  || (seriesModel.getReferringComponents('geo', SINGLE_REFERRING).models[0] || {}).coordinateSystem : null;\n}\nexport default Geo;", "map": {"version": 3, "names": ["__extends", "zrUtil", "BoundingRect", "View", "geoSourceManager", "SINGLE_REFERRING", "warn", "GEO_DEFAULT_PARAMS", "aspectScale", "invertLongitute", "geo2DDimensions", "Geo", "_super", "name", "map", "opt", "_this", "call", "dimensions", "type", "_nameCoordMap", "createHashMap", "projection", "source", "load", "nameMap", "nameProperty", "resource", "getGeoResource", "resourceType", "regions", "defaultParams", "_regionsMap", "regionsMap", "process", "env", "NODE_ENV", "project", "unproject", "boundingRect", "i", "length", "regionRect", "getBoundingRect", "clone", "union", "setBoundingRect", "x", "y", "width", "height", "retrieve2", "_invertLongitute", "prototype", "_transformTo", "rect", "rawTransformable", "_rawTransformable", "transform", "calculateTransform", "rawParent", "parent", "decomposeTransform", "scaleY", "_updateTransform", "getRegion", "get", "getRegionByCoord", "coord", "region", "contain", "addGeoCoord", "geoCoord", "set", "getGeoCoord", "getCenter", "dataToPoint", "data", "noRoam", "out", "isString", "projectedToPoint", "pointToData", "point", "pointToProjected", "projected", "convertToPixel", "ecModel", "finder", "value", "coordSys", "getCoordSys", "convertFromPixel", "pixel", "mixin", "geoModel", "seriesModel", "coordinateSystem", "getReferringComponents", "models"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/coord/geo/Geo.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport View from '../View.js';\nimport geoSourceManager from './geoSourceManager.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nimport { warn } from '../../util/log.js';\nvar GEO_DEFAULT_PARAMS = {\n  'geoJSON': {\n    aspectScale: 0.75,\n    invertLongitute: true\n  },\n  'geoSVG': {\n    aspectScale: 1,\n    invertLongitute: false\n  }\n};\nexport var geo2DDimensions = ['lng', 'lat'];\nvar Geo = /** @class */function (_super) {\n  __extends(Geo, _super);\n  function Geo(name, map, opt) {\n    var _this = _super.call(this, name) || this;\n    _this.dimensions = geo2DDimensions;\n    _this.type = 'geo';\n    // Only store specified name coord via `addGeoCoord`.\n    _this._nameCoordMap = zrUtil.createHashMap();\n    _this.map = map;\n    var projection = opt.projection;\n    var source = geoSourceManager.load(map, opt.nameMap, opt.nameProperty);\n    var resource = geoSourceManager.getGeoResource(map);\n    var resourceType = _this.resourceType = resource ? resource.type : null;\n    var regions = _this.regions = source.regions;\n    var defaultParams = GEO_DEFAULT_PARAMS[resource.type];\n    _this._regionsMap = source.regionsMap;\n    _this.regions = source.regions;\n    if (process.env.NODE_ENV !== 'production' && projection) {\n      // Do some check\n      if (resourceType === 'geoSVG') {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\"Map \" + map + \" with SVG source can't use projection. Only GeoJSON source supports projection.\");\n        }\n        projection = null;\n      }\n      if (!(projection.project && projection.unproject)) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn('project and unproject must be both provided in the projeciton.');\n        }\n        projection = null;\n      }\n    }\n    _this.projection = projection;\n    var boundingRect;\n    if (projection) {\n      // Can't reuse the raw bounding rect\n      for (var i = 0; i < regions.length; i++) {\n        var regionRect = regions[i].getBoundingRect(projection);\n        boundingRect = boundingRect || regionRect.clone();\n        boundingRect.union(regionRect);\n      }\n    } else {\n      boundingRect = source.boundingRect;\n    }\n    _this.setBoundingRect(boundingRect.x, boundingRect.y, boundingRect.width, boundingRect.height);\n    // aspectScale and invertLongitute actually is the parameters default raw projection.\n    // So we ignore them if projection is given.\n    // Ignore default aspect scale if projection exits.\n    _this.aspectScale = projection ? 1 : zrUtil.retrieve2(opt.aspectScale, defaultParams.aspectScale);\n    // Not invert longitude if projection exits.\n    _this._invertLongitute = projection ? false : defaultParams.invertLongitute;\n    return _this;\n  }\n  Geo.prototype._transformTo = function (x, y, width, height) {\n    var rect = this.getBoundingRect();\n    var invertLongitute = this._invertLongitute;\n    rect = rect.clone();\n    if (invertLongitute) {\n      // Longitude is inverted.\n      rect.y = -rect.y - rect.height;\n    }\n    var rawTransformable = this._rawTransformable;\n    rawTransformable.transform = rect.calculateTransform(new BoundingRect(x, y, width, height));\n    var rawParent = rawTransformable.parent;\n    rawTransformable.parent = null;\n    rawTransformable.decomposeTransform();\n    rawTransformable.parent = rawParent;\n    if (invertLongitute) {\n      rawTransformable.scaleY = -rawTransformable.scaleY;\n    }\n    this._updateTransform();\n  };\n  Geo.prototype.getRegion = function (name) {\n    return this._regionsMap.get(name);\n  };\n  Geo.prototype.getRegionByCoord = function (coord) {\n    var regions = this.regions;\n    for (var i = 0; i < regions.length; i++) {\n      var region = regions[i];\n      if (region.type === 'geoJSON' && region.contain(coord)) {\n        return regions[i];\n      }\n    }\n  };\n  /**\r\n   * Add geoCoord for indexing by name\r\n   */\n  Geo.prototype.addGeoCoord = function (name, geoCoord) {\n    this._nameCoordMap.set(name, geoCoord);\n  };\n  /**\r\n   * Get geoCoord by name\r\n   */\n  Geo.prototype.getGeoCoord = function (name) {\n    var region = this._regionsMap.get(name);\n    // Calculate center only on demand.\n    return this._nameCoordMap.get(name) || region && region.getCenter();\n  };\n  Geo.prototype.dataToPoint = function (data, noRoam, out) {\n    if (zrUtil.isString(data)) {\n      // Map area name to geoCoord\n      data = this.getGeoCoord(data);\n    }\n    if (data) {\n      var projection = this.projection;\n      if (projection) {\n        // projection may return null point.\n        data = projection.project(data);\n      }\n      return data && this.projectedToPoint(data, noRoam, out);\n    }\n  };\n  Geo.prototype.pointToData = function (point) {\n    var projection = this.projection;\n    if (projection) {\n      // projection may return null point.\n      point = projection.unproject(point);\n    }\n    return point && this.pointToProjected(point);\n  };\n  /**\r\n   * Point to projected data. Same with pointToData when projection is used.\r\n   */\n  Geo.prototype.pointToProjected = function (point) {\n    return _super.prototype.pointToData.call(this, point);\n  };\n  Geo.prototype.projectedToPoint = function (projected, noRoam, out) {\n    return _super.prototype.dataToPoint.call(this, projected, noRoam, out);\n  };\n  Geo.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.dataToPoint(value) : null;\n  };\n  Geo.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.pointToData(pixel) : null;\n  };\n  return Geo;\n}(View);\n;\nzrUtil.mixin(Geo, View);\nfunction getCoordSys(finder) {\n  var geoModel = finder.geoModel;\n  var seriesModel = finder.seriesModel;\n  return geoModel ? geoModel.coordinateSystem : seriesModel ? seriesModel.coordinateSystem // For map series.\n  || (seriesModel.getReferringComponents('geo', SINGLE_REFERRING).models[0] || {}).coordinateSystem : null;\n}\nexport default Geo;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,IAAIC,kBAAkB,GAAG;EACvB,SAAS,EAAE;IACTC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE;EACnB,CAAC;EACD,QAAQ,EAAE;IACRD,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE;EACnB;AACF,CAAC;AACD,OAAO,IAAIC,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;AAC3C,IAAIC,GAAG,GAAG,aAAa,UAAUC,MAAM,EAAE;EACvCZ,SAAS,CAACW,GAAG,EAAEC,MAAM,CAAC;EACtB,SAASD,GAAGA,CAACE,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAC3B,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,IAAI,CAAC,IAAI,IAAI;IAC3CG,KAAK,CAACE,UAAU,GAAGR,eAAe;IAClCM,KAAK,CAACG,IAAI,GAAG,KAAK;IAClB;IACAH,KAAK,CAACI,aAAa,GAAGnB,MAAM,CAACoB,aAAa,CAAC,CAAC;IAC5CL,KAAK,CAACF,GAAG,GAAGA,GAAG;IACf,IAAIQ,UAAU,GAAGP,GAAG,CAACO,UAAU;IAC/B,IAAIC,MAAM,GAAGnB,gBAAgB,CAACoB,IAAI,CAACV,GAAG,EAAEC,GAAG,CAACU,OAAO,EAAEV,GAAG,CAACW,YAAY,CAAC;IACtE,IAAIC,QAAQ,GAAGvB,gBAAgB,CAACwB,cAAc,CAACd,GAAG,CAAC;IACnD,IAAIe,YAAY,GAAGb,KAAK,CAACa,YAAY,GAAGF,QAAQ,GAAGA,QAAQ,CAACR,IAAI,GAAG,IAAI;IACvE,IAAIW,OAAO,GAAGd,KAAK,CAACc,OAAO,GAAGP,MAAM,CAACO,OAAO;IAC5C,IAAIC,aAAa,GAAGxB,kBAAkB,CAACoB,QAAQ,CAACR,IAAI,CAAC;IACrDH,KAAK,CAACgB,WAAW,GAAGT,MAAM,CAACU,UAAU;IACrCjB,KAAK,CAACc,OAAO,GAAGP,MAAM,CAACO,OAAO;IAC9B,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAId,UAAU,EAAE;MACvD;MACA,IAAIO,YAAY,KAAK,QAAQ,EAAE;QAC7B,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC9B,IAAI,CAAC,MAAM,GAAGQ,GAAG,GAAG,iFAAiF,CAAC;QACxG;QACAQ,UAAU,GAAG,IAAI;MACnB;MACA,IAAI,EAAEA,UAAU,CAACe,OAAO,IAAIf,UAAU,CAACgB,SAAS,CAAC,EAAE;QACjD,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC9B,IAAI,CAAC,gEAAgE,CAAC;QACxE;QACAgB,UAAU,GAAG,IAAI;MACnB;IACF;IACAN,KAAK,CAACM,UAAU,GAAGA,UAAU;IAC7B,IAAIiB,YAAY;IAChB,IAAIjB,UAAU,EAAE;MACd;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAIE,UAAU,GAAGZ,OAAO,CAACU,CAAC,CAAC,CAACG,eAAe,CAACrB,UAAU,CAAC;QACvDiB,YAAY,GAAGA,YAAY,IAAIG,UAAU,CAACE,KAAK,CAAC,CAAC;QACjDL,YAAY,CAACM,KAAK,CAACH,UAAU,CAAC;MAChC;IACF,CAAC,MAAM;MACLH,YAAY,GAAGhB,MAAM,CAACgB,YAAY;IACpC;IACAvB,KAAK,CAAC8B,eAAe,CAACP,YAAY,CAACQ,CAAC,EAAER,YAAY,CAACS,CAAC,EAAET,YAAY,CAACU,KAAK,EAAEV,YAAY,CAACW,MAAM,CAAC;IAC9F;IACA;IACA;IACAlC,KAAK,CAACR,WAAW,GAAGc,UAAU,GAAG,CAAC,GAAGrB,MAAM,CAACkD,SAAS,CAACpC,GAAG,CAACP,WAAW,EAAEuB,aAAa,CAACvB,WAAW,CAAC;IACjG;IACAQ,KAAK,CAACoC,gBAAgB,GAAG9B,UAAU,GAAG,KAAK,GAAGS,aAAa,CAACtB,eAAe;IAC3E,OAAOO,KAAK;EACd;EACAL,GAAG,CAAC0C,SAAS,CAACC,YAAY,GAAG,UAAUP,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC1D,IAAIK,IAAI,GAAG,IAAI,CAACZ,eAAe,CAAC,CAAC;IACjC,IAAIlC,eAAe,GAAG,IAAI,CAAC2C,gBAAgB;IAC3CG,IAAI,GAAGA,IAAI,CAACX,KAAK,CAAC,CAAC;IACnB,IAAInC,eAAe,EAAE;MACnB;MACA8C,IAAI,CAACP,CAAC,GAAG,CAACO,IAAI,CAACP,CAAC,GAAGO,IAAI,CAACL,MAAM;IAChC;IACA,IAAIM,gBAAgB,GAAG,IAAI,CAACC,iBAAiB;IAC7CD,gBAAgB,CAACE,SAAS,GAAGH,IAAI,CAACI,kBAAkB,CAAC,IAAIzD,YAAY,CAAC6C,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,CAAC,CAAC;IAC3F,IAAIU,SAAS,GAAGJ,gBAAgB,CAACK,MAAM;IACvCL,gBAAgB,CAACK,MAAM,GAAG,IAAI;IAC9BL,gBAAgB,CAACM,kBAAkB,CAAC,CAAC;IACrCN,gBAAgB,CAACK,MAAM,GAAGD,SAAS;IACnC,IAAInD,eAAe,EAAE;MACnB+C,gBAAgB,CAACO,MAAM,GAAG,CAACP,gBAAgB,CAACO,MAAM;IACpD;IACA,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EACDrD,GAAG,CAAC0C,SAAS,CAACY,SAAS,GAAG,UAAUpD,IAAI,EAAE;IACxC,OAAO,IAAI,CAACmB,WAAW,CAACkC,GAAG,CAACrD,IAAI,CAAC;EACnC,CAAC;EACDF,GAAG,CAAC0C,SAAS,CAACc,gBAAgB,GAAG,UAAUC,KAAK,EAAE;IAChD,IAAItC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,IAAI6B,MAAM,GAAGvC,OAAO,CAACU,CAAC,CAAC;MACvB,IAAI6B,MAAM,CAAClD,IAAI,KAAK,SAAS,IAAIkD,MAAM,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;QACtD,OAAOtC,OAAO,CAACU,CAAC,CAAC;MACnB;IACF;EACF,CAAC;EACD;AACF;AACA;EACE7B,GAAG,CAAC0C,SAAS,CAACkB,WAAW,GAAG,UAAU1D,IAAI,EAAE2D,QAAQ,EAAE;IACpD,IAAI,CAACpD,aAAa,CAACqD,GAAG,CAAC5D,IAAI,EAAE2D,QAAQ,CAAC;EACxC,CAAC;EACD;AACF;AACA;EACE7D,GAAG,CAAC0C,SAAS,CAACqB,WAAW,GAAG,UAAU7D,IAAI,EAAE;IAC1C,IAAIwD,MAAM,GAAG,IAAI,CAACrC,WAAW,CAACkC,GAAG,CAACrD,IAAI,CAAC;IACvC;IACA,OAAO,IAAI,CAACO,aAAa,CAAC8C,GAAG,CAACrD,IAAI,CAAC,IAAIwD,MAAM,IAAIA,MAAM,CAACM,SAAS,CAAC,CAAC;EACrE,CAAC;EACDhE,GAAG,CAAC0C,SAAS,CAACuB,WAAW,GAAG,UAAUC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACvD,IAAI9E,MAAM,CAAC+E,QAAQ,CAACH,IAAI,CAAC,EAAE;MACzB;MACAA,IAAI,GAAG,IAAI,CAACH,WAAW,CAACG,IAAI,CAAC;IAC/B;IACA,IAAIA,IAAI,EAAE;MACR,IAAIvD,UAAU,GAAG,IAAI,CAACA,UAAU;MAChC,IAAIA,UAAU,EAAE;QACd;QACAuD,IAAI,GAAGvD,UAAU,CAACe,OAAO,CAACwC,IAAI,CAAC;MACjC;MACA,OAAOA,IAAI,IAAI,IAAI,CAACI,gBAAgB,CAACJ,IAAI,EAAEC,MAAM,EAAEC,GAAG,CAAC;IACzD;EACF,CAAC;EACDpE,GAAG,CAAC0C,SAAS,CAAC6B,WAAW,GAAG,UAAUC,KAAK,EAAE;IAC3C,IAAI7D,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIA,UAAU,EAAE;MACd;MACA6D,KAAK,GAAG7D,UAAU,CAACgB,SAAS,CAAC6C,KAAK,CAAC;IACrC;IACA,OAAOA,KAAK,IAAI,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAAC;EAC9C,CAAC;EACD;AACF;AACA;EACExE,GAAG,CAAC0C,SAAS,CAAC+B,gBAAgB,GAAG,UAAUD,KAAK,EAAE;IAChD,OAAOvE,MAAM,CAACyC,SAAS,CAAC6B,WAAW,CAACjE,IAAI,CAAC,IAAI,EAAEkE,KAAK,CAAC;EACvD,CAAC;EACDxE,GAAG,CAAC0C,SAAS,CAAC4B,gBAAgB,GAAG,UAAUI,SAAS,EAAEP,MAAM,EAAEC,GAAG,EAAE;IACjE,OAAOnE,MAAM,CAACyC,SAAS,CAACuB,WAAW,CAAC3D,IAAI,CAAC,IAAI,EAAEoE,SAAS,EAAEP,MAAM,EAAEC,GAAG,CAAC;EACxE,CAAC;EACDpE,GAAG,CAAC0C,SAAS,CAACiC,cAAc,GAAG,UAAUC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAC/D,IAAIC,QAAQ,GAAGC,WAAW,CAACH,MAAM,CAAC;IAClC,OAAOE,QAAQ,KAAK,IAAI,GAAGA,QAAQ,CAACd,WAAW,CAACa,KAAK,CAAC,GAAG,IAAI;EAC/D,CAAC;EACD9E,GAAG,CAAC0C,SAAS,CAACuC,gBAAgB,GAAG,UAAUL,OAAO,EAAEC,MAAM,EAAEK,KAAK,EAAE;IACjE,IAAIH,QAAQ,GAAGC,WAAW,CAACH,MAAM,CAAC;IAClC,OAAOE,QAAQ,KAAK,IAAI,GAAGA,QAAQ,CAACR,WAAW,CAACW,KAAK,CAAC,GAAG,IAAI;EAC/D,CAAC;EACD,OAAOlF,GAAG;AACZ,CAAC,CAACR,IAAI,CAAC;AACP;AACAF,MAAM,CAAC6F,KAAK,CAACnF,GAAG,EAAER,IAAI,CAAC;AACvB,SAASwF,WAAWA,CAACH,MAAM,EAAE;EAC3B,IAAIO,QAAQ,GAAGP,MAAM,CAACO,QAAQ;EAC9B,IAAIC,WAAW,GAAGR,MAAM,CAACQ,WAAW;EACpC,OAAOD,QAAQ,GAAGA,QAAQ,CAACE,gBAAgB,GAAGD,WAAW,GAAGA,WAAW,CAACC,gBAAgB,CAAC;EAAA,GACtF,CAACD,WAAW,CAACE,sBAAsB,CAAC,KAAK,EAAE7F,gBAAgB,CAAC,CAAC8F,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEF,gBAAgB,GAAG,IAAI;AAC1G;AACA,eAAetF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}