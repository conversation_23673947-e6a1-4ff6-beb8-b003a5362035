{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { usePanelContext } from \"../../context\";\nimport useScrollTo from \"./useScrollTo\";\nvar SCROLL_DELAY = 300;\n// Not use JSON.stringify to avoid dead loop\nfunction flattenUnits(units) {\n  return units.map(function (_ref) {\n    var value = _ref.value,\n      label = _ref.label,\n      disabled = _ref.disabled;\n    return [value, label, disabled].join(',');\n  }).join(';');\n}\nexport default function TimeColumn(props) {\n  var units = props.units,\n    value = props.value,\n    optionalValue = props.optionalValue,\n    type = props.type,\n    onChange = props.onChange,\n    onHover = props.onHover,\n    onDblClick = props.onDblClick,\n    changeOnScroll = props.changeOnScroll;\n  var _usePanelContext = usePanelContext(),\n    prefixCls = _usePanelContext.prefixCls,\n    cellRender = _usePanelContext.cellRender,\n    now = _usePanelContext.now,\n    locale = _usePanelContext.locale;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n  var cellPrefixCls = \"\".concat(prefixCls, \"-time-panel-cell\");\n\n  // ========================== Refs ==========================\n  var ulRef = React.useRef(null);\n\n  // ========================= Scroll =========================\n  var checkDelayRef = React.useRef();\n  var clearDelayCheck = function clearDelayCheck() {\n    clearTimeout(checkDelayRef.current);\n  };\n\n  // ========================== Sync ==========================\n  var _useScrollTo = useScrollTo(ulRef, value !== null && value !== void 0 ? value : optionalValue),\n    _useScrollTo2 = _slicedToArray(_useScrollTo, 3),\n    syncScroll = _useScrollTo2[0],\n    stopScroll = _useScrollTo2[1],\n    isScrolling = _useScrollTo2[2];\n\n  // Effect sync value scroll\n  useLayoutEffect(function () {\n    syncScroll();\n    clearDelayCheck();\n    return function () {\n      stopScroll();\n      clearDelayCheck();\n    };\n  }, [value, optionalValue, flattenUnits(units)]);\n\n  // ========================= Change =========================\n  // Scroll event if sync onScroll\n  var onInternalScroll = function onInternalScroll(event) {\n    clearDelayCheck();\n    var target = event.target;\n    if (!isScrolling() && changeOnScroll) {\n      checkDelayRef.current = setTimeout(function () {\n        var ul = ulRef.current;\n        var firstLiTop = ul.querySelector(\"li\").offsetTop;\n        var liList = Array.from(ul.querySelectorAll(\"li\"));\n        var liTopList = liList.map(function (li) {\n          return li.offsetTop - firstLiTop;\n        });\n        var liDistList = liTopList.map(function (top, index) {\n          if (units[index].disabled) {\n            return Number.MAX_SAFE_INTEGER;\n          }\n          return Math.abs(top - target.scrollTop);\n        });\n\n        // Find min distance index\n        var minDist = Math.min.apply(Math, _toConsumableArray(liDistList));\n        var minDistIndex = liDistList.findIndex(function (dist) {\n          return dist === minDist;\n        });\n        var targetUnit = units[minDistIndex];\n        if (targetUnit && !targetUnit.disabled) {\n          onChange(targetUnit.value);\n        }\n      }, SCROLL_DELAY);\n    }\n  };\n\n  // ========================= Render =========================\n  var columnPrefixCls = \"\".concat(panelPrefixCls, \"-column\");\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: columnPrefixCls,\n    ref: ulRef,\n    \"data-type\": type,\n    onScroll: onInternalScroll\n  }, units.map(function (_ref2) {\n    var label = _ref2.label,\n      unitValue = _ref2.value,\n      disabled = _ref2.disabled;\n    var inner = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(cellPrefixCls, \"-inner\")\n    }, label);\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: unitValue,\n      className: classNames(cellPrefixCls, _defineProperty(_defineProperty({}, \"\".concat(cellPrefixCls, \"-selected\"), value === unitValue), \"\".concat(cellPrefixCls, \"-disabled\"), disabled)),\n      onClick: function onClick() {\n        if (!disabled) {\n          onChange(unitValue);\n        }\n      },\n      onDoubleClick: function onDoubleClick() {\n        if (!disabled && onDblClick) {\n          onDblClick();\n        }\n      },\n      onMouseEnter: function onMouseEnter() {\n        onHover(unitValue);\n      },\n      onMouseLeave: function onMouseLeave() {\n        onHover(null);\n      },\n      \"data-value\": unitValue\n    }, cellRender ? cellRender(unitValue, {\n      prefixCls: prefixCls,\n      originNode: inner,\n      today: now,\n      type: 'time',\n      subType: type,\n      locale: locale\n    }) : inner);\n  }));\n}", "map": {"version": 3, "names": ["_defineProperty", "_toConsumableArray", "_slicedToArray", "classNames", "useLayoutEffect", "React", "usePanelContext", "useScrollTo", "SCROLL_DELAY", "flattenUnits", "units", "map", "_ref", "value", "label", "disabled", "join", "TimeColumn", "props", "optionalValue", "type", "onChange", "onHover", "onDblClick", "changeOnScroll", "_usePanelContext", "prefixCls", "cellRender", "now", "locale", "panelPrefixCls", "concat", "cellPrefixCls", "ulRef", "useRef", "checkDelayRef", "clearDelayCheck", "clearTimeout", "current", "_useScrollTo", "_useScrollTo2", "syncScroll", "stopScroll", "isScrolling", "onInternalScroll", "event", "target", "setTimeout", "ul", "firstLiTop", "querySelector", "offsetTop", "liList", "Array", "from", "querySelectorAll", "liTopList", "li", "liDistList", "top", "index", "Number", "MAX_SAFE_INTEGER", "Math", "abs", "scrollTop", "minDist", "min", "apply", "minDistIndex", "findIndex", "dist", "targetUnit", "columnPrefixCls", "createElement", "className", "ref", "onScroll", "_ref2", "unitValue", "inner", "key", "onClick", "onDoubleClick", "onMouseEnter", "onMouseLeave", "originNode", "today", "subType"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/rc-picker/es/PickerPanel/TimePanel/TimePanelBody/TimeColumn.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { usePanelContext } from \"../../context\";\nimport useScrollTo from \"./useScrollTo\";\nvar SCROLL_DELAY = 300;\n// Not use JSON.stringify to avoid dead loop\nfunction flattenUnits(units) {\n  return units.map(function (_ref) {\n    var value = _ref.value,\n      label = _ref.label,\n      disabled = _ref.disabled;\n    return [value, label, disabled].join(',');\n  }).join(';');\n}\nexport default function TimeColumn(props) {\n  var units = props.units,\n    value = props.value,\n    optionalValue = props.optionalValue,\n    type = props.type,\n    onChange = props.onChange,\n    onHover = props.onHover,\n    onDblClick = props.onDblClick,\n    changeOnScroll = props.changeOnScroll;\n  var _usePanelContext = usePanelContext(),\n    prefixCls = _usePanelContext.prefixCls,\n    cellRender = _usePanelContext.cellRender,\n    now = _usePanelContext.now,\n    locale = _usePanelContext.locale;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n  var cellPrefixCls = \"\".concat(prefixCls, \"-time-panel-cell\");\n\n  // ========================== Refs ==========================\n  var ulRef = React.useRef(null);\n\n  // ========================= Scroll =========================\n  var checkDelayRef = React.useRef();\n  var clearDelayCheck = function clearDelayCheck() {\n    clearTimeout(checkDelayRef.current);\n  };\n\n  // ========================== Sync ==========================\n  var _useScrollTo = useScrollTo(ulRef, value !== null && value !== void 0 ? value : optionalValue),\n    _useScrollTo2 = _slicedToArray(_useScrollTo, 3),\n    syncScroll = _useScrollTo2[0],\n    stopScroll = _useScrollTo2[1],\n    isScrolling = _useScrollTo2[2];\n\n  // Effect sync value scroll\n  useLayoutEffect(function () {\n    syncScroll();\n    clearDelayCheck();\n    return function () {\n      stopScroll();\n      clearDelayCheck();\n    };\n  }, [value, optionalValue, flattenUnits(units)]);\n\n  // ========================= Change =========================\n  // Scroll event if sync onScroll\n  var onInternalScroll = function onInternalScroll(event) {\n    clearDelayCheck();\n    var target = event.target;\n    if (!isScrolling() && changeOnScroll) {\n      checkDelayRef.current = setTimeout(function () {\n        var ul = ulRef.current;\n        var firstLiTop = ul.querySelector(\"li\").offsetTop;\n        var liList = Array.from(ul.querySelectorAll(\"li\"));\n        var liTopList = liList.map(function (li) {\n          return li.offsetTop - firstLiTop;\n        });\n        var liDistList = liTopList.map(function (top, index) {\n          if (units[index].disabled) {\n            return Number.MAX_SAFE_INTEGER;\n          }\n          return Math.abs(top - target.scrollTop);\n        });\n\n        // Find min distance index\n        var minDist = Math.min.apply(Math, _toConsumableArray(liDistList));\n        var minDistIndex = liDistList.findIndex(function (dist) {\n          return dist === minDist;\n        });\n        var targetUnit = units[minDistIndex];\n        if (targetUnit && !targetUnit.disabled) {\n          onChange(targetUnit.value);\n        }\n      }, SCROLL_DELAY);\n    }\n  };\n\n  // ========================= Render =========================\n  var columnPrefixCls = \"\".concat(panelPrefixCls, \"-column\");\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: columnPrefixCls,\n    ref: ulRef,\n    \"data-type\": type,\n    onScroll: onInternalScroll\n  }, units.map(function (_ref2) {\n    var label = _ref2.label,\n      unitValue = _ref2.value,\n      disabled = _ref2.disabled;\n    var inner = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(cellPrefixCls, \"-inner\")\n    }, label);\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: unitValue,\n      className: classNames(cellPrefixCls, _defineProperty(_defineProperty({}, \"\".concat(cellPrefixCls, \"-selected\"), value === unitValue), \"\".concat(cellPrefixCls, \"-disabled\"), disabled)),\n      onClick: function onClick() {\n        if (!disabled) {\n          onChange(unitValue);\n        }\n      },\n      onDoubleClick: function onDoubleClick() {\n        if (!disabled && onDblClick) {\n          onDblClick();\n        }\n      },\n      onMouseEnter: function onMouseEnter() {\n        onHover(unitValue);\n      },\n      onMouseLeave: function onMouseLeave() {\n        onHover(null);\n      },\n      \"data-value\": unitValue\n    }, cellRender ? cellRender(unitValue, {\n      prefixCls: prefixCls,\n      originNode: inner,\n      today: now,\n      type: 'time',\n      subType: type,\n      locale: locale\n    }) : inner);\n  }));\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,eAAe;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,IAAIC,YAAY,GAAG,GAAG;AACtB;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC/B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpBC,KAAK,GAAGF,IAAI,CAACE,KAAK;MAClBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IAC1B,OAAO,CAACF,KAAK,EAAEC,KAAK,EAAEC,QAAQ,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC3C,CAAC,CAAC,CAACA,IAAI,CAAC,GAAG,CAAC;AACd;AACA,eAAe,SAASC,UAAUA,CAACC,KAAK,EAAE;EACxC,IAAIR,KAAK,GAAGQ,KAAK,CAACR,KAAK;IACrBG,KAAK,GAAGK,KAAK,CAACL,KAAK;IACnBM,aAAa,GAAGD,KAAK,CAACC,aAAa;IACnCC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACjBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvBC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,cAAc,GAAGN,KAAK,CAACM,cAAc;EACvC,IAAIC,gBAAgB,GAAGnB,eAAe,CAAC,CAAC;IACtCoB,SAAS,GAAGD,gBAAgB,CAACC,SAAS;IACtCC,UAAU,GAAGF,gBAAgB,CAACE,UAAU;IACxCC,GAAG,GAAGH,gBAAgB,CAACG,GAAG;IAC1BC,MAAM,GAAGJ,gBAAgB,CAACI,MAAM;EAClC,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,aAAa,CAAC;EACxD,IAAIM,aAAa,GAAG,EAAE,CAACD,MAAM,CAACL,SAAS,EAAE,kBAAkB,CAAC;;EAE5D;EACA,IAAIO,KAAK,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,IAAIC,aAAa,GAAG9B,KAAK,CAAC6B,MAAM,CAAC,CAAC;EAClC,IAAIE,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/CC,YAAY,CAACF,aAAa,CAACG,OAAO,CAAC;EACrC,CAAC;;EAED;EACA,IAAIC,YAAY,GAAGhC,WAAW,CAAC0B,KAAK,EAAEpB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGM,aAAa,CAAC;IAC/FqB,aAAa,GAAGtC,cAAc,CAACqC,YAAY,EAAE,CAAC,CAAC;IAC/CE,UAAU,GAAGD,aAAa,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,aAAa,CAAC,CAAC,CAAC;IAC7BG,WAAW,GAAGH,aAAa,CAAC,CAAC,CAAC;;EAEhC;EACApC,eAAe,CAAC,YAAY;IAC1BqC,UAAU,CAAC,CAAC;IACZL,eAAe,CAAC,CAAC;IACjB,OAAO,YAAY;MACjBM,UAAU,CAAC,CAAC;MACZN,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,CAACvB,KAAK,EAAEM,aAAa,EAAEV,YAAY,CAACC,KAAK,CAAC,CAAC,CAAC;;EAE/C;EACA;EACA,IAAIkC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAE;IACtDT,eAAe,CAAC,CAAC;IACjB,IAAIU,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,IAAI,CAACH,WAAW,CAAC,CAAC,IAAInB,cAAc,EAAE;MACpCW,aAAa,CAACG,OAAO,GAAGS,UAAU,CAAC,YAAY;QAC7C,IAAIC,EAAE,GAAGf,KAAK,CAACK,OAAO;QACtB,IAAIW,UAAU,GAAGD,EAAE,CAACE,aAAa,CAAC,IAAI,CAAC,CAACC,SAAS;QACjD,IAAIC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACN,EAAE,CAACO,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAIC,SAAS,GAAGJ,MAAM,CAACzC,GAAG,CAAC,UAAU8C,EAAE,EAAE;UACvC,OAAOA,EAAE,CAACN,SAAS,GAAGF,UAAU;QAClC,CAAC,CAAC;QACF,IAAIS,UAAU,GAAGF,SAAS,CAAC7C,GAAG,CAAC,UAAUgD,GAAG,EAAEC,KAAK,EAAE;UACnD,IAAIlD,KAAK,CAACkD,KAAK,CAAC,CAAC7C,QAAQ,EAAE;YACzB,OAAO8C,MAAM,CAACC,gBAAgB;UAChC;UACA,OAAOC,IAAI,CAACC,GAAG,CAACL,GAAG,GAAGb,MAAM,CAACmB,SAAS,CAAC;QACzC,CAAC,CAAC;;QAEF;QACA,IAAIC,OAAO,GAAGH,IAAI,CAACI,GAAG,CAACC,KAAK,CAACL,IAAI,EAAE9D,kBAAkB,CAACyD,UAAU,CAAC,CAAC;QAClE,IAAIW,YAAY,GAAGX,UAAU,CAACY,SAAS,CAAC,UAAUC,IAAI,EAAE;UACtD,OAAOA,IAAI,KAAKL,OAAO;QACzB,CAAC,CAAC;QACF,IAAIM,UAAU,GAAG9D,KAAK,CAAC2D,YAAY,CAAC;QACpC,IAAIG,UAAU,IAAI,CAACA,UAAU,CAACzD,QAAQ,EAAE;UACtCM,QAAQ,CAACmD,UAAU,CAAC3D,KAAK,CAAC;QAC5B;MACF,CAAC,EAAEL,YAAY,CAAC;IAClB;EACF,CAAC;;EAED;EACA,IAAIiE,eAAe,GAAG,EAAE,CAAC1C,MAAM,CAACD,cAAc,EAAE,SAAS,CAAC;EAC1D,OAAO,aAAazB,KAAK,CAACqE,aAAa,CAAC,IAAI,EAAE;IAC5CC,SAAS,EAAEF,eAAe;IAC1BG,GAAG,EAAE3C,KAAK;IACV,WAAW,EAAEb,IAAI;IACjByD,QAAQ,EAAEjC;EACZ,CAAC,EAAElC,KAAK,CAACC,GAAG,CAAC,UAAUmE,KAAK,EAAE;IAC5B,IAAIhE,KAAK,GAAGgE,KAAK,CAAChE,KAAK;MACrBiE,SAAS,GAAGD,KAAK,CAACjE,KAAK;MACvBE,QAAQ,GAAG+D,KAAK,CAAC/D,QAAQ;IAC3B,IAAIiE,KAAK,GAAG,aAAa3E,KAAK,CAACqE,aAAa,CAAC,KAAK,EAAE;MAClDC,SAAS,EAAE,EAAE,CAAC5C,MAAM,CAACC,aAAa,EAAE,QAAQ;IAC9C,CAAC,EAAElB,KAAK,CAAC;IACT,OAAO,aAAaT,KAAK,CAACqE,aAAa,CAAC,IAAI,EAAE;MAC5CO,GAAG,EAAEF,SAAS;MACdJ,SAAS,EAAExE,UAAU,CAAC6B,aAAa,EAAEhC,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC+B,MAAM,CAACC,aAAa,EAAE,WAAW,CAAC,EAAEnB,KAAK,KAAKkE,SAAS,CAAC,EAAE,EAAE,CAAChD,MAAM,CAACC,aAAa,EAAE,WAAW,CAAC,EAAEjB,QAAQ,CAAC,CAAC;MACvLmE,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B,IAAI,CAACnE,QAAQ,EAAE;UACbM,QAAQ,CAAC0D,SAAS,CAAC;QACrB;MACF,CAAC;MACDI,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;QACtC,IAAI,CAACpE,QAAQ,IAAIQ,UAAU,EAAE;UAC3BA,UAAU,CAAC,CAAC;QACd;MACF,CAAC;MACD6D,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpC9D,OAAO,CAACyD,SAAS,CAAC;MACpB,CAAC;MACDM,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpC/D,OAAO,CAAC,IAAI,CAAC;MACf,CAAC;MACD,YAAY,EAAEyD;IAChB,CAAC,EAAEpD,UAAU,GAAGA,UAAU,CAACoD,SAAS,EAAE;MACpCrD,SAAS,EAAEA,SAAS;MACpB4D,UAAU,EAAEN,KAAK;MACjBO,KAAK,EAAE3D,GAAG;MACVR,IAAI,EAAE,MAAM;MACZoE,OAAO,EAAEpE,IAAI;MACbS,MAAM,EAAEA;IACV,CAAC,CAAC,GAAGmD,KAAK,CAAC;EACb,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}