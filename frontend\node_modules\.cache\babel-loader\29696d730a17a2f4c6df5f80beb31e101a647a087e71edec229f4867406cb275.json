{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner } from '../../util/model.js';\nimport * as modelHelper from './modelHelper.js';\nimport findPointFromSeries from './findPointFromSeries.js';\nimport { each, curry, bind, extend } from 'zrender/lib/core/util.js';\nvar inner = makeInner();\n/**\r\n * Basic logic: check all axis, if they do not demand show/highlight,\r\n * then hide/downplay them.\r\n *\r\n * @return content of event obj for echarts.connect.\r\n */\nexport default function axisTrigger(payload, ecModel, api) {\n  var currTrigger = payload.currTrigger;\n  var point = [payload.x, payload.y];\n  var finder = payload;\n  var dispatchAction = payload.dispatchAction || bind(api.dispatchAction, api);\n  var coordSysAxesInfo = ecModel.getComponent('axisPointer').coordSysAxesInfo;\n  // Pending\n  // See #6121. But we are not able to reproduce it yet.\n  if (!coordSysAxesInfo) {\n    return;\n  }\n  if (illegalPoint(point)) {\n    // Used in the default behavior of `connection`: use the sample seriesIndex\n    // and dataIndex. And also used in the tooltipView trigger.\n    point = findPointFromSeries({\n      seriesIndex: finder.seriesIndex,\n      // Do not use dataIndexInside from other ec instance.\n      // FIXME: auto detect it?\n      dataIndex: finder.dataIndex\n    }, ecModel).point;\n  }\n  var isIllegalPoint = illegalPoint(point);\n  // Axis and value can be specified when calling dispatchAction({type: 'updateAxisPointer'}).\n  // Notice: In this case, it is difficult to get the `point` (which is necessary to show\n  // tooltip, so if point is not given, we just use the point found by sample seriesIndex\n  // and dataIndex.\n  var inputAxesInfo = finder.axesInfo;\n  var axesInfo = coordSysAxesInfo.axesInfo;\n  var shouldHide = currTrigger === 'leave' || illegalPoint(point);\n  var outputPayload = {};\n  var showValueMap = {};\n  var dataByCoordSys = {\n    list: [],\n    map: {}\n  };\n  var updaters = {\n    showPointer: curry(showPointer, showValueMap),\n    showTooltip: curry(showTooltip, dataByCoordSys)\n  };\n  // Process for triggered axes.\n  each(coordSysAxesInfo.coordSysMap, function (coordSys, coordSysKey) {\n    // If a point given, it must be contained by the coordinate system.\n    var coordSysContainsPoint = isIllegalPoint || coordSys.containPoint(point);\n    each(coordSysAxesInfo.coordSysAxesInfo[coordSysKey], function (axisInfo, key) {\n      var axis = axisInfo.axis;\n      var inputAxisInfo = findInputAxisInfo(inputAxesInfo, axisInfo);\n      // If no inputAxesInfo, no axis is restricted.\n      if (!shouldHide && coordSysContainsPoint && (!inputAxesInfo || inputAxisInfo)) {\n        var val = inputAxisInfo && inputAxisInfo.value;\n        if (val == null && !isIllegalPoint) {\n          val = axis.pointToData(point);\n        }\n        val != null && processOnAxis(axisInfo, val, updaters, false, outputPayload);\n      }\n    });\n  });\n  // Process for linked axes.\n  var linkTriggers = {};\n  each(axesInfo, function (tarAxisInfo, tarKey) {\n    var linkGroup = tarAxisInfo.linkGroup;\n    // If axis has been triggered in the previous stage, it should not be triggered by link.\n    if (linkGroup && !showValueMap[tarKey]) {\n      each(linkGroup.axesInfo, function (srcAxisInfo, srcKey) {\n        var srcValItem = showValueMap[srcKey];\n        // If srcValItem exist, source axis is triggered, so link to target axis.\n        if (srcAxisInfo !== tarAxisInfo && srcValItem) {\n          var val = srcValItem.value;\n          linkGroup.mapper && (val = tarAxisInfo.axis.scale.parse(linkGroup.mapper(val, makeMapperParam(srcAxisInfo), makeMapperParam(tarAxisInfo))));\n          linkTriggers[tarAxisInfo.key] = val;\n        }\n      });\n    }\n  });\n  each(linkTriggers, function (val, tarKey) {\n    processOnAxis(axesInfo[tarKey], val, updaters, true, outputPayload);\n  });\n  updateModelActually(showValueMap, axesInfo, outputPayload);\n  dispatchTooltipActually(dataByCoordSys, point, payload, dispatchAction);\n  dispatchHighDownActually(axesInfo, dispatchAction, api);\n  return outputPayload;\n}\nfunction processOnAxis(axisInfo, newValue, updaters, noSnap, outputFinder) {\n  var axis = axisInfo.axis;\n  if (axis.scale.isBlank() || !axis.containData(newValue)) {\n    return;\n  }\n  if (!axisInfo.involveSeries) {\n    updaters.showPointer(axisInfo, newValue);\n    return;\n  }\n  // Heavy calculation. So put it after axis.containData checking.\n  var payloadInfo = buildPayloadsBySeries(newValue, axisInfo);\n  var payloadBatch = payloadInfo.payloadBatch;\n  var snapToValue = payloadInfo.snapToValue;\n  // Fill content of event obj for echarts.connect.\n  // By default use the first involved series data as a sample to connect.\n  if (payloadBatch[0] && outputFinder.seriesIndex == null) {\n    extend(outputFinder, payloadBatch[0]);\n  }\n  // If no linkSource input, this process is for collecting link\n  // target, where snap should not be accepted.\n  if (!noSnap && axisInfo.snap) {\n    if (axis.containData(snapToValue) && snapToValue != null) {\n      newValue = snapToValue;\n    }\n  }\n  updaters.showPointer(axisInfo, newValue, payloadBatch);\n  // Tooltip should always be snapToValue, otherwise there will be\n  // incorrect \"axis value ~ series value\" mapping displayed in tooltip.\n  updaters.showTooltip(axisInfo, payloadInfo, snapToValue);\n}\nfunction buildPayloadsBySeries(value, axisInfo) {\n  var axis = axisInfo.axis;\n  var dim = axis.dim;\n  var snapToValue = value;\n  var payloadBatch = [];\n  var minDist = Number.MAX_VALUE;\n  var minDiff = -1;\n  each(axisInfo.seriesModels, function (series, idx) {\n    var dataDim = series.getData().mapDimensionsAll(dim);\n    var seriesNestestValue;\n    var dataIndices;\n    if (series.getAxisTooltipData) {\n      var result = series.getAxisTooltipData(dataDim, value, axis);\n      dataIndices = result.dataIndices;\n      seriesNestestValue = result.nestestValue;\n    } else {\n      dataIndices = series.getData().indicesOfNearest(dataDim[0], value,\n      // Add a threshold to avoid find the wrong dataIndex\n      // when data length is not same.\n      // false,\n      axis.type === 'category' ? 0.5 : null);\n      if (!dataIndices.length) {\n        return;\n      }\n      seriesNestestValue = series.getData().get(dataDim[0], dataIndices[0]);\n    }\n    if (seriesNestestValue == null || !isFinite(seriesNestestValue)) {\n      return;\n    }\n    var diff = value - seriesNestestValue;\n    var dist = Math.abs(diff);\n    // Consider category case\n    if (dist <= minDist) {\n      if (dist < minDist || diff >= 0 && minDiff < 0) {\n        minDist = dist;\n        minDiff = diff;\n        snapToValue = seriesNestestValue;\n        payloadBatch.length = 0;\n      }\n      each(dataIndices, function (dataIndex) {\n        payloadBatch.push({\n          seriesIndex: series.seriesIndex,\n          dataIndexInside: dataIndex,\n          dataIndex: series.getData().getRawIndex(dataIndex)\n        });\n      });\n    }\n  });\n  return {\n    payloadBatch: payloadBatch,\n    snapToValue: snapToValue\n  };\n}\nfunction showPointer(showValueMap, axisInfo, value, payloadBatch) {\n  showValueMap[axisInfo.key] = {\n    value: value,\n    payloadBatch: payloadBatch\n  };\n}\nfunction showTooltip(dataByCoordSys, axisInfo, payloadInfo, value) {\n  var payloadBatch = payloadInfo.payloadBatch;\n  var axis = axisInfo.axis;\n  var axisModel = axis.model;\n  var axisPointerModel = axisInfo.axisPointerModel;\n  // If no data, do not create anything in dataByCoordSys,\n  // whose length will be used to judge whether dispatch action.\n  if (!axisInfo.triggerTooltip || !payloadBatch.length) {\n    return;\n  }\n  var coordSysModel = axisInfo.coordSys.model;\n  var coordSysKey = modelHelper.makeKey(coordSysModel);\n  var coordSysItem = dataByCoordSys.map[coordSysKey];\n  if (!coordSysItem) {\n    coordSysItem = dataByCoordSys.map[coordSysKey] = {\n      coordSysId: coordSysModel.id,\n      coordSysIndex: coordSysModel.componentIndex,\n      coordSysType: coordSysModel.type,\n      coordSysMainType: coordSysModel.mainType,\n      dataByAxis: []\n    };\n    dataByCoordSys.list.push(coordSysItem);\n  }\n  coordSysItem.dataByAxis.push({\n    axisDim: axis.dim,\n    axisIndex: axisModel.componentIndex,\n    axisType: axisModel.type,\n    axisId: axisModel.id,\n    value: value,\n    // Caustion: viewHelper.getValueLabel is actually on \"view stage\", which\n    // depends that all models have been updated. So it should not be performed\n    // here. Considering axisPointerModel used here is volatile, which is hard\n    // to be retrieve in TooltipView, we prepare parameters here.\n    valueLabelOpt: {\n      precision: axisPointerModel.get(['label', 'precision']),\n      formatter: axisPointerModel.get(['label', 'formatter'])\n    },\n    seriesDataIndices: payloadBatch.slice()\n  });\n}\nfunction updateModelActually(showValueMap, axesInfo, outputPayload) {\n  var outputAxesInfo = outputPayload.axesInfo = [];\n  // Basic logic: If no 'show' required, 'hide' this axisPointer.\n  each(axesInfo, function (axisInfo, key) {\n    var option = axisInfo.axisPointerModel.option;\n    var valItem = showValueMap[key];\n    if (valItem) {\n      !axisInfo.useHandle && (option.status = 'show');\n      option.value = valItem.value;\n      // For label formatter param and highlight.\n      option.seriesDataIndices = (valItem.payloadBatch || []).slice();\n    }\n    // When always show (e.g., handle used), remain\n    // original value and status.\n    else {\n      // If hide, value still need to be set, consider\n      // click legend to toggle axis blank.\n      !axisInfo.useHandle && (option.status = 'hide');\n    }\n    // If status is 'hide', should be no info in payload.\n    option.status === 'show' && outputAxesInfo.push({\n      axisDim: axisInfo.axis.dim,\n      axisIndex: axisInfo.axis.model.componentIndex,\n      value: option.value\n    });\n  });\n}\nfunction dispatchTooltipActually(dataByCoordSys, point, payload, dispatchAction) {\n  // Basic logic: If no showTip required, hideTip will be dispatched.\n  if (illegalPoint(point) || !dataByCoordSys.list.length) {\n    dispatchAction({\n      type: 'hideTip'\n    });\n    return;\n  }\n  // In most case only one axis (or event one series is used). It is\n  // convenient to fetch payload.seriesIndex and payload.dataIndex\n  // directly. So put the first seriesIndex and dataIndex of the first\n  // axis on the payload.\n  var sampleItem = ((dataByCoordSys.list[0].dataByAxis[0] || {}).seriesDataIndices || [])[0] || {};\n  dispatchAction({\n    type: 'showTip',\n    escapeConnect: true,\n    x: point[0],\n    y: point[1],\n    tooltipOption: payload.tooltipOption,\n    position: payload.position,\n    dataIndexInside: sampleItem.dataIndexInside,\n    dataIndex: sampleItem.dataIndex,\n    seriesIndex: sampleItem.seriesIndex,\n    dataByCoordSys: dataByCoordSys.list\n  });\n}\nfunction dispatchHighDownActually(axesInfo, dispatchAction, api) {\n  // FIXME\n  // highlight status modification should be a stage of main process?\n  // (Consider confilct (e.g., legend and axisPointer) and setOption)\n  var zr = api.getZr();\n  var highDownKey = 'axisPointerLastHighlights';\n  var lastHighlights = inner(zr)[highDownKey] || {};\n  var newHighlights = inner(zr)[highDownKey] = {};\n  // Update highlight/downplay status according to axisPointer model.\n  // Build hash map and remove duplicate incidentally.\n  each(axesInfo, function (axisInfo, key) {\n    var option = axisInfo.axisPointerModel.option;\n    option.status === 'show' && axisInfo.triggerEmphasis && each(option.seriesDataIndices, function (batchItem) {\n      var key = batchItem.seriesIndex + ' | ' + batchItem.dataIndex;\n      newHighlights[key] = batchItem;\n    });\n  });\n  // Diff.\n  var toHighlight = [];\n  var toDownplay = [];\n  each(lastHighlights, function (batchItem, key) {\n    !newHighlights[key] && toDownplay.push(batchItem);\n  });\n  each(newHighlights, function (batchItem, key) {\n    !lastHighlights[key] && toHighlight.push(batchItem);\n  });\n  toDownplay.length && api.dispatchAction({\n    type: 'downplay',\n    escapeConnect: true,\n    // Not blur others when highlight in axisPointer.\n    notBlur: true,\n    batch: toDownplay\n  });\n  toHighlight.length && api.dispatchAction({\n    type: 'highlight',\n    escapeConnect: true,\n    // Not blur others when highlight in axisPointer.\n    notBlur: true,\n    batch: toHighlight\n  });\n}\nfunction findInputAxisInfo(inputAxesInfo, axisInfo) {\n  for (var i = 0; i < (inputAxesInfo || []).length; i++) {\n    var inputAxisInfo = inputAxesInfo[i];\n    if (axisInfo.axis.dim === inputAxisInfo.axisDim && axisInfo.axis.model.componentIndex === inputAxisInfo.axisIndex) {\n      return inputAxisInfo;\n    }\n  }\n}\nfunction makeMapperParam(axisInfo) {\n  var axisModel = axisInfo.axis.model;\n  var item = {};\n  var dim = item.axisDim = axisInfo.axis.dim;\n  item.axisIndex = item[dim + 'AxisIndex'] = axisModel.componentIndex;\n  item.axisName = item[dim + 'AxisName'] = axisModel.name;\n  item.axisId = item[dim + 'AxisId'] = axisModel.id;\n  return item;\n}\nfunction illegalPoint(point) {\n  return !point || point[0] == null || isNaN(point[0]) || point[1] == null || isNaN(point[1]);\n}", "map": {"version": 3, "names": ["makeInner", "modelHelper", "findPointFromSeries", "each", "curry", "bind", "extend", "inner", "axisTrigger", "payload", "ecModel", "api", "currTrigger", "point", "x", "y", "finder", "dispatchAction", "coordSysAxesInfo", "getComponent", "illegalPoint", "seriesIndex", "dataIndex", "isIllegalPoint", "inputAxesInfo", "axesInfo", "shouldHide", "outputPayload", "showValueMap", "dataByCoordSys", "list", "map", "updaters", "showPointer", "showTooltip", "coordSysMap", "coordSys", "coordSysKey", "coordSysContainsPoint", "containPoint", "axisInfo", "key", "axis", "inputAxisInfo", "findInputAxisInfo", "val", "value", "pointToData", "processOnAxis", "linkTriggers", "tarAxisInfo", "tarKey", "linkGroup", "srcAxisInfo", "srcKey", "srcValItem", "mapper", "scale", "parse", "makeMapperParam", "updateModelActually", "dispatchTooltipActually", "dispatchHighDownActually", "newValue", "noSnap", "outputFinder", "isBlank", "containData", "involveSeries", "payloadInfo", "buildPayloadsBySeries", "payloadBatch", "snapToValue", "snap", "dim", "minDist", "Number", "MAX_VALUE", "minDiff", "seriesModels", "series", "idx", "dataDim", "getData", "mapDimensionsAll", "seriesNestestValue", "dataIndices", "getAxisTooltipData", "result", "nestest<PERSON><PERSON>ue", "indicesOfNearest", "type", "length", "get", "isFinite", "diff", "dist", "Math", "abs", "push", "dataIndexInside", "getRawIndex", "axisModel", "model", "axisPointerModel", "triggerTooltip", "coordSysModel", "<PERSON><PERSON><PERSON>", "coordSysItem", "coordSysId", "id", "coordSysIndex", "componentIndex", "coordSysType", "coordSysMainType", "mainType", "dataByAxis", "axisDim", "axisIndex", "axisType", "axisId", "valueLabelOpt", "precision", "formatter", "seriesDataIndices", "slice", "outputAxesInfo", "option", "valItem", "useHandle", "status", "sampleItem", "escapeConnect", "tooltipOption", "position", "zr", "getZr", "highDownKey", "lastHighlights", "newHighlights", "triggerEmphasis", "batchItem", "toHighlight", "toDownplay", "notBlur", "batch", "i", "item", "axisName", "name", "isNaN"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/component/axisPointer/axisTrigger.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner } from '../../util/model.js';\nimport * as modelHelper from './modelHelper.js';\nimport findPointFromSeries from './findPointFromSeries.js';\nimport { each, curry, bind, extend } from 'zrender/lib/core/util.js';\nvar inner = makeInner();\n/**\r\n * Basic logic: check all axis, if they do not demand show/highlight,\r\n * then hide/downplay them.\r\n *\r\n * @return content of event obj for echarts.connect.\r\n */\nexport default function axisTrigger(payload, ecModel, api) {\n  var currTrigger = payload.currTrigger;\n  var point = [payload.x, payload.y];\n  var finder = payload;\n  var dispatchAction = payload.dispatchAction || bind(api.dispatchAction, api);\n  var coordSysAxesInfo = ecModel.getComponent('axisPointer').coordSysAxesInfo;\n  // Pending\n  // See #6121. But we are not able to reproduce it yet.\n  if (!coordSysAxesInfo) {\n    return;\n  }\n  if (illegalPoint(point)) {\n    // Used in the default behavior of `connection`: use the sample seriesIndex\n    // and dataIndex. And also used in the tooltipView trigger.\n    point = findPointFromSeries({\n      seriesIndex: finder.seriesIndex,\n      // Do not use dataIndexInside from other ec instance.\n      // FIXME: auto detect it?\n      dataIndex: finder.dataIndex\n    }, ecModel).point;\n  }\n  var isIllegalPoint = illegalPoint(point);\n  // Axis and value can be specified when calling dispatchAction({type: 'updateAxisPointer'}).\n  // Notice: In this case, it is difficult to get the `point` (which is necessary to show\n  // tooltip, so if point is not given, we just use the point found by sample seriesIndex\n  // and dataIndex.\n  var inputAxesInfo = finder.axesInfo;\n  var axesInfo = coordSysAxesInfo.axesInfo;\n  var shouldHide = currTrigger === 'leave' || illegalPoint(point);\n  var outputPayload = {};\n  var showValueMap = {};\n  var dataByCoordSys = {\n    list: [],\n    map: {}\n  };\n  var updaters = {\n    showPointer: curry(showPointer, showValueMap),\n    showTooltip: curry(showTooltip, dataByCoordSys)\n  };\n  // Process for triggered axes.\n  each(coordSysAxesInfo.coordSysMap, function (coordSys, coordSysKey) {\n    // If a point given, it must be contained by the coordinate system.\n    var coordSysContainsPoint = isIllegalPoint || coordSys.containPoint(point);\n    each(coordSysAxesInfo.coordSysAxesInfo[coordSysKey], function (axisInfo, key) {\n      var axis = axisInfo.axis;\n      var inputAxisInfo = findInputAxisInfo(inputAxesInfo, axisInfo);\n      // If no inputAxesInfo, no axis is restricted.\n      if (!shouldHide && coordSysContainsPoint && (!inputAxesInfo || inputAxisInfo)) {\n        var val = inputAxisInfo && inputAxisInfo.value;\n        if (val == null && !isIllegalPoint) {\n          val = axis.pointToData(point);\n        }\n        val != null && processOnAxis(axisInfo, val, updaters, false, outputPayload);\n      }\n    });\n  });\n  // Process for linked axes.\n  var linkTriggers = {};\n  each(axesInfo, function (tarAxisInfo, tarKey) {\n    var linkGroup = tarAxisInfo.linkGroup;\n    // If axis has been triggered in the previous stage, it should not be triggered by link.\n    if (linkGroup && !showValueMap[tarKey]) {\n      each(linkGroup.axesInfo, function (srcAxisInfo, srcKey) {\n        var srcValItem = showValueMap[srcKey];\n        // If srcValItem exist, source axis is triggered, so link to target axis.\n        if (srcAxisInfo !== tarAxisInfo && srcValItem) {\n          var val = srcValItem.value;\n          linkGroup.mapper && (val = tarAxisInfo.axis.scale.parse(linkGroup.mapper(val, makeMapperParam(srcAxisInfo), makeMapperParam(tarAxisInfo))));\n          linkTriggers[tarAxisInfo.key] = val;\n        }\n      });\n    }\n  });\n  each(linkTriggers, function (val, tarKey) {\n    processOnAxis(axesInfo[tarKey], val, updaters, true, outputPayload);\n  });\n  updateModelActually(showValueMap, axesInfo, outputPayload);\n  dispatchTooltipActually(dataByCoordSys, point, payload, dispatchAction);\n  dispatchHighDownActually(axesInfo, dispatchAction, api);\n  return outputPayload;\n}\nfunction processOnAxis(axisInfo, newValue, updaters, noSnap, outputFinder) {\n  var axis = axisInfo.axis;\n  if (axis.scale.isBlank() || !axis.containData(newValue)) {\n    return;\n  }\n  if (!axisInfo.involveSeries) {\n    updaters.showPointer(axisInfo, newValue);\n    return;\n  }\n  // Heavy calculation. So put it after axis.containData checking.\n  var payloadInfo = buildPayloadsBySeries(newValue, axisInfo);\n  var payloadBatch = payloadInfo.payloadBatch;\n  var snapToValue = payloadInfo.snapToValue;\n  // Fill content of event obj for echarts.connect.\n  // By default use the first involved series data as a sample to connect.\n  if (payloadBatch[0] && outputFinder.seriesIndex == null) {\n    extend(outputFinder, payloadBatch[0]);\n  }\n  // If no linkSource input, this process is for collecting link\n  // target, where snap should not be accepted.\n  if (!noSnap && axisInfo.snap) {\n    if (axis.containData(snapToValue) && snapToValue != null) {\n      newValue = snapToValue;\n    }\n  }\n  updaters.showPointer(axisInfo, newValue, payloadBatch);\n  // Tooltip should always be snapToValue, otherwise there will be\n  // incorrect \"axis value ~ series value\" mapping displayed in tooltip.\n  updaters.showTooltip(axisInfo, payloadInfo, snapToValue);\n}\nfunction buildPayloadsBySeries(value, axisInfo) {\n  var axis = axisInfo.axis;\n  var dim = axis.dim;\n  var snapToValue = value;\n  var payloadBatch = [];\n  var minDist = Number.MAX_VALUE;\n  var minDiff = -1;\n  each(axisInfo.seriesModels, function (series, idx) {\n    var dataDim = series.getData().mapDimensionsAll(dim);\n    var seriesNestestValue;\n    var dataIndices;\n    if (series.getAxisTooltipData) {\n      var result = series.getAxisTooltipData(dataDim, value, axis);\n      dataIndices = result.dataIndices;\n      seriesNestestValue = result.nestestValue;\n    } else {\n      dataIndices = series.getData().indicesOfNearest(dataDim[0], value,\n      // Add a threshold to avoid find the wrong dataIndex\n      // when data length is not same.\n      // false,\n      axis.type === 'category' ? 0.5 : null);\n      if (!dataIndices.length) {\n        return;\n      }\n      seriesNestestValue = series.getData().get(dataDim[0], dataIndices[0]);\n    }\n    if (seriesNestestValue == null || !isFinite(seriesNestestValue)) {\n      return;\n    }\n    var diff = value - seriesNestestValue;\n    var dist = Math.abs(diff);\n    // Consider category case\n    if (dist <= minDist) {\n      if (dist < minDist || diff >= 0 && minDiff < 0) {\n        minDist = dist;\n        minDiff = diff;\n        snapToValue = seriesNestestValue;\n        payloadBatch.length = 0;\n      }\n      each(dataIndices, function (dataIndex) {\n        payloadBatch.push({\n          seriesIndex: series.seriesIndex,\n          dataIndexInside: dataIndex,\n          dataIndex: series.getData().getRawIndex(dataIndex)\n        });\n      });\n    }\n  });\n  return {\n    payloadBatch: payloadBatch,\n    snapToValue: snapToValue\n  };\n}\nfunction showPointer(showValueMap, axisInfo, value, payloadBatch) {\n  showValueMap[axisInfo.key] = {\n    value: value,\n    payloadBatch: payloadBatch\n  };\n}\nfunction showTooltip(dataByCoordSys, axisInfo, payloadInfo, value) {\n  var payloadBatch = payloadInfo.payloadBatch;\n  var axis = axisInfo.axis;\n  var axisModel = axis.model;\n  var axisPointerModel = axisInfo.axisPointerModel;\n  // If no data, do not create anything in dataByCoordSys,\n  // whose length will be used to judge whether dispatch action.\n  if (!axisInfo.triggerTooltip || !payloadBatch.length) {\n    return;\n  }\n  var coordSysModel = axisInfo.coordSys.model;\n  var coordSysKey = modelHelper.makeKey(coordSysModel);\n  var coordSysItem = dataByCoordSys.map[coordSysKey];\n  if (!coordSysItem) {\n    coordSysItem = dataByCoordSys.map[coordSysKey] = {\n      coordSysId: coordSysModel.id,\n      coordSysIndex: coordSysModel.componentIndex,\n      coordSysType: coordSysModel.type,\n      coordSysMainType: coordSysModel.mainType,\n      dataByAxis: []\n    };\n    dataByCoordSys.list.push(coordSysItem);\n  }\n  coordSysItem.dataByAxis.push({\n    axisDim: axis.dim,\n    axisIndex: axisModel.componentIndex,\n    axisType: axisModel.type,\n    axisId: axisModel.id,\n    value: value,\n    // Caustion: viewHelper.getValueLabel is actually on \"view stage\", which\n    // depends that all models have been updated. So it should not be performed\n    // here. Considering axisPointerModel used here is volatile, which is hard\n    // to be retrieve in TooltipView, we prepare parameters here.\n    valueLabelOpt: {\n      precision: axisPointerModel.get(['label', 'precision']),\n      formatter: axisPointerModel.get(['label', 'formatter'])\n    },\n    seriesDataIndices: payloadBatch.slice()\n  });\n}\nfunction updateModelActually(showValueMap, axesInfo, outputPayload) {\n  var outputAxesInfo = outputPayload.axesInfo = [];\n  // Basic logic: If no 'show' required, 'hide' this axisPointer.\n  each(axesInfo, function (axisInfo, key) {\n    var option = axisInfo.axisPointerModel.option;\n    var valItem = showValueMap[key];\n    if (valItem) {\n      !axisInfo.useHandle && (option.status = 'show');\n      option.value = valItem.value;\n      // For label formatter param and highlight.\n      option.seriesDataIndices = (valItem.payloadBatch || []).slice();\n    }\n    // When always show (e.g., handle used), remain\n    // original value and status.\n    else {\n      // If hide, value still need to be set, consider\n      // click legend to toggle axis blank.\n      !axisInfo.useHandle && (option.status = 'hide');\n    }\n    // If status is 'hide', should be no info in payload.\n    option.status === 'show' && outputAxesInfo.push({\n      axisDim: axisInfo.axis.dim,\n      axisIndex: axisInfo.axis.model.componentIndex,\n      value: option.value\n    });\n  });\n}\nfunction dispatchTooltipActually(dataByCoordSys, point, payload, dispatchAction) {\n  // Basic logic: If no showTip required, hideTip will be dispatched.\n  if (illegalPoint(point) || !dataByCoordSys.list.length) {\n    dispatchAction({\n      type: 'hideTip'\n    });\n    return;\n  }\n  // In most case only one axis (or event one series is used). It is\n  // convenient to fetch payload.seriesIndex and payload.dataIndex\n  // directly. So put the first seriesIndex and dataIndex of the first\n  // axis on the payload.\n  var sampleItem = ((dataByCoordSys.list[0].dataByAxis[0] || {}).seriesDataIndices || [])[0] || {};\n  dispatchAction({\n    type: 'showTip',\n    escapeConnect: true,\n    x: point[0],\n    y: point[1],\n    tooltipOption: payload.tooltipOption,\n    position: payload.position,\n    dataIndexInside: sampleItem.dataIndexInside,\n    dataIndex: sampleItem.dataIndex,\n    seriesIndex: sampleItem.seriesIndex,\n    dataByCoordSys: dataByCoordSys.list\n  });\n}\nfunction dispatchHighDownActually(axesInfo, dispatchAction, api) {\n  // FIXME\n  // highlight status modification should be a stage of main process?\n  // (Consider confilct (e.g., legend and axisPointer) and setOption)\n  var zr = api.getZr();\n  var highDownKey = 'axisPointerLastHighlights';\n  var lastHighlights = inner(zr)[highDownKey] || {};\n  var newHighlights = inner(zr)[highDownKey] = {};\n  // Update highlight/downplay status according to axisPointer model.\n  // Build hash map and remove duplicate incidentally.\n  each(axesInfo, function (axisInfo, key) {\n    var option = axisInfo.axisPointerModel.option;\n    option.status === 'show' && axisInfo.triggerEmphasis && each(option.seriesDataIndices, function (batchItem) {\n      var key = batchItem.seriesIndex + ' | ' + batchItem.dataIndex;\n      newHighlights[key] = batchItem;\n    });\n  });\n  // Diff.\n  var toHighlight = [];\n  var toDownplay = [];\n  each(lastHighlights, function (batchItem, key) {\n    !newHighlights[key] && toDownplay.push(batchItem);\n  });\n  each(newHighlights, function (batchItem, key) {\n    !lastHighlights[key] && toHighlight.push(batchItem);\n  });\n  toDownplay.length && api.dispatchAction({\n    type: 'downplay',\n    escapeConnect: true,\n    // Not blur others when highlight in axisPointer.\n    notBlur: true,\n    batch: toDownplay\n  });\n  toHighlight.length && api.dispatchAction({\n    type: 'highlight',\n    escapeConnect: true,\n    // Not blur others when highlight in axisPointer.\n    notBlur: true,\n    batch: toHighlight\n  });\n}\nfunction findInputAxisInfo(inputAxesInfo, axisInfo) {\n  for (var i = 0; i < (inputAxesInfo || []).length; i++) {\n    var inputAxisInfo = inputAxesInfo[i];\n    if (axisInfo.axis.dim === inputAxisInfo.axisDim && axisInfo.axis.model.componentIndex === inputAxisInfo.axisIndex) {\n      return inputAxisInfo;\n    }\n  }\n}\nfunction makeMapperParam(axisInfo) {\n  var axisModel = axisInfo.axis.model;\n  var item = {};\n  var dim = item.axisDim = axisInfo.axis.dim;\n  item.axisIndex = item[dim + 'AxisIndex'] = axisModel.componentIndex;\n  item.axisName = item[dim + 'AxisName'] = axisModel.name;\n  item.axisId = item[dim + 'AxisId'] = axisModel.id;\n  return item;\n}\nfunction illegalPoint(point) {\n  return !point || point[0] == null || isNaN(point[0]) || point[1] == null || isNaN(point[1]);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,KAAKC,WAAW,MAAM,kBAAkB;AAC/C,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,QAAQ,0BAA0B;AACpE,IAAIC,KAAK,GAAGP,SAAS,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASQ,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAE;EACzD,IAAIC,WAAW,GAAGH,OAAO,CAACG,WAAW;EACrC,IAAIC,KAAK,GAAG,CAACJ,OAAO,CAACK,CAAC,EAAEL,OAAO,CAACM,CAAC,CAAC;EAClC,IAAIC,MAAM,GAAGP,OAAO;EACpB,IAAIQ,cAAc,GAAGR,OAAO,CAACQ,cAAc,IAAIZ,IAAI,CAACM,GAAG,CAACM,cAAc,EAAEN,GAAG,CAAC;EAC5E,IAAIO,gBAAgB,GAAGR,OAAO,CAACS,YAAY,CAAC,aAAa,CAAC,CAACD,gBAAgB;EAC3E;EACA;EACA,IAAI,CAACA,gBAAgB,EAAE;IACrB;EACF;EACA,IAAIE,YAAY,CAACP,KAAK,CAAC,EAAE;IACvB;IACA;IACAA,KAAK,GAAGX,mBAAmB,CAAC;MAC1BmB,WAAW,EAAEL,MAAM,CAACK,WAAW;MAC/B;MACA;MACAC,SAAS,EAAEN,MAAM,CAACM;IACpB,CAAC,EAAEZ,OAAO,CAAC,CAACG,KAAK;EACnB;EACA,IAAIU,cAAc,GAAGH,YAAY,CAACP,KAAK,CAAC;EACxC;EACA;EACA;EACA;EACA,IAAIW,aAAa,GAAGR,MAAM,CAACS,QAAQ;EACnC,IAAIA,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ;EACxC,IAAIC,UAAU,GAAGd,WAAW,KAAK,OAAO,IAAIQ,YAAY,CAACP,KAAK,CAAC;EAC/D,IAAIc,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIC,cAAc,GAAG;IACnBC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,CAAC;EACR,CAAC;EACD,IAAIC,QAAQ,GAAG;IACbC,WAAW,EAAE7B,KAAK,CAAC6B,WAAW,EAAEL,YAAY,CAAC;IAC7CM,WAAW,EAAE9B,KAAK,CAAC8B,WAAW,EAAEL,cAAc;EAChD,CAAC;EACD;EACA1B,IAAI,CAACe,gBAAgB,CAACiB,WAAW,EAAE,UAAUC,QAAQ,EAAEC,WAAW,EAAE;IAClE;IACA,IAAIC,qBAAqB,GAAGf,cAAc,IAAIa,QAAQ,CAACG,YAAY,CAAC1B,KAAK,CAAC;IAC1EV,IAAI,CAACe,gBAAgB,CAACA,gBAAgB,CAACmB,WAAW,CAAC,EAAE,UAAUG,QAAQ,EAAEC,GAAG,EAAE;MAC5E,IAAIC,IAAI,GAAGF,QAAQ,CAACE,IAAI;MACxB,IAAIC,aAAa,GAAGC,iBAAiB,CAACpB,aAAa,EAAEgB,QAAQ,CAAC;MAC9D;MACA,IAAI,CAACd,UAAU,IAAIY,qBAAqB,KAAK,CAACd,aAAa,IAAImB,aAAa,CAAC,EAAE;QAC7E,IAAIE,GAAG,GAAGF,aAAa,IAAIA,aAAa,CAACG,KAAK;QAC9C,IAAID,GAAG,IAAI,IAAI,IAAI,CAACtB,cAAc,EAAE;UAClCsB,GAAG,GAAGH,IAAI,CAACK,WAAW,CAAClC,KAAK,CAAC;QAC/B;QACAgC,GAAG,IAAI,IAAI,IAAIG,aAAa,CAACR,QAAQ,EAAEK,GAAG,EAAEb,QAAQ,EAAE,KAAK,EAAEL,aAAa,CAAC;MAC7E;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;EACA,IAAIsB,YAAY,GAAG,CAAC,CAAC;EACrB9C,IAAI,CAACsB,QAAQ,EAAE,UAAUyB,WAAW,EAAEC,MAAM,EAAE;IAC5C,IAAIC,SAAS,GAAGF,WAAW,CAACE,SAAS;IACrC;IACA,IAAIA,SAAS,IAAI,CAACxB,YAAY,CAACuB,MAAM,CAAC,EAAE;MACtChD,IAAI,CAACiD,SAAS,CAAC3B,QAAQ,EAAE,UAAU4B,WAAW,EAAEC,MAAM,EAAE;QACtD,IAAIC,UAAU,GAAG3B,YAAY,CAAC0B,MAAM,CAAC;QACrC;QACA,IAAID,WAAW,KAAKH,WAAW,IAAIK,UAAU,EAAE;UAC7C,IAAIV,GAAG,GAAGU,UAAU,CAACT,KAAK;UAC1BM,SAAS,CAACI,MAAM,KAAKX,GAAG,GAAGK,WAAW,CAACR,IAAI,CAACe,KAAK,CAACC,KAAK,CAACN,SAAS,CAACI,MAAM,CAACX,GAAG,EAAEc,eAAe,CAACN,WAAW,CAAC,EAAEM,eAAe,CAACT,WAAW,CAAC,CAAC,CAAC,CAAC;UAC3ID,YAAY,CAACC,WAAW,CAACT,GAAG,CAAC,GAAGI,GAAG;QACrC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF1C,IAAI,CAAC8C,YAAY,EAAE,UAAUJ,GAAG,EAAEM,MAAM,EAAE;IACxCH,aAAa,CAACvB,QAAQ,CAAC0B,MAAM,CAAC,EAAEN,GAAG,EAAEb,QAAQ,EAAE,IAAI,EAAEL,aAAa,CAAC;EACrE,CAAC,CAAC;EACFiC,mBAAmB,CAAChC,YAAY,EAAEH,QAAQ,EAAEE,aAAa,CAAC;EAC1DkC,uBAAuB,CAAChC,cAAc,EAAEhB,KAAK,EAAEJ,OAAO,EAAEQ,cAAc,CAAC;EACvE6C,wBAAwB,CAACrC,QAAQ,EAAER,cAAc,EAAEN,GAAG,CAAC;EACvD,OAAOgB,aAAa;AACtB;AACA,SAASqB,aAAaA,CAACR,QAAQ,EAAEuB,QAAQ,EAAE/B,QAAQ,EAAEgC,MAAM,EAAEC,YAAY,EAAE;EACzE,IAAIvB,IAAI,GAAGF,QAAQ,CAACE,IAAI;EACxB,IAAIA,IAAI,CAACe,KAAK,CAACS,OAAO,CAAC,CAAC,IAAI,CAACxB,IAAI,CAACyB,WAAW,CAACJ,QAAQ,CAAC,EAAE;IACvD;EACF;EACA,IAAI,CAACvB,QAAQ,CAAC4B,aAAa,EAAE;IAC3BpC,QAAQ,CAACC,WAAW,CAACO,QAAQ,EAAEuB,QAAQ,CAAC;IACxC;EACF;EACA;EACA,IAAIM,WAAW,GAAGC,qBAAqB,CAACP,QAAQ,EAAEvB,QAAQ,CAAC;EAC3D,IAAI+B,YAAY,GAAGF,WAAW,CAACE,YAAY;EAC3C,IAAIC,WAAW,GAAGH,WAAW,CAACG,WAAW;EACzC;EACA;EACA,IAAID,YAAY,CAAC,CAAC,CAAC,IAAIN,YAAY,CAAC5C,WAAW,IAAI,IAAI,EAAE;IACvDf,MAAM,CAAC2D,YAAY,EAAEM,YAAY,CAAC,CAAC,CAAC,CAAC;EACvC;EACA;EACA;EACA,IAAI,CAACP,MAAM,IAAIxB,QAAQ,CAACiC,IAAI,EAAE;IAC5B,IAAI/B,IAAI,CAACyB,WAAW,CAACK,WAAW,CAAC,IAAIA,WAAW,IAAI,IAAI,EAAE;MACxDT,QAAQ,GAAGS,WAAW;IACxB;EACF;EACAxC,QAAQ,CAACC,WAAW,CAACO,QAAQ,EAAEuB,QAAQ,EAAEQ,YAAY,CAAC;EACtD;EACA;EACAvC,QAAQ,CAACE,WAAW,CAACM,QAAQ,EAAE6B,WAAW,EAAEG,WAAW,CAAC;AAC1D;AACA,SAASF,qBAAqBA,CAACxB,KAAK,EAAEN,QAAQ,EAAE;EAC9C,IAAIE,IAAI,GAAGF,QAAQ,CAACE,IAAI;EACxB,IAAIgC,GAAG,GAAGhC,IAAI,CAACgC,GAAG;EAClB,IAAIF,WAAW,GAAG1B,KAAK;EACvB,IAAIyB,YAAY,GAAG,EAAE;EACrB,IAAII,OAAO,GAAGC,MAAM,CAACC,SAAS;EAC9B,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB3E,IAAI,CAACqC,QAAQ,CAACuC,YAAY,EAAE,UAAUC,MAAM,EAAEC,GAAG,EAAE;IACjD,IAAIC,OAAO,GAAGF,MAAM,CAACG,OAAO,CAAC,CAAC,CAACC,gBAAgB,CAACV,GAAG,CAAC;IACpD,IAAIW,kBAAkB;IACtB,IAAIC,WAAW;IACf,IAAIN,MAAM,CAACO,kBAAkB,EAAE;MAC7B,IAAIC,MAAM,GAAGR,MAAM,CAACO,kBAAkB,CAACL,OAAO,EAAEpC,KAAK,EAAEJ,IAAI,CAAC;MAC5D4C,WAAW,GAAGE,MAAM,CAACF,WAAW;MAChCD,kBAAkB,GAAGG,MAAM,CAACC,YAAY;IAC1C,CAAC,MAAM;MACLH,WAAW,GAAGN,MAAM,CAACG,OAAO,CAAC,CAAC,CAACO,gBAAgB,CAACR,OAAO,CAAC,CAAC,CAAC,EAAEpC,KAAK;MACjE;MACA;MACA;MACAJ,IAAI,CAACiD,IAAI,KAAK,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC;MACtC,IAAI,CAACL,WAAW,CAACM,MAAM,EAAE;QACvB;MACF;MACAP,kBAAkB,GAAGL,MAAM,CAACG,OAAO,CAAC,CAAC,CAACU,GAAG,CAACX,OAAO,CAAC,CAAC,CAAC,EAAEI,WAAW,CAAC,CAAC,CAAC,CAAC;IACvE;IACA,IAAID,kBAAkB,IAAI,IAAI,IAAI,CAACS,QAAQ,CAACT,kBAAkB,CAAC,EAAE;MAC/D;IACF;IACA,IAAIU,IAAI,GAAGjD,KAAK,GAAGuC,kBAAkB;IACrC,IAAIW,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC;IACzB;IACA,IAAIC,IAAI,IAAIrB,OAAO,EAAE;MACnB,IAAIqB,IAAI,GAAGrB,OAAO,IAAIoB,IAAI,IAAI,CAAC,IAAIjB,OAAO,GAAG,CAAC,EAAE;QAC9CH,OAAO,GAAGqB,IAAI;QACdlB,OAAO,GAAGiB,IAAI;QACdvB,WAAW,GAAGa,kBAAkB;QAChCd,YAAY,CAACqB,MAAM,GAAG,CAAC;MACzB;MACAzF,IAAI,CAACmF,WAAW,EAAE,UAAUhE,SAAS,EAAE;QACrCiD,YAAY,CAAC4B,IAAI,CAAC;UAChB9E,WAAW,EAAE2D,MAAM,CAAC3D,WAAW;UAC/B+E,eAAe,EAAE9E,SAAS;UAC1BA,SAAS,EAAE0D,MAAM,CAACG,OAAO,CAAC,CAAC,CAACkB,WAAW,CAAC/E,SAAS;QACnD,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAO;IACLiD,YAAY,EAAEA,YAAY;IAC1BC,WAAW,EAAEA;EACf,CAAC;AACH;AACA,SAASvC,WAAWA,CAACL,YAAY,EAAEY,QAAQ,EAAEM,KAAK,EAAEyB,YAAY,EAAE;EAChE3C,YAAY,CAACY,QAAQ,CAACC,GAAG,CAAC,GAAG;IAC3BK,KAAK,EAAEA,KAAK;IACZyB,YAAY,EAAEA;EAChB,CAAC;AACH;AACA,SAASrC,WAAWA,CAACL,cAAc,EAAEW,QAAQ,EAAE6B,WAAW,EAAEvB,KAAK,EAAE;EACjE,IAAIyB,YAAY,GAAGF,WAAW,CAACE,YAAY;EAC3C,IAAI7B,IAAI,GAAGF,QAAQ,CAACE,IAAI;EACxB,IAAI4D,SAAS,GAAG5D,IAAI,CAAC6D,KAAK;EAC1B,IAAIC,gBAAgB,GAAGhE,QAAQ,CAACgE,gBAAgB;EAChD;EACA;EACA,IAAI,CAAChE,QAAQ,CAACiE,cAAc,IAAI,CAAClC,YAAY,CAACqB,MAAM,EAAE;IACpD;EACF;EACA,IAAIc,aAAa,GAAGlE,QAAQ,CAACJ,QAAQ,CAACmE,KAAK;EAC3C,IAAIlE,WAAW,GAAGpC,WAAW,CAAC0G,OAAO,CAACD,aAAa,CAAC;EACpD,IAAIE,YAAY,GAAG/E,cAAc,CAACE,GAAG,CAACM,WAAW,CAAC;EAClD,IAAI,CAACuE,YAAY,EAAE;IACjBA,YAAY,GAAG/E,cAAc,CAACE,GAAG,CAACM,WAAW,CAAC,GAAG;MAC/CwE,UAAU,EAAEH,aAAa,CAACI,EAAE;MAC5BC,aAAa,EAAEL,aAAa,CAACM,cAAc;MAC3CC,YAAY,EAAEP,aAAa,CAACf,IAAI;MAChCuB,gBAAgB,EAAER,aAAa,CAACS,QAAQ;MACxCC,UAAU,EAAE;IACd,CAAC;IACDvF,cAAc,CAACC,IAAI,CAACqE,IAAI,CAACS,YAAY,CAAC;EACxC;EACAA,YAAY,CAACQ,UAAU,CAACjB,IAAI,CAAC;IAC3BkB,OAAO,EAAE3E,IAAI,CAACgC,GAAG;IACjB4C,SAAS,EAAEhB,SAAS,CAACU,cAAc;IACnCO,QAAQ,EAAEjB,SAAS,CAACX,IAAI;IACxB6B,MAAM,EAAElB,SAAS,CAACQ,EAAE;IACpBhE,KAAK,EAAEA,KAAK;IACZ;IACA;IACA;IACA;IACA2E,aAAa,EAAE;MACbC,SAAS,EAAElB,gBAAgB,CAACX,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;MACvD8B,SAAS,EAAEnB,gBAAgB,CAACX,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC;IACxD,CAAC;IACD+B,iBAAiB,EAAErD,YAAY,CAACsD,KAAK,CAAC;EACxC,CAAC,CAAC;AACJ;AACA,SAASjE,mBAAmBA,CAAChC,YAAY,EAAEH,QAAQ,EAAEE,aAAa,EAAE;EAClE,IAAImG,cAAc,GAAGnG,aAAa,CAACF,QAAQ,GAAG,EAAE;EAChD;EACAtB,IAAI,CAACsB,QAAQ,EAAE,UAAUe,QAAQ,EAAEC,GAAG,EAAE;IACtC,IAAIsF,MAAM,GAAGvF,QAAQ,CAACgE,gBAAgB,CAACuB,MAAM;IAC7C,IAAIC,OAAO,GAAGpG,YAAY,CAACa,GAAG,CAAC;IAC/B,IAAIuF,OAAO,EAAE;MACX,CAACxF,QAAQ,CAACyF,SAAS,KAAKF,MAAM,CAACG,MAAM,GAAG,MAAM,CAAC;MAC/CH,MAAM,CAACjF,KAAK,GAAGkF,OAAO,CAAClF,KAAK;MAC5B;MACAiF,MAAM,CAACH,iBAAiB,GAAG,CAACI,OAAO,CAACzD,YAAY,IAAI,EAAE,EAAEsD,KAAK,CAAC,CAAC;IACjE;IACA;IACA;IAAA,KACK;MACH;MACA;MACA,CAACrF,QAAQ,CAACyF,SAAS,KAAKF,MAAM,CAACG,MAAM,GAAG,MAAM,CAAC;IACjD;IACA;IACAH,MAAM,CAACG,MAAM,KAAK,MAAM,IAAIJ,cAAc,CAAC3B,IAAI,CAAC;MAC9CkB,OAAO,EAAE7E,QAAQ,CAACE,IAAI,CAACgC,GAAG;MAC1B4C,SAAS,EAAE9E,QAAQ,CAACE,IAAI,CAAC6D,KAAK,CAACS,cAAc;MAC7ClE,KAAK,EAAEiF,MAAM,CAACjF;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASe,uBAAuBA,CAAChC,cAAc,EAAEhB,KAAK,EAAEJ,OAAO,EAAEQ,cAAc,EAAE;EAC/E;EACA,IAAIG,YAAY,CAACP,KAAK,CAAC,IAAI,CAACgB,cAAc,CAACC,IAAI,CAAC8D,MAAM,EAAE;IACtD3E,cAAc,CAAC;MACb0E,IAAI,EAAE;IACR,CAAC,CAAC;IACF;EACF;EACA;EACA;EACA;EACA;EACA,IAAIwC,UAAU,GAAG,CAAC,CAACtG,cAAc,CAACC,IAAI,CAAC,CAAC,CAAC,CAACsF,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEQ,iBAAiB,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;EAChG3G,cAAc,CAAC;IACb0E,IAAI,EAAE,SAAS;IACfyC,aAAa,EAAE,IAAI;IACnBtH,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC;IACXE,CAAC,EAAEF,KAAK,CAAC,CAAC,CAAC;IACXwH,aAAa,EAAE5H,OAAO,CAAC4H,aAAa;IACpCC,QAAQ,EAAE7H,OAAO,CAAC6H,QAAQ;IAC1BlC,eAAe,EAAE+B,UAAU,CAAC/B,eAAe;IAC3C9E,SAAS,EAAE6G,UAAU,CAAC7G,SAAS;IAC/BD,WAAW,EAAE8G,UAAU,CAAC9G,WAAW;IACnCQ,cAAc,EAAEA,cAAc,CAACC;EACjC,CAAC,CAAC;AACJ;AACA,SAASgC,wBAAwBA,CAACrC,QAAQ,EAAER,cAAc,EAAEN,GAAG,EAAE;EAC/D;EACA;EACA;EACA,IAAI4H,EAAE,GAAG5H,GAAG,CAAC6H,KAAK,CAAC,CAAC;EACpB,IAAIC,WAAW,GAAG,2BAA2B;EAC7C,IAAIC,cAAc,GAAGnI,KAAK,CAACgI,EAAE,CAAC,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC;EACjD,IAAIE,aAAa,GAAGpI,KAAK,CAACgI,EAAE,CAAC,CAACE,WAAW,CAAC,GAAG,CAAC,CAAC;EAC/C;EACA;EACAtI,IAAI,CAACsB,QAAQ,EAAE,UAAUe,QAAQ,EAAEC,GAAG,EAAE;IACtC,IAAIsF,MAAM,GAAGvF,QAAQ,CAACgE,gBAAgB,CAACuB,MAAM;IAC7CA,MAAM,CAACG,MAAM,KAAK,MAAM,IAAI1F,QAAQ,CAACoG,eAAe,IAAIzI,IAAI,CAAC4H,MAAM,CAACH,iBAAiB,EAAE,UAAUiB,SAAS,EAAE;MAC1G,IAAIpG,GAAG,GAAGoG,SAAS,CAACxH,WAAW,GAAG,KAAK,GAAGwH,SAAS,CAACvH,SAAS;MAC7DqH,aAAa,CAAClG,GAAG,CAAC,GAAGoG,SAAS;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;EACA,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,UAAU,GAAG,EAAE;EACnB5I,IAAI,CAACuI,cAAc,EAAE,UAAUG,SAAS,EAAEpG,GAAG,EAAE;IAC7C,CAACkG,aAAa,CAAClG,GAAG,CAAC,IAAIsG,UAAU,CAAC5C,IAAI,CAAC0C,SAAS,CAAC;EACnD,CAAC,CAAC;EACF1I,IAAI,CAACwI,aAAa,EAAE,UAAUE,SAAS,EAAEpG,GAAG,EAAE;IAC5C,CAACiG,cAAc,CAACjG,GAAG,CAAC,IAAIqG,WAAW,CAAC3C,IAAI,CAAC0C,SAAS,CAAC;EACrD,CAAC,CAAC;EACFE,UAAU,CAACnD,MAAM,IAAIjF,GAAG,CAACM,cAAc,CAAC;IACtC0E,IAAI,EAAE,UAAU;IAChByC,aAAa,EAAE,IAAI;IACnB;IACAY,OAAO,EAAE,IAAI;IACbC,KAAK,EAAEF;EACT,CAAC,CAAC;EACFD,WAAW,CAAClD,MAAM,IAAIjF,GAAG,CAACM,cAAc,CAAC;IACvC0E,IAAI,EAAE,WAAW;IACjByC,aAAa,EAAE,IAAI;IACnB;IACAY,OAAO,EAAE,IAAI;IACbC,KAAK,EAAEH;EACT,CAAC,CAAC;AACJ;AACA,SAASlG,iBAAiBA,CAACpB,aAAa,EAAEgB,QAAQ,EAAE;EAClD,KAAK,IAAI0G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC1H,aAAa,IAAI,EAAE,EAAEoE,MAAM,EAAEsD,CAAC,EAAE,EAAE;IACrD,IAAIvG,aAAa,GAAGnB,aAAa,CAAC0H,CAAC,CAAC;IACpC,IAAI1G,QAAQ,CAACE,IAAI,CAACgC,GAAG,KAAK/B,aAAa,CAAC0E,OAAO,IAAI7E,QAAQ,CAACE,IAAI,CAAC6D,KAAK,CAACS,cAAc,KAAKrE,aAAa,CAAC2E,SAAS,EAAE;MACjH,OAAO3E,aAAa;IACtB;EACF;AACF;AACA,SAASgB,eAAeA,CAACnB,QAAQ,EAAE;EACjC,IAAI8D,SAAS,GAAG9D,QAAQ,CAACE,IAAI,CAAC6D,KAAK;EACnC,IAAI4C,IAAI,GAAG,CAAC,CAAC;EACb,IAAIzE,GAAG,GAAGyE,IAAI,CAAC9B,OAAO,GAAG7E,QAAQ,CAACE,IAAI,CAACgC,GAAG;EAC1CyE,IAAI,CAAC7B,SAAS,GAAG6B,IAAI,CAACzE,GAAG,GAAG,WAAW,CAAC,GAAG4B,SAAS,CAACU,cAAc;EACnEmC,IAAI,CAACC,QAAQ,GAAGD,IAAI,CAACzE,GAAG,GAAG,UAAU,CAAC,GAAG4B,SAAS,CAAC+C,IAAI;EACvDF,IAAI,CAAC3B,MAAM,GAAG2B,IAAI,CAACzE,GAAG,GAAG,QAAQ,CAAC,GAAG4B,SAAS,CAACQ,EAAE;EACjD,OAAOqC,IAAI;AACb;AACA,SAAS/H,YAAYA,CAACP,KAAK,EAAE;EAC3B,OAAO,CAACA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIyI,KAAK,CAACzI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIyI,KAAK,CAACzI,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}