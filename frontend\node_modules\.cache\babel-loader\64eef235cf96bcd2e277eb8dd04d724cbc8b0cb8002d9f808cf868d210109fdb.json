{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parsePercent } from '../../util/number.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// let PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nexport default function sunburstLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var center = seriesModel.get('center');\n    var radius = seriesModel.get('radius');\n    if (!zrUtil.isArray(radius)) {\n      radius = [0, radius];\n    }\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    var width = api.getWidth();\n    var height = api.getHeight();\n    var size = Math.min(width, height);\n    var cx = parsePercent(center[0], width);\n    var cy = parsePercent(center[1], height);\n    var r0 = parsePercent(radius[0], size / 2);\n    var r = parsePercent(radius[1], size / 2);\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var virtualRoot = seriesModel.getData().tree.root;\n    var treeRoot = seriesModel.getViewRoot();\n    var rootDepth = treeRoot.depth;\n    var sort = seriesModel.get('sort');\n    if (sort != null) {\n      initChildren(treeRoot, sort);\n    }\n    var validDataCount = 0;\n    zrUtil.each(treeRoot.children, function (child) {\n      !isNaN(child.getValue()) && validDataCount++;\n    });\n    var sum = treeRoot.getValue();\n    // Sum may be 0\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var renderRollupNode = treeRoot.depth > 0;\n    var levels = treeRoot.height - (renderRollupNode ? -1 : 1);\n    var rPerLevel = (r - r0) / (levels || 1);\n    var clockwise = seriesModel.get('clockwise');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum');\n    // In the case some sector angle is smaller than minAngle\n    // let restAngle = PI2;\n    // let valueSumLargerThanMinAngle = 0;\n    var dir = clockwise ? 1 : -1;\n    /**\r\n     * Render a tree\r\n     * @return increased angle\r\n     */\n    var renderNode = function (node, startAngle) {\n      if (!node) {\n        return;\n      }\n      var endAngle = startAngle;\n      // Render self\n      if (node !== virtualRoot) {\n        // Tree node is virtual, so it doesn't need to be drawn\n        var value = node.getValue();\n        var angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n        if (angle < minAngle) {\n          angle = minAngle;\n          // restAngle -= minAngle;\n        }\n        // else {\n        //     valueSumLargerThanMinAngle += value;\n        // }\n        endAngle = startAngle + dir * angle;\n        var depth = node.depth - rootDepth - (renderRollupNode ? -1 : 1);\n        var rStart = r0 + rPerLevel * depth;\n        var rEnd = r0 + rPerLevel * (depth + 1);\n        var levelModel = seriesModel.getLevelModel(node);\n        if (levelModel) {\n          var r0_1 = levelModel.get('r0', true);\n          var r_1 = levelModel.get('r', true);\n          var radius_1 = levelModel.get('radius', true);\n          if (radius_1 != null) {\n            r0_1 = radius_1[0];\n            r_1 = radius_1[1];\n          }\n          r0_1 != null && (rStart = parsePercent(r0_1, size / 2));\n          r_1 != null && (rEnd = parsePercent(r_1, size / 2));\n        }\n        node.setLayout({\n          angle: angle,\n          startAngle: startAngle,\n          endAngle: endAngle,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: rStart,\n          r: rEnd\n        });\n      }\n      // Render children\n      if (node.children && node.children.length) {\n        // currentAngle = startAngle;\n        var siblingAngle_1 = 0;\n        zrUtil.each(node.children, function (node) {\n          siblingAngle_1 += renderNode(node, startAngle + siblingAngle_1);\n        });\n      }\n      return endAngle - startAngle;\n    };\n    // Virtual root node for roll up\n    if (renderRollupNode) {\n      var rStart = r0;\n      var rEnd = r0 + rPerLevel;\n      var angle = Math.PI * 2;\n      virtualRoot.setLayout({\n        angle: angle,\n        startAngle: startAngle,\n        endAngle: startAngle + angle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: rStart,\n        r: rEnd\n      });\n    }\n    renderNode(treeRoot, startAngle);\n  });\n}\n/**\r\n * Init node children by order and update visual\r\n */\nfunction initChildren(node, sortOrder) {\n  var children = node.children || [];\n  node.children = sort(children, sortOrder);\n  // Init children recursively\n  if (children.length) {\n    zrUtil.each(node.children, function (child) {\n      initChildren(child, sortOrder);\n    });\n  }\n}\n/**\r\n * Sort children nodes\r\n *\r\n * @param {TreeNode[]}               children children of node to be sorted\r\n * @param {string | function | null} sort sort method\r\n *                                   See SunburstSeries.js for details.\r\n */\nfunction sort(children, sortOrder) {\n  if (zrUtil.isFunction(sortOrder)) {\n    var sortTargets = zrUtil.map(children, function (child, idx) {\n      var value = child.getValue();\n      return {\n        params: {\n          depth: child.depth,\n          height: child.height,\n          dataIndex: child.dataIndex,\n          getValue: function () {\n            return value;\n          }\n        },\n        index: idx\n      };\n    });\n    sortTargets.sort(function (a, b) {\n      return sortOrder(a.params, b.params);\n    });\n    return zrUtil.map(sortTargets, function (target) {\n      return children[target.index];\n    });\n  } else {\n    var isAsc_1 = sortOrder === 'asc';\n    return children.sort(function (a, b) {\n      var diff = (a.getValue() - b.getValue()) * (isAsc_1 ? 1 : -1);\n      return diff === 0 ? (a.dataIndex - b.dataIndex) * (isAsc_1 ? -1 : 1) : diff;\n    });\n  }\n}", "map": {"version": 3, "names": ["parsePercent", "zrUtil", "RADIAN", "Math", "PI", "sunburstLayout", "seriesType", "ecModel", "api", "eachSeriesByType", "seriesModel", "center", "get", "radius", "isArray", "width", "getWidth", "height", "getHeight", "size", "min", "cx", "cy", "r0", "r", "startAngle", "minAngle", "virtualRoot", "getData", "tree", "root", "treeRoot", "getViewRoot", "<PERSON><PERSON><PERSON>h", "depth", "sort", "initChildren", "validDataCount", "each", "children", "child", "isNaN", "getValue", "sum", "unitRadian", "renderRollupNode", "levels", "rPerLevel", "clockwise", "stillShowZeroSum", "dir", "renderNode", "node", "endAngle", "value", "angle", "rStart", "rEnd", "levelModel", "getLevelModel", "r0_1", "r_1", "radius_1", "setLayout", "length", "siblingAngle_1", "sortOrder", "isFunction", "sortTargets", "map", "idx", "params", "dataIndex", "index", "a", "b", "target", "isAsc_1", "diff"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/chart/sunburst/sunburstLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parsePercent } from '../../util/number.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// let PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nexport default function sunburstLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var center = seriesModel.get('center');\n    var radius = seriesModel.get('radius');\n    if (!zrUtil.isArray(radius)) {\n      radius = [0, radius];\n    }\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    var width = api.getWidth();\n    var height = api.getHeight();\n    var size = Math.min(width, height);\n    var cx = parsePercent(center[0], width);\n    var cy = parsePercent(center[1], height);\n    var r0 = parsePercent(radius[0], size / 2);\n    var r = parsePercent(radius[1], size / 2);\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var virtualRoot = seriesModel.getData().tree.root;\n    var treeRoot = seriesModel.getViewRoot();\n    var rootDepth = treeRoot.depth;\n    var sort = seriesModel.get('sort');\n    if (sort != null) {\n      initChildren(treeRoot, sort);\n    }\n    var validDataCount = 0;\n    zrUtil.each(treeRoot.children, function (child) {\n      !isNaN(child.getValue()) && validDataCount++;\n    });\n    var sum = treeRoot.getValue();\n    // Sum may be 0\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var renderRollupNode = treeRoot.depth > 0;\n    var levels = treeRoot.height - (renderRollupNode ? -1 : 1);\n    var rPerLevel = (r - r0) / (levels || 1);\n    var clockwise = seriesModel.get('clockwise');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum');\n    // In the case some sector angle is smaller than minAngle\n    // let restAngle = PI2;\n    // let valueSumLargerThanMinAngle = 0;\n    var dir = clockwise ? 1 : -1;\n    /**\r\n     * Render a tree\r\n     * @return increased angle\r\n     */\n    var renderNode = function (node, startAngle) {\n      if (!node) {\n        return;\n      }\n      var endAngle = startAngle;\n      // Render self\n      if (node !== virtualRoot) {\n        // Tree node is virtual, so it doesn't need to be drawn\n        var value = node.getValue();\n        var angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n        if (angle < minAngle) {\n          angle = minAngle;\n          // restAngle -= minAngle;\n        }\n        // else {\n        //     valueSumLargerThanMinAngle += value;\n        // }\n        endAngle = startAngle + dir * angle;\n        var depth = node.depth - rootDepth - (renderRollupNode ? -1 : 1);\n        var rStart = r0 + rPerLevel * depth;\n        var rEnd = r0 + rPerLevel * (depth + 1);\n        var levelModel = seriesModel.getLevelModel(node);\n        if (levelModel) {\n          var r0_1 = levelModel.get('r0', true);\n          var r_1 = levelModel.get('r', true);\n          var radius_1 = levelModel.get('radius', true);\n          if (radius_1 != null) {\n            r0_1 = radius_1[0];\n            r_1 = radius_1[1];\n          }\n          r0_1 != null && (rStart = parsePercent(r0_1, size / 2));\n          r_1 != null && (rEnd = parsePercent(r_1, size / 2));\n        }\n        node.setLayout({\n          angle: angle,\n          startAngle: startAngle,\n          endAngle: endAngle,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: rStart,\n          r: rEnd\n        });\n      }\n      // Render children\n      if (node.children && node.children.length) {\n        // currentAngle = startAngle;\n        var siblingAngle_1 = 0;\n        zrUtil.each(node.children, function (node) {\n          siblingAngle_1 += renderNode(node, startAngle + siblingAngle_1);\n        });\n      }\n      return endAngle - startAngle;\n    };\n    // Virtual root node for roll up\n    if (renderRollupNode) {\n      var rStart = r0;\n      var rEnd = r0 + rPerLevel;\n      var angle = Math.PI * 2;\n      virtualRoot.setLayout({\n        angle: angle,\n        startAngle: startAngle,\n        endAngle: startAngle + angle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: rStart,\n        r: rEnd\n      });\n    }\n    renderNode(treeRoot, startAngle);\n  });\n}\n/**\r\n * Init node children by order and update visual\r\n */\nfunction initChildren(node, sortOrder) {\n  var children = node.children || [];\n  node.children = sort(children, sortOrder);\n  // Init children recursively\n  if (children.length) {\n    zrUtil.each(node.children, function (child) {\n      initChildren(child, sortOrder);\n    });\n  }\n}\n/**\r\n * Sort children nodes\r\n *\r\n * @param {TreeNode[]}               children children of node to be sorted\r\n * @param {string | function | null} sort sort method\r\n *                                   See SunburstSeries.js for details.\r\n */\nfunction sort(children, sortOrder) {\n  if (zrUtil.isFunction(sortOrder)) {\n    var sortTargets = zrUtil.map(children, function (child, idx) {\n      var value = child.getValue();\n      return {\n        params: {\n          depth: child.depth,\n          height: child.height,\n          dataIndex: child.dataIndex,\n          getValue: function () {\n            return value;\n          }\n        },\n        index: idx\n      };\n    });\n    sortTargets.sort(function (a, b) {\n      return sortOrder(a.params, b.params);\n    });\n    return zrUtil.map(sortTargets, function (target) {\n      return children[target.index];\n    });\n  } else {\n    var isAsc_1 = sortOrder === 'asc';\n    return children.sort(function (a, b) {\n      var diff = (a.getValue() - b.getValue()) * (isAsc_1 ? 1 : -1);\n      return diff === 0 ? (a.dataIndex - b.dataIndex) * (isAsc_1 ? -1 : 1) : diff;\n    });\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,sBAAsB;AACnD,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD;AACA,IAAIC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAC1B,eAAe,SAASC,cAAcA,CAACC,UAAU,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAC/DD,OAAO,CAACE,gBAAgB,CAACH,UAAU,EAAE,UAAUI,WAAW,EAAE;IAC1D,IAAIC,MAAM,GAAGD,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAIC,MAAM,GAAGH,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAI,CAACX,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC,EAAE;MAC3BA,MAAM,GAAG,CAAC,CAAC,EAAEA,MAAM,CAAC;IACtB;IACA,IAAI,CAACZ,MAAM,CAACa,OAAO,CAACH,MAAM,CAAC,EAAE;MAC3BA,MAAM,GAAG,CAACA,MAAM,EAAEA,MAAM,CAAC;IAC3B;IACA,IAAII,KAAK,GAAGP,GAAG,CAACQ,QAAQ,CAAC,CAAC;IAC1B,IAAIC,MAAM,GAAGT,GAAG,CAACU,SAAS,CAAC,CAAC;IAC5B,IAAIC,IAAI,GAAGhB,IAAI,CAACiB,GAAG,CAACL,KAAK,EAAEE,MAAM,CAAC;IAClC,IAAII,EAAE,GAAGrB,YAAY,CAACW,MAAM,CAAC,CAAC,CAAC,EAAEI,KAAK,CAAC;IACvC,IAAIO,EAAE,GAAGtB,YAAY,CAACW,MAAM,CAAC,CAAC,CAAC,EAAEM,MAAM,CAAC;IACxC,IAAIM,EAAE,GAAGvB,YAAY,CAACa,MAAM,CAAC,CAAC,CAAC,EAAEM,IAAI,GAAG,CAAC,CAAC;IAC1C,IAAIK,CAAC,GAAGxB,YAAY,CAACa,MAAM,CAAC,CAAC,CAAC,EAAEM,IAAI,GAAG,CAAC,CAAC;IACzC,IAAIM,UAAU,GAAG,CAACf,WAAW,CAACE,GAAG,CAAC,YAAY,CAAC,GAAGV,MAAM;IACxD,IAAIwB,QAAQ,GAAGhB,WAAW,CAACE,GAAG,CAAC,UAAU,CAAC,GAAGV,MAAM;IACnD,IAAIyB,WAAW,GAAGjB,WAAW,CAACkB,OAAO,CAAC,CAAC,CAACC,IAAI,CAACC,IAAI;IACjD,IAAIC,QAAQ,GAAGrB,WAAW,CAACsB,WAAW,CAAC,CAAC;IACxC,IAAIC,SAAS,GAAGF,QAAQ,CAACG,KAAK;IAC9B,IAAIC,IAAI,GAAGzB,WAAW,CAACE,GAAG,CAAC,MAAM,CAAC;IAClC,IAAIuB,IAAI,IAAI,IAAI,EAAE;MAChBC,YAAY,CAACL,QAAQ,EAAEI,IAAI,CAAC;IAC9B;IACA,IAAIE,cAAc,GAAG,CAAC;IACtBpC,MAAM,CAACqC,IAAI,CAACP,QAAQ,CAACQ,QAAQ,EAAE,UAAUC,KAAK,EAAE;MAC9C,CAACC,KAAK,CAACD,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC,IAAIL,cAAc,EAAE;IAC9C,CAAC,CAAC;IACF,IAAIM,GAAG,GAAGZ,QAAQ,CAACW,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAIE,UAAU,GAAGzC,IAAI,CAACC,EAAE,IAAIuC,GAAG,IAAIN,cAAc,CAAC,GAAG,CAAC;IACtD,IAAIQ,gBAAgB,GAAGd,QAAQ,CAACG,KAAK,GAAG,CAAC;IACzC,IAAIY,MAAM,GAAGf,QAAQ,CAACd,MAAM,IAAI4B,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1D,IAAIE,SAAS,GAAG,CAACvB,CAAC,GAAGD,EAAE,KAAKuB,MAAM,IAAI,CAAC,CAAC;IACxC,IAAIE,SAAS,GAAGtC,WAAW,CAACE,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIqC,gBAAgB,GAAGvC,WAAW,CAACE,GAAG,CAAC,kBAAkB,CAAC;IAC1D;IACA;IACA;IACA,IAAIsC,GAAG,GAAGF,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B;AACJ;AACA;AACA;IACI,IAAIG,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAE3B,UAAU,EAAE;MAC3C,IAAI,CAAC2B,IAAI,EAAE;QACT;MACF;MACA,IAAIC,QAAQ,GAAG5B,UAAU;MACzB;MACA,IAAI2B,IAAI,KAAKzB,WAAW,EAAE;QACxB;QACA,IAAI2B,KAAK,GAAGF,IAAI,CAACV,QAAQ,CAAC,CAAC;QAC3B,IAAIa,KAAK,GAAGZ,GAAG,KAAK,CAAC,IAAIM,gBAAgB,GAAGL,UAAU,GAAGU,KAAK,GAAGV,UAAU;QAC3E,IAAIW,KAAK,GAAG7B,QAAQ,EAAE;UACpB6B,KAAK,GAAG7B,QAAQ;UAChB;QACF;QACA;QACA;QACA;QACA2B,QAAQ,GAAG5B,UAAU,GAAGyB,GAAG,GAAGK,KAAK;QACnC,IAAIrB,KAAK,GAAGkB,IAAI,CAAClB,KAAK,GAAGD,SAAS,IAAIY,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAChE,IAAIW,MAAM,GAAGjC,EAAE,GAAGwB,SAAS,GAAGb,KAAK;QACnC,IAAIuB,IAAI,GAAGlC,EAAE,GAAGwB,SAAS,IAAIb,KAAK,GAAG,CAAC,CAAC;QACvC,IAAIwB,UAAU,GAAGhD,WAAW,CAACiD,aAAa,CAACP,IAAI,CAAC;QAChD,IAAIM,UAAU,EAAE;UACd,IAAIE,IAAI,GAAGF,UAAU,CAAC9C,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;UACrC,IAAIiD,GAAG,GAAGH,UAAU,CAAC9C,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;UACnC,IAAIkD,QAAQ,GAAGJ,UAAU,CAAC9C,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;UAC7C,IAAIkD,QAAQ,IAAI,IAAI,EAAE;YACpBF,IAAI,GAAGE,QAAQ,CAAC,CAAC,CAAC;YAClBD,GAAG,GAAGC,QAAQ,CAAC,CAAC,CAAC;UACnB;UACAF,IAAI,IAAI,IAAI,KAAKJ,MAAM,GAAGxD,YAAY,CAAC4D,IAAI,EAAEzC,IAAI,GAAG,CAAC,CAAC,CAAC;UACvD0C,GAAG,IAAI,IAAI,KAAKJ,IAAI,GAAGzD,YAAY,CAAC6D,GAAG,EAAE1C,IAAI,GAAG,CAAC,CAAC,CAAC;QACrD;QACAiC,IAAI,CAACW,SAAS,CAAC;UACbR,KAAK,EAAEA,KAAK;UACZ9B,UAAU,EAAEA,UAAU;UACtB4B,QAAQ,EAAEA,QAAQ;UAClBL,SAAS,EAAEA,SAAS;UACpB3B,EAAE,EAAEA,EAAE;UACNC,EAAE,EAAEA,EAAE;UACNC,EAAE,EAAEiC,MAAM;UACVhC,CAAC,EAAEiC;QACL,CAAC,CAAC;MACJ;MACA;MACA,IAAIL,IAAI,CAACb,QAAQ,IAAIa,IAAI,CAACb,QAAQ,CAACyB,MAAM,EAAE;QACzC;QACA,IAAIC,cAAc,GAAG,CAAC;QACtBhE,MAAM,CAACqC,IAAI,CAACc,IAAI,CAACb,QAAQ,EAAE,UAAUa,IAAI,EAAE;UACzCa,cAAc,IAAId,UAAU,CAACC,IAAI,EAAE3B,UAAU,GAAGwC,cAAc,CAAC;QACjE,CAAC,CAAC;MACJ;MACA,OAAOZ,QAAQ,GAAG5B,UAAU;IAC9B,CAAC;IACD;IACA,IAAIoB,gBAAgB,EAAE;MACpB,IAAIW,MAAM,GAAGjC,EAAE;MACf,IAAIkC,IAAI,GAAGlC,EAAE,GAAGwB,SAAS;MACzB,IAAIQ,KAAK,GAAGpD,IAAI,CAACC,EAAE,GAAG,CAAC;MACvBuB,WAAW,CAACoC,SAAS,CAAC;QACpBR,KAAK,EAAEA,KAAK;QACZ9B,UAAU,EAAEA,UAAU;QACtB4B,QAAQ,EAAE5B,UAAU,GAAG8B,KAAK;QAC5BP,SAAS,EAAEA,SAAS;QACpB3B,EAAE,EAAEA,EAAE;QACNC,EAAE,EAAEA,EAAE;QACNC,EAAE,EAAEiC,MAAM;QACVhC,CAAC,EAAEiC;MACL,CAAC,CAAC;IACJ;IACAN,UAAU,CAACpB,QAAQ,EAAEN,UAAU,CAAC;EAClC,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,SAASW,YAAYA,CAACgB,IAAI,EAAEc,SAAS,EAAE;EACrC,IAAI3B,QAAQ,GAAGa,IAAI,CAACb,QAAQ,IAAI,EAAE;EAClCa,IAAI,CAACb,QAAQ,GAAGJ,IAAI,CAACI,QAAQ,EAAE2B,SAAS,CAAC;EACzC;EACA,IAAI3B,QAAQ,CAACyB,MAAM,EAAE;IACnB/D,MAAM,CAACqC,IAAI,CAACc,IAAI,CAACb,QAAQ,EAAE,UAAUC,KAAK,EAAE;MAC1CJ,YAAY,CAACI,KAAK,EAAE0B,SAAS,CAAC;IAChC,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/B,IAAIA,CAACI,QAAQ,EAAE2B,SAAS,EAAE;EACjC,IAAIjE,MAAM,CAACkE,UAAU,CAACD,SAAS,CAAC,EAAE;IAChC,IAAIE,WAAW,GAAGnE,MAAM,CAACoE,GAAG,CAAC9B,QAAQ,EAAE,UAAUC,KAAK,EAAE8B,GAAG,EAAE;MAC3D,IAAIhB,KAAK,GAAGd,KAAK,CAACE,QAAQ,CAAC,CAAC;MAC5B,OAAO;QACL6B,MAAM,EAAE;UACNrC,KAAK,EAAEM,KAAK,CAACN,KAAK;UAClBjB,MAAM,EAAEuB,KAAK,CAACvB,MAAM;UACpBuD,SAAS,EAAEhC,KAAK,CAACgC,SAAS;UAC1B9B,QAAQ,EAAE,SAAAA,CAAA,EAAY;YACpB,OAAOY,KAAK;UACd;QACF,CAAC;QACDmB,KAAK,EAAEH;MACT,CAAC;IACH,CAAC,CAAC;IACFF,WAAW,CAACjC,IAAI,CAAC,UAAUuC,CAAC,EAAEC,CAAC,EAAE;MAC/B,OAAOT,SAAS,CAACQ,CAAC,CAACH,MAAM,EAAEI,CAAC,CAACJ,MAAM,CAAC;IACtC,CAAC,CAAC;IACF,OAAOtE,MAAM,CAACoE,GAAG,CAACD,WAAW,EAAE,UAAUQ,MAAM,EAAE;MAC/C,OAAOrC,QAAQ,CAACqC,MAAM,CAACH,KAAK,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAII,OAAO,GAAGX,SAAS,KAAK,KAAK;IACjC,OAAO3B,QAAQ,CAACJ,IAAI,CAAC,UAAUuC,CAAC,EAAEC,CAAC,EAAE;MACnC,IAAIG,IAAI,GAAG,CAACJ,CAAC,CAAChC,QAAQ,CAAC,CAAC,GAAGiC,CAAC,CAACjC,QAAQ,CAAC,CAAC,KAAKmC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7D,OAAOC,IAAI,KAAK,CAAC,GAAG,CAACJ,CAAC,CAACF,SAAS,GAAGG,CAAC,CAACH,SAAS,KAAKK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI;IAC7E,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}