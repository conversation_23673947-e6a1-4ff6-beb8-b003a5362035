import { createSlice } from '@reduxjs/toolkit';

// 模拟订单数据
const mockOrders = [
  {
    id: 'ORD-2024-001',
    customerId: 1,
    customerName: '华为技术有限公司',
    title: '服务器设备采购',
    amount: 500000,
    status: 'completed',
    paymentStatus: 'paid',
    progress: 100,
    estimatedProfit: 125000,
    actualProfit: 120000,
    profitMargin: 24,
    actualProfitMargin: 24,
    createTime: '2024-01-15',
    deliveryDate: '2024-02-15',
    completedDate: '2024-02-10',
    salesperson: '李业务',
    priority: 'high',
    costs: {
      material: 300000,
      labor: 50000,
      other: 30000,
      total: 380000
    },
    payments: [
      { date: '2024-01-20', amount: 150000, type: '预付款' },
      { date: '2024-02-15', amount: 350000, type: '尾款' }
    ],
    tasks: [
      { name: '需求分析', status: 'completed', assignee: '张工程师' },
      { name: '方案设计', status: 'completed', assignee: '李工程师' },
      { name: '设备采购', status: 'completed', assignee: '王采购' },
      { name: '安装调试', status: 'completed', assignee: '赵技术' }
    ],
    bomId: 'BOM-001',
    bomName: '高性能服务器配置'
  },
  {
    id: 'ORD-2024-002',
    customerId: 2,
    customerName: '小米科技有限公司',
    title: '智能设备定制开发',
    amount: 300000,
    status: 'in_progress',
    paymentStatus: 'partial',
    progress: 75,
    estimatedProfit: 90000,
    actualProfit: 85000,
    profitMargin: 30,
    actualProfitMargin: 28.3,
    createTime: '2024-01-20',
    deliveryDate: '2024-03-20',
    completedDate: null,
    salesperson: '李业务',
    priority: 'medium',
    costs: {
      material: 150000,
      labor: 45000,
      other: 20000,
      total: 215000
    },
    payments: [
      { date: '2024-01-25', amount: 90000, type: '预付款' }
    ],
    receivables: [
      { amount: 210000, dueDate: '2024-03-25', status: 'pending' }
    ],
    tasks: [
      { name: '需求调研', status: 'completed', assignee: '张工程师' },
      { name: '原型开发', status: 'completed', assignee: '李工程师' },
      { name: '功能测试', status: 'in_progress', assignee: '王测试' },
      { name: '交付部署', status: 'pending', assignee: '赵技术' }
    ],
    bomId: 'BOM-002',
    bomName: '图形工作站配置'
  },
  {
    id: 'ORD-2024-003',
    customerId: 3,
    customerName: '阿里巴巴集团',
    title: '数据分析平台建设',
    amount: 800000,
    status: 'pending',
    paymentStatus: 'unpaid',
    progress: 10,
    estimatedProfit: 200000,
    actualProfit: 0,
    profitMargin: 25,
    actualProfitMargin: 0,
    createTime: '2024-02-01',
    deliveryDate: '2024-05-01',
    completedDate: null,
    salesperson: '李业务',
    priority: 'high',
    costs: {
      material: 400000,
      labor: 150000,
      other: 50000,
      total: 600000
    },
    payments: [],
    receivables: [
      { amount: 240000, dueDate: '2024-02-15', status: 'overdue' },
      { amount: 560000, dueDate: '2024-05-15', status: 'pending' }
    ],
    tasks: [
      { name: '需求分析', status: 'in_progress', assignee: '张工程师' },
      { name: '架构设计', status: 'pending', assignee: '李工程师' },
      { name: '开发实施', status: 'pending', assignee: '开发团队' },
      { name: '测试验收', status: 'pending', assignee: '王测试' }
    ],
    bomId: 'BOM-003',
    bomName: '基础办公电脑配置'
  }
];

const initialState = {
  orders: mockOrders,
  currentOrder: null,
  loading: false,
  error: null,
  searchKeyword: '',
  filters: {
    status: '',
    paymentStatus: '',
    salesperson: '',
    priority: '',
    dateRange: []
  },
  pagination: {
    current: 1,
    pageSize: 10,
    total: mockOrders.length
  }
};

const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setOrders: (state, action) => {
      state.orders = action.payload;
      state.pagination.total = action.payload.length;
    },
    addOrder: (state, action) => {
      const newOrder = {
        ...action.payload,
        id: `ORD-${new Date().getFullYear()}-${String(state.orders.length + 1).padStart(3, '0')}`,
        createTime: new Date().toISOString().split('T')[0],
        status: 'pending',
        paymentStatus: 'unpaid',
        progress: 0,
        actualProfit: 0,
        actualProfitMargin: 0,
        payments: [],
        tasks: []
      };
      state.orders.unshift(newOrder);
      state.pagination.total = state.orders.length;
    },
    updateOrder: (state, action) => {
      const index = state.orders.findIndex(o => o.id === action.payload.id);
      if (index !== -1) {
        state.orders[index] = { ...state.orders[index], ...action.payload };
      }
    },
    updateOrderStatus: (state, action) => {
      const { id, status } = action.payload;
      const order = state.orders.find(o => o.id === id);
      if (order) {
        order.status = status;
        if (status === 'completed') {
          order.progress = 100;
          order.completedDate = new Date().toISOString().split('T')[0];
        }
      }
    },
    updateOrderProgress: (state, action) => {
      const { id, progress } = action.payload;
      const order = state.orders.find(o => o.id === id);
      if (order) {
        order.progress = progress;
        if (progress === 100 && order.status !== 'completed') {
          order.status = 'completed';
          order.completedDate = new Date().toISOString().split('T')[0];
        }
      }
    },
    addPayment: (state, action) => {
      const { orderId, payment } = action.payload;
      const order = state.orders.find(o => o.id === orderId);
      if (order) {
        order.payments.push(payment);
        const totalPaid = order.payments.reduce((sum, p) => sum + p.amount, 0);
        if (totalPaid >= order.amount) {
          order.paymentStatus = 'paid';
        } else if (totalPaid > 0) {
          order.paymentStatus = 'partial';
        }
      }
    },
    deleteOrder: (state, action) => {
      state.orders = state.orders.filter(o => o.id !== action.payload);
      state.pagination.total = state.orders.length;
    },
    setCurrentOrder: (state, action) => {
      state.currentOrder = action.payload;
    },
    setSearchKeyword: (state, action) => {
      state.searchKeyword = action.payload;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    resetFilters: (state) => {
      state.searchKeyword = '';
      state.filters = {
        status: '',
        paymentStatus: '',
        salesperson: '',
        priority: '',
        dateRange: []
      };
      state.pagination.current = 1;
    }
  }
});

export const {
  setLoading,
  setError,
  clearError,
  setOrders,
  addOrder,
  updateOrder,
  updateOrderStatus,
  updateOrderProgress,
  addPayment,
  deleteOrder,
  setCurrentOrder,
  setSearchKeyword,
  setFilters,
  setPagination,
  resetFilters
} = orderSlice.actions;

// 选择器
export const selectOrders = (state) => state.order.orders;
export const selectCurrentOrder = (state) => state.order.currentOrder;
export const selectOrderLoading = (state) => state.order.loading;
export const selectOrderError = (state) => state.order.error;
export const selectOrderFilters = (state) => state.order.filters;
export const selectOrderPagination = (state) => state.order.pagination;

// 过滤后的订单列表
export const selectFilteredOrders = (state) => {
  const { orders, searchKeyword, filters } = state.order;
  
  return orders.filter(order => {
    // 搜索关键词过滤
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      if (!order.id.toLowerCase().includes(keyword) &&
          !order.customerName.toLowerCase().includes(keyword) &&
          !order.title.toLowerCase().includes(keyword)) {
        return false;
      }
    }
    
    // 状态过滤
    if (filters.status && order.status !== filters.status) {
      return false;
    }
    
    // 付款状态过滤
    if (filters.paymentStatus && order.paymentStatus !== filters.paymentStatus) {
      return false;
    }
    
    // 业务员过滤
    if (filters.salesperson && order.salesperson !== filters.salesperson) {
      return false;
    }
    
    // 优先级过滤
    if (filters.priority && order.priority !== filters.priority) {
      return false;
    }
    
    return true;
  });
};

export default orderSlice.reducer;
