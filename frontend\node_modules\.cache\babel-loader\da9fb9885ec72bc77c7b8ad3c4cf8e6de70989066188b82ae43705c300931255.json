{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.SizeSensorId = exports.SensorTabIndex = exports.SensorClassName = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar SizeSensorId = 'size-sensor-id';\nexports.SizeSensorId = SizeSensorId;\nvar SensorClassName = 'size-sensor-object';\nexports.SensorClassName = SensorClassName;\nvar SensorTabIndex = '-1';\nexports.SensorTabIndex = SensorTabIndex;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "SizeSensorId", "SensorTabIndex", "SensorClassName"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/size-sensor/lib/constant.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.SizeSensorId = exports.SensorTabIndex = exports.SensorClassName = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar SizeSensorId = 'size-sensor-id';\nexports.SizeSensorId = SizeSensorId;\nvar SensorClassName = 'size-sensor-object';\nexports.SensorClassName = SensorClassName;\nvar SensorTabIndex = '-1';\nexports.SensorTabIndex = SensorTabIndex;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,cAAc,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AAChF;AACA;AACA;AACA;;AAEA,IAAIF,YAAY,GAAG,gBAAgB;AACnCF,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnC,IAAIE,eAAe,GAAG,oBAAoB;AAC1CJ,OAAO,CAACI,eAAe,GAAGA,eAAe;AACzC,IAAID,cAAc,GAAG,IAAI;AACzBH,OAAO,CAACG,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}