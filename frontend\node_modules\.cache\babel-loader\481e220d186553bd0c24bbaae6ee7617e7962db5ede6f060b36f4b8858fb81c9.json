{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parseSVG, makeViewBoxTransform } from 'zrender/lib/tool/parseSVG.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport Rect from 'zrender/lib/graphic/shape/Rect.js';\nimport { assert, createHashMap, each } from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { parseXML } from 'zrender/lib/tool/parseXML.js';\nimport { GeoSVGRegion } from './Region.js';\n/**\r\n * \"region available\" means that: enable users to set attribute `name=\"xxx\"` on those tags\r\n * to make it be a region.\r\n * 1. region styles and its label styles can be defined in echarts opton:\r\n * ```js\r\n * geo: {\r\n *     regions: [{\r\n *         name: 'xxx',\r\n *         itemStyle: { ... },\r\n *         label: { ... }\r\n *     }, {\r\n *         ...\r\n *     },\r\n *     ...]\r\n * };\r\n * ```\r\n * 2. name can be duplicated in different SVG tag. All of the tags with the same name share\r\n * a region option. For exampel if there are two <path> representing two lung lobes. They have\r\n * no common parents but both of them need to display label \"lung\" inside.\r\n */\nvar REGION_AVAILABLE_SVG_TAG_MAP = createHashMap(['rect', 'circle', 'line', 'ellipse', 'polygon', 'polyline', 'path',\n// <text> <tspan> are also enabled because some SVG might paint text itself,\n// but still need to trigger events or tooltip.\n'text', 'tspan',\n// <g> is also enabled because this case: if multiple tags share one name\n// and need label displayed, every tags will display the name, which is not\n// expected. So we can put them into a <g name=\"xxx\">. Thereby only one label\n// displayed and located based on the bounding rect of the <g>.\n'g']);\nvar GeoSVGResource = /** @class */function () {\n  function GeoSVGResource(mapName, svg) {\n    this.type = 'geoSVG';\n    // All used graphics. key: hostKey, value: root\n    this._usedGraphicMap = createHashMap();\n    // All unused graphics.\n    this._freedGraphics = [];\n    this._mapName = mapName;\n    // Only perform parse to XML object here, which might be time\n    // consiming for large SVG.\n    // Although convert XML to zrender element is also time consiming,\n    // if we do it here, the clone of zrender elements has to be\n    // required. So we do it once for each geo instance, util real\n    // performance issues call for optimizing it.\n    this._parsedXML = parseXML(svg);\n  }\n  GeoSVGResource.prototype.load = function /* nameMap: NameMap */\n  () {\n    // In the \"load\" stage, graphic need to be built to\n    // get boundingRect for geo coordinate system.\n    var firstGraphic = this._firstGraphic;\n    // Create the return data structure only when first graphic created.\n    // Because they will be used in geo coordinate system update stage,\n    // and `regions` will be mounted at `geo` coordinate system,\n    // in which there is no \"view\" info, so that it should better not to\n    // make references to graphic elements.\n    if (!firstGraphic) {\n      firstGraphic = this._firstGraphic = this._buildGraphic(this._parsedXML);\n      this._freedGraphics.push(firstGraphic);\n      this._boundingRect = this._firstGraphic.boundingRect.clone();\n      // PENDING: `nameMap` will not be supported until some real requirement come.\n      // if (nameMap) {\n      //     named = applyNameMap(named, nameMap);\n      // }\n      var _a = createRegions(firstGraphic.named),\n        regions = _a.regions,\n        regionsMap = _a.regionsMap;\n      this._regions = regions;\n      this._regionsMap = regionsMap;\n    }\n    return {\n      boundingRect: this._boundingRect,\n      regions: this._regions,\n      regionsMap: this._regionsMap\n    };\n  };\n  GeoSVGResource.prototype._buildGraphic = function (svgXML) {\n    var result;\n    var rootFromParse;\n    try {\n      result = svgXML && parseSVG(svgXML, {\n        ignoreViewBox: true,\n        ignoreRootClip: true\n      }) || {};\n      rootFromParse = result.root;\n      assert(rootFromParse != null);\n    } catch (e) {\n      throw new Error('Invalid svg format\\n' + e.message);\n    }\n    // Note: we keep the covenant that the root has no transform. So always add an extra root.\n    var root = new Group();\n    root.add(rootFromParse);\n    root.isGeoSVGGraphicRoot = true;\n    // [THE_RULE_OF_VIEWPORT_AND_VIEWBOX]\n    //\n    // Consider: `<svg width=\"...\" height=\"...\" viewBox=\"...\">`\n    // - the `width/height` we call it `svgWidth/svgHeight` for short.\n    // - `(0, 0, svgWidth, svgHeight)` defines the viewport of the SVG, or say,\n    //   \"viewport boundingRect\", or `boundingRect` for short.\n    // - `viewBox` defines the transform from the real content ot the viewport.\n    //   `viewBox` has the same unit as the content of SVG.\n    //   If `viewBox` exists, a transform is defined, so the unit of `svgWidth/svgHeight` become\n    //   different from the content of SVG. Otherwise, they are the same.\n    //\n    // If both `svgWidth/svgHeight/viewBox` are specified in a SVG file, the transform rule will be:\n    // 0. `boundingRect` is `(0, 0, svgWidth, svgHeight)`. Set it to Geo['_rect'] (View['_rect']).\n    // 1. Make a transform from `viewBox` to `boundingRect`.\n    //    Note: only support `preserveAspectRatio 'xMidYMid'` here. That is, this transform will preserve\n    //    the aspect ratio.\n    // 2. Make a transform from boundingRect to Geo['_viewRect'] (View['_viewRect'])\n    //    (`Geo`/`View` will do this job).\n    //    Note: this transform might not preserve aspect radio, which depending on how users specify\n    //    viewRect in echarts option (e.g., `geo.left/top/width/height` will not preserve aspect ratio,\n    //    but `geo.layoutCenter/layoutSize` will preserve aspect ratio).\n    //\n    // If `svgWidth/svgHeight` not specified, we use `viewBox` as the `boundingRect` to make the SVG\n    // layout look good.\n    //\n    // If neither `svgWidth/svgHeight` nor `viewBox` are not specified, we calculate the boundingRect\n    // of the SVG content and use them to make SVG layout look good.\n    var svgWidth = result.width;\n    var svgHeight = result.height;\n    var viewBoxRect = result.viewBoxRect;\n    var boundingRect = this._boundingRect;\n    if (!boundingRect) {\n      var bRectX = void 0;\n      var bRectY = void 0;\n      var bRectWidth = void 0;\n      var bRectHeight = void 0;\n      if (svgWidth != null) {\n        bRectX = 0;\n        bRectWidth = svgWidth;\n      } else if (viewBoxRect) {\n        bRectX = viewBoxRect.x;\n        bRectWidth = viewBoxRect.width;\n      }\n      if (svgHeight != null) {\n        bRectY = 0;\n        bRectHeight = svgHeight;\n      } else if (viewBoxRect) {\n        bRectY = viewBoxRect.y;\n        bRectHeight = viewBoxRect.height;\n      }\n      // If both viewBox and svgWidth/svgHeight not specified,\n      // we have to determine how to layout those element to make them look good.\n      if (bRectX == null || bRectY == null) {\n        var calculatedBoundingRect = rootFromParse.getBoundingRect();\n        if (bRectX == null) {\n          bRectX = calculatedBoundingRect.x;\n          bRectWidth = calculatedBoundingRect.width;\n        }\n        if (bRectY == null) {\n          bRectY = calculatedBoundingRect.y;\n          bRectHeight = calculatedBoundingRect.height;\n        }\n      }\n      boundingRect = this._boundingRect = new BoundingRect(bRectX, bRectY, bRectWidth, bRectHeight);\n    }\n    if (viewBoxRect) {\n      var viewBoxTransform = makeViewBoxTransform(viewBoxRect, boundingRect);\n      // Only support `preserveAspectRatio 'xMidYMid'`\n      rootFromParse.scaleX = rootFromParse.scaleY = viewBoxTransform.scale;\n      rootFromParse.x = viewBoxTransform.x;\n      rootFromParse.y = viewBoxTransform.y;\n    }\n    // SVG needs to clip based on `viewBox`. And some SVG files really rely on this feature.\n    // They do not strictly confine all of the content inside a display rect, but deliberately\n    // use a `viewBox` to define a displayable rect.\n    // PENDING:\n    // The drawback of the `setClipPath` here is: the region label (genereted by echarts) near the\n    // edge might also be clipped, because region labels are put as `textContent` of the SVG path.\n    root.setClipPath(new Rect({\n      shape: boundingRect.plain()\n    }));\n    var named = [];\n    each(result.named, function (namedItem) {\n      if (REGION_AVAILABLE_SVG_TAG_MAP.get(namedItem.svgNodeTagLower) != null) {\n        named.push(namedItem);\n        setSilent(namedItem.el);\n      }\n    });\n    return {\n      root: root,\n      boundingRect: boundingRect,\n      named: named\n    };\n  };\n  /**\r\n   * Consider:\r\n   * (1) One graphic element can not be shared by different `geoView` running simultaneously.\r\n   *     Notice, also need to consider multiple echarts instances share a `mapRecord`.\r\n   * (2) Converting SVG to graphic elements is time consuming.\r\n   * (3) In the current architecture, `load` should be called frequently to get boundingRect,\r\n   *     and it is called without view info.\r\n   * So we maintain graphic elements in this module, and enables `view` to use/return these\r\n   * graphics from/to the pool with it's uid.\r\n   */\n  GeoSVGResource.prototype.useGraphic = function (hostKey /* , nameMap: NameMap */) {\n    var usedRootMap = this._usedGraphicMap;\n    var svgGraphic = usedRootMap.get(hostKey);\n    if (svgGraphic) {\n      return svgGraphic;\n    }\n    svgGraphic = this._freedGraphics.pop()\n    // use the first boundingRect to avoid duplicated boundingRect calculation.\n    || this._buildGraphic(this._parsedXML);\n    usedRootMap.set(hostKey, svgGraphic);\n    // PENDING: `nameMap` will not be supported until some real requirement come.\n    // `nameMap` can only be obtained from echarts option.\n    // The original `named` must not be modified.\n    // if (nameMap) {\n    //     svgGraphic = extend({}, svgGraphic);\n    //     svgGraphic.named = applyNameMap(svgGraphic.named, nameMap);\n    // }\n    return svgGraphic;\n  };\n  GeoSVGResource.prototype.freeGraphic = function (hostKey) {\n    var usedRootMap = this._usedGraphicMap;\n    var svgGraphic = usedRootMap.get(hostKey);\n    if (svgGraphic) {\n      usedRootMap.removeKey(hostKey);\n      this._freedGraphics.push(svgGraphic);\n    }\n  };\n  return GeoSVGResource;\n}();\nexport { GeoSVGResource };\nfunction setSilent(el) {\n  // Only named element has silent: false, other elements should\n  // act as background and has no user interaction.\n  el.silent = false;\n  // text|tspan will be converted to group.\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.silent = false;\n    });\n  }\n}\nfunction createRegions(named) {\n  var regions = [];\n  var regionsMap = createHashMap();\n  // Create resions only for the first graphic.\n  each(named, function (namedItem) {\n    // Region has feature to calculate center for tooltip or other features.\n    // If there is a <g name=\"xxx\">, the center should be the center of the\n    // bounding rect of the g.\n    if (namedItem.namedFrom != null) {\n      return;\n    }\n    var region = new GeoSVGRegion(namedItem.name, namedItem.el);\n    // PENDING: if `nameMap` supported, this region can not be mounted on\n    // `this`, but can only be created each time `load()` called.\n    regions.push(region);\n    // PENDING: if multiple tag named with the same name, only one will be\n    // found by `_regionsMap`. `_regionsMap` is used to find a coordinate\n    // by name. We use `region.getCenter()` as the coordinate.\n    regionsMap.set(namedItem.name, region);\n  });\n  return {\n    regions: regions,\n    regionsMap: regionsMap\n  };\n}\n// PENDING: `nameMap` will not be supported until some real requirement come.\n// /**\n//  * Use the alias in geoNameMap.\n//  * The input `named` must not be modified.\n//  */\n// function applyNameMap(\n//     named: GeoSVGGraphicRecord['named'],\n//     nameMap: NameMap\n// ): GeoSVGGraphicRecord['named'] {\n//     const result = [] as GeoSVGGraphicRecord['named'];\n//     for (let i = 0; i < named.length; i++) {\n//         let regionGraphic = named[i];\n//         const name = regionGraphic.name;\n//         if (nameMap && nameMap.hasOwnProperty(name)) {\n//             regionGraphic = extend({}, regionGraphic);\n//             regionGraphic.name = name;\n//         }\n//         result.push(regionGraphic);\n//     }\n//     return result;\n// }", "map": {"version": 3, "names": ["parseSVG", "makeViewBoxTransform", "Group", "Rect", "assert", "createHashMap", "each", "BoundingRect", "parseXML", "GeoSVGRegion", "REGION_AVAILABLE_SVG_TAG_MAP", "GeoSVGResource", "mapName", "svg", "type", "_usedGraphicMap", "_freedGraphics", "_mapName", "_parsedXML", "prototype", "load", "firstGraphic", "_firstGraphic", "_buildGraphic", "push", "_boundingRect", "boundingRect", "clone", "_a", "createRegions", "named", "regions", "regionsMap", "_regions", "_regionsMap", "svgXML", "result", "rootFromParse", "ignoreViewBox", "ignoreRootClip", "root", "e", "Error", "message", "add", "isGeoSVGGraphicRoot", "svgWidth", "width", "svgHeight", "height", "viewBoxRect", "bRectX", "bRectY", "bRect<PERSON>idth", "bRectHeight", "x", "y", "calculatedBoundingRect", "getBoundingRect", "viewBoxTransform", "scaleX", "scaleY", "scale", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shape", "plain", "namedItem", "get", "svgNodeTagLower", "setSilent", "el", "useGraphic", "<PERSON><PERSON><PERSON>", "usedRootMap", "svgGraphic", "pop", "set", "freeGraphic", "<PERSON><PERSON><PERSON>", "silent", "isGroup", "traverse", "child", "named<PERSON><PERSON>", "region", "name"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/coord/geo/GeoSVGResource.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parseSVG, makeViewBoxTransform } from 'zrender/lib/tool/parseSVG.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport Rect from 'zrender/lib/graphic/shape/Rect.js';\nimport { assert, createHashMap, each } from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { parseXML } from 'zrender/lib/tool/parseXML.js';\nimport { GeoSVGRegion } from './Region.js';\n/**\r\n * \"region available\" means that: enable users to set attribute `name=\"xxx\"` on those tags\r\n * to make it be a region.\r\n * 1. region styles and its label styles can be defined in echarts opton:\r\n * ```js\r\n * geo: {\r\n *     regions: [{\r\n *         name: 'xxx',\r\n *         itemStyle: { ... },\r\n *         label: { ... }\r\n *     }, {\r\n *         ...\r\n *     },\r\n *     ...]\r\n * };\r\n * ```\r\n * 2. name can be duplicated in different SVG tag. All of the tags with the same name share\r\n * a region option. For exampel if there are two <path> representing two lung lobes. They have\r\n * no common parents but both of them need to display label \"lung\" inside.\r\n */\nvar REGION_AVAILABLE_SVG_TAG_MAP = createHashMap(['rect', 'circle', 'line', 'ellipse', 'polygon', 'polyline', 'path',\n// <text> <tspan> are also enabled because some SVG might paint text itself,\n// but still need to trigger events or tooltip.\n'text', 'tspan',\n// <g> is also enabled because this case: if multiple tags share one name\n// and need label displayed, every tags will display the name, which is not\n// expected. So we can put them into a <g name=\"xxx\">. Thereby only one label\n// displayed and located based on the bounding rect of the <g>.\n'g']);\nvar GeoSVGResource = /** @class */function () {\n  function GeoSVGResource(mapName, svg) {\n    this.type = 'geoSVG';\n    // All used graphics. key: hostKey, value: root\n    this._usedGraphicMap = createHashMap();\n    // All unused graphics.\n    this._freedGraphics = [];\n    this._mapName = mapName;\n    // Only perform parse to XML object here, which might be time\n    // consiming for large SVG.\n    // Although convert XML to zrender element is also time consiming,\n    // if we do it here, the clone of zrender elements has to be\n    // required. So we do it once for each geo instance, util real\n    // performance issues call for optimizing it.\n    this._parsedXML = parseXML(svg);\n  }\n  GeoSVGResource.prototype.load = function /* nameMap: NameMap */\n  () {\n    // In the \"load\" stage, graphic need to be built to\n    // get boundingRect for geo coordinate system.\n    var firstGraphic = this._firstGraphic;\n    // Create the return data structure only when first graphic created.\n    // Because they will be used in geo coordinate system update stage,\n    // and `regions` will be mounted at `geo` coordinate system,\n    // in which there is no \"view\" info, so that it should better not to\n    // make references to graphic elements.\n    if (!firstGraphic) {\n      firstGraphic = this._firstGraphic = this._buildGraphic(this._parsedXML);\n      this._freedGraphics.push(firstGraphic);\n      this._boundingRect = this._firstGraphic.boundingRect.clone();\n      // PENDING: `nameMap` will not be supported until some real requirement come.\n      // if (nameMap) {\n      //     named = applyNameMap(named, nameMap);\n      // }\n      var _a = createRegions(firstGraphic.named),\n        regions = _a.regions,\n        regionsMap = _a.regionsMap;\n      this._regions = regions;\n      this._regionsMap = regionsMap;\n    }\n    return {\n      boundingRect: this._boundingRect,\n      regions: this._regions,\n      regionsMap: this._regionsMap\n    };\n  };\n  GeoSVGResource.prototype._buildGraphic = function (svgXML) {\n    var result;\n    var rootFromParse;\n    try {\n      result = svgXML && parseSVG(svgXML, {\n        ignoreViewBox: true,\n        ignoreRootClip: true\n      }) || {};\n      rootFromParse = result.root;\n      assert(rootFromParse != null);\n    } catch (e) {\n      throw new Error('Invalid svg format\\n' + e.message);\n    }\n    // Note: we keep the covenant that the root has no transform. So always add an extra root.\n    var root = new Group();\n    root.add(rootFromParse);\n    root.isGeoSVGGraphicRoot = true;\n    // [THE_RULE_OF_VIEWPORT_AND_VIEWBOX]\n    //\n    // Consider: `<svg width=\"...\" height=\"...\" viewBox=\"...\">`\n    // - the `width/height` we call it `svgWidth/svgHeight` for short.\n    // - `(0, 0, svgWidth, svgHeight)` defines the viewport of the SVG, or say,\n    //   \"viewport boundingRect\", or `boundingRect` for short.\n    // - `viewBox` defines the transform from the real content ot the viewport.\n    //   `viewBox` has the same unit as the content of SVG.\n    //   If `viewBox` exists, a transform is defined, so the unit of `svgWidth/svgHeight` become\n    //   different from the content of SVG. Otherwise, they are the same.\n    //\n    // If both `svgWidth/svgHeight/viewBox` are specified in a SVG file, the transform rule will be:\n    // 0. `boundingRect` is `(0, 0, svgWidth, svgHeight)`. Set it to Geo['_rect'] (View['_rect']).\n    // 1. Make a transform from `viewBox` to `boundingRect`.\n    //    Note: only support `preserveAspectRatio 'xMidYMid'` here. That is, this transform will preserve\n    //    the aspect ratio.\n    // 2. Make a transform from boundingRect to Geo['_viewRect'] (View['_viewRect'])\n    //    (`Geo`/`View` will do this job).\n    //    Note: this transform might not preserve aspect radio, which depending on how users specify\n    //    viewRect in echarts option (e.g., `geo.left/top/width/height` will not preserve aspect ratio,\n    //    but `geo.layoutCenter/layoutSize` will preserve aspect ratio).\n    //\n    // If `svgWidth/svgHeight` not specified, we use `viewBox` as the `boundingRect` to make the SVG\n    // layout look good.\n    //\n    // If neither `svgWidth/svgHeight` nor `viewBox` are not specified, we calculate the boundingRect\n    // of the SVG content and use them to make SVG layout look good.\n    var svgWidth = result.width;\n    var svgHeight = result.height;\n    var viewBoxRect = result.viewBoxRect;\n    var boundingRect = this._boundingRect;\n    if (!boundingRect) {\n      var bRectX = void 0;\n      var bRectY = void 0;\n      var bRectWidth = void 0;\n      var bRectHeight = void 0;\n      if (svgWidth != null) {\n        bRectX = 0;\n        bRectWidth = svgWidth;\n      } else if (viewBoxRect) {\n        bRectX = viewBoxRect.x;\n        bRectWidth = viewBoxRect.width;\n      }\n      if (svgHeight != null) {\n        bRectY = 0;\n        bRectHeight = svgHeight;\n      } else if (viewBoxRect) {\n        bRectY = viewBoxRect.y;\n        bRectHeight = viewBoxRect.height;\n      }\n      // If both viewBox and svgWidth/svgHeight not specified,\n      // we have to determine how to layout those element to make them look good.\n      if (bRectX == null || bRectY == null) {\n        var calculatedBoundingRect = rootFromParse.getBoundingRect();\n        if (bRectX == null) {\n          bRectX = calculatedBoundingRect.x;\n          bRectWidth = calculatedBoundingRect.width;\n        }\n        if (bRectY == null) {\n          bRectY = calculatedBoundingRect.y;\n          bRectHeight = calculatedBoundingRect.height;\n        }\n      }\n      boundingRect = this._boundingRect = new BoundingRect(bRectX, bRectY, bRectWidth, bRectHeight);\n    }\n    if (viewBoxRect) {\n      var viewBoxTransform = makeViewBoxTransform(viewBoxRect, boundingRect);\n      // Only support `preserveAspectRatio 'xMidYMid'`\n      rootFromParse.scaleX = rootFromParse.scaleY = viewBoxTransform.scale;\n      rootFromParse.x = viewBoxTransform.x;\n      rootFromParse.y = viewBoxTransform.y;\n    }\n    // SVG needs to clip based on `viewBox`. And some SVG files really rely on this feature.\n    // They do not strictly confine all of the content inside a display rect, but deliberately\n    // use a `viewBox` to define a displayable rect.\n    // PENDING:\n    // The drawback of the `setClipPath` here is: the region label (genereted by echarts) near the\n    // edge might also be clipped, because region labels are put as `textContent` of the SVG path.\n    root.setClipPath(new Rect({\n      shape: boundingRect.plain()\n    }));\n    var named = [];\n    each(result.named, function (namedItem) {\n      if (REGION_AVAILABLE_SVG_TAG_MAP.get(namedItem.svgNodeTagLower) != null) {\n        named.push(namedItem);\n        setSilent(namedItem.el);\n      }\n    });\n    return {\n      root: root,\n      boundingRect: boundingRect,\n      named: named\n    };\n  };\n  /**\r\n   * Consider:\r\n   * (1) One graphic element can not be shared by different `geoView` running simultaneously.\r\n   *     Notice, also need to consider multiple echarts instances share a `mapRecord`.\r\n   * (2) Converting SVG to graphic elements is time consuming.\r\n   * (3) In the current architecture, `load` should be called frequently to get boundingRect,\r\n   *     and it is called without view info.\r\n   * So we maintain graphic elements in this module, and enables `view` to use/return these\r\n   * graphics from/to the pool with it's uid.\r\n   */\n  GeoSVGResource.prototype.useGraphic = function (hostKey /* , nameMap: NameMap */) {\n    var usedRootMap = this._usedGraphicMap;\n    var svgGraphic = usedRootMap.get(hostKey);\n    if (svgGraphic) {\n      return svgGraphic;\n    }\n    svgGraphic = this._freedGraphics.pop()\n    // use the first boundingRect to avoid duplicated boundingRect calculation.\n    || this._buildGraphic(this._parsedXML);\n    usedRootMap.set(hostKey, svgGraphic);\n    // PENDING: `nameMap` will not be supported until some real requirement come.\n    // `nameMap` can only be obtained from echarts option.\n    // The original `named` must not be modified.\n    // if (nameMap) {\n    //     svgGraphic = extend({}, svgGraphic);\n    //     svgGraphic.named = applyNameMap(svgGraphic.named, nameMap);\n    // }\n    return svgGraphic;\n  };\n  GeoSVGResource.prototype.freeGraphic = function (hostKey) {\n    var usedRootMap = this._usedGraphicMap;\n    var svgGraphic = usedRootMap.get(hostKey);\n    if (svgGraphic) {\n      usedRootMap.removeKey(hostKey);\n      this._freedGraphics.push(svgGraphic);\n    }\n  };\n  return GeoSVGResource;\n}();\nexport { GeoSVGResource };\nfunction setSilent(el) {\n  // Only named element has silent: false, other elements should\n  // act as background and has no user interaction.\n  el.silent = false;\n  // text|tspan will be converted to group.\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.silent = false;\n    });\n  }\n}\nfunction createRegions(named) {\n  var regions = [];\n  var regionsMap = createHashMap();\n  // Create resions only for the first graphic.\n  each(named, function (namedItem) {\n    // Region has feature to calculate center for tooltip or other features.\n    // If there is a <g name=\"xxx\">, the center should be the center of the\n    // bounding rect of the g.\n    if (namedItem.namedFrom != null) {\n      return;\n    }\n    var region = new GeoSVGRegion(namedItem.name, namedItem.el);\n    // PENDING: if `nameMap` supported, this region can not be mounted on\n    // `this`, but can only be created each time `load()` called.\n    regions.push(region);\n    // PENDING: if multiple tag named with the same name, only one will be\n    // found by `_regionsMap`. `_regionsMap` is used to find a coordinate\n    // by name. We use `region.getCenter()` as the coordinate.\n    regionsMap.set(namedItem.name, region);\n  });\n  return {\n    regions: regions,\n    regionsMap: regionsMap\n  };\n}\n// PENDING: `nameMap` will not be supported until some real requirement come.\n// /**\n//  * Use the alias in geoNameMap.\n//  * The input `named` must not be modified.\n//  */\n// function applyNameMap(\n//     named: GeoSVGGraphicRecord['named'],\n//     nameMap: NameMap\n// ): GeoSVGGraphicRecord['named'] {\n//     const result = [] as GeoSVGGraphicRecord['named'];\n//     for (let i = 0; i < named.length; i++) {\n//         let regionGraphic = named[i];\n//         const name = regionGraphic.name;\n//         if (nameMap && nameMap.hasOwnProperty(name)) {\n//             regionGraphic = extend({}, regionGraphic);\n//             regionGraphic.name = name;\n//         }\n//         result.push(regionGraphic);\n//     }\n//     return result;\n// }"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,oBAAoB,QAAQ,8BAA8B;AAC7E,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,IAAI,MAAM,mCAAmC;AACpD,SAASC,MAAM,EAAEC,aAAa,EAAEC,IAAI,QAAQ,0BAA0B;AACtE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,YAAY,QAAQ,aAAa;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,4BAA4B,GAAGL,aAAa,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM;AACpH;AACA;AACA,MAAM,EAAE,OAAO;AACf;AACA;AACA;AACA;AACA,GAAG,CAAC,CAAC;AACL,IAAIM,cAAc,GAAG,aAAa,YAAY;EAC5C,SAASA,cAAcA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACpC,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,eAAe,GAAGV,aAAa,CAAC,CAAC;IACtC;IACA,IAAI,CAACW,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,QAAQ,GAAGL,OAAO;IACvB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACM,UAAU,GAAGV,QAAQ,CAACK,GAAG,CAAC;EACjC;EACAF,cAAc,CAACQ,SAAS,CAACC,IAAI,GAAG,SAAS;EAAA,GACtC;IACD;IACA;IACA,IAAIC,YAAY,GAAG,IAAI,CAACC,aAAa;IACrC;IACA;IACA;IACA;IACA;IACA,IAAI,CAACD,YAAY,EAAE;MACjBA,YAAY,GAAG,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAACL,UAAU,CAAC;MACvE,IAAI,CAACF,cAAc,CAACQ,IAAI,CAACH,YAAY,CAAC;MACtC,IAAI,CAACI,aAAa,GAAG,IAAI,CAACH,aAAa,CAACI,YAAY,CAACC,KAAK,CAAC,CAAC;MAC5D;MACA;MACA;MACA;MACA,IAAIC,EAAE,GAAGC,aAAa,CAACR,YAAY,CAACS,KAAK,CAAC;QACxCC,OAAO,GAAGH,EAAE,CAACG,OAAO;QACpBC,UAAU,GAAGJ,EAAE,CAACI,UAAU;MAC5B,IAAI,CAACC,QAAQ,GAAGF,OAAO;MACvB,IAAI,CAACG,WAAW,GAAGF,UAAU;IAC/B;IACA,OAAO;MACLN,YAAY,EAAE,IAAI,CAACD,aAAa;MAChCM,OAAO,EAAE,IAAI,CAACE,QAAQ;MACtBD,UAAU,EAAE,IAAI,CAACE;IACnB,CAAC;EACH,CAAC;EACDvB,cAAc,CAACQ,SAAS,CAACI,aAAa,GAAG,UAAUY,MAAM,EAAE;IACzD,IAAIC,MAAM;IACV,IAAIC,aAAa;IACjB,IAAI;MACFD,MAAM,GAAGD,MAAM,IAAInC,QAAQ,CAACmC,MAAM,EAAE;QAClCG,aAAa,EAAE,IAAI;QACnBC,cAAc,EAAE;MAClB,CAAC,CAAC,IAAI,CAAC,CAAC;MACRF,aAAa,GAAGD,MAAM,CAACI,IAAI;MAC3BpC,MAAM,CAACiC,aAAa,IAAI,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOI,CAAC,EAAE;MACV,MAAM,IAAIC,KAAK,CAAC,sBAAsB,GAAGD,CAAC,CAACE,OAAO,CAAC;IACrD;IACA;IACA,IAAIH,IAAI,GAAG,IAAItC,KAAK,CAAC,CAAC;IACtBsC,IAAI,CAACI,GAAG,CAACP,aAAa,CAAC;IACvBG,IAAI,CAACK,mBAAmB,GAAG,IAAI;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,QAAQ,GAAGV,MAAM,CAACW,KAAK;IAC3B,IAAIC,SAAS,GAAGZ,MAAM,CAACa,MAAM;IAC7B,IAAIC,WAAW,GAAGd,MAAM,CAACc,WAAW;IACpC,IAAIxB,YAAY,GAAG,IAAI,CAACD,aAAa;IACrC,IAAI,CAACC,YAAY,EAAE;MACjB,IAAIyB,MAAM,GAAG,KAAK,CAAC;MACnB,IAAIC,MAAM,GAAG,KAAK,CAAC;MACnB,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIC,WAAW,GAAG,KAAK,CAAC;MACxB,IAAIR,QAAQ,IAAI,IAAI,EAAE;QACpBK,MAAM,GAAG,CAAC;QACVE,UAAU,GAAGP,QAAQ;MACvB,CAAC,MAAM,IAAII,WAAW,EAAE;QACtBC,MAAM,GAAGD,WAAW,CAACK,CAAC;QACtBF,UAAU,GAAGH,WAAW,CAACH,KAAK;MAChC;MACA,IAAIC,SAAS,IAAI,IAAI,EAAE;QACrBI,MAAM,GAAG,CAAC;QACVE,WAAW,GAAGN,SAAS;MACzB,CAAC,MAAM,IAAIE,WAAW,EAAE;QACtBE,MAAM,GAAGF,WAAW,CAACM,CAAC;QACtBF,WAAW,GAAGJ,WAAW,CAACD,MAAM;MAClC;MACA;MACA;MACA,IAAIE,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;QACpC,IAAIK,sBAAsB,GAAGpB,aAAa,CAACqB,eAAe,CAAC,CAAC;QAC5D,IAAIP,MAAM,IAAI,IAAI,EAAE;UAClBA,MAAM,GAAGM,sBAAsB,CAACF,CAAC;UACjCF,UAAU,GAAGI,sBAAsB,CAACV,KAAK;QAC3C;QACA,IAAIK,MAAM,IAAI,IAAI,EAAE;UAClBA,MAAM,GAAGK,sBAAsB,CAACD,CAAC;UACjCF,WAAW,GAAGG,sBAAsB,CAACR,MAAM;QAC7C;MACF;MACAvB,YAAY,GAAG,IAAI,CAACD,aAAa,GAAG,IAAIlB,YAAY,CAAC4C,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,CAAC;IAC/F;IACA,IAAIJ,WAAW,EAAE;MACf,IAAIS,gBAAgB,GAAG1D,oBAAoB,CAACiD,WAAW,EAAExB,YAAY,CAAC;MACtE;MACAW,aAAa,CAACuB,MAAM,GAAGvB,aAAa,CAACwB,MAAM,GAAGF,gBAAgB,CAACG,KAAK;MACpEzB,aAAa,CAACkB,CAAC,GAAGI,gBAAgB,CAACJ,CAAC;MACpClB,aAAa,CAACmB,CAAC,GAAGG,gBAAgB,CAACH,CAAC;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACAhB,IAAI,CAACuB,WAAW,CAAC,IAAI5D,IAAI,CAAC;MACxB6D,KAAK,EAAEtC,YAAY,CAACuC,KAAK,CAAC;IAC5B,CAAC,CAAC,CAAC;IACH,IAAInC,KAAK,GAAG,EAAE;IACdxB,IAAI,CAAC8B,MAAM,CAACN,KAAK,EAAE,UAAUoC,SAAS,EAAE;MACtC,IAAIxD,4BAA4B,CAACyD,GAAG,CAACD,SAAS,CAACE,eAAe,CAAC,IAAI,IAAI,EAAE;QACvEtC,KAAK,CAACN,IAAI,CAAC0C,SAAS,CAAC;QACrBG,SAAS,CAACH,SAAS,CAACI,EAAE,CAAC;MACzB;IACF,CAAC,CAAC;IACF,OAAO;MACL9B,IAAI,EAAEA,IAAI;MACVd,YAAY,EAAEA,YAAY;MAC1BI,KAAK,EAAEA;IACT,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEnB,cAAc,CAACQ,SAAS,CAACoD,UAAU,GAAG,UAAUC,OAAO,CAAC,0BAA0B;IAChF,IAAIC,WAAW,GAAG,IAAI,CAAC1D,eAAe;IACtC,IAAI2D,UAAU,GAAGD,WAAW,CAACN,GAAG,CAACK,OAAO,CAAC;IACzC,IAAIE,UAAU,EAAE;MACd,OAAOA,UAAU;IACnB;IACAA,UAAU,GAAG,IAAI,CAAC1D,cAAc,CAAC2D,GAAG,CAAC;IACrC;IAAA,GACG,IAAI,CAACpD,aAAa,CAAC,IAAI,CAACL,UAAU,CAAC;IACtCuD,WAAW,CAACG,GAAG,CAACJ,OAAO,EAAEE,UAAU,CAAC;IACpC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAOA,UAAU;EACnB,CAAC;EACD/D,cAAc,CAACQ,SAAS,CAAC0D,WAAW,GAAG,UAAUL,OAAO,EAAE;IACxD,IAAIC,WAAW,GAAG,IAAI,CAAC1D,eAAe;IACtC,IAAI2D,UAAU,GAAGD,WAAW,CAACN,GAAG,CAACK,OAAO,CAAC;IACzC,IAAIE,UAAU,EAAE;MACdD,WAAW,CAACK,SAAS,CAACN,OAAO,CAAC;MAC9B,IAAI,CAACxD,cAAc,CAACQ,IAAI,CAACkD,UAAU,CAAC;IACtC;EACF,CAAC;EACD,OAAO/D,cAAc;AACvB,CAAC,CAAC,CAAC;AACH,SAASA,cAAc;AACvB,SAAS0D,SAASA,CAACC,EAAE,EAAE;EACrB;EACA;EACAA,EAAE,CAACS,MAAM,GAAG,KAAK;EACjB;EACA,IAAIT,EAAE,CAACU,OAAO,EAAE;IACdV,EAAE,CAACW,QAAQ,CAAC,UAAUC,KAAK,EAAE;MAC3BA,KAAK,CAACH,MAAM,GAAG,KAAK;IACtB,CAAC,CAAC;EACJ;AACF;AACA,SAASlD,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,UAAU,GAAG3B,aAAa,CAAC,CAAC;EAChC;EACAC,IAAI,CAACwB,KAAK,EAAE,UAAUoC,SAAS,EAAE;IAC/B;IACA;IACA;IACA,IAAIA,SAAS,CAACiB,SAAS,IAAI,IAAI,EAAE;MAC/B;IACF;IACA,IAAIC,MAAM,GAAG,IAAI3E,YAAY,CAACyD,SAAS,CAACmB,IAAI,EAAEnB,SAAS,CAACI,EAAE,CAAC;IAC3D;IACA;IACAvC,OAAO,CAACP,IAAI,CAAC4D,MAAM,CAAC;IACpB;IACA;IACA;IACApD,UAAU,CAAC4C,GAAG,CAACV,SAAS,CAACmB,IAAI,EAAED,MAAM,CAAC;EACxC,CAAC,CAAC;EACF,OAAO;IACLrD,OAAO,EAAEA,OAAO;IAChBC,UAAU,EAAEA;EACd,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}