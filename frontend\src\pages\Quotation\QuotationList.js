import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  message,
  Tooltip,
  Progress
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  ExportOutlined,
  FileTextOutlined,
  SwapOutlined
} from '@ant-design/icons';

import {
  selectFilteredQuotations,
  selectQuotationLoading,
  selectQuotationFilters,
  selectQuotationPagination,
  setSearchKeyword,
  setFilters,
  setPagination,
  resetFilters,
  deleteQuotation,
  convertToOrder
} from '../../store/slices/quotationSlice';

const { Title, Text } = Typography;
const { Option } = Select;

const QuotationList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const quotations = useSelector(selectFilteredQuotations);
  const loading = useSelector(selectQuotationLoading);
  const filters = useSelector(selectQuotationFilters);
  const pagination = useSelector(selectQuotationPagination);

  const handleSearch = (value) => {
    dispatch(setSearchKeyword(value));
    dispatch(setPagination({ current: 1 }));
  };

  const handleFilterChange = (key, value) => {
    dispatch(setFilters({ [key]: value }));
    dispatch(setPagination({ current: 1 }));
  };

  const handleTableChange = (paginationConfig) => {
    dispatch(setPagination({
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    }));
  };

  const handleDelete = (quotation) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除报价单"${quotation.id}"吗？`,
      onOk: () => {
        dispatch(deleteQuotation(quotation.id));
        message.success('删除成功');
      }
    });
  };

  const handleConvertToOrder = (quotation) => {
    Modal.confirm({
      title: '转换为订单',
      content: `确定要将报价单"${quotation.id}"转换为订单吗？`,
      onOk: () => {
        const orderId = `ORD-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`;
        dispatch(convertToOrder({ quotationId: quotation.id, orderId }));
        message.success('转换成功');
      }
    });
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getStatusColor = (status) => {
    const colors = {
      'draft': 'orange',
      'pending': 'blue',
      'accepted': 'green',
      'rejected': 'red',
      'expired': 'gray'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      'draft': '草稿',
      'pending': '待确认',
      'accepted': '已接受',
      'rejected': '已拒绝',
      'expired': '已过期'
    };
    return texts[status] || status;
  };

  const columns = [
    {
      title: '报价单号',
      dataIndex: 'id',
      key: 'id',
      width: 150,
      fixed: 'left',
      render: (text) => (
        <Button 
          type="link" 
          style={{ padding: 0, fontWeight: 'bold' }}
          onClick={() => navigate(`/quotations/${text}`)}
        >
          {text}
        </Button>
      )
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200
    },
    {
      title: '报价标题',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      ellipsis: true
    },
    {
      title: '报价金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount) => (
        <Text strong style={{ color: '#1890ff' }}>
          {formatCurrency(amount)}
        </Text>
      )
    },
    {
      title: '关联BOM',
      key: 'bom',
      width: 150,
      render: (_, record) => (
        record.bomId ? (
          <Space direction="vertical" size={0}>
            <Text style={{ fontSize: '12px', color: '#1890ff' }}>
              {record.bomId}
            </Text>
            <Text style={{ fontSize: '11px', color: '#8c8c8c' }}>
              {record.bomName}
            </Text>
          </Space>
        ) : (
          <Text type="secondary" style={{ fontSize: '12px' }}>
            未关联BOM
          </Text>
        )
      )
    },
    {
      title: '预估利润',
      key: 'profit',
      width: 150,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text style={{ fontSize: '12px', color: '#52c41a' }}>
            {formatCurrency(record.estimatedProfit)}
          </Text>
          <Text style={{ fontSize: '12px' }}>
            利润率: {record.profitMargin}%
          </Text>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '业务员',
      dataIndex: 'salesperson',
      key: 'salesperson',
      width: 100
    },
    {
      title: '有效期',
      dataIndex: 'validUntil',
      key: 'validUntil',
      width: 100,
      render: (date) => {
        const isExpired = new Date(date) < new Date();
        return (
          <Text style={{ color: isExpired ? '#ff4d4f' : 'inherit' }}>
            {date}
          </Text>
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 100
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => navigate(`/quotations/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              disabled={record.status === 'accepted'}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="转为订单">
              <Button 
                type="text" 
                icon={<SwapOutlined />} 
                size="small"
                onClick={() => handleConvertToOrder(record)}
              />
            </Tooltip>
          )}
          <Tooltip title="删除">
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 计算统计数据
  const stats = {
    total: quotations.length,
    pending: quotations.filter(q => q.status === 'pending').length,
    accepted: quotations.filter(q => q.status === 'accepted').length,
    totalAmount: quotations.reduce((sum, q) => sum + q.totalAmount, 0),
    totalProfit: quotations.reduce((sum, q) => sum + q.estimatedProfit, 0)
  };

  return (
    <div>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ margin: 0 }}>报价管理</Title>
        <Space>
          <Button icon={<ReloadOutlined />}>刷新</Button>
          <Button icon={<ExportOutlined />}>导出</Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => navigate('/quotations/new')}>
            新建报价
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="报价总数" 
              value={stats.total} 
              suffix="个"
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="待确认" value={stats.pending} suffix="个" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="已接受" value={stats.accepted} suffix="个" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="报价总额" 
              value={stats.totalAmount} 
              formatter={(value) => formatCurrency(value)}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="预估利润" 
              value={stats.totalProfit} 
              formatter={(value) => formatCurrency(value)}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索报价单号、客户名称、标题"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="draft">草稿</Option>
              <Option value="pending">待确认</Option>
              <Option value="accepted">已接受</Option>
              <Option value="rejected">已拒绝</Option>
              <Option value="expired">已过期</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="业务员"
              allowClear
              style={{ width: '100%' }}
              value={filters.salesperson}
              onChange={(value) => handleFilterChange('salesperson', value)}
            >
              <Option value="李业务">李业务</Option>
              <Option value="张业务">张业务</Option>
              <Option value="王业务">王业务</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Button onClick={() => dispatch(resetFilters())}>重置</Button>
          </Col>
        </Row>
      </Card>

      {/* 报价单列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={quotations}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default QuotationList;
