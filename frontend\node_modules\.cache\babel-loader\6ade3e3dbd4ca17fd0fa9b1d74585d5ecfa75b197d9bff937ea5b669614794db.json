{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { encodeHTML } from 'zrender/lib/core/dom.js';\nimport { parseDate, isNumeric, numericToNumber } from './number.js';\nimport { format as timeFormat, pad } from './time.js';\nimport { deprecateReplaceLog } from './log.js';\n/**\r\n * Add a comma each three digit.\r\n */\nexport function addCommas(x) {\n  if (!isNumeric(x)) {\n    return zrUtil.isString(x) ? x : '-';\n  }\n  var parts = (x + '').split('.');\n  return parts[0].replace(/(\\d{1,3})(?=(?:\\d{3})+(?!\\d))/g, '$1,') + (parts.length > 1 ? '.' + parts[1] : '');\n}\nexport function toCamelCase(str, upperCaseFirst) {\n  str = (str || '').toLowerCase().replace(/-(.)/g, function (match, group1) {\n    return group1.toUpperCase();\n  });\n  if (upperCaseFirst && str) {\n    str = str.charAt(0).toUpperCase() + str.slice(1);\n  }\n  return str;\n}\nexport var normalizeCssArray = zrUtil.normalizeCssArray;\nexport { encodeHTML };\n/**\r\n * Make value user readable for tooltip and label.\r\n * \"User readable\":\r\n *     Try to not print programmer-specific text like NaN, Infinity, null, undefined.\r\n *     Avoid to display an empty string, which users can not recognize there is\r\n *     a value and it might look like a bug.\r\n */\nexport function makeValueReadable(value, valueType, useUTC) {\n  var USER_READABLE_DEFUALT_TIME_PATTERN = '{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}';\n  function stringToUserReadable(str) {\n    return str && zrUtil.trim(str) ? str : '-';\n  }\n  function isNumberUserReadable(num) {\n    return !!(num != null && !isNaN(num) && isFinite(num));\n  }\n  var isTypeTime = valueType === 'time';\n  var isValueDate = value instanceof Date;\n  if (isTypeTime || isValueDate) {\n    var date = isTypeTime ? parseDate(value) : value;\n    if (!isNaN(+date)) {\n      return timeFormat(date, USER_READABLE_DEFUALT_TIME_PATTERN, useUTC);\n    } else if (isValueDate) {\n      return '-';\n    }\n    // In other cases, continue to try to display the value in the following code.\n  }\n  if (valueType === 'ordinal') {\n    return zrUtil.isStringSafe(value) ? stringToUserReadable(value) : zrUtil.isNumber(value) ? isNumberUserReadable(value) ? value + '' : '-' : '-';\n  }\n  // By default.\n  var numericResult = numericToNumber(value);\n  return isNumberUserReadable(numericResult) ? addCommas(numericResult) : zrUtil.isStringSafe(value) ? stringToUserReadable(value) : typeof value === 'boolean' ? value + '' : '-';\n}\nvar TPL_VAR_ALIAS = ['a', 'b', 'c', 'd', 'e', 'f', 'g'];\nvar wrapVar = function (varName, seriesIdx) {\n  return '{' + varName + (seriesIdx == null ? '' : seriesIdx) + '}';\n};\n/**\r\n * Template formatter\r\n * @param {Array.<Object>|Object} paramsList\r\n */\nexport function formatTpl(tpl, paramsList, encode) {\n  if (!zrUtil.isArray(paramsList)) {\n    paramsList = [paramsList];\n  }\n  var seriesLen = paramsList.length;\n  if (!seriesLen) {\n    return '';\n  }\n  var $vars = paramsList[0].$vars || [];\n  for (var i = 0; i < $vars.length; i++) {\n    var alias = TPL_VAR_ALIAS[i];\n    tpl = tpl.replace(wrapVar(alias), wrapVar(alias, 0));\n  }\n  for (var seriesIdx = 0; seriesIdx < seriesLen; seriesIdx++) {\n    for (var k = 0; k < $vars.length; k++) {\n      var val = paramsList[seriesIdx][$vars[k]];\n      tpl = tpl.replace(wrapVar(TPL_VAR_ALIAS[k], seriesIdx), encode ? encodeHTML(val) : val);\n    }\n  }\n  return tpl;\n}\n/**\r\n * simple Template formatter\r\n */\nexport function formatTplSimple(tpl, param, encode) {\n  zrUtil.each(param, function (value, key) {\n    tpl = tpl.replace('{' + key + '}', encode ? encodeHTML(value) : value);\n  });\n  return tpl;\n}\nexport function getTooltipMarker(inOpt, extraCssText) {\n  var opt = zrUtil.isString(inOpt) ? {\n    color: inOpt,\n    extraCssText: extraCssText\n  } : inOpt || {};\n  var color = opt.color;\n  var type = opt.type;\n  extraCssText = opt.extraCssText;\n  var renderMode = opt.renderMode || 'html';\n  if (!color) {\n    return '';\n  }\n  if (renderMode === 'html') {\n    return type === 'subItem' ? '<span style=\"display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;' + 'border-radius:4px;width:4px;height:4px;background-color:'\n    // Only support string\n    + encodeHTML(color) + ';' + (extraCssText || '') + '\"></span>' : '<span style=\"display:inline-block;margin-right:4px;' + 'border-radius:10px;width:10px;height:10px;background-color:' + encodeHTML(color) + ';' + (extraCssText || '') + '\"></span>';\n  } else {\n    // Should better not to auto generate style name by auto-increment number here.\n    // Because this util is usually called in tooltip formatter, which is probably\n    // called repeatedly when mouse move and the auto-increment number increases fast.\n    // Users can make their own style name by theirselves, make it unique and readable.\n    var markerId = opt.markerId || 'markerX';\n    return {\n      renderMode: renderMode,\n      content: '{' + markerId + '|}  ',\n      style: type === 'subItem' ? {\n        width: 4,\n        height: 4,\n        borderRadius: 2,\n        backgroundColor: color\n      } : {\n        width: 10,\n        height: 10,\n        borderRadius: 5,\n        backgroundColor: color\n      }\n    };\n  }\n}\n/**\r\n * @deprecated Use `time/format` instead.\r\n * ISO Date format\r\n * @param {string} tpl\r\n * @param {number} value\r\n * @param {boolean} [isUTC=false] Default in local time.\r\n *           see `module:echarts/scale/Time`\r\n *           and `module:echarts/util/number#parseDate`.\r\n * @inner\r\n */\nexport function formatTime(tpl, value, isUTC) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateReplaceLog('echarts.format.formatTime', 'echarts.time.format');\n  }\n  if (tpl === 'week' || tpl === 'month' || tpl === 'quarter' || tpl === 'half-year' || tpl === 'year') {\n    tpl = 'MM-dd\\nyyyy';\n  }\n  var date = parseDate(value);\n  var getUTC = isUTC ? 'getUTC' : 'get';\n  var y = date[getUTC + 'FullYear']();\n  var M = date[getUTC + 'Month']() + 1;\n  var d = date[getUTC + 'Date']();\n  var h = date[getUTC + 'Hours']();\n  var m = date[getUTC + 'Minutes']();\n  var s = date[getUTC + 'Seconds']();\n  var S = date[getUTC + 'Milliseconds']();\n  tpl = tpl.replace('MM', pad(M, 2)).replace('M', M).replace('yyyy', y).replace('yy', pad(y % 100 + '', 2)).replace('dd', pad(d, 2)).replace('d', d).replace('hh', pad(h, 2)).replace('h', h).replace('mm', pad(m, 2)).replace('m', m).replace('ss', pad(s, 2)).replace('s', s).replace('SSS', pad(S, 3));\n  return tpl;\n}\n/**\r\n * Capital first\r\n * @param {string} str\r\n * @return {string}\r\n */\nexport function capitalFirst(str) {\n  return str ? str.charAt(0).toUpperCase() + str.substr(1) : str;\n}\n/**\r\n * @return Never be null/undefined.\r\n */\nexport function convertToColorString(color, defaultColor) {\n  defaultColor = defaultColor || 'transparent';\n  return zrUtil.isString(color) ? color : zrUtil.isObject(color) ? color.colorStops && (color.colorStops[0] || {}).color || defaultColor : defaultColor;\n}\nexport { truncateText } from 'zrender/lib/graphic/helper/parseText.js';\n/**\r\n * open new tab\r\n * @param link url\r\n * @param target blank or self\r\n */\nexport function windowOpen(link, target) {\n  /* global window */\n  if (target === '_blank' || target === 'blank') {\n    var blank = window.open();\n    blank.opener = null;\n    blank.location.href = link;\n  } else {\n    window.open(link, target);\n  }\n}\nexport { getTextRect } from '../legacy/getTextRect.js';", "map": {"version": 3, "names": ["zrUtil", "encodeHTML", "parseDate", "isNumeric", "numericToNumber", "format", "timeFormat", "pad", "deprecateReplaceLog", "addCommas", "x", "isString", "parts", "split", "replace", "length", "toCamelCase", "str", "upperCaseFirst", "toLowerCase", "match", "group1", "toUpperCase", "char<PERSON>t", "slice", "normalizeCssArray", "makeValueReadable", "value", "valueType", "useUTC", "USER_READABLE_DEFUALT_TIME_PATTERN", "stringToUserReadable", "trim", "isNumberUserReadable", "num", "isNaN", "isFinite", "isTypeTime", "isValueDate", "Date", "date", "isStringSafe", "isNumber", "numericResult", "TPL_VAR_ALIAS", "wrapVar", "varName", "seriesIdx", "formatTpl", "tpl", "paramsList", "encode", "isArray", "seriesLen", "$vars", "i", "alias", "k", "val", "formatTplSimple", "param", "each", "key", "getTooltipMarker", "inOpt", "extraCssText", "opt", "color", "type", "renderMode", "markerId", "content", "style", "width", "height", "borderRadius", "backgroundColor", "formatTime", "isUTC", "process", "env", "NODE_ENV", "getUTC", "y", "M", "d", "h", "m", "s", "S", "capitalFirst", "substr", "convertToColorString", "defaultColor", "isObject", "colorStops", "truncateText", "windowOpen", "link", "target", "blank", "window", "open", "opener", "location", "href", "getTextRect"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/util/format.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { encodeHTML } from 'zrender/lib/core/dom.js';\nimport { parseDate, isNumeric, numericToNumber } from './number.js';\nimport { format as timeFormat, pad } from './time.js';\nimport { deprecateReplaceLog } from './log.js';\n/**\r\n * Add a comma each three digit.\r\n */\nexport function addCommas(x) {\n  if (!isNumeric(x)) {\n    return zrUtil.isString(x) ? x : '-';\n  }\n  var parts = (x + '').split('.');\n  return parts[0].replace(/(\\d{1,3})(?=(?:\\d{3})+(?!\\d))/g, '$1,') + (parts.length > 1 ? '.' + parts[1] : '');\n}\nexport function toCamelCase(str, upperCaseFirst) {\n  str = (str || '').toLowerCase().replace(/-(.)/g, function (match, group1) {\n    return group1.toUpperCase();\n  });\n  if (upperCaseFirst && str) {\n    str = str.charAt(0).toUpperCase() + str.slice(1);\n  }\n  return str;\n}\nexport var normalizeCssArray = zrUtil.normalizeCssArray;\nexport { encodeHTML };\n/**\r\n * Make value user readable for tooltip and label.\r\n * \"User readable\":\r\n *     Try to not print programmer-specific text like NaN, Infinity, null, undefined.\r\n *     Avoid to display an empty string, which users can not recognize there is\r\n *     a value and it might look like a bug.\r\n */\nexport function makeValueReadable(value, valueType, useUTC) {\n  var USER_READABLE_DEFUALT_TIME_PATTERN = '{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}';\n  function stringToUserReadable(str) {\n    return str && zrUtil.trim(str) ? str : '-';\n  }\n  function isNumberUserReadable(num) {\n    return !!(num != null && !isNaN(num) && isFinite(num));\n  }\n  var isTypeTime = valueType === 'time';\n  var isValueDate = value instanceof Date;\n  if (isTypeTime || isValueDate) {\n    var date = isTypeTime ? parseDate(value) : value;\n    if (!isNaN(+date)) {\n      return timeFormat(date, USER_READABLE_DEFUALT_TIME_PATTERN, useUTC);\n    } else if (isValueDate) {\n      return '-';\n    }\n    // In other cases, continue to try to display the value in the following code.\n  }\n  if (valueType === 'ordinal') {\n    return zrUtil.isStringSafe(value) ? stringToUserReadable(value) : zrUtil.isNumber(value) ? isNumberUserReadable(value) ? value + '' : '-' : '-';\n  }\n  // By default.\n  var numericResult = numericToNumber(value);\n  return isNumberUserReadable(numericResult) ? addCommas(numericResult) : zrUtil.isStringSafe(value) ? stringToUserReadable(value) : typeof value === 'boolean' ? value + '' : '-';\n}\nvar TPL_VAR_ALIAS = ['a', 'b', 'c', 'd', 'e', 'f', 'g'];\nvar wrapVar = function (varName, seriesIdx) {\n  return '{' + varName + (seriesIdx == null ? '' : seriesIdx) + '}';\n};\n/**\r\n * Template formatter\r\n * @param {Array.<Object>|Object} paramsList\r\n */\nexport function formatTpl(tpl, paramsList, encode) {\n  if (!zrUtil.isArray(paramsList)) {\n    paramsList = [paramsList];\n  }\n  var seriesLen = paramsList.length;\n  if (!seriesLen) {\n    return '';\n  }\n  var $vars = paramsList[0].$vars || [];\n  for (var i = 0; i < $vars.length; i++) {\n    var alias = TPL_VAR_ALIAS[i];\n    tpl = tpl.replace(wrapVar(alias), wrapVar(alias, 0));\n  }\n  for (var seriesIdx = 0; seriesIdx < seriesLen; seriesIdx++) {\n    for (var k = 0; k < $vars.length; k++) {\n      var val = paramsList[seriesIdx][$vars[k]];\n      tpl = tpl.replace(wrapVar(TPL_VAR_ALIAS[k], seriesIdx), encode ? encodeHTML(val) : val);\n    }\n  }\n  return tpl;\n}\n/**\r\n * simple Template formatter\r\n */\nexport function formatTplSimple(tpl, param, encode) {\n  zrUtil.each(param, function (value, key) {\n    tpl = tpl.replace('{' + key + '}', encode ? encodeHTML(value) : value);\n  });\n  return tpl;\n}\nexport function getTooltipMarker(inOpt, extraCssText) {\n  var opt = zrUtil.isString(inOpt) ? {\n    color: inOpt,\n    extraCssText: extraCssText\n  } : inOpt || {};\n  var color = opt.color;\n  var type = opt.type;\n  extraCssText = opt.extraCssText;\n  var renderMode = opt.renderMode || 'html';\n  if (!color) {\n    return '';\n  }\n  if (renderMode === 'html') {\n    return type === 'subItem' ? '<span style=\"display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;' + 'border-radius:4px;width:4px;height:4px;background-color:'\n    // Only support string\n    + encodeHTML(color) + ';' + (extraCssText || '') + '\"></span>' : '<span style=\"display:inline-block;margin-right:4px;' + 'border-radius:10px;width:10px;height:10px;background-color:' + encodeHTML(color) + ';' + (extraCssText || '') + '\"></span>';\n  } else {\n    // Should better not to auto generate style name by auto-increment number here.\n    // Because this util is usually called in tooltip formatter, which is probably\n    // called repeatedly when mouse move and the auto-increment number increases fast.\n    // Users can make their own style name by theirselves, make it unique and readable.\n    var markerId = opt.markerId || 'markerX';\n    return {\n      renderMode: renderMode,\n      content: '{' + markerId + '|}  ',\n      style: type === 'subItem' ? {\n        width: 4,\n        height: 4,\n        borderRadius: 2,\n        backgroundColor: color\n      } : {\n        width: 10,\n        height: 10,\n        borderRadius: 5,\n        backgroundColor: color\n      }\n    };\n  }\n}\n/**\r\n * @deprecated Use `time/format` instead.\r\n * ISO Date format\r\n * @param {string} tpl\r\n * @param {number} value\r\n * @param {boolean} [isUTC=false] Default in local time.\r\n *           see `module:echarts/scale/Time`\r\n *           and `module:echarts/util/number#parseDate`.\r\n * @inner\r\n */\nexport function formatTime(tpl, value, isUTC) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateReplaceLog('echarts.format.formatTime', 'echarts.time.format');\n  }\n  if (tpl === 'week' || tpl === 'month' || tpl === 'quarter' || tpl === 'half-year' || tpl === 'year') {\n    tpl = 'MM-dd\\nyyyy';\n  }\n  var date = parseDate(value);\n  var getUTC = isUTC ? 'getUTC' : 'get';\n  var y = date[getUTC + 'FullYear']();\n  var M = date[getUTC + 'Month']() + 1;\n  var d = date[getUTC + 'Date']();\n  var h = date[getUTC + 'Hours']();\n  var m = date[getUTC + 'Minutes']();\n  var s = date[getUTC + 'Seconds']();\n  var S = date[getUTC + 'Milliseconds']();\n  tpl = tpl.replace('MM', pad(M, 2)).replace('M', M).replace('yyyy', y).replace('yy', pad(y % 100 + '', 2)).replace('dd', pad(d, 2)).replace('d', d).replace('hh', pad(h, 2)).replace('h', h).replace('mm', pad(m, 2)).replace('m', m).replace('ss', pad(s, 2)).replace('s', s).replace('SSS', pad(S, 3));\n  return tpl;\n}\n/**\r\n * Capital first\r\n * @param {string} str\r\n * @return {string}\r\n */\nexport function capitalFirst(str) {\n  return str ? str.charAt(0).toUpperCase() + str.substr(1) : str;\n}\n/**\r\n * @return Never be null/undefined.\r\n */\nexport function convertToColorString(color, defaultColor) {\n  defaultColor = defaultColor || 'transparent';\n  return zrUtil.isString(color) ? color : zrUtil.isObject(color) ? color.colorStops && (color.colorStops[0] || {}).color || defaultColor : defaultColor;\n}\nexport { truncateText } from 'zrender/lib/graphic/helper/parseText.js';\n/**\r\n * open new tab\r\n * @param link url\r\n * @param target blank or self\r\n */\nexport function windowOpen(link, target) {\n  /* global window */\n  if (target === '_blank' || target === 'blank') {\n    var blank = window.open();\n    blank.opener = null;\n    blank.location.href = link;\n  } else {\n    window.open(link, target);\n  }\n}\nexport { getTextRect } from '../legacy/getTextRect.js';"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,SAAS,EAAEC,SAAS,EAAEC,eAAe,QAAQ,aAAa;AACnE,SAASC,MAAM,IAAIC,UAAU,EAAEC,GAAG,QAAQ,WAAW;AACrD,SAASC,mBAAmB,QAAQ,UAAU;AAC9C;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,CAAC,EAAE;EAC3B,IAAI,CAACP,SAAS,CAACO,CAAC,CAAC,EAAE;IACjB,OAAOV,MAAM,CAACW,QAAQ,CAACD,CAAC,CAAC,GAAGA,CAAC,GAAG,GAAG;EACrC;EACA,IAAIE,KAAK,GAAG,CAACF,CAAC,GAAG,EAAE,EAAEG,KAAK,CAAC,GAAG,CAAC;EAC/B,OAAOD,KAAK,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,gCAAgC,EAAE,KAAK,CAAC,IAAIF,KAAK,CAACG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC7G;AACA,OAAO,SAASI,WAAWA,CAACC,GAAG,EAAEC,cAAc,EAAE;EAC/CD,GAAG,GAAG,CAACA,GAAG,IAAI,EAAE,EAAEE,WAAW,CAAC,CAAC,CAACL,OAAO,CAAC,OAAO,EAAE,UAAUM,KAAK,EAAEC,MAAM,EAAE;IACxE,OAAOA,MAAM,CAACC,WAAW,CAAC,CAAC;EAC7B,CAAC,CAAC;EACF,IAAIJ,cAAc,IAAID,GAAG,EAAE;IACzBA,GAAG,GAAGA,GAAG,CAACM,MAAM,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC,CAAC,GAAGL,GAAG,CAACO,KAAK,CAAC,CAAC,CAAC;EAClD;EACA,OAAOP,GAAG;AACZ;AACA,OAAO,IAAIQ,iBAAiB,GAAGzB,MAAM,CAACyB,iBAAiB;AACvD,SAASxB,UAAU;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyB,iBAAiBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAE;EAC1D,IAAIC,kCAAkC,GAAG,iCAAiC;EAC1E,SAASC,oBAAoBA,CAACd,GAAG,EAAE;IACjC,OAAOA,GAAG,IAAIjB,MAAM,CAACgC,IAAI,CAACf,GAAG,CAAC,GAAGA,GAAG,GAAG,GAAG;EAC5C;EACA,SAASgB,oBAAoBA,CAACC,GAAG,EAAE;IACjC,OAAO,CAAC,EAAEA,GAAG,IAAI,IAAI,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC,IAAIE,QAAQ,CAACF,GAAG,CAAC,CAAC;EACxD;EACA,IAAIG,UAAU,GAAGT,SAAS,KAAK,MAAM;EACrC,IAAIU,WAAW,GAAGX,KAAK,YAAYY,IAAI;EACvC,IAAIF,UAAU,IAAIC,WAAW,EAAE;IAC7B,IAAIE,IAAI,GAAGH,UAAU,GAAGnC,SAAS,CAACyB,KAAK,CAAC,GAAGA,KAAK;IAChD,IAAI,CAACQ,KAAK,CAAC,CAACK,IAAI,CAAC,EAAE;MACjB,OAAOlC,UAAU,CAACkC,IAAI,EAAEV,kCAAkC,EAAED,MAAM,CAAC;IACrE,CAAC,MAAM,IAAIS,WAAW,EAAE;MACtB,OAAO,GAAG;IACZ;IACA;EACF;EACA,IAAIV,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAO5B,MAAM,CAACyC,YAAY,CAACd,KAAK,CAAC,GAAGI,oBAAoB,CAACJ,KAAK,CAAC,GAAG3B,MAAM,CAAC0C,QAAQ,CAACf,KAAK,CAAC,GAAGM,oBAAoB,CAACN,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG;EACjJ;EACA;EACA,IAAIgB,aAAa,GAAGvC,eAAe,CAACuB,KAAK,CAAC;EAC1C,OAAOM,oBAAoB,CAACU,aAAa,CAAC,GAAGlC,SAAS,CAACkC,aAAa,CAAC,GAAG3C,MAAM,CAACyC,YAAY,CAACd,KAAK,CAAC,GAAGI,oBAAoB,CAACJ,KAAK,CAAC,GAAG,OAAOA,KAAK,KAAK,SAAS,GAAGA,KAAK,GAAG,EAAE,GAAG,GAAG;AAClL;AACA,IAAIiB,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACvD,IAAIC,OAAO,GAAG,SAAAA,CAAUC,OAAO,EAAEC,SAAS,EAAE;EAC1C,OAAO,GAAG,GAAGD,OAAO,IAAIC,SAAS,IAAI,IAAI,GAAG,EAAE,GAAGA,SAAS,CAAC,GAAG,GAAG;AACnE,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAE;EACjD,IAAI,CAACnD,MAAM,CAACoD,OAAO,CAACF,UAAU,CAAC,EAAE;IAC/BA,UAAU,GAAG,CAACA,UAAU,CAAC;EAC3B;EACA,IAAIG,SAAS,GAAGH,UAAU,CAACnC,MAAM;EACjC,IAAI,CAACsC,SAAS,EAAE;IACd,OAAO,EAAE;EACX;EACA,IAAIC,KAAK,GAAGJ,UAAU,CAAC,CAAC,CAAC,CAACI,KAAK,IAAI,EAAE;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACvC,MAAM,EAAEwC,CAAC,EAAE,EAAE;IACrC,IAAIC,KAAK,GAAGZ,aAAa,CAACW,CAAC,CAAC;IAC5BN,GAAG,GAAGA,GAAG,CAACnC,OAAO,CAAC+B,OAAO,CAACW,KAAK,CAAC,EAAEX,OAAO,CAACW,KAAK,EAAE,CAAC,CAAC,CAAC;EACtD;EACA,KAAK,IAAIT,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGM,SAAS,EAAEN,SAAS,EAAE,EAAE;IAC1D,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACvC,MAAM,EAAE0C,CAAC,EAAE,EAAE;MACrC,IAAIC,GAAG,GAAGR,UAAU,CAACH,SAAS,CAAC,CAACO,KAAK,CAACG,CAAC,CAAC,CAAC;MACzCR,GAAG,GAAGA,GAAG,CAACnC,OAAO,CAAC+B,OAAO,CAACD,aAAa,CAACa,CAAC,CAAC,EAAEV,SAAS,CAAC,EAAEI,MAAM,GAAGlD,UAAU,CAACyD,GAAG,CAAC,GAAGA,GAAG,CAAC;IACzF;EACF;EACA,OAAOT,GAAG;AACZ;AACA;AACA;AACA;AACA,OAAO,SAASU,eAAeA,CAACV,GAAG,EAAEW,KAAK,EAAET,MAAM,EAAE;EAClDnD,MAAM,CAAC6D,IAAI,CAACD,KAAK,EAAE,UAAUjC,KAAK,EAAEmC,GAAG,EAAE;IACvCb,GAAG,GAAGA,GAAG,CAACnC,OAAO,CAAC,GAAG,GAAGgD,GAAG,GAAG,GAAG,EAAEX,MAAM,GAAGlD,UAAU,CAAC0B,KAAK,CAAC,GAAGA,KAAK,CAAC;EACxE,CAAC,CAAC;EACF,OAAOsB,GAAG;AACZ;AACA,OAAO,SAASc,gBAAgBA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACpD,IAAIC,GAAG,GAAGlE,MAAM,CAACW,QAAQ,CAACqD,KAAK,CAAC,GAAG;IACjCG,KAAK,EAAEH,KAAK;IACZC,YAAY,EAAEA;EAChB,CAAC,GAAGD,KAAK,IAAI,CAAC,CAAC;EACf,IAAIG,KAAK,GAAGD,GAAG,CAACC,KAAK;EACrB,IAAIC,IAAI,GAAGF,GAAG,CAACE,IAAI;EACnBH,YAAY,GAAGC,GAAG,CAACD,YAAY;EAC/B,IAAII,UAAU,GAAGH,GAAG,CAACG,UAAU,IAAI,MAAM;EACzC,IAAI,CAACF,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EACA,IAAIE,UAAU,KAAK,MAAM,EAAE;IACzB,OAAOD,IAAI,KAAK,SAAS,GAAG,2FAA2F,GAAG;IAC1H;IAAA,EACEnE,UAAU,CAACkE,KAAK,CAAC,GAAG,GAAG,IAAIF,YAAY,IAAI,EAAE,CAAC,GAAG,WAAW,GAAG,qDAAqD,GAAG,6DAA6D,GAAGhE,UAAU,CAACkE,KAAK,CAAC,GAAG,GAAG,IAAIF,YAAY,IAAI,EAAE,CAAC,GAAG,WAAW;EACvP,CAAC,MAAM;IACL;IACA;IACA;IACA;IACA,IAAIK,QAAQ,GAAGJ,GAAG,CAACI,QAAQ,IAAI,SAAS;IACxC,OAAO;MACLD,UAAU,EAAEA,UAAU;MACtBE,OAAO,EAAE,GAAG,GAAGD,QAAQ,GAAG,MAAM;MAChCE,KAAK,EAAEJ,IAAI,KAAK,SAAS,GAAG;QAC1BK,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfC,eAAe,EAAET;MACnB,CAAC,GAAG;QACFM,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,CAAC;QACfC,eAAe,EAAET;MACnB;IACF,CAAC;EACH;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,UAAUA,CAAC5B,GAAG,EAAEtB,KAAK,EAAEmD,KAAK,EAAE;EAC5C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCzE,mBAAmB,CAAC,2BAA2B,EAAE,qBAAqB,CAAC;EACzE;EACA,IAAIyC,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,MAAM,EAAE;IACnGA,GAAG,GAAG,aAAa;EACrB;EACA,IAAIT,IAAI,GAAGtC,SAAS,CAACyB,KAAK,CAAC;EAC3B,IAAIuD,MAAM,GAAGJ,KAAK,GAAG,QAAQ,GAAG,KAAK;EACrC,IAAIK,CAAC,GAAG3C,IAAI,CAAC0C,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC;EACnC,IAAIE,CAAC,GAAG5C,IAAI,CAAC0C,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACpC,IAAIG,CAAC,GAAG7C,IAAI,CAAC0C,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;EAC/B,IAAII,CAAC,GAAG9C,IAAI,CAAC0C,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;EAChC,IAAIK,CAAC,GAAG/C,IAAI,CAAC0C,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC;EAClC,IAAIM,CAAC,GAAGhD,IAAI,CAAC0C,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC;EAClC,IAAIO,CAAC,GAAGjD,IAAI,CAAC0C,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC;EACvCjC,GAAG,GAAGA,GAAG,CAACnC,OAAO,CAAC,IAAI,EAAEP,GAAG,CAAC6E,CAAC,EAAE,CAAC,CAAC,CAAC,CAACtE,OAAO,CAAC,GAAG,EAAEsE,CAAC,CAAC,CAACtE,OAAO,CAAC,MAAM,EAAEqE,CAAC,CAAC,CAACrE,OAAO,CAAC,IAAI,EAAEP,GAAG,CAAC4E,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAACrE,OAAO,CAAC,IAAI,EAAEP,GAAG,CAAC8E,CAAC,EAAE,CAAC,CAAC,CAAC,CAACvE,OAAO,CAAC,GAAG,EAAEuE,CAAC,CAAC,CAACvE,OAAO,CAAC,IAAI,EAAEP,GAAG,CAAC+E,CAAC,EAAE,CAAC,CAAC,CAAC,CAACxE,OAAO,CAAC,GAAG,EAAEwE,CAAC,CAAC,CAACxE,OAAO,CAAC,IAAI,EAAEP,GAAG,CAACgF,CAAC,EAAE,CAAC,CAAC,CAAC,CAACzE,OAAO,CAAC,GAAG,EAAEyE,CAAC,CAAC,CAACzE,OAAO,CAAC,IAAI,EAAEP,GAAG,CAACiF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC1E,OAAO,CAAC,GAAG,EAAE0E,CAAC,CAAC,CAAC1E,OAAO,CAAC,KAAK,EAAEP,GAAG,CAACkF,CAAC,EAAE,CAAC,CAAC,CAAC;EACvS,OAAOxC,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyC,YAAYA,CAACzE,GAAG,EAAE;EAChC,OAAOA,GAAG,GAAGA,GAAG,CAACM,MAAM,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC,CAAC,GAAGL,GAAG,CAAC0E,MAAM,CAAC,CAAC,CAAC,GAAG1E,GAAG;AAChE;AACA;AACA;AACA;AACA,OAAO,SAAS2E,oBAAoBA,CAACzB,KAAK,EAAE0B,YAAY,EAAE;EACxDA,YAAY,GAAGA,YAAY,IAAI,aAAa;EAC5C,OAAO7F,MAAM,CAACW,QAAQ,CAACwD,KAAK,CAAC,GAAGA,KAAK,GAAGnE,MAAM,CAAC8F,QAAQ,CAAC3B,KAAK,CAAC,GAAGA,KAAK,CAAC4B,UAAU,IAAI,CAAC5B,KAAK,CAAC4B,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE5B,KAAK,IAAI0B,YAAY,GAAGA,YAAY;AACvJ;AACA,SAASG,YAAY,QAAQ,yCAAyC;AACtE;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACvC;EACA,IAAIA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,OAAO,EAAE;IAC7C,IAAIC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC;IACzBF,KAAK,CAACG,MAAM,GAAG,IAAI;IACnBH,KAAK,CAACI,QAAQ,CAACC,IAAI,GAAGP,IAAI;EAC5B,CAAC,MAAM;IACLG,MAAM,CAACC,IAAI,CAACJ,IAAI,EAAEC,MAAM,CAAC;EAC3B;AACF;AACA,SAASO,WAAW,QAAQ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}