{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../util/number.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nfunction getSeriesStackId(seriesModel) {\n  return seriesModel.get('stack') || '__ec_stack_' + seriesModel.seriesIndex;\n}\nfunction getAxisKey(polar, axis) {\n  return axis.dim + polar.model.componentIndex;\n}\nfunction barLayoutPolar(seriesType, ecModel, api) {\n  var lastStackCoords = {};\n  var barWidthAndOffset = calRadialBar(zrUtil.filter(ecModel.getSeriesByType(seriesType), function (seriesModel) {\n    return !ecModel.isSeriesFiltered(seriesModel) && seriesModel.coordinateSystem && seriesModel.coordinateSystem.type === 'polar';\n  }));\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    // Check series coordinate, do layout for polar only\n    if (seriesModel.coordinateSystem.type !== 'polar') {\n      return;\n    }\n    var data = seriesModel.getData();\n    var polar = seriesModel.coordinateSystem;\n    var baseAxis = polar.getBaseAxis();\n    var axisKey = getAxisKey(polar, baseAxis);\n    var stackId = getSeriesStackId(seriesModel);\n    var columnLayoutInfo = barWidthAndOffset[axisKey][stackId];\n    var columnOffset = columnLayoutInfo.offset;\n    var columnWidth = columnLayoutInfo.width;\n    var valueAxis = polar.getOtherAxis(baseAxis);\n    var cx = seriesModel.coordinateSystem.cx;\n    var cy = seriesModel.coordinateSystem.cy;\n    var barMinHeight = seriesModel.get('barMinHeight') || 0;\n    var barMinAngle = seriesModel.get('barMinAngle') || 0;\n    lastStackCoords[stackId] = lastStackCoords[stackId] || [];\n    var valueDim = data.mapDimension(valueAxis.dim);\n    var baseDim = data.mapDimension(baseAxis.dim);\n    var stacked = isDimensionStacked(data, valueDim /* , baseDim */);\n    var clampLayout = baseAxis.dim !== 'radius' || !seriesModel.get('roundCap', true);\n    var valueAxisModel = valueAxis.model;\n    var startValue = valueAxisModel.get('startValue');\n    var valueAxisStart = valueAxis.dataToCoord(startValue || 0);\n    for (var idx = 0, len = data.count(); idx < len; idx++) {\n      var value = data.get(valueDim, idx);\n      var baseValue = data.get(baseDim, idx);\n      var sign = value >= 0 ? 'p' : 'n';\n      var baseCoord = valueAxisStart;\n      // Because of the barMinHeight, we can not use the value in\n      // stackResultDimension directly.\n      // Only ordinal axis can be stacked.\n      if (stacked) {\n        if (!lastStackCoords[stackId][baseValue]) {\n          lastStackCoords[stackId][baseValue] = {\n            p: valueAxisStart,\n            n: valueAxisStart // Negative stack\n          };\n        }\n        // Should also consider #4243\n        baseCoord = lastStackCoords[stackId][baseValue][sign];\n      }\n      var r0 = void 0;\n      var r = void 0;\n      var startAngle = void 0;\n      var endAngle = void 0;\n      // radial sector\n      if (valueAxis.dim === 'radius') {\n        var radiusSpan = valueAxis.dataToCoord(value) - valueAxisStart;\n        var angle = baseAxis.dataToCoord(baseValue);\n        if (Math.abs(radiusSpan) < barMinHeight) {\n          radiusSpan = (radiusSpan < 0 ? -1 : 1) * barMinHeight;\n        }\n        r0 = baseCoord;\n        r = baseCoord + radiusSpan;\n        startAngle = angle - columnOffset;\n        endAngle = startAngle - columnWidth;\n        stacked && (lastStackCoords[stackId][baseValue][sign] = r);\n      }\n      // tangential sector\n      else {\n        var angleSpan = valueAxis.dataToCoord(value, clampLayout) - valueAxisStart;\n        var radius = baseAxis.dataToCoord(baseValue);\n        if (Math.abs(angleSpan) < barMinAngle) {\n          angleSpan = (angleSpan < 0 ? -1 : 1) * barMinAngle;\n        }\n        r0 = radius + columnOffset;\n        r = r0 + columnWidth;\n        startAngle = baseCoord;\n        endAngle = baseCoord + angleSpan;\n        // if the previous stack is at the end of the ring,\n        // add a round to differentiate it from origin\n        // let extent = angleAxis.getExtent();\n        // let stackCoord = angle;\n        // if (stackCoord === extent[0] && value > 0) {\n        //     stackCoord = extent[1];\n        // }\n        // else if (stackCoord === extent[1] && value < 0) {\n        //     stackCoord = extent[0];\n        // }\n        stacked && (lastStackCoords[stackId][baseValue][sign] = endAngle);\n      }\n      data.setItemLayout(idx, {\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: r,\n        // Consider that positive angle is anti-clockwise,\n        // while positive radian of sector is clockwise\n        startAngle: -startAngle * Math.PI / 180,\n        endAngle: -endAngle * Math.PI / 180,\n        /**\r\n         * Keep the same logic with bar in catesion: use end value to\r\n         * control direction. Notice that if clockwise is true (by\r\n         * default), the sector will always draw clockwisely, no matter\r\n         * whether endAngle is greater or less than startAngle.\r\n         */\n        clockwise: startAngle >= endAngle\n      });\n    }\n  });\n}\n/**\r\n * Calculate bar width and offset for radial bar charts\r\n */\nfunction calRadialBar(barSeries) {\n  // Columns info on each category axis. Key is polar name\n  var columnsMap = {};\n  zrUtil.each(barSeries, function (seriesModel, idx) {\n    var data = seriesModel.getData();\n    var polar = seriesModel.coordinateSystem;\n    var baseAxis = polar.getBaseAxis();\n    var axisKey = getAxisKey(polar, baseAxis);\n    var axisExtent = baseAxis.getExtent();\n    var bandWidth = baseAxis.type === 'category' ? baseAxis.getBandWidth() : Math.abs(axisExtent[1] - axisExtent[0]) / data.count();\n    var columnsOnAxis = columnsMap[axisKey] || {\n      bandWidth: bandWidth,\n      remainedWidth: bandWidth,\n      autoWidthCount: 0,\n      categoryGap: '20%',\n      gap: '30%',\n      stacks: {}\n    };\n    var stacks = columnsOnAxis.stacks;\n    columnsMap[axisKey] = columnsOnAxis;\n    var stackId = getSeriesStackId(seriesModel);\n    if (!stacks[stackId]) {\n      columnsOnAxis.autoWidthCount++;\n    }\n    stacks[stackId] = stacks[stackId] || {\n      width: 0,\n      maxWidth: 0\n    };\n    var barWidth = parsePercent(seriesModel.get('barWidth'), bandWidth);\n    var barMaxWidth = parsePercent(seriesModel.get('barMaxWidth'), bandWidth);\n    var barGap = seriesModel.get('barGap');\n    var barCategoryGap = seriesModel.get('barCategoryGap');\n    if (barWidth && !stacks[stackId].width) {\n      barWidth = Math.min(columnsOnAxis.remainedWidth, barWidth);\n      stacks[stackId].width = barWidth;\n      columnsOnAxis.remainedWidth -= barWidth;\n    }\n    barMaxWidth && (stacks[stackId].maxWidth = barMaxWidth);\n    barGap != null && (columnsOnAxis.gap = barGap);\n    barCategoryGap != null && (columnsOnAxis.categoryGap = barCategoryGap);\n  });\n  var result = {};\n  zrUtil.each(columnsMap, function (columnsOnAxis, coordSysName) {\n    result[coordSysName] = {};\n    var stacks = columnsOnAxis.stacks;\n    var bandWidth = columnsOnAxis.bandWidth;\n    var categoryGap = parsePercent(columnsOnAxis.categoryGap, bandWidth);\n    var barGapPercent = parsePercent(columnsOnAxis.gap, 1);\n    var remainedWidth = columnsOnAxis.remainedWidth;\n    var autoWidthCount = columnsOnAxis.autoWidthCount;\n    var autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    // Find if any auto calculated bar exceeded maxBarWidth\n    zrUtil.each(stacks, function (column, stack) {\n      var maxWidth = column.maxWidth;\n      if (maxWidth && maxWidth < autoWidth) {\n        maxWidth = Math.min(maxWidth, remainedWidth);\n        if (column.width) {\n          maxWidth = Math.min(maxWidth, column.width);\n        }\n        remainedWidth -= maxWidth;\n        column.width = maxWidth;\n        autoWidthCount--;\n      }\n    });\n    // Recalculate width again\n    autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    var widthSum = 0;\n    var lastColumn;\n    zrUtil.each(stacks, function (column, idx) {\n      if (!column.width) {\n        column.width = autoWidth;\n      }\n      lastColumn = column;\n      widthSum += column.width * (1 + barGapPercent);\n    });\n    if (lastColumn) {\n      widthSum -= lastColumn.width * barGapPercent;\n    }\n    var offset = -widthSum / 2;\n    zrUtil.each(stacks, function (column, stackId) {\n      result[coordSysName][stackId] = result[coordSysName][stackId] || {\n        offset: offset,\n        width: column.width\n      };\n      offset += column.width * (1 + barGapPercent);\n    });\n  });\n  return result;\n}\nexport default barLayoutPolar;", "map": {"version": 3, "names": ["zrUtil", "parsePercent", "isDimensionStacked", "getSeriesStackId", "seriesModel", "get", "seriesIndex", "getAxis<PERSON>ey", "polar", "axis", "dim", "model", "componentIndex", "barLayoutPolar", "seriesType", "ecModel", "api", "lastStackCoords", "barWidthAndOffset", "calRadialBar", "filter", "getSeriesByType", "isSeriesFiltered", "coordinateSystem", "type", "eachSeriesByType", "data", "getData", "baseAxis", "getBaseAxis", "axisKey", "stackId", "columnLayoutInfo", "columnOffset", "offset", "columnWidth", "width", "valueAxis", "getOtherAxis", "cx", "cy", "barMinHeight", "barMinAngle", "valueDim", "mapDimension", "baseDim", "stacked", "clampLayout", "valueAxisModel", "startValue", "valueAxisStart", "dataToCoord", "idx", "len", "count", "value", "baseValue", "sign", "baseCoord", "p", "n", "r0", "r", "startAngle", "endAngle", "radiusSpan", "angle", "Math", "abs", "angleSpan", "radius", "setItemLayout", "PI", "clockwise", "barSeries", "columnsMap", "each", "axisExtent", "getExtent", "bandWidth", "getBandWidth", "columnsOnAxis", "remainedWidth", "autoWidthCount", "categoryGap", "gap", "stacks", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "barMaxWidth", "barGap", "barCategoryGap", "min", "result", "coordSysName", "barGapPercent", "autoWidth", "max", "column", "stack", "widthSum", "lastColumn"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/layout/barPolar.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../util/number.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nfunction getSeriesStackId(seriesModel) {\n  return seriesModel.get('stack') || '__ec_stack_' + seriesModel.seriesIndex;\n}\nfunction getAxisKey(polar, axis) {\n  return axis.dim + polar.model.componentIndex;\n}\nfunction barLayoutPolar(seriesType, ecModel, api) {\n  var lastStackCoords = {};\n  var barWidthAndOffset = calRadialBar(zrUtil.filter(ecModel.getSeriesByType(seriesType), function (seriesModel) {\n    return !ecModel.isSeriesFiltered(seriesModel) && seriesModel.coordinateSystem && seriesModel.coordinateSystem.type === 'polar';\n  }));\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    // Check series coordinate, do layout for polar only\n    if (seriesModel.coordinateSystem.type !== 'polar') {\n      return;\n    }\n    var data = seriesModel.getData();\n    var polar = seriesModel.coordinateSystem;\n    var baseAxis = polar.getBaseAxis();\n    var axisKey = getAxisKey(polar, baseAxis);\n    var stackId = getSeriesStackId(seriesModel);\n    var columnLayoutInfo = barWidthAndOffset[axisKey][stackId];\n    var columnOffset = columnLayoutInfo.offset;\n    var columnWidth = columnLayoutInfo.width;\n    var valueAxis = polar.getOtherAxis(baseAxis);\n    var cx = seriesModel.coordinateSystem.cx;\n    var cy = seriesModel.coordinateSystem.cy;\n    var barMinHeight = seriesModel.get('barMinHeight') || 0;\n    var barMinAngle = seriesModel.get('barMinAngle') || 0;\n    lastStackCoords[stackId] = lastStackCoords[stackId] || [];\n    var valueDim = data.mapDimension(valueAxis.dim);\n    var baseDim = data.mapDimension(baseAxis.dim);\n    var stacked = isDimensionStacked(data, valueDim /* , baseDim */);\n    var clampLayout = baseAxis.dim !== 'radius' || !seriesModel.get('roundCap', true);\n    var valueAxisModel = valueAxis.model;\n    var startValue = valueAxisModel.get('startValue');\n    var valueAxisStart = valueAxis.dataToCoord(startValue || 0);\n    for (var idx = 0, len = data.count(); idx < len; idx++) {\n      var value = data.get(valueDim, idx);\n      var baseValue = data.get(baseDim, idx);\n      var sign = value >= 0 ? 'p' : 'n';\n      var baseCoord = valueAxisStart;\n      // Because of the barMinHeight, we can not use the value in\n      // stackResultDimension directly.\n      // Only ordinal axis can be stacked.\n      if (stacked) {\n        if (!lastStackCoords[stackId][baseValue]) {\n          lastStackCoords[stackId][baseValue] = {\n            p: valueAxisStart,\n            n: valueAxisStart // Negative stack\n          };\n        }\n        // Should also consider #4243\n        baseCoord = lastStackCoords[stackId][baseValue][sign];\n      }\n      var r0 = void 0;\n      var r = void 0;\n      var startAngle = void 0;\n      var endAngle = void 0;\n      // radial sector\n      if (valueAxis.dim === 'radius') {\n        var radiusSpan = valueAxis.dataToCoord(value) - valueAxisStart;\n        var angle = baseAxis.dataToCoord(baseValue);\n        if (Math.abs(radiusSpan) < barMinHeight) {\n          radiusSpan = (radiusSpan < 0 ? -1 : 1) * barMinHeight;\n        }\n        r0 = baseCoord;\n        r = baseCoord + radiusSpan;\n        startAngle = angle - columnOffset;\n        endAngle = startAngle - columnWidth;\n        stacked && (lastStackCoords[stackId][baseValue][sign] = r);\n      }\n      // tangential sector\n      else {\n        var angleSpan = valueAxis.dataToCoord(value, clampLayout) - valueAxisStart;\n        var radius = baseAxis.dataToCoord(baseValue);\n        if (Math.abs(angleSpan) < barMinAngle) {\n          angleSpan = (angleSpan < 0 ? -1 : 1) * barMinAngle;\n        }\n        r0 = radius + columnOffset;\n        r = r0 + columnWidth;\n        startAngle = baseCoord;\n        endAngle = baseCoord + angleSpan;\n        // if the previous stack is at the end of the ring,\n        // add a round to differentiate it from origin\n        // let extent = angleAxis.getExtent();\n        // let stackCoord = angle;\n        // if (stackCoord === extent[0] && value > 0) {\n        //     stackCoord = extent[1];\n        // }\n        // else if (stackCoord === extent[1] && value < 0) {\n        //     stackCoord = extent[0];\n        // }\n        stacked && (lastStackCoords[stackId][baseValue][sign] = endAngle);\n      }\n      data.setItemLayout(idx, {\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: r,\n        // Consider that positive angle is anti-clockwise,\n        // while positive radian of sector is clockwise\n        startAngle: -startAngle * Math.PI / 180,\n        endAngle: -endAngle * Math.PI / 180,\n        /**\r\n         * Keep the same logic with bar in catesion: use end value to\r\n         * control direction. Notice that if clockwise is true (by\r\n         * default), the sector will always draw clockwisely, no matter\r\n         * whether endAngle is greater or less than startAngle.\r\n         */\n        clockwise: startAngle >= endAngle\n      });\n    }\n  });\n}\n/**\r\n * Calculate bar width and offset for radial bar charts\r\n */\nfunction calRadialBar(barSeries) {\n  // Columns info on each category axis. Key is polar name\n  var columnsMap = {};\n  zrUtil.each(barSeries, function (seriesModel, idx) {\n    var data = seriesModel.getData();\n    var polar = seriesModel.coordinateSystem;\n    var baseAxis = polar.getBaseAxis();\n    var axisKey = getAxisKey(polar, baseAxis);\n    var axisExtent = baseAxis.getExtent();\n    var bandWidth = baseAxis.type === 'category' ? baseAxis.getBandWidth() : Math.abs(axisExtent[1] - axisExtent[0]) / data.count();\n    var columnsOnAxis = columnsMap[axisKey] || {\n      bandWidth: bandWidth,\n      remainedWidth: bandWidth,\n      autoWidthCount: 0,\n      categoryGap: '20%',\n      gap: '30%',\n      stacks: {}\n    };\n    var stacks = columnsOnAxis.stacks;\n    columnsMap[axisKey] = columnsOnAxis;\n    var stackId = getSeriesStackId(seriesModel);\n    if (!stacks[stackId]) {\n      columnsOnAxis.autoWidthCount++;\n    }\n    stacks[stackId] = stacks[stackId] || {\n      width: 0,\n      maxWidth: 0\n    };\n    var barWidth = parsePercent(seriesModel.get('barWidth'), bandWidth);\n    var barMaxWidth = parsePercent(seriesModel.get('barMaxWidth'), bandWidth);\n    var barGap = seriesModel.get('barGap');\n    var barCategoryGap = seriesModel.get('barCategoryGap');\n    if (barWidth && !stacks[stackId].width) {\n      barWidth = Math.min(columnsOnAxis.remainedWidth, barWidth);\n      stacks[stackId].width = barWidth;\n      columnsOnAxis.remainedWidth -= barWidth;\n    }\n    barMaxWidth && (stacks[stackId].maxWidth = barMaxWidth);\n    barGap != null && (columnsOnAxis.gap = barGap);\n    barCategoryGap != null && (columnsOnAxis.categoryGap = barCategoryGap);\n  });\n  var result = {};\n  zrUtil.each(columnsMap, function (columnsOnAxis, coordSysName) {\n    result[coordSysName] = {};\n    var stacks = columnsOnAxis.stacks;\n    var bandWidth = columnsOnAxis.bandWidth;\n    var categoryGap = parsePercent(columnsOnAxis.categoryGap, bandWidth);\n    var barGapPercent = parsePercent(columnsOnAxis.gap, 1);\n    var remainedWidth = columnsOnAxis.remainedWidth;\n    var autoWidthCount = columnsOnAxis.autoWidthCount;\n    var autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    // Find if any auto calculated bar exceeded maxBarWidth\n    zrUtil.each(stacks, function (column, stack) {\n      var maxWidth = column.maxWidth;\n      if (maxWidth && maxWidth < autoWidth) {\n        maxWidth = Math.min(maxWidth, remainedWidth);\n        if (column.width) {\n          maxWidth = Math.min(maxWidth, column.width);\n        }\n        remainedWidth -= maxWidth;\n        column.width = maxWidth;\n        autoWidthCount--;\n      }\n    });\n    // Recalculate width again\n    autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    var widthSum = 0;\n    var lastColumn;\n    zrUtil.each(stacks, function (column, idx) {\n      if (!column.width) {\n        column.width = autoWidth;\n      }\n      lastColumn = column;\n      widthSum += column.width * (1 + barGapPercent);\n    });\n    if (lastColumn) {\n      widthSum -= lastColumn.width * barGapPercent;\n    }\n    var offset = -widthSum / 2;\n    zrUtil.each(stacks, function (column, stackId) {\n      result[coordSysName][stackId] = result[coordSysName][stackId] || {\n        offset: offset,\n        width: column.width\n      };\n      offset += column.width * (1 + barGapPercent);\n    });\n  });\n  return result;\n}\nexport default barLayoutPolar;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EACrC,OAAOA,WAAW,CAACC,GAAG,CAAC,OAAO,CAAC,IAAI,aAAa,GAAGD,WAAW,CAACE,WAAW;AAC5E;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC/B,OAAOA,IAAI,CAACC,GAAG,GAAGF,KAAK,CAACG,KAAK,CAACC,cAAc;AAC9C;AACA,SAASC,cAAcA,CAACC,UAAU,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAChD,IAAIC,eAAe,GAAG,CAAC,CAAC;EACxB,IAAIC,iBAAiB,GAAGC,YAAY,CAACnB,MAAM,CAACoB,MAAM,CAACL,OAAO,CAACM,eAAe,CAACP,UAAU,CAAC,EAAE,UAAUV,WAAW,EAAE;IAC7G,OAAO,CAACW,OAAO,CAACO,gBAAgB,CAAClB,WAAW,CAAC,IAAIA,WAAW,CAACmB,gBAAgB,IAAInB,WAAW,CAACmB,gBAAgB,CAACC,IAAI,KAAK,OAAO;EAChI,CAAC,CAAC,CAAC;EACHT,OAAO,CAACU,gBAAgB,CAACX,UAAU,EAAE,UAAUV,WAAW,EAAE;IAC1D;IACA,IAAIA,WAAW,CAACmB,gBAAgB,CAACC,IAAI,KAAK,OAAO,EAAE;MACjD;IACF;IACA,IAAIE,IAAI,GAAGtB,WAAW,CAACuB,OAAO,CAAC,CAAC;IAChC,IAAInB,KAAK,GAAGJ,WAAW,CAACmB,gBAAgB;IACxC,IAAIK,QAAQ,GAAGpB,KAAK,CAACqB,WAAW,CAAC,CAAC;IAClC,IAAIC,OAAO,GAAGvB,UAAU,CAACC,KAAK,EAAEoB,QAAQ,CAAC;IACzC,IAAIG,OAAO,GAAG5B,gBAAgB,CAACC,WAAW,CAAC;IAC3C,IAAI4B,gBAAgB,GAAGd,iBAAiB,CAACY,OAAO,CAAC,CAACC,OAAO,CAAC;IAC1D,IAAIE,YAAY,GAAGD,gBAAgB,CAACE,MAAM;IAC1C,IAAIC,WAAW,GAAGH,gBAAgB,CAACI,KAAK;IACxC,IAAIC,SAAS,GAAG7B,KAAK,CAAC8B,YAAY,CAACV,QAAQ,CAAC;IAC5C,IAAIW,EAAE,GAAGnC,WAAW,CAACmB,gBAAgB,CAACgB,EAAE;IACxC,IAAIC,EAAE,GAAGpC,WAAW,CAACmB,gBAAgB,CAACiB,EAAE;IACxC,IAAIC,YAAY,GAAGrC,WAAW,CAACC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC;IACvD,IAAIqC,WAAW,GAAGtC,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC;IACrDY,eAAe,CAACc,OAAO,CAAC,GAAGd,eAAe,CAACc,OAAO,CAAC,IAAI,EAAE;IACzD,IAAIY,QAAQ,GAAGjB,IAAI,CAACkB,YAAY,CAACP,SAAS,CAAC3B,GAAG,CAAC;IAC/C,IAAImC,OAAO,GAAGnB,IAAI,CAACkB,YAAY,CAAChB,QAAQ,CAAClB,GAAG,CAAC;IAC7C,IAAIoC,OAAO,GAAG5C,kBAAkB,CAACwB,IAAI,EAAEiB,QAAQ,CAAC,eAAe,CAAC;IAChE,IAAII,WAAW,GAAGnB,QAAQ,CAAClB,GAAG,KAAK,QAAQ,IAAI,CAACN,WAAW,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;IACjF,IAAI2C,cAAc,GAAGX,SAAS,CAAC1B,KAAK;IACpC,IAAIsC,UAAU,GAAGD,cAAc,CAAC3C,GAAG,CAAC,YAAY,CAAC;IACjD,IAAI6C,cAAc,GAAGb,SAAS,CAACc,WAAW,CAACF,UAAU,IAAI,CAAC,CAAC;IAC3D,KAAK,IAAIG,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG3B,IAAI,CAAC4B,KAAK,CAAC,CAAC,EAAEF,GAAG,GAAGC,GAAG,EAAED,GAAG,EAAE,EAAE;MACtD,IAAIG,KAAK,GAAG7B,IAAI,CAACrB,GAAG,CAACsC,QAAQ,EAAES,GAAG,CAAC;MACnC,IAAII,SAAS,GAAG9B,IAAI,CAACrB,GAAG,CAACwC,OAAO,EAAEO,GAAG,CAAC;MACtC,IAAIK,IAAI,GAAGF,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;MACjC,IAAIG,SAAS,GAAGR,cAAc;MAC9B;MACA;MACA;MACA,IAAIJ,OAAO,EAAE;QACX,IAAI,CAAC7B,eAAe,CAACc,OAAO,CAAC,CAACyB,SAAS,CAAC,EAAE;UACxCvC,eAAe,CAACc,OAAO,CAAC,CAACyB,SAAS,CAAC,GAAG;YACpCG,CAAC,EAAET,cAAc;YACjBU,CAAC,EAAEV,cAAc,CAAC;UACpB,CAAC;QACH;QACA;QACAQ,SAAS,GAAGzC,eAAe,CAACc,OAAO,CAAC,CAACyB,SAAS,CAAC,CAACC,IAAI,CAAC;MACvD;MACA,IAAII,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,CAAC,GAAG,KAAK,CAAC;MACd,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIC,QAAQ,GAAG,KAAK,CAAC;MACrB;MACA,IAAI3B,SAAS,CAAC3B,GAAG,KAAK,QAAQ,EAAE;QAC9B,IAAIuD,UAAU,GAAG5B,SAAS,CAACc,WAAW,CAACI,KAAK,CAAC,GAAGL,cAAc;QAC9D,IAAIgB,KAAK,GAAGtC,QAAQ,CAACuB,WAAW,CAACK,SAAS,CAAC;QAC3C,IAAIW,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC,GAAGxB,YAAY,EAAE;UACvCwB,UAAU,GAAG,CAACA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIxB,YAAY;QACvD;QACAoB,EAAE,GAAGH,SAAS;QACdI,CAAC,GAAGJ,SAAS,GAAGO,UAAU;QAC1BF,UAAU,GAAGG,KAAK,GAAGjC,YAAY;QACjC+B,QAAQ,GAAGD,UAAU,GAAG5B,WAAW;QACnCW,OAAO,KAAK7B,eAAe,CAACc,OAAO,CAAC,CAACyB,SAAS,CAAC,CAACC,IAAI,CAAC,GAAGK,CAAC,CAAC;MAC5D;MACA;MAAA,KACK;QACH,IAAIO,SAAS,GAAGhC,SAAS,CAACc,WAAW,CAACI,KAAK,EAAER,WAAW,CAAC,GAAGG,cAAc;QAC1E,IAAIoB,MAAM,GAAG1C,QAAQ,CAACuB,WAAW,CAACK,SAAS,CAAC;QAC5C,IAAIW,IAAI,CAACC,GAAG,CAACC,SAAS,CAAC,GAAG3B,WAAW,EAAE;UACrC2B,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI3B,WAAW;QACpD;QACAmB,EAAE,GAAGS,MAAM,GAAGrC,YAAY;QAC1B6B,CAAC,GAAGD,EAAE,GAAG1B,WAAW;QACpB4B,UAAU,GAAGL,SAAS;QACtBM,QAAQ,GAAGN,SAAS,GAAGW,SAAS;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAvB,OAAO,KAAK7B,eAAe,CAACc,OAAO,CAAC,CAACyB,SAAS,CAAC,CAACC,IAAI,CAAC,GAAGO,QAAQ,CAAC;MACnE;MACAtC,IAAI,CAAC6C,aAAa,CAACnB,GAAG,EAAE;QACtBb,EAAE,EAAEA,EAAE;QACNC,EAAE,EAAEA,EAAE;QACNqB,EAAE,EAAEA,EAAE;QACNC,CAAC,EAAEA,CAAC;QACJ;QACA;QACAC,UAAU,EAAE,CAACA,UAAU,GAAGI,IAAI,CAACK,EAAE,GAAG,GAAG;QACvCR,QAAQ,EAAE,CAACA,QAAQ,GAAGG,IAAI,CAACK,EAAE,GAAG,GAAG;QACnC;AACR;AACA;AACA;AACA;AACA;QACQC,SAAS,EAAEV,UAAU,IAAIC;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,SAAS7C,YAAYA,CAACuD,SAAS,EAAE;EAC/B;EACA,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnB3E,MAAM,CAAC4E,IAAI,CAACF,SAAS,EAAE,UAAUtE,WAAW,EAAEgD,GAAG,EAAE;IACjD,IAAI1B,IAAI,GAAGtB,WAAW,CAACuB,OAAO,CAAC,CAAC;IAChC,IAAInB,KAAK,GAAGJ,WAAW,CAACmB,gBAAgB;IACxC,IAAIK,QAAQ,GAAGpB,KAAK,CAACqB,WAAW,CAAC,CAAC;IAClC,IAAIC,OAAO,GAAGvB,UAAU,CAACC,KAAK,EAAEoB,QAAQ,CAAC;IACzC,IAAIiD,UAAU,GAAGjD,QAAQ,CAACkD,SAAS,CAAC,CAAC;IACrC,IAAIC,SAAS,GAAGnD,QAAQ,CAACJ,IAAI,KAAK,UAAU,GAAGI,QAAQ,CAACoD,YAAY,CAAC,CAAC,GAAGb,IAAI,CAACC,GAAG,CAACS,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGnD,IAAI,CAAC4B,KAAK,CAAC,CAAC;IAC/H,IAAI2B,aAAa,GAAGN,UAAU,CAAC7C,OAAO,CAAC,IAAI;MACzCiD,SAAS,EAAEA,SAAS;MACpBG,aAAa,EAAEH,SAAS;MACxBI,cAAc,EAAE,CAAC;MACjBC,WAAW,EAAE,KAAK;MAClBC,GAAG,EAAE,KAAK;MACVC,MAAM,EAAE,CAAC;IACX,CAAC;IACD,IAAIA,MAAM,GAAGL,aAAa,CAACK,MAAM;IACjCX,UAAU,CAAC7C,OAAO,CAAC,GAAGmD,aAAa;IACnC,IAAIlD,OAAO,GAAG5B,gBAAgB,CAACC,WAAW,CAAC;IAC3C,IAAI,CAACkF,MAAM,CAACvD,OAAO,CAAC,EAAE;MACpBkD,aAAa,CAACE,cAAc,EAAE;IAChC;IACAG,MAAM,CAACvD,OAAO,CAAC,GAAGuD,MAAM,CAACvD,OAAO,CAAC,IAAI;MACnCK,KAAK,EAAE,CAAC;MACRmD,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIC,QAAQ,GAAGvF,YAAY,CAACG,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAE0E,SAAS,CAAC;IACnE,IAAIU,WAAW,GAAGxF,YAAY,CAACG,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAE0E,SAAS,CAAC;IACzE,IAAIW,MAAM,GAAGtF,WAAW,CAACC,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAIsF,cAAc,GAAGvF,WAAW,CAACC,GAAG,CAAC,gBAAgB,CAAC;IACtD,IAAImF,QAAQ,IAAI,CAACF,MAAM,CAACvD,OAAO,CAAC,CAACK,KAAK,EAAE;MACtCoD,QAAQ,GAAGrB,IAAI,CAACyB,GAAG,CAACX,aAAa,CAACC,aAAa,EAAEM,QAAQ,CAAC;MAC1DF,MAAM,CAACvD,OAAO,CAAC,CAACK,KAAK,GAAGoD,QAAQ;MAChCP,aAAa,CAACC,aAAa,IAAIM,QAAQ;IACzC;IACAC,WAAW,KAAKH,MAAM,CAACvD,OAAO,CAAC,CAACwD,QAAQ,GAAGE,WAAW,CAAC;IACvDC,MAAM,IAAI,IAAI,KAAKT,aAAa,CAACI,GAAG,GAAGK,MAAM,CAAC;IAC9CC,cAAc,IAAI,IAAI,KAAKV,aAAa,CAACG,WAAW,GAAGO,cAAc,CAAC;EACxE,CAAC,CAAC;EACF,IAAIE,MAAM,GAAG,CAAC,CAAC;EACf7F,MAAM,CAAC4E,IAAI,CAACD,UAAU,EAAE,UAAUM,aAAa,EAAEa,YAAY,EAAE;IAC7DD,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;IACzB,IAAIR,MAAM,GAAGL,aAAa,CAACK,MAAM;IACjC,IAAIP,SAAS,GAAGE,aAAa,CAACF,SAAS;IACvC,IAAIK,WAAW,GAAGnF,YAAY,CAACgF,aAAa,CAACG,WAAW,EAAEL,SAAS,CAAC;IACpE,IAAIgB,aAAa,GAAG9F,YAAY,CAACgF,aAAa,CAACI,GAAG,EAAE,CAAC,CAAC;IACtD,IAAIH,aAAa,GAAGD,aAAa,CAACC,aAAa;IAC/C,IAAIC,cAAc,GAAGF,aAAa,CAACE,cAAc;IACjD,IAAIa,SAAS,GAAG,CAACd,aAAa,GAAGE,WAAW,KAAKD,cAAc,GAAG,CAACA,cAAc,GAAG,CAAC,IAAIY,aAAa,CAAC;IACvGC,SAAS,GAAG7B,IAAI,CAAC8B,GAAG,CAACD,SAAS,EAAE,CAAC,CAAC;IAClC;IACAhG,MAAM,CAAC4E,IAAI,CAACU,MAAM,EAAE,UAAUY,MAAM,EAAEC,KAAK,EAAE;MAC3C,IAAIZ,QAAQ,GAAGW,MAAM,CAACX,QAAQ;MAC9B,IAAIA,QAAQ,IAAIA,QAAQ,GAAGS,SAAS,EAAE;QACpCT,QAAQ,GAAGpB,IAAI,CAACyB,GAAG,CAACL,QAAQ,EAAEL,aAAa,CAAC;QAC5C,IAAIgB,MAAM,CAAC9D,KAAK,EAAE;UAChBmD,QAAQ,GAAGpB,IAAI,CAACyB,GAAG,CAACL,QAAQ,EAAEW,MAAM,CAAC9D,KAAK,CAAC;QAC7C;QACA8C,aAAa,IAAIK,QAAQ;QACzBW,MAAM,CAAC9D,KAAK,GAAGmD,QAAQ;QACvBJ,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF;IACAa,SAAS,GAAG,CAACd,aAAa,GAAGE,WAAW,KAAKD,cAAc,GAAG,CAACA,cAAc,GAAG,CAAC,IAAIY,aAAa,CAAC;IACnGC,SAAS,GAAG7B,IAAI,CAAC8B,GAAG,CAACD,SAAS,EAAE,CAAC,CAAC;IAClC,IAAII,QAAQ,GAAG,CAAC;IAChB,IAAIC,UAAU;IACdrG,MAAM,CAAC4E,IAAI,CAACU,MAAM,EAAE,UAAUY,MAAM,EAAE9C,GAAG,EAAE;MACzC,IAAI,CAAC8C,MAAM,CAAC9D,KAAK,EAAE;QACjB8D,MAAM,CAAC9D,KAAK,GAAG4D,SAAS;MAC1B;MACAK,UAAU,GAAGH,MAAM;MACnBE,QAAQ,IAAIF,MAAM,CAAC9D,KAAK,IAAI,CAAC,GAAG2D,aAAa,CAAC;IAChD,CAAC,CAAC;IACF,IAAIM,UAAU,EAAE;MACdD,QAAQ,IAAIC,UAAU,CAACjE,KAAK,GAAG2D,aAAa;IAC9C;IACA,IAAI7D,MAAM,GAAG,CAACkE,QAAQ,GAAG,CAAC;IAC1BpG,MAAM,CAAC4E,IAAI,CAACU,MAAM,EAAE,UAAUY,MAAM,EAAEnE,OAAO,EAAE;MAC7C8D,MAAM,CAACC,YAAY,CAAC,CAAC/D,OAAO,CAAC,GAAG8D,MAAM,CAACC,YAAY,CAAC,CAAC/D,OAAO,CAAC,IAAI;QAC/DG,MAAM,EAAEA,MAAM;QACdE,KAAK,EAAE8D,MAAM,CAAC9D;MAChB,CAAC;MACDF,MAAM,IAAIgE,MAAM,CAAC9D,KAAK,IAAI,CAAC,GAAG2D,aAAa,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOF,MAAM;AACf;AACA,eAAehF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}