{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, defaults, keys } from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../util/number.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nimport createRenderPlanner from '../chart/helper/createRenderPlanner.js';\nimport { createFloat32Array } from '../util/vendor.js';\nvar STACK_PREFIX = '__ec_stack_';\nfunction getSeriesStackId(seriesModel) {\n  return seriesModel.get('stack') || STACK_PREFIX + seriesModel.seriesIndex;\n}\nfunction getAxisKey(axis) {\n  return axis.dim + axis.index;\n}\n/**\r\n * @return {Object} {width, offset, offsetCenter} If axis.type is not 'category', return undefined.\r\n */\nexport function getLayoutOnAxis(opt) {\n  var params = [];\n  var baseAxis = opt.axis;\n  var axisKey = 'axis0';\n  if (baseAxis.type !== 'category') {\n    return;\n  }\n  var bandWidth = baseAxis.getBandWidth();\n  for (var i = 0; i < opt.count || 0; i++) {\n    params.push(defaults({\n      bandWidth: bandWidth,\n      axisKey: axisKey,\n      stackId: STACK_PREFIX + i\n    }, opt));\n  }\n  var widthAndOffsets = doCalBarWidthAndOffset(params);\n  var result = [];\n  for (var i = 0; i < opt.count; i++) {\n    var item = widthAndOffsets[axisKey][STACK_PREFIX + i];\n    item.offsetCenter = item.offset + item.width / 2;\n    result.push(item);\n  }\n  return result;\n}\nexport function prepareLayoutBarSeries(seriesType, ecModel) {\n  var seriesModels = [];\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    // Check series coordinate, do layout for cartesian2d only\n    if (isOnCartesian(seriesModel)) {\n      seriesModels.push(seriesModel);\n    }\n  });\n  return seriesModels;\n}\n/**\r\n * Map from (baseAxis.dim + '_' + baseAxis.index) to min gap of two adjacent\r\n * values.\r\n * This works for time axes, value axes, and log axes.\r\n * For a single time axis, return value is in the form like\r\n * {'x_0': [1000000]}.\r\n * The value of 1000000 is in milliseconds.\r\n */\nfunction getValueAxesMinGaps(barSeries) {\n  /**\r\n   * Map from axis.index to values.\r\n   * For a single time axis, axisValues is in the form like\r\n   * {'x_0': [1495555200000, 1495641600000, 1495728000000]}.\r\n   * Items in axisValues[x], e.g. 1495555200000, are time values of all\r\n   * series.\r\n   */\n  var axisValues = {};\n  each(barSeries, function (seriesModel) {\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    if (baseAxis.type !== 'time' && baseAxis.type !== 'value') {\n      return;\n    }\n    var data = seriesModel.getData();\n    var key = baseAxis.dim + '_' + baseAxis.index;\n    var dimIdx = data.getDimensionIndex(data.mapDimension(baseAxis.dim));\n    var store = data.getStore();\n    for (var i = 0, cnt = store.count(); i < cnt; ++i) {\n      var value = store.get(dimIdx, i);\n      if (!axisValues[key]) {\n        // No previous data for the axis\n        axisValues[key] = [value];\n      } else {\n        // No value in previous series\n        axisValues[key].push(value);\n      }\n      // Ignore duplicated time values in the same axis\n    }\n  });\n  var axisMinGaps = {};\n  for (var key in axisValues) {\n    if (axisValues.hasOwnProperty(key)) {\n      var valuesInAxis = axisValues[key];\n      if (valuesInAxis) {\n        // Sort axis values into ascending order to calculate gaps\n        valuesInAxis.sort(function (a, b) {\n          return a - b;\n        });\n        var min = null;\n        for (var j = 1; j < valuesInAxis.length; ++j) {\n          var delta = valuesInAxis[j] - valuesInAxis[j - 1];\n          if (delta > 0) {\n            // Ignore 0 delta because they are of the same axis value\n            min = min === null ? delta : Math.min(min, delta);\n          }\n        }\n        // Set to null if only have one data\n        axisMinGaps[key] = min;\n      }\n    }\n  }\n  return axisMinGaps;\n}\nexport function makeColumnLayout(barSeries) {\n  var axisMinGaps = getValueAxesMinGaps(barSeries);\n  var seriesInfoList = [];\n  each(barSeries, function (seriesModel) {\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var axisExtent = baseAxis.getExtent();\n    var bandWidth;\n    if (baseAxis.type === 'category') {\n      bandWidth = baseAxis.getBandWidth();\n    } else if (baseAxis.type === 'value' || baseAxis.type === 'time') {\n      var key = baseAxis.dim + '_' + baseAxis.index;\n      var minGap = axisMinGaps[key];\n      var extentSpan = Math.abs(axisExtent[1] - axisExtent[0]);\n      var scale = baseAxis.scale.getExtent();\n      var scaleSpan = Math.abs(scale[1] - scale[0]);\n      bandWidth = minGap ? extentSpan / scaleSpan * minGap : extentSpan; // When there is only one data value\n    } else {\n      var data = seriesModel.getData();\n      bandWidth = Math.abs(axisExtent[1] - axisExtent[0]) / data.count();\n    }\n    var barWidth = parsePercent(seriesModel.get('barWidth'), bandWidth);\n    var barMaxWidth = parsePercent(seriesModel.get('barMaxWidth'), bandWidth);\n    var barMinWidth = parsePercent(\n    // barMinWidth by default is 0.5 / 1 in cartesian. Because in value axis,\n    // the auto-calculated bar width might be less than 0.5 / 1.\n    seriesModel.get('barMinWidth') || (isInLargeMode(seriesModel) ? 0.5 : 1), bandWidth);\n    var barGap = seriesModel.get('barGap');\n    var barCategoryGap = seriesModel.get('barCategoryGap');\n    seriesInfoList.push({\n      bandWidth: bandWidth,\n      barWidth: barWidth,\n      barMaxWidth: barMaxWidth,\n      barMinWidth: barMinWidth,\n      barGap: barGap,\n      barCategoryGap: barCategoryGap,\n      axisKey: getAxisKey(baseAxis),\n      stackId: getSeriesStackId(seriesModel)\n    });\n  });\n  return doCalBarWidthAndOffset(seriesInfoList);\n}\nfunction doCalBarWidthAndOffset(seriesInfoList) {\n  // Columns info on each category axis. Key is cartesian name\n  var columnsMap = {};\n  each(seriesInfoList, function (seriesInfo, idx) {\n    var axisKey = seriesInfo.axisKey;\n    var bandWidth = seriesInfo.bandWidth;\n    var columnsOnAxis = columnsMap[axisKey] || {\n      bandWidth: bandWidth,\n      remainedWidth: bandWidth,\n      autoWidthCount: 0,\n      categoryGap: null,\n      gap: '20%',\n      stacks: {}\n    };\n    var stacks = columnsOnAxis.stacks;\n    columnsMap[axisKey] = columnsOnAxis;\n    var stackId = seriesInfo.stackId;\n    if (!stacks[stackId]) {\n      columnsOnAxis.autoWidthCount++;\n    }\n    stacks[stackId] = stacks[stackId] || {\n      width: 0,\n      maxWidth: 0\n    };\n    // Caution: In a single coordinate system, these barGrid attributes\n    // will be shared by series. Consider that they have default values,\n    // only the attributes set on the last series will work.\n    // Do not change this fact unless there will be a break change.\n    var barWidth = seriesInfo.barWidth;\n    if (barWidth && !stacks[stackId].width) {\n      // See #6312, do not restrict width.\n      stacks[stackId].width = barWidth;\n      barWidth = Math.min(columnsOnAxis.remainedWidth, barWidth);\n      columnsOnAxis.remainedWidth -= barWidth;\n    }\n    var barMaxWidth = seriesInfo.barMaxWidth;\n    barMaxWidth && (stacks[stackId].maxWidth = barMaxWidth);\n    var barMinWidth = seriesInfo.barMinWidth;\n    barMinWidth && (stacks[stackId].minWidth = barMinWidth);\n    var barGap = seriesInfo.barGap;\n    barGap != null && (columnsOnAxis.gap = barGap);\n    var barCategoryGap = seriesInfo.barCategoryGap;\n    barCategoryGap != null && (columnsOnAxis.categoryGap = barCategoryGap);\n  });\n  var result = {};\n  each(columnsMap, function (columnsOnAxis, coordSysName) {\n    result[coordSysName] = {};\n    var stacks = columnsOnAxis.stacks;\n    var bandWidth = columnsOnAxis.bandWidth;\n    var categoryGapPercent = columnsOnAxis.categoryGap;\n    if (categoryGapPercent == null) {\n      var columnCount = keys(stacks).length;\n      // More columns in one group\n      // the spaces between group is smaller. Or the column will be too thin.\n      categoryGapPercent = Math.max(35 - columnCount * 4, 15) + '%';\n    }\n    var categoryGap = parsePercent(categoryGapPercent, bandWidth);\n    var barGapPercent = parsePercent(columnsOnAxis.gap, 1);\n    var remainedWidth = columnsOnAxis.remainedWidth;\n    var autoWidthCount = columnsOnAxis.autoWidthCount;\n    var autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    // Find if any auto calculated bar exceeded maxBarWidth\n    each(stacks, function (column) {\n      var maxWidth = column.maxWidth;\n      var minWidth = column.minWidth;\n      if (!column.width) {\n        var finalWidth = autoWidth;\n        if (maxWidth && maxWidth < finalWidth) {\n          finalWidth = Math.min(maxWidth, remainedWidth);\n        }\n        // `minWidth` has higher priority. `minWidth` decide that whether the\n        // bar is able to be visible. So `minWidth` should not be restricted\n        // by `maxWidth` or `remainedWidth` (which is from `bandWidth`). In\n        // the extreme cases for `value` axis, bars are allowed to overlap\n        // with each other if `minWidth` specified.\n        if (minWidth && minWidth > finalWidth) {\n          finalWidth = minWidth;\n        }\n        if (finalWidth !== autoWidth) {\n          column.width = finalWidth;\n          remainedWidth -= finalWidth + barGapPercent * finalWidth;\n          autoWidthCount--;\n        }\n      } else {\n        // `barMinWidth/barMaxWidth` has higher priority than `barWidth`, as\n        // CSS does. Because barWidth can be a percent value, where\n        // `barMaxWidth` can be used to restrict the final width.\n        var finalWidth = column.width;\n        if (maxWidth) {\n          finalWidth = Math.min(finalWidth, maxWidth);\n        }\n        // `minWidth` has higher priority, as described above\n        if (minWidth) {\n          finalWidth = Math.max(finalWidth, minWidth);\n        }\n        column.width = finalWidth;\n        remainedWidth -= finalWidth + barGapPercent * finalWidth;\n        autoWidthCount--;\n      }\n    });\n    // Recalculate width again\n    autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    var widthSum = 0;\n    var lastColumn;\n    each(stacks, function (column, idx) {\n      if (!column.width) {\n        column.width = autoWidth;\n      }\n      lastColumn = column;\n      widthSum += column.width * (1 + barGapPercent);\n    });\n    if (lastColumn) {\n      widthSum -= lastColumn.width * barGapPercent;\n    }\n    var offset = -widthSum / 2;\n    each(stacks, function (column, stackId) {\n      result[coordSysName][stackId] = result[coordSysName][stackId] || {\n        bandWidth: bandWidth,\n        offset: offset,\n        width: column.width\n      };\n      offset += column.width * (1 + barGapPercent);\n    });\n  });\n  return result;\n}\nfunction retrieveColumnLayout(barWidthAndOffset, axis, seriesModel) {\n  if (barWidthAndOffset && axis) {\n    var result = barWidthAndOffset[getAxisKey(axis)];\n    if (result != null && seriesModel != null) {\n      return result[getSeriesStackId(seriesModel)];\n    }\n    return result;\n  }\n}\nexport { retrieveColumnLayout };\nexport function layout(seriesType, ecModel) {\n  var seriesModels = prepareLayoutBarSeries(seriesType, ecModel);\n  var barWidthAndOffset = makeColumnLayout(seriesModels);\n  each(seriesModels, function (seriesModel) {\n    var data = seriesModel.getData();\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var stackId = getSeriesStackId(seriesModel);\n    var columnLayoutInfo = barWidthAndOffset[getAxisKey(baseAxis)][stackId];\n    var columnOffset = columnLayoutInfo.offset;\n    var columnWidth = columnLayoutInfo.width;\n    data.setLayout({\n      bandWidth: columnLayoutInfo.bandWidth,\n      offset: columnOffset,\n      size: columnWidth\n    });\n  });\n}\n// TODO: Do not support stack in large mode yet.\nexport function createProgressiveLayout(seriesType) {\n  return {\n    seriesType: seriesType,\n    plan: createRenderPlanner(),\n    reset: function (seriesModel) {\n      if (!isOnCartesian(seriesModel)) {\n        return;\n      }\n      var data = seriesModel.getData();\n      var cartesian = seriesModel.coordinateSystem;\n      var baseAxis = cartesian.getBaseAxis();\n      var valueAxis = cartesian.getOtherAxis(baseAxis);\n      var valueDimIdx = data.getDimensionIndex(data.mapDimension(valueAxis.dim));\n      var baseDimIdx = data.getDimensionIndex(data.mapDimension(baseAxis.dim));\n      var drawBackground = seriesModel.get('showBackground', true);\n      var valueDim = data.mapDimension(valueAxis.dim);\n      var stackResultDim = data.getCalculationInfo('stackResultDimension');\n      var stacked = isDimensionStacked(data, valueDim) && !!data.getCalculationInfo('stackedOnSeries');\n      var isValueAxisH = valueAxis.isHorizontal();\n      var valueAxisStart = getValueAxisStart(baseAxis, valueAxis);\n      var isLarge = isInLargeMode(seriesModel);\n      var barMinHeight = seriesModel.get('barMinHeight') || 0;\n      var stackedDimIdx = stackResultDim && data.getDimensionIndex(stackResultDim);\n      // Layout info.\n      var columnWidth = data.getLayout('size');\n      var columnOffset = data.getLayout('offset');\n      return {\n        progress: function (params, data) {\n          var count = params.count;\n          var largePoints = isLarge && createFloat32Array(count * 3);\n          var largeBackgroundPoints = isLarge && drawBackground && createFloat32Array(count * 3);\n          var largeDataIndices = isLarge && createFloat32Array(count);\n          var coordLayout = cartesian.master.getRect();\n          var bgSize = isValueAxisH ? coordLayout.width : coordLayout.height;\n          var dataIndex;\n          var store = data.getStore();\n          var idxOffset = 0;\n          while ((dataIndex = params.next()) != null) {\n            var value = store.get(stacked ? stackedDimIdx : valueDimIdx, dataIndex);\n            var baseValue = store.get(baseDimIdx, dataIndex);\n            var baseCoord = valueAxisStart;\n            var stackStartValue = void 0;\n            // Because of the barMinHeight, we can not use the value in\n            // stackResultDimension directly.\n            if (stacked) {\n              stackStartValue = +value - store.get(valueDimIdx, dataIndex);\n            }\n            var x = void 0;\n            var y = void 0;\n            var width = void 0;\n            var height = void 0;\n            if (isValueAxisH) {\n              var coord = cartesian.dataToPoint([value, baseValue]);\n              if (stacked) {\n                var startCoord = cartesian.dataToPoint([stackStartValue, baseValue]);\n                baseCoord = startCoord[0];\n              }\n              x = baseCoord;\n              y = coord[1] + columnOffset;\n              width = coord[0] - baseCoord;\n              height = columnWidth;\n              if (Math.abs(width) < barMinHeight) {\n                width = (width < 0 ? -1 : 1) * barMinHeight;\n              }\n            } else {\n              var coord = cartesian.dataToPoint([baseValue, value]);\n              if (stacked) {\n                var startCoord = cartesian.dataToPoint([baseValue, stackStartValue]);\n                baseCoord = startCoord[1];\n              }\n              x = coord[0] + columnOffset;\n              y = baseCoord;\n              width = columnWidth;\n              height = coord[1] - baseCoord;\n              if (Math.abs(height) < barMinHeight) {\n                // Include zero to has a positive bar\n                height = (height <= 0 ? -1 : 1) * barMinHeight;\n              }\n            }\n            if (!isLarge) {\n              data.setItemLayout(dataIndex, {\n                x: x,\n                y: y,\n                width: width,\n                height: height\n              });\n            } else {\n              largePoints[idxOffset] = x;\n              largePoints[idxOffset + 1] = y;\n              largePoints[idxOffset + 2] = isValueAxisH ? width : height;\n              if (largeBackgroundPoints) {\n                largeBackgroundPoints[idxOffset] = isValueAxisH ? coordLayout.x : x;\n                largeBackgroundPoints[idxOffset + 1] = isValueAxisH ? y : coordLayout.y;\n                largeBackgroundPoints[idxOffset + 2] = bgSize;\n              }\n              largeDataIndices[dataIndex] = dataIndex;\n            }\n            idxOffset += 3;\n          }\n          if (isLarge) {\n            data.setLayout({\n              largePoints: largePoints,\n              largeDataIndices: largeDataIndices,\n              largeBackgroundPoints: largeBackgroundPoints,\n              valueAxisHorizontal: isValueAxisH\n            });\n          }\n        }\n      };\n    }\n  };\n}\nfunction isOnCartesian(seriesModel) {\n  return seriesModel.coordinateSystem && seriesModel.coordinateSystem.type === 'cartesian2d';\n}\nfunction isInLargeMode(seriesModel) {\n  return seriesModel.pipelineContext && seriesModel.pipelineContext.large;\n}\n// See cases in `test/bar-start.html` and `#7412`, `#8747`.\nfunction getValueAxisStart(baseAxis, valueAxis) {\n  var startValue = valueAxis.model.get('startValue');\n  if (!startValue) {\n    startValue = 0;\n  }\n  return valueAxis.toGlobalCoord(valueAxis.dataToCoord(valueAxis.type === 'log' ? startValue > 0 ? startValue : 1 : startValue));\n}", "map": {"version": 3, "names": ["each", "defaults", "keys", "parsePercent", "isDimensionStacked", "createRenderPlanner", "createFloat32Array", "STACK_PREFIX", "getSeriesStackId", "seriesModel", "get", "seriesIndex", "getAxis<PERSON>ey", "axis", "dim", "index", "getLayoutOnAxis", "opt", "params", "baseAxis", "axisKey", "type", "bandWidth", "getBandWidth", "i", "count", "push", "stackId", "widthAndOffsets", "doCalBarWidthAndOffset", "result", "item", "offsetCenter", "offset", "width", "prepareLayoutBarSeries", "seriesType", "ecModel", "seriesModels", "eachSeriesByType", "isOnCartesian", "getValueAxesMinGaps", "barSeries", "axisValues", "cartesian", "coordinateSystem", "getBaseAxis", "data", "getData", "key", "dimIdx", "getDimensionIndex", "mapDimension", "store", "getStore", "cnt", "value", "axisMinGaps", "hasOwnProperty", "valuesInAxis", "sort", "a", "b", "min", "j", "length", "delta", "Math", "makeColumnLayout", "seriesInfoList", "axisExtent", "getExtent", "minGap", "extentSpan", "abs", "scale", "scaleSpan", "<PERSON><PERSON><PERSON><PERSON>", "barMaxWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isInLargeMode", "barGap", "barCategoryGap", "columnsMap", "seriesInfo", "idx", "columnsOnAxis", "remainedWidth", "autoWidthCount", "categoryGap", "gap", "stacks", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "coordSysName", "categoryGapPercent", "columnCount", "max", "barGapPercent", "autoWidth", "column", "finalWidth", "widthSum", "lastColumn", "retrieveColumnLayout", "barWidthAndOffset", "layout", "columnLayoutInfo", "columnOffset", "columnWidth", "setLayout", "size", "createProgressiveLayout", "plan", "reset", "valueAxis", "getOtherAxis", "valueDimIdx", "baseDimIdx", "drawBackground", "valueDim", "stackResultDim", "getCalculationInfo", "stacked", "isValueAxisH", "isHorizontal", "valueAxisStart", "getValueAxisStart", "is<PERSON>arge", "barMinHeight", "stackedDimIdx", "getLayout", "progress", "largePoints", "largeBackgroundPoints", "largeDataIndices", "coordLayout", "master", "getRect", "bgSize", "height", "dataIndex", "idxOffset", "next", "baseValue", "baseCoord", "stackStartValue", "x", "y", "coord", "dataToPoint", "startCoord", "setItemLayout", "valueAxisHorizontal", "pipelineContext", "large", "startValue", "model", "toGlobalCoord", "dataToCoord"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/layout/barGrid.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, defaults, keys } from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../util/number.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nimport createRenderPlanner from '../chart/helper/createRenderPlanner.js';\nimport { createFloat32Array } from '../util/vendor.js';\nvar STACK_PREFIX = '__ec_stack_';\nfunction getSeriesStackId(seriesModel) {\n  return seriesModel.get('stack') || STACK_PREFIX + seriesModel.seriesIndex;\n}\nfunction getAxisKey(axis) {\n  return axis.dim + axis.index;\n}\n/**\r\n * @return {Object} {width, offset, offsetCenter} If axis.type is not 'category', return undefined.\r\n */\nexport function getLayoutOnAxis(opt) {\n  var params = [];\n  var baseAxis = opt.axis;\n  var axisKey = 'axis0';\n  if (baseAxis.type !== 'category') {\n    return;\n  }\n  var bandWidth = baseAxis.getBandWidth();\n  for (var i = 0; i < opt.count || 0; i++) {\n    params.push(defaults({\n      bandWidth: bandWidth,\n      axisKey: axisKey,\n      stackId: STACK_PREFIX + i\n    }, opt));\n  }\n  var widthAndOffsets = doCalBarWidthAndOffset(params);\n  var result = [];\n  for (var i = 0; i < opt.count; i++) {\n    var item = widthAndOffsets[axisKey][STACK_PREFIX + i];\n    item.offsetCenter = item.offset + item.width / 2;\n    result.push(item);\n  }\n  return result;\n}\nexport function prepareLayoutBarSeries(seriesType, ecModel) {\n  var seriesModels = [];\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    // Check series coordinate, do layout for cartesian2d only\n    if (isOnCartesian(seriesModel)) {\n      seriesModels.push(seriesModel);\n    }\n  });\n  return seriesModels;\n}\n/**\r\n * Map from (baseAxis.dim + '_' + baseAxis.index) to min gap of two adjacent\r\n * values.\r\n * This works for time axes, value axes, and log axes.\r\n * For a single time axis, return value is in the form like\r\n * {'x_0': [1000000]}.\r\n * The value of 1000000 is in milliseconds.\r\n */\nfunction getValueAxesMinGaps(barSeries) {\n  /**\r\n   * Map from axis.index to values.\r\n   * For a single time axis, axisValues is in the form like\r\n   * {'x_0': [1495555200000, 1495641600000, 1495728000000]}.\r\n   * Items in axisValues[x], e.g. 1495555200000, are time values of all\r\n   * series.\r\n   */\n  var axisValues = {};\n  each(barSeries, function (seriesModel) {\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    if (baseAxis.type !== 'time' && baseAxis.type !== 'value') {\n      return;\n    }\n    var data = seriesModel.getData();\n    var key = baseAxis.dim + '_' + baseAxis.index;\n    var dimIdx = data.getDimensionIndex(data.mapDimension(baseAxis.dim));\n    var store = data.getStore();\n    for (var i = 0, cnt = store.count(); i < cnt; ++i) {\n      var value = store.get(dimIdx, i);\n      if (!axisValues[key]) {\n        // No previous data for the axis\n        axisValues[key] = [value];\n      } else {\n        // No value in previous series\n        axisValues[key].push(value);\n      }\n      // Ignore duplicated time values in the same axis\n    }\n  });\n  var axisMinGaps = {};\n  for (var key in axisValues) {\n    if (axisValues.hasOwnProperty(key)) {\n      var valuesInAxis = axisValues[key];\n      if (valuesInAxis) {\n        // Sort axis values into ascending order to calculate gaps\n        valuesInAxis.sort(function (a, b) {\n          return a - b;\n        });\n        var min = null;\n        for (var j = 1; j < valuesInAxis.length; ++j) {\n          var delta = valuesInAxis[j] - valuesInAxis[j - 1];\n          if (delta > 0) {\n            // Ignore 0 delta because they are of the same axis value\n            min = min === null ? delta : Math.min(min, delta);\n          }\n        }\n        // Set to null if only have one data\n        axisMinGaps[key] = min;\n      }\n    }\n  }\n  return axisMinGaps;\n}\nexport function makeColumnLayout(barSeries) {\n  var axisMinGaps = getValueAxesMinGaps(barSeries);\n  var seriesInfoList = [];\n  each(barSeries, function (seriesModel) {\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var axisExtent = baseAxis.getExtent();\n    var bandWidth;\n    if (baseAxis.type === 'category') {\n      bandWidth = baseAxis.getBandWidth();\n    } else if (baseAxis.type === 'value' || baseAxis.type === 'time') {\n      var key = baseAxis.dim + '_' + baseAxis.index;\n      var minGap = axisMinGaps[key];\n      var extentSpan = Math.abs(axisExtent[1] - axisExtent[0]);\n      var scale = baseAxis.scale.getExtent();\n      var scaleSpan = Math.abs(scale[1] - scale[0]);\n      bandWidth = minGap ? extentSpan / scaleSpan * minGap : extentSpan; // When there is only one data value\n    } else {\n      var data = seriesModel.getData();\n      bandWidth = Math.abs(axisExtent[1] - axisExtent[0]) / data.count();\n    }\n    var barWidth = parsePercent(seriesModel.get('barWidth'), bandWidth);\n    var barMaxWidth = parsePercent(seriesModel.get('barMaxWidth'), bandWidth);\n    var barMinWidth = parsePercent(\n    // barMinWidth by default is 0.5 / 1 in cartesian. Because in value axis,\n    // the auto-calculated bar width might be less than 0.5 / 1.\n    seriesModel.get('barMinWidth') || (isInLargeMode(seriesModel) ? 0.5 : 1), bandWidth);\n    var barGap = seriesModel.get('barGap');\n    var barCategoryGap = seriesModel.get('barCategoryGap');\n    seriesInfoList.push({\n      bandWidth: bandWidth,\n      barWidth: barWidth,\n      barMaxWidth: barMaxWidth,\n      barMinWidth: barMinWidth,\n      barGap: barGap,\n      barCategoryGap: barCategoryGap,\n      axisKey: getAxisKey(baseAxis),\n      stackId: getSeriesStackId(seriesModel)\n    });\n  });\n  return doCalBarWidthAndOffset(seriesInfoList);\n}\nfunction doCalBarWidthAndOffset(seriesInfoList) {\n  // Columns info on each category axis. Key is cartesian name\n  var columnsMap = {};\n  each(seriesInfoList, function (seriesInfo, idx) {\n    var axisKey = seriesInfo.axisKey;\n    var bandWidth = seriesInfo.bandWidth;\n    var columnsOnAxis = columnsMap[axisKey] || {\n      bandWidth: bandWidth,\n      remainedWidth: bandWidth,\n      autoWidthCount: 0,\n      categoryGap: null,\n      gap: '20%',\n      stacks: {}\n    };\n    var stacks = columnsOnAxis.stacks;\n    columnsMap[axisKey] = columnsOnAxis;\n    var stackId = seriesInfo.stackId;\n    if (!stacks[stackId]) {\n      columnsOnAxis.autoWidthCount++;\n    }\n    stacks[stackId] = stacks[stackId] || {\n      width: 0,\n      maxWidth: 0\n    };\n    // Caution: In a single coordinate system, these barGrid attributes\n    // will be shared by series. Consider that they have default values,\n    // only the attributes set on the last series will work.\n    // Do not change this fact unless there will be a break change.\n    var barWidth = seriesInfo.barWidth;\n    if (barWidth && !stacks[stackId].width) {\n      // See #6312, do not restrict width.\n      stacks[stackId].width = barWidth;\n      barWidth = Math.min(columnsOnAxis.remainedWidth, barWidth);\n      columnsOnAxis.remainedWidth -= barWidth;\n    }\n    var barMaxWidth = seriesInfo.barMaxWidth;\n    barMaxWidth && (stacks[stackId].maxWidth = barMaxWidth);\n    var barMinWidth = seriesInfo.barMinWidth;\n    barMinWidth && (stacks[stackId].minWidth = barMinWidth);\n    var barGap = seriesInfo.barGap;\n    barGap != null && (columnsOnAxis.gap = barGap);\n    var barCategoryGap = seriesInfo.barCategoryGap;\n    barCategoryGap != null && (columnsOnAxis.categoryGap = barCategoryGap);\n  });\n  var result = {};\n  each(columnsMap, function (columnsOnAxis, coordSysName) {\n    result[coordSysName] = {};\n    var stacks = columnsOnAxis.stacks;\n    var bandWidth = columnsOnAxis.bandWidth;\n    var categoryGapPercent = columnsOnAxis.categoryGap;\n    if (categoryGapPercent == null) {\n      var columnCount = keys(stacks).length;\n      // More columns in one group\n      // the spaces between group is smaller. Or the column will be too thin.\n      categoryGapPercent = Math.max(35 - columnCount * 4, 15) + '%';\n    }\n    var categoryGap = parsePercent(categoryGapPercent, bandWidth);\n    var barGapPercent = parsePercent(columnsOnAxis.gap, 1);\n    var remainedWidth = columnsOnAxis.remainedWidth;\n    var autoWidthCount = columnsOnAxis.autoWidthCount;\n    var autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    // Find if any auto calculated bar exceeded maxBarWidth\n    each(stacks, function (column) {\n      var maxWidth = column.maxWidth;\n      var minWidth = column.minWidth;\n      if (!column.width) {\n        var finalWidth = autoWidth;\n        if (maxWidth && maxWidth < finalWidth) {\n          finalWidth = Math.min(maxWidth, remainedWidth);\n        }\n        // `minWidth` has higher priority. `minWidth` decide that whether the\n        // bar is able to be visible. So `minWidth` should not be restricted\n        // by `maxWidth` or `remainedWidth` (which is from `bandWidth`). In\n        // the extreme cases for `value` axis, bars are allowed to overlap\n        // with each other if `minWidth` specified.\n        if (minWidth && minWidth > finalWidth) {\n          finalWidth = minWidth;\n        }\n        if (finalWidth !== autoWidth) {\n          column.width = finalWidth;\n          remainedWidth -= finalWidth + barGapPercent * finalWidth;\n          autoWidthCount--;\n        }\n      } else {\n        // `barMinWidth/barMaxWidth` has higher priority than `barWidth`, as\n        // CSS does. Because barWidth can be a percent value, where\n        // `barMaxWidth` can be used to restrict the final width.\n        var finalWidth = column.width;\n        if (maxWidth) {\n          finalWidth = Math.min(finalWidth, maxWidth);\n        }\n        // `minWidth` has higher priority, as described above\n        if (minWidth) {\n          finalWidth = Math.max(finalWidth, minWidth);\n        }\n        column.width = finalWidth;\n        remainedWidth -= finalWidth + barGapPercent * finalWidth;\n        autoWidthCount--;\n      }\n    });\n    // Recalculate width again\n    autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    var widthSum = 0;\n    var lastColumn;\n    each(stacks, function (column, idx) {\n      if (!column.width) {\n        column.width = autoWidth;\n      }\n      lastColumn = column;\n      widthSum += column.width * (1 + barGapPercent);\n    });\n    if (lastColumn) {\n      widthSum -= lastColumn.width * barGapPercent;\n    }\n    var offset = -widthSum / 2;\n    each(stacks, function (column, stackId) {\n      result[coordSysName][stackId] = result[coordSysName][stackId] || {\n        bandWidth: bandWidth,\n        offset: offset,\n        width: column.width\n      };\n      offset += column.width * (1 + barGapPercent);\n    });\n  });\n  return result;\n}\nfunction retrieveColumnLayout(barWidthAndOffset, axis, seriesModel) {\n  if (barWidthAndOffset && axis) {\n    var result = barWidthAndOffset[getAxisKey(axis)];\n    if (result != null && seriesModel != null) {\n      return result[getSeriesStackId(seriesModel)];\n    }\n    return result;\n  }\n}\nexport { retrieveColumnLayout };\nexport function layout(seriesType, ecModel) {\n  var seriesModels = prepareLayoutBarSeries(seriesType, ecModel);\n  var barWidthAndOffset = makeColumnLayout(seriesModels);\n  each(seriesModels, function (seriesModel) {\n    var data = seriesModel.getData();\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var stackId = getSeriesStackId(seriesModel);\n    var columnLayoutInfo = barWidthAndOffset[getAxisKey(baseAxis)][stackId];\n    var columnOffset = columnLayoutInfo.offset;\n    var columnWidth = columnLayoutInfo.width;\n    data.setLayout({\n      bandWidth: columnLayoutInfo.bandWidth,\n      offset: columnOffset,\n      size: columnWidth\n    });\n  });\n}\n// TODO: Do not support stack in large mode yet.\nexport function createProgressiveLayout(seriesType) {\n  return {\n    seriesType: seriesType,\n    plan: createRenderPlanner(),\n    reset: function (seriesModel) {\n      if (!isOnCartesian(seriesModel)) {\n        return;\n      }\n      var data = seriesModel.getData();\n      var cartesian = seriesModel.coordinateSystem;\n      var baseAxis = cartesian.getBaseAxis();\n      var valueAxis = cartesian.getOtherAxis(baseAxis);\n      var valueDimIdx = data.getDimensionIndex(data.mapDimension(valueAxis.dim));\n      var baseDimIdx = data.getDimensionIndex(data.mapDimension(baseAxis.dim));\n      var drawBackground = seriesModel.get('showBackground', true);\n      var valueDim = data.mapDimension(valueAxis.dim);\n      var stackResultDim = data.getCalculationInfo('stackResultDimension');\n      var stacked = isDimensionStacked(data, valueDim) && !!data.getCalculationInfo('stackedOnSeries');\n      var isValueAxisH = valueAxis.isHorizontal();\n      var valueAxisStart = getValueAxisStart(baseAxis, valueAxis);\n      var isLarge = isInLargeMode(seriesModel);\n      var barMinHeight = seriesModel.get('barMinHeight') || 0;\n      var stackedDimIdx = stackResultDim && data.getDimensionIndex(stackResultDim);\n      // Layout info.\n      var columnWidth = data.getLayout('size');\n      var columnOffset = data.getLayout('offset');\n      return {\n        progress: function (params, data) {\n          var count = params.count;\n          var largePoints = isLarge && createFloat32Array(count * 3);\n          var largeBackgroundPoints = isLarge && drawBackground && createFloat32Array(count * 3);\n          var largeDataIndices = isLarge && createFloat32Array(count);\n          var coordLayout = cartesian.master.getRect();\n          var bgSize = isValueAxisH ? coordLayout.width : coordLayout.height;\n          var dataIndex;\n          var store = data.getStore();\n          var idxOffset = 0;\n          while ((dataIndex = params.next()) != null) {\n            var value = store.get(stacked ? stackedDimIdx : valueDimIdx, dataIndex);\n            var baseValue = store.get(baseDimIdx, dataIndex);\n            var baseCoord = valueAxisStart;\n            var stackStartValue = void 0;\n            // Because of the barMinHeight, we can not use the value in\n            // stackResultDimension directly.\n            if (stacked) {\n              stackStartValue = +value - store.get(valueDimIdx, dataIndex);\n            }\n            var x = void 0;\n            var y = void 0;\n            var width = void 0;\n            var height = void 0;\n            if (isValueAxisH) {\n              var coord = cartesian.dataToPoint([value, baseValue]);\n              if (stacked) {\n                var startCoord = cartesian.dataToPoint([stackStartValue, baseValue]);\n                baseCoord = startCoord[0];\n              }\n              x = baseCoord;\n              y = coord[1] + columnOffset;\n              width = coord[0] - baseCoord;\n              height = columnWidth;\n              if (Math.abs(width) < barMinHeight) {\n                width = (width < 0 ? -1 : 1) * barMinHeight;\n              }\n            } else {\n              var coord = cartesian.dataToPoint([baseValue, value]);\n              if (stacked) {\n                var startCoord = cartesian.dataToPoint([baseValue, stackStartValue]);\n                baseCoord = startCoord[1];\n              }\n              x = coord[0] + columnOffset;\n              y = baseCoord;\n              width = columnWidth;\n              height = coord[1] - baseCoord;\n              if (Math.abs(height) < barMinHeight) {\n                // Include zero to has a positive bar\n                height = (height <= 0 ? -1 : 1) * barMinHeight;\n              }\n            }\n            if (!isLarge) {\n              data.setItemLayout(dataIndex, {\n                x: x,\n                y: y,\n                width: width,\n                height: height\n              });\n            } else {\n              largePoints[idxOffset] = x;\n              largePoints[idxOffset + 1] = y;\n              largePoints[idxOffset + 2] = isValueAxisH ? width : height;\n              if (largeBackgroundPoints) {\n                largeBackgroundPoints[idxOffset] = isValueAxisH ? coordLayout.x : x;\n                largeBackgroundPoints[idxOffset + 1] = isValueAxisH ? y : coordLayout.y;\n                largeBackgroundPoints[idxOffset + 2] = bgSize;\n              }\n              largeDataIndices[dataIndex] = dataIndex;\n            }\n            idxOffset += 3;\n          }\n          if (isLarge) {\n            data.setLayout({\n              largePoints: largePoints,\n              largeDataIndices: largeDataIndices,\n              largeBackgroundPoints: largeBackgroundPoints,\n              valueAxisHorizontal: isValueAxisH\n            });\n          }\n        }\n      };\n    }\n  };\n}\nfunction isOnCartesian(seriesModel) {\n  return seriesModel.coordinateSystem && seriesModel.coordinateSystem.type === 'cartesian2d';\n}\nfunction isInLargeMode(seriesModel) {\n  return seriesModel.pipelineContext && seriesModel.pipelineContext.large;\n}\n// See cases in `test/bar-start.html` and `#7412`, `#8747`.\nfunction getValueAxisStart(baseAxis, valueAxis) {\n  var startValue = valueAxis.model.get('startValue');\n  if (!startValue) {\n    startValue = 0;\n  }\n  return valueAxis.toGlobalCoord(valueAxis.dataToCoord(valueAxis.type === 'log' ? startValue > 0 ? startValue : 1 : startValue));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,0BAA0B;AAC/D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,IAAIC,YAAY,GAAG,aAAa;AAChC,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EACrC,OAAOA,WAAW,CAACC,GAAG,CAAC,OAAO,CAAC,IAAIH,YAAY,GAAGE,WAAW,CAACE,WAAW;AAC3E;AACA,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOA,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACE,KAAK;AAC9B;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,GAAG,EAAE;EACnC,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGF,GAAG,CAACJ,IAAI;EACvB,IAAIO,OAAO,GAAG,OAAO;EACrB,IAAID,QAAQ,CAACE,IAAI,KAAK,UAAU,EAAE;IAChC;EACF;EACA,IAAIC,SAAS,GAAGH,QAAQ,CAACI,YAAY,CAAC,CAAC;EACvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,GAAG,CAACQ,KAAK,IAAI,CAAC,EAAED,CAAC,EAAE,EAAE;IACvCN,MAAM,CAACQ,IAAI,CAACzB,QAAQ,CAAC;MACnBqB,SAAS,EAAEA,SAAS;MACpBF,OAAO,EAAEA,OAAO;MAChBO,OAAO,EAAEpB,YAAY,GAAGiB;IAC1B,CAAC,EAAEP,GAAG,CAAC,CAAC;EACV;EACA,IAAIW,eAAe,GAAGC,sBAAsB,CAACX,MAAM,CAAC;EACpD,IAAIY,MAAM,GAAG,EAAE;EACf,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,GAAG,CAACQ,KAAK,EAAED,CAAC,EAAE,EAAE;IAClC,IAAIO,IAAI,GAAGH,eAAe,CAACR,OAAO,CAAC,CAACb,YAAY,GAAGiB,CAAC,CAAC;IACrDO,IAAI,CAACC,YAAY,GAAGD,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACG,KAAK,GAAG,CAAC;IAChDJ,MAAM,CAACJ,IAAI,CAACK,IAAI,CAAC;EACnB;EACA,OAAOD,MAAM;AACf;AACA,OAAO,SAASK,sBAAsBA,CAACC,UAAU,EAAEC,OAAO,EAAE;EAC1D,IAAIC,YAAY,GAAG,EAAE;EACrBD,OAAO,CAACE,gBAAgB,CAACH,UAAU,EAAE,UAAU3B,WAAW,EAAE;IAC1D;IACA,IAAI+B,aAAa,CAAC/B,WAAW,CAAC,EAAE;MAC9B6B,YAAY,CAACZ,IAAI,CAACjB,WAAW,CAAC;IAChC;EACF,CAAC,CAAC;EACF,OAAO6B,YAAY;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,mBAAmBA,CAACC,SAAS,EAAE;EACtC;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnB3C,IAAI,CAAC0C,SAAS,EAAE,UAAUjC,WAAW,EAAE;IACrC,IAAImC,SAAS,GAAGnC,WAAW,CAACoC,gBAAgB;IAC5C,IAAI1B,QAAQ,GAAGyB,SAAS,CAACE,WAAW,CAAC,CAAC;IACtC,IAAI3B,QAAQ,CAACE,IAAI,KAAK,MAAM,IAAIF,QAAQ,CAACE,IAAI,KAAK,OAAO,EAAE;MACzD;IACF;IACA,IAAI0B,IAAI,GAAGtC,WAAW,CAACuC,OAAO,CAAC,CAAC;IAChC,IAAIC,GAAG,GAAG9B,QAAQ,CAACL,GAAG,GAAG,GAAG,GAAGK,QAAQ,CAACJ,KAAK;IAC7C,IAAImC,MAAM,GAAGH,IAAI,CAACI,iBAAiB,CAACJ,IAAI,CAACK,YAAY,CAACjC,QAAQ,CAACL,GAAG,CAAC,CAAC;IACpE,IAAIuC,KAAK,GAAGN,IAAI,CAACO,QAAQ,CAAC,CAAC;IAC3B,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAE+B,GAAG,GAAGF,KAAK,CAAC5B,KAAK,CAAC,CAAC,EAAED,CAAC,GAAG+B,GAAG,EAAE,EAAE/B,CAAC,EAAE;MACjD,IAAIgC,KAAK,GAAGH,KAAK,CAAC3C,GAAG,CAACwC,MAAM,EAAE1B,CAAC,CAAC;MAChC,IAAI,CAACmB,UAAU,CAACM,GAAG,CAAC,EAAE;QACpB;QACAN,UAAU,CAACM,GAAG,CAAC,GAAG,CAACO,KAAK,CAAC;MAC3B,CAAC,MAAM;QACL;QACAb,UAAU,CAACM,GAAG,CAAC,CAACvB,IAAI,CAAC8B,KAAK,CAAC;MAC7B;MACA;IACF;EACF,CAAC,CAAC;EACF,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,KAAK,IAAIR,GAAG,IAAIN,UAAU,EAAE;IAC1B,IAAIA,UAAU,CAACe,cAAc,CAACT,GAAG,CAAC,EAAE;MAClC,IAAIU,YAAY,GAAGhB,UAAU,CAACM,GAAG,CAAC;MAClC,IAAIU,YAAY,EAAE;QAChB;QACAA,YAAY,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;UAChC,OAAOD,CAAC,GAAGC,CAAC;QACd,CAAC,CAAC;QACF,IAAIC,GAAG,GAAG,IAAI;QACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,YAAY,CAACM,MAAM,EAAE,EAAED,CAAC,EAAE;UAC5C,IAAIE,KAAK,GAAGP,YAAY,CAACK,CAAC,CAAC,GAAGL,YAAY,CAACK,CAAC,GAAG,CAAC,CAAC;UACjD,IAAIE,KAAK,GAAG,CAAC,EAAE;YACb;YACAH,GAAG,GAAGA,GAAG,KAAK,IAAI,GAAGG,KAAK,GAAGC,IAAI,CAACJ,GAAG,CAACA,GAAG,EAAEG,KAAK,CAAC;UACnD;QACF;QACA;QACAT,WAAW,CAACR,GAAG,CAAC,GAAGc,GAAG;MACxB;IACF;EACF;EACA,OAAON,WAAW;AACpB;AACA,OAAO,SAASW,gBAAgBA,CAAC1B,SAAS,EAAE;EAC1C,IAAIe,WAAW,GAAGhB,mBAAmB,CAACC,SAAS,CAAC;EAChD,IAAI2B,cAAc,GAAG,EAAE;EACvBrE,IAAI,CAAC0C,SAAS,EAAE,UAAUjC,WAAW,EAAE;IACrC,IAAImC,SAAS,GAAGnC,WAAW,CAACoC,gBAAgB;IAC5C,IAAI1B,QAAQ,GAAGyB,SAAS,CAACE,WAAW,CAAC,CAAC;IACtC,IAAIwB,UAAU,GAAGnD,QAAQ,CAACoD,SAAS,CAAC,CAAC;IACrC,IAAIjD,SAAS;IACb,IAAIH,QAAQ,CAACE,IAAI,KAAK,UAAU,EAAE;MAChCC,SAAS,GAAGH,QAAQ,CAACI,YAAY,CAAC,CAAC;IACrC,CAAC,MAAM,IAAIJ,QAAQ,CAACE,IAAI,KAAK,OAAO,IAAIF,QAAQ,CAACE,IAAI,KAAK,MAAM,EAAE;MAChE,IAAI4B,GAAG,GAAG9B,QAAQ,CAACL,GAAG,GAAG,GAAG,GAAGK,QAAQ,CAACJ,KAAK;MAC7C,IAAIyD,MAAM,GAAGf,WAAW,CAACR,GAAG,CAAC;MAC7B,IAAIwB,UAAU,GAAGN,IAAI,CAACO,GAAG,CAACJ,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC;MACxD,IAAIK,KAAK,GAAGxD,QAAQ,CAACwD,KAAK,CAACJ,SAAS,CAAC,CAAC;MACtC,IAAIK,SAAS,GAAGT,IAAI,CAACO,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC7CrD,SAAS,GAAGkD,MAAM,GAAGC,UAAU,GAAGG,SAAS,GAAGJ,MAAM,GAAGC,UAAU,CAAC,CAAC;IACrE,CAAC,MAAM;MACL,IAAI1B,IAAI,GAAGtC,WAAW,CAACuC,OAAO,CAAC,CAAC;MAChC1B,SAAS,GAAG6C,IAAI,CAACO,GAAG,CAACJ,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGvB,IAAI,CAACtB,KAAK,CAAC,CAAC;IACpE;IACA,IAAIoD,QAAQ,GAAG1E,YAAY,CAACM,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEY,SAAS,CAAC;IACnE,IAAIwD,WAAW,GAAG3E,YAAY,CAACM,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEY,SAAS,CAAC;IACzE,IAAIyD,WAAW,GAAG5E,YAAY;IAC9B;IACA;IACAM,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,KAAKsE,aAAa,CAACvE,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAEa,SAAS,CAAC;IACpF,IAAI2D,MAAM,GAAGxE,WAAW,CAACC,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAIwE,cAAc,GAAGzE,WAAW,CAACC,GAAG,CAAC,gBAAgB,CAAC;IACtD2D,cAAc,CAAC3C,IAAI,CAAC;MAClBJ,SAAS,EAAEA,SAAS;MACpBuD,QAAQ,EAAEA,QAAQ;MAClBC,WAAW,EAAEA,WAAW;MACxBC,WAAW,EAAEA,WAAW;MACxBE,MAAM,EAAEA,MAAM;MACdC,cAAc,EAAEA,cAAc;MAC9B9D,OAAO,EAAER,UAAU,CAACO,QAAQ,CAAC;MAC7BQ,OAAO,EAAEnB,gBAAgB,CAACC,WAAW;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOoB,sBAAsB,CAACwC,cAAc,CAAC;AAC/C;AACA,SAASxC,sBAAsBA,CAACwC,cAAc,EAAE;EAC9C;EACA,IAAIc,UAAU,GAAG,CAAC,CAAC;EACnBnF,IAAI,CAACqE,cAAc,EAAE,UAAUe,UAAU,EAAEC,GAAG,EAAE;IAC9C,IAAIjE,OAAO,GAAGgE,UAAU,CAAChE,OAAO;IAChC,IAAIE,SAAS,GAAG8D,UAAU,CAAC9D,SAAS;IACpC,IAAIgE,aAAa,GAAGH,UAAU,CAAC/D,OAAO,CAAC,IAAI;MACzCE,SAAS,EAAEA,SAAS;MACpBiE,aAAa,EAAEjE,SAAS;MACxBkE,cAAc,EAAE,CAAC;MACjBC,WAAW,EAAE,IAAI;MACjBC,GAAG,EAAE,KAAK;MACVC,MAAM,EAAE,CAAC;IACX,CAAC;IACD,IAAIA,MAAM,GAAGL,aAAa,CAACK,MAAM;IACjCR,UAAU,CAAC/D,OAAO,CAAC,GAAGkE,aAAa;IACnC,IAAI3D,OAAO,GAAGyD,UAAU,CAACzD,OAAO;IAChC,IAAI,CAACgE,MAAM,CAAChE,OAAO,CAAC,EAAE;MACpB2D,aAAa,CAACE,cAAc,EAAE;IAChC;IACAG,MAAM,CAAChE,OAAO,CAAC,GAAGgE,MAAM,CAAChE,OAAO,CAAC,IAAI;MACnCO,KAAK,EAAE,CAAC;MACR0D,QAAQ,EAAE;IACZ,CAAC;IACD;IACA;IACA;IACA;IACA,IAAIf,QAAQ,GAAGO,UAAU,CAACP,QAAQ;IAClC,IAAIA,QAAQ,IAAI,CAACc,MAAM,CAAChE,OAAO,CAAC,CAACO,KAAK,EAAE;MACtC;MACAyD,MAAM,CAAChE,OAAO,CAAC,CAACO,KAAK,GAAG2C,QAAQ;MAChCA,QAAQ,GAAGV,IAAI,CAACJ,GAAG,CAACuB,aAAa,CAACC,aAAa,EAAEV,QAAQ,CAAC;MAC1DS,aAAa,CAACC,aAAa,IAAIV,QAAQ;IACzC;IACA,IAAIC,WAAW,GAAGM,UAAU,CAACN,WAAW;IACxCA,WAAW,KAAKa,MAAM,CAAChE,OAAO,CAAC,CAACiE,QAAQ,GAAGd,WAAW,CAAC;IACvD,IAAIC,WAAW,GAAGK,UAAU,CAACL,WAAW;IACxCA,WAAW,KAAKY,MAAM,CAAChE,OAAO,CAAC,CAACkE,QAAQ,GAAGd,WAAW,CAAC;IACvD,IAAIE,MAAM,GAAGG,UAAU,CAACH,MAAM;IAC9BA,MAAM,IAAI,IAAI,KAAKK,aAAa,CAACI,GAAG,GAAGT,MAAM,CAAC;IAC9C,IAAIC,cAAc,GAAGE,UAAU,CAACF,cAAc;IAC9CA,cAAc,IAAI,IAAI,KAAKI,aAAa,CAACG,WAAW,GAAGP,cAAc,CAAC;EACxE,CAAC,CAAC;EACF,IAAIpD,MAAM,GAAG,CAAC,CAAC;EACf9B,IAAI,CAACmF,UAAU,EAAE,UAAUG,aAAa,EAAEQ,YAAY,EAAE;IACtDhE,MAAM,CAACgE,YAAY,CAAC,GAAG,CAAC,CAAC;IACzB,IAAIH,MAAM,GAAGL,aAAa,CAACK,MAAM;IACjC,IAAIrE,SAAS,GAAGgE,aAAa,CAAChE,SAAS;IACvC,IAAIyE,kBAAkB,GAAGT,aAAa,CAACG,WAAW;IAClD,IAAIM,kBAAkB,IAAI,IAAI,EAAE;MAC9B,IAAIC,WAAW,GAAG9F,IAAI,CAACyF,MAAM,CAAC,CAAC1B,MAAM;MACrC;MACA;MACA8B,kBAAkB,GAAG5B,IAAI,CAAC8B,GAAG,CAAC,EAAE,GAAGD,WAAW,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;IAC/D;IACA,IAAIP,WAAW,GAAGtF,YAAY,CAAC4F,kBAAkB,EAAEzE,SAAS,CAAC;IAC7D,IAAI4E,aAAa,GAAG/F,YAAY,CAACmF,aAAa,CAACI,GAAG,EAAE,CAAC,CAAC;IACtD,IAAIH,aAAa,GAAGD,aAAa,CAACC,aAAa;IAC/C,IAAIC,cAAc,GAAGF,aAAa,CAACE,cAAc;IACjD,IAAIW,SAAS,GAAG,CAACZ,aAAa,GAAGE,WAAW,KAAKD,cAAc,GAAG,CAACA,cAAc,GAAG,CAAC,IAAIU,aAAa,CAAC;IACvGC,SAAS,GAAGhC,IAAI,CAAC8B,GAAG,CAACE,SAAS,EAAE,CAAC,CAAC;IAClC;IACAnG,IAAI,CAAC2F,MAAM,EAAE,UAAUS,MAAM,EAAE;MAC7B,IAAIR,QAAQ,GAAGQ,MAAM,CAACR,QAAQ;MAC9B,IAAIC,QAAQ,GAAGO,MAAM,CAACP,QAAQ;MAC9B,IAAI,CAACO,MAAM,CAAClE,KAAK,EAAE;QACjB,IAAImE,UAAU,GAAGF,SAAS;QAC1B,IAAIP,QAAQ,IAAIA,QAAQ,GAAGS,UAAU,EAAE;UACrCA,UAAU,GAAGlC,IAAI,CAACJ,GAAG,CAAC6B,QAAQ,EAAEL,aAAa,CAAC;QAChD;QACA;QACA;QACA;QACA;QACA;QACA,IAAIM,QAAQ,IAAIA,QAAQ,GAAGQ,UAAU,EAAE;UACrCA,UAAU,GAAGR,QAAQ;QACvB;QACA,IAAIQ,UAAU,KAAKF,SAAS,EAAE;UAC5BC,MAAM,CAAClE,KAAK,GAAGmE,UAAU;UACzBd,aAAa,IAAIc,UAAU,GAAGH,aAAa,GAAGG,UAAU;UACxDb,cAAc,EAAE;QAClB;MACF,CAAC,MAAM;QACL;QACA;QACA;QACA,IAAIa,UAAU,GAAGD,MAAM,CAAClE,KAAK;QAC7B,IAAI0D,QAAQ,EAAE;UACZS,UAAU,GAAGlC,IAAI,CAACJ,GAAG,CAACsC,UAAU,EAAET,QAAQ,CAAC;QAC7C;QACA;QACA,IAAIC,QAAQ,EAAE;UACZQ,UAAU,GAAGlC,IAAI,CAAC8B,GAAG,CAACI,UAAU,EAAER,QAAQ,CAAC;QAC7C;QACAO,MAAM,CAAClE,KAAK,GAAGmE,UAAU;QACzBd,aAAa,IAAIc,UAAU,GAAGH,aAAa,GAAGG,UAAU;QACxDb,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF;IACAW,SAAS,GAAG,CAACZ,aAAa,GAAGE,WAAW,KAAKD,cAAc,GAAG,CAACA,cAAc,GAAG,CAAC,IAAIU,aAAa,CAAC;IACnGC,SAAS,GAAGhC,IAAI,CAAC8B,GAAG,CAACE,SAAS,EAAE,CAAC,CAAC;IAClC,IAAIG,QAAQ,GAAG,CAAC;IAChB,IAAIC,UAAU;IACdvG,IAAI,CAAC2F,MAAM,EAAE,UAAUS,MAAM,EAAEf,GAAG,EAAE;MAClC,IAAI,CAACe,MAAM,CAAClE,KAAK,EAAE;QACjBkE,MAAM,CAAClE,KAAK,GAAGiE,SAAS;MAC1B;MACAI,UAAU,GAAGH,MAAM;MACnBE,QAAQ,IAAIF,MAAM,CAAClE,KAAK,IAAI,CAAC,GAAGgE,aAAa,CAAC;IAChD,CAAC,CAAC;IACF,IAAIK,UAAU,EAAE;MACdD,QAAQ,IAAIC,UAAU,CAACrE,KAAK,GAAGgE,aAAa;IAC9C;IACA,IAAIjE,MAAM,GAAG,CAACqE,QAAQ,GAAG,CAAC;IAC1BtG,IAAI,CAAC2F,MAAM,EAAE,UAAUS,MAAM,EAAEzE,OAAO,EAAE;MACtCG,MAAM,CAACgE,YAAY,CAAC,CAACnE,OAAO,CAAC,GAAGG,MAAM,CAACgE,YAAY,CAAC,CAACnE,OAAO,CAAC,IAAI;QAC/DL,SAAS,EAAEA,SAAS;QACpBW,MAAM,EAAEA,MAAM;QACdC,KAAK,EAAEkE,MAAM,CAAClE;MAChB,CAAC;MACDD,MAAM,IAAImE,MAAM,CAAClE,KAAK,IAAI,CAAC,GAAGgE,aAAa,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOpE,MAAM;AACf;AACA,SAAS0E,oBAAoBA,CAACC,iBAAiB,EAAE5F,IAAI,EAAEJ,WAAW,EAAE;EAClE,IAAIgG,iBAAiB,IAAI5F,IAAI,EAAE;IAC7B,IAAIiB,MAAM,GAAG2E,iBAAiB,CAAC7F,UAAU,CAACC,IAAI,CAAC,CAAC;IAChD,IAAIiB,MAAM,IAAI,IAAI,IAAIrB,WAAW,IAAI,IAAI,EAAE;MACzC,OAAOqB,MAAM,CAACtB,gBAAgB,CAACC,WAAW,CAAC,CAAC;IAC9C;IACA,OAAOqB,MAAM;EACf;AACF;AACA,SAAS0E,oBAAoB;AAC7B,OAAO,SAASE,MAAMA,CAACtE,UAAU,EAAEC,OAAO,EAAE;EAC1C,IAAIC,YAAY,GAAGH,sBAAsB,CAACC,UAAU,EAAEC,OAAO,CAAC;EAC9D,IAAIoE,iBAAiB,GAAGrC,gBAAgB,CAAC9B,YAAY,CAAC;EACtDtC,IAAI,CAACsC,YAAY,EAAE,UAAU7B,WAAW,EAAE;IACxC,IAAIsC,IAAI,GAAGtC,WAAW,CAACuC,OAAO,CAAC,CAAC;IAChC,IAAIJ,SAAS,GAAGnC,WAAW,CAACoC,gBAAgB;IAC5C,IAAI1B,QAAQ,GAAGyB,SAAS,CAACE,WAAW,CAAC,CAAC;IACtC,IAAInB,OAAO,GAAGnB,gBAAgB,CAACC,WAAW,CAAC;IAC3C,IAAIkG,gBAAgB,GAAGF,iBAAiB,CAAC7F,UAAU,CAACO,QAAQ,CAAC,CAAC,CAACQ,OAAO,CAAC;IACvE,IAAIiF,YAAY,GAAGD,gBAAgB,CAAC1E,MAAM;IAC1C,IAAI4E,WAAW,GAAGF,gBAAgB,CAACzE,KAAK;IACxCa,IAAI,CAAC+D,SAAS,CAAC;MACbxF,SAAS,EAAEqF,gBAAgB,CAACrF,SAAS;MACrCW,MAAM,EAAE2E,YAAY;MACpBG,IAAI,EAAEF;IACR,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASG,uBAAuBA,CAAC5E,UAAU,EAAE;EAClD,OAAO;IACLA,UAAU,EAAEA,UAAU;IACtB6E,IAAI,EAAE5G,mBAAmB,CAAC,CAAC;IAC3B6G,KAAK,EAAE,SAAAA,CAAUzG,WAAW,EAAE;MAC5B,IAAI,CAAC+B,aAAa,CAAC/B,WAAW,CAAC,EAAE;QAC/B;MACF;MACA,IAAIsC,IAAI,GAAGtC,WAAW,CAACuC,OAAO,CAAC,CAAC;MAChC,IAAIJ,SAAS,GAAGnC,WAAW,CAACoC,gBAAgB;MAC5C,IAAI1B,QAAQ,GAAGyB,SAAS,CAACE,WAAW,CAAC,CAAC;MACtC,IAAIqE,SAAS,GAAGvE,SAAS,CAACwE,YAAY,CAACjG,QAAQ,CAAC;MAChD,IAAIkG,WAAW,GAAGtE,IAAI,CAACI,iBAAiB,CAACJ,IAAI,CAACK,YAAY,CAAC+D,SAAS,CAACrG,GAAG,CAAC,CAAC;MAC1E,IAAIwG,UAAU,GAAGvE,IAAI,CAACI,iBAAiB,CAACJ,IAAI,CAACK,YAAY,CAACjC,QAAQ,CAACL,GAAG,CAAC,CAAC;MACxE,IAAIyG,cAAc,GAAG9G,WAAW,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC;MAC5D,IAAI8G,QAAQ,GAAGzE,IAAI,CAACK,YAAY,CAAC+D,SAAS,CAACrG,GAAG,CAAC;MAC/C,IAAI2G,cAAc,GAAG1E,IAAI,CAAC2E,kBAAkB,CAAC,sBAAsB,CAAC;MACpE,IAAIC,OAAO,GAAGvH,kBAAkB,CAAC2C,IAAI,EAAEyE,QAAQ,CAAC,IAAI,CAAC,CAACzE,IAAI,CAAC2E,kBAAkB,CAAC,iBAAiB,CAAC;MAChG,IAAIE,YAAY,GAAGT,SAAS,CAACU,YAAY,CAAC,CAAC;MAC3C,IAAIC,cAAc,GAAGC,iBAAiB,CAAC5G,QAAQ,EAAEgG,SAAS,CAAC;MAC3D,IAAIa,OAAO,GAAGhD,aAAa,CAACvE,WAAW,CAAC;MACxC,IAAIwH,YAAY,GAAGxH,WAAW,CAACC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC;MACvD,IAAIwH,aAAa,GAAGT,cAAc,IAAI1E,IAAI,CAACI,iBAAiB,CAACsE,cAAc,CAAC;MAC5E;MACA,IAAIZ,WAAW,GAAG9D,IAAI,CAACoF,SAAS,CAAC,MAAM,CAAC;MACxC,IAAIvB,YAAY,GAAG7D,IAAI,CAACoF,SAAS,CAAC,QAAQ,CAAC;MAC3C,OAAO;QACLC,QAAQ,EAAE,SAAAA,CAAUlH,MAAM,EAAE6B,IAAI,EAAE;UAChC,IAAItB,KAAK,GAAGP,MAAM,CAACO,KAAK;UACxB,IAAI4G,WAAW,GAAGL,OAAO,IAAI1H,kBAAkB,CAACmB,KAAK,GAAG,CAAC,CAAC;UAC1D,IAAI6G,qBAAqB,GAAGN,OAAO,IAAIT,cAAc,IAAIjH,kBAAkB,CAACmB,KAAK,GAAG,CAAC,CAAC;UACtF,IAAI8G,gBAAgB,GAAGP,OAAO,IAAI1H,kBAAkB,CAACmB,KAAK,CAAC;UAC3D,IAAI+G,WAAW,GAAG5F,SAAS,CAAC6F,MAAM,CAACC,OAAO,CAAC,CAAC;UAC5C,IAAIC,MAAM,GAAGf,YAAY,GAAGY,WAAW,CAACtG,KAAK,GAAGsG,WAAW,CAACI,MAAM;UAClE,IAAIC,SAAS;UACb,IAAIxF,KAAK,GAAGN,IAAI,CAACO,QAAQ,CAAC,CAAC;UAC3B,IAAIwF,SAAS,GAAG,CAAC;UACjB,OAAO,CAACD,SAAS,GAAG3H,MAAM,CAAC6H,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;YAC1C,IAAIvF,KAAK,GAAGH,KAAK,CAAC3C,GAAG,CAACiH,OAAO,GAAGO,aAAa,GAAGb,WAAW,EAAEwB,SAAS,CAAC;YACvE,IAAIG,SAAS,GAAG3F,KAAK,CAAC3C,GAAG,CAAC4G,UAAU,EAAEuB,SAAS,CAAC;YAChD,IAAII,SAAS,GAAGnB,cAAc;YAC9B,IAAIoB,eAAe,GAAG,KAAK,CAAC;YAC5B;YACA;YACA,IAAIvB,OAAO,EAAE;cACXuB,eAAe,GAAG,CAAC1F,KAAK,GAAGH,KAAK,CAAC3C,GAAG,CAAC2G,WAAW,EAAEwB,SAAS,CAAC;YAC9D;YACA,IAAIM,CAAC,GAAG,KAAK,CAAC;YACd,IAAIC,CAAC,GAAG,KAAK,CAAC;YACd,IAAIlH,KAAK,GAAG,KAAK,CAAC;YAClB,IAAI0G,MAAM,GAAG,KAAK,CAAC;YACnB,IAAIhB,YAAY,EAAE;cAChB,IAAIyB,KAAK,GAAGzG,SAAS,CAAC0G,WAAW,CAAC,CAAC9F,KAAK,EAAEwF,SAAS,CAAC,CAAC;cACrD,IAAIrB,OAAO,EAAE;gBACX,IAAI4B,UAAU,GAAG3G,SAAS,CAAC0G,WAAW,CAAC,CAACJ,eAAe,EAAEF,SAAS,CAAC,CAAC;gBACpEC,SAAS,GAAGM,UAAU,CAAC,CAAC,CAAC;cAC3B;cACAJ,CAAC,GAAGF,SAAS;cACbG,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,GAAGzC,YAAY;cAC3B1E,KAAK,GAAGmH,KAAK,CAAC,CAAC,CAAC,GAAGJ,SAAS;cAC5BL,MAAM,GAAG/B,WAAW;cACpB,IAAI1C,IAAI,CAACO,GAAG,CAACxC,KAAK,CAAC,GAAG+F,YAAY,EAAE;gBAClC/F,KAAK,GAAG,CAACA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI+F,YAAY;cAC7C;YACF,CAAC,MAAM;cACL,IAAIoB,KAAK,GAAGzG,SAAS,CAAC0G,WAAW,CAAC,CAACN,SAAS,EAAExF,KAAK,CAAC,CAAC;cACrD,IAAImE,OAAO,EAAE;gBACX,IAAI4B,UAAU,GAAG3G,SAAS,CAAC0G,WAAW,CAAC,CAACN,SAAS,EAAEE,eAAe,CAAC,CAAC;gBACpED,SAAS,GAAGM,UAAU,CAAC,CAAC,CAAC;cAC3B;cACAJ,CAAC,GAAGE,KAAK,CAAC,CAAC,CAAC,GAAGzC,YAAY;cAC3BwC,CAAC,GAAGH,SAAS;cACb/G,KAAK,GAAG2E,WAAW;cACnB+B,MAAM,GAAGS,KAAK,CAAC,CAAC,CAAC,GAAGJ,SAAS;cAC7B,IAAI9E,IAAI,CAACO,GAAG,CAACkE,MAAM,CAAC,GAAGX,YAAY,EAAE;gBACnC;gBACAW,MAAM,GAAG,CAACA,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIX,YAAY;cAChD;YACF;YACA,IAAI,CAACD,OAAO,EAAE;cACZjF,IAAI,CAACyG,aAAa,CAACX,SAAS,EAAE;gBAC5BM,CAAC,EAAEA,CAAC;gBACJC,CAAC,EAAEA,CAAC;gBACJlH,KAAK,EAAEA,KAAK;gBACZ0G,MAAM,EAAEA;cACV,CAAC,CAAC;YACJ,CAAC,MAAM;cACLP,WAAW,CAACS,SAAS,CAAC,GAAGK,CAAC;cAC1Bd,WAAW,CAACS,SAAS,GAAG,CAAC,CAAC,GAAGM,CAAC;cAC9Bf,WAAW,CAACS,SAAS,GAAG,CAAC,CAAC,GAAGlB,YAAY,GAAG1F,KAAK,GAAG0G,MAAM;cAC1D,IAAIN,qBAAqB,EAAE;gBACzBA,qBAAqB,CAACQ,SAAS,CAAC,GAAGlB,YAAY,GAAGY,WAAW,CAACW,CAAC,GAAGA,CAAC;gBACnEb,qBAAqB,CAACQ,SAAS,GAAG,CAAC,CAAC,GAAGlB,YAAY,GAAGwB,CAAC,GAAGZ,WAAW,CAACY,CAAC;gBACvEd,qBAAqB,CAACQ,SAAS,GAAG,CAAC,CAAC,GAAGH,MAAM;cAC/C;cACAJ,gBAAgB,CAACM,SAAS,CAAC,GAAGA,SAAS;YACzC;YACAC,SAAS,IAAI,CAAC;UAChB;UACA,IAAId,OAAO,EAAE;YACXjF,IAAI,CAAC+D,SAAS,CAAC;cACbuB,WAAW,EAAEA,WAAW;cACxBE,gBAAgB,EAAEA,gBAAgB;cAClCD,qBAAqB,EAAEA,qBAAqB;cAC5CmB,mBAAmB,EAAE7B;YACvB,CAAC,CAAC;UACJ;QACF;MACF,CAAC;IACH;EACF,CAAC;AACH;AACA,SAASpF,aAAaA,CAAC/B,WAAW,EAAE;EAClC,OAAOA,WAAW,CAACoC,gBAAgB,IAAIpC,WAAW,CAACoC,gBAAgB,CAACxB,IAAI,KAAK,aAAa;AAC5F;AACA,SAAS2D,aAAaA,CAACvE,WAAW,EAAE;EAClC,OAAOA,WAAW,CAACiJ,eAAe,IAAIjJ,WAAW,CAACiJ,eAAe,CAACC,KAAK;AACzE;AACA;AACA,SAAS5B,iBAAiBA,CAAC5G,QAAQ,EAAEgG,SAAS,EAAE;EAC9C,IAAIyC,UAAU,GAAGzC,SAAS,CAAC0C,KAAK,CAACnJ,GAAG,CAAC,YAAY,CAAC;EAClD,IAAI,CAACkJ,UAAU,EAAE;IACfA,UAAU,GAAG,CAAC;EAChB;EACA,OAAOzC,SAAS,CAAC2C,aAAa,CAAC3C,SAAS,CAAC4C,WAAW,CAAC5C,SAAS,CAAC9F,IAAI,KAAK,KAAK,GAAGuI,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC,GAAGA,UAAU,CAAC,CAAC;AAChI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}