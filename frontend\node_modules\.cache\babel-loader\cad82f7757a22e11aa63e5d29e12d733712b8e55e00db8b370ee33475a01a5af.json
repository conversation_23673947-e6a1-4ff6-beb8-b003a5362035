{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global Float32Array */\n// TODO Batch by color\nimport * as graphic from '../../util/graphic.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { getECData } from '../../util/innerStore.js';\nvar BOOST_SIZE_THRESHOLD = 4;\nvar LargeSymbolPathShape = /** @class */function () {\n  function LargeSymbolPathShape() {}\n  return LargeSymbolPathShape;\n}();\nvar LargeSymbolPath = /** @class */function (_super) {\n  __extends(LargeSymbolPath, _super);\n  function LargeSymbolPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this._off = 0;\n    _this.hoverDataIdx = -1;\n    return _this;\n  }\n  LargeSymbolPath.prototype.getDefaultShape = function () {\n    return new LargeSymbolPathShape();\n  };\n  LargeSymbolPath.prototype.reset = function () {\n    this.notClear = false;\n    this._off = 0;\n  };\n  LargeSymbolPath.prototype.buildPath = function (path, shape) {\n    var points = shape.points;\n    var size = shape.size;\n    var symbolProxy = this.symbolProxy;\n    var symbolProxyShape = symbolProxy.shape;\n    var ctx = path.getContext ? path.getContext() : path;\n    var canBoost = ctx && size[0] < BOOST_SIZE_THRESHOLD;\n    var softClipShape = this.softClipShape;\n    var i;\n    // Do draw in afterBrush.\n    if (canBoost) {\n      this._ctx = ctx;\n      return;\n    }\n    this._ctx = null;\n    for (i = this._off; i < points.length;) {\n      var x = points[i++];\n      var y = points[i++];\n      if (isNaN(x) || isNaN(y)) {\n        continue;\n      }\n      if (softClipShape && !softClipShape.contain(x, y)) {\n        continue;\n      }\n      symbolProxyShape.x = x - size[0] / 2;\n      symbolProxyShape.y = y - size[1] / 2;\n      symbolProxyShape.width = size[0];\n      symbolProxyShape.height = size[1];\n      symbolProxy.buildPath(path, symbolProxyShape, true);\n    }\n    if (this.incremental) {\n      this._off = i;\n      this.notClear = true;\n    }\n  };\n  LargeSymbolPath.prototype.afterBrush = function () {\n    var shape = this.shape;\n    var points = shape.points;\n    var size = shape.size;\n    var ctx = this._ctx;\n    var softClipShape = this.softClipShape;\n    var i;\n    if (!ctx) {\n      return;\n    }\n    // PENDING If style or other canvas status changed?\n    for (i = this._off; i < points.length;) {\n      var x = points[i++];\n      var y = points[i++];\n      if (isNaN(x) || isNaN(y)) {\n        continue;\n      }\n      if (softClipShape && !softClipShape.contain(x, y)) {\n        continue;\n      }\n      // fillRect is faster than building a rect path and draw.\n      // And it support light globalCompositeOperation.\n      ctx.fillRect(x - size[0] / 2, y - size[1] / 2, size[0], size[1]);\n    }\n    if (this.incremental) {\n      this._off = i;\n      this.notClear = true;\n    }\n  };\n  LargeSymbolPath.prototype.findDataIndex = function (x, y) {\n    // TODO ???\n    // Consider transform\n    var shape = this.shape;\n    var points = shape.points;\n    var size = shape.size;\n    var w = Math.max(size[0], 4);\n    var h = Math.max(size[1], 4);\n    // Not consider transform\n    // Treat each element as a rect\n    // top down traverse\n    for (var idx = points.length / 2 - 1; idx >= 0; idx--) {\n      var i = idx * 2;\n      var x0 = points[i] - w / 2;\n      var y0 = points[i + 1] - h / 2;\n      if (x >= x0 && y >= y0 && x <= x0 + w && y <= y0 + h) {\n        return idx;\n      }\n    }\n    return -1;\n  };\n  LargeSymbolPath.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    x = localPos[0];\n    y = localPos[1];\n    if (rect.contain(x, y)) {\n      // Cache found data index.\n      var dataIdx = this.hoverDataIdx = this.findDataIndex(x, y);\n      return dataIdx >= 0;\n    }\n    this.hoverDataIdx = -1;\n    return false;\n  };\n  LargeSymbolPath.prototype.getBoundingRect = function () {\n    // Ignore stroke for large symbol draw.\n    var rect = this._rect;\n    if (!rect) {\n      var shape = this.shape;\n      var points = shape.points;\n      var size = shape.size;\n      var w = size[0];\n      var h = size[1];\n      var minX = Infinity;\n      var minY = Infinity;\n      var maxX = -Infinity;\n      var maxY = -Infinity;\n      for (var i = 0; i < points.length;) {\n        var x = points[i++];\n        var y = points[i++];\n        minX = Math.min(x, minX);\n        maxX = Math.max(x, maxX);\n        minY = Math.min(y, minY);\n        maxY = Math.max(y, maxY);\n      }\n      rect = this._rect = new graphic.BoundingRect(minX - w / 2, minY - h / 2, maxX - minX + w, maxY - minY + h);\n    }\n    return rect;\n  };\n  return LargeSymbolPath;\n}(graphic.Path);\nvar LargeSymbolDraw = /** @class */function () {\n  function LargeSymbolDraw() {\n    this.group = new graphic.Group();\n  }\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  LargeSymbolDraw.prototype.updateData = function (data, opt) {\n    this._clear();\n    var symbolEl = this._create();\n    symbolEl.setShape({\n      points: data.getLayout('points')\n    });\n    this._setCommon(symbolEl, data, opt);\n  };\n  LargeSymbolDraw.prototype.updateLayout = function (data) {\n    var points = data.getLayout('points');\n    this.group.eachChild(function (child) {\n      if (child.startIndex != null) {\n        var len = (child.endIndex - child.startIndex) * 2;\n        var byteOffset = child.startIndex * 4 * 2;\n        points = new Float32Array(points.buffer, byteOffset, len);\n      }\n      child.setShape('points', points);\n      // Reset draw cursor.\n      child.reset();\n    });\n  };\n  LargeSymbolDraw.prototype.incrementalPrepareUpdate = function (data) {\n    this._clear();\n  };\n  LargeSymbolDraw.prototype.incrementalUpdate = function (taskParams, data, opt) {\n    var lastAdded = this._newAdded[0];\n    var points = data.getLayout('points');\n    var oldPoints = lastAdded && lastAdded.shape.points;\n    // Merging the exists. Each element has 1e4 points.\n    // Consider the performance balance between too much elements and too much points in one shape(may affect hover optimization)\n    if (oldPoints && oldPoints.length < 2e4) {\n      var oldLen = oldPoints.length;\n      var newPoints = new Float32Array(oldLen + points.length);\n      // Concat two array\n      newPoints.set(oldPoints);\n      newPoints.set(points, oldLen);\n      // Update endIndex\n      lastAdded.endIndex = taskParams.end;\n      lastAdded.setShape({\n        points: newPoints\n      });\n    } else {\n      // Clear\n      this._newAdded = [];\n      var symbolEl = this._create();\n      symbolEl.startIndex = taskParams.start;\n      symbolEl.endIndex = taskParams.end;\n      symbolEl.incremental = true;\n      symbolEl.setShape({\n        points: points\n      });\n      this._setCommon(symbolEl, data, opt);\n    }\n  };\n  LargeSymbolDraw.prototype.eachRendered = function (cb) {\n    this._newAdded[0] && cb(this._newAdded[0]);\n  };\n  LargeSymbolDraw.prototype._create = function () {\n    var symbolEl = new LargeSymbolPath({\n      cursor: 'default'\n    });\n    symbolEl.ignoreCoarsePointer = true;\n    this.group.add(symbolEl);\n    this._newAdded.push(symbolEl);\n    return symbolEl;\n  };\n  LargeSymbolDraw.prototype._setCommon = function (symbolEl, data, opt) {\n    var hostModel = data.hostModel;\n    opt = opt || {};\n    var size = data.getVisual('symbolSize');\n    symbolEl.setShape('size', size instanceof Array ? size : [size, size]);\n    symbolEl.softClipShape = opt.clipShape || null;\n    // Create symbolProxy to build path for each data\n    symbolEl.symbolProxy = createSymbol(data.getVisual('symbol'), 0, 0, 0, 0);\n    // Use symbolProxy setColor method\n    symbolEl.setColor = symbolEl.symbolProxy.setColor;\n    var extrudeShadow = symbolEl.shape.size[0] < BOOST_SIZE_THRESHOLD;\n    symbolEl.useStyle(\n    // Draw shadow when doing fillRect is extremely slow.\n    hostModel.getModel('itemStyle').getItemStyle(extrudeShadow ? ['color', 'shadowBlur', 'shadowColor'] : ['color']));\n    var globalStyle = data.getVisual('style');\n    var visualColor = globalStyle && globalStyle.fill;\n    if (visualColor) {\n      symbolEl.setColor(visualColor);\n    }\n    var ecData = getECData(symbolEl);\n    // Enable tooltip\n    // PENDING May have performance issue when path is extremely large\n    ecData.seriesIndex = hostModel.seriesIndex;\n    symbolEl.on('mousemove', function (e) {\n      ecData.dataIndex = null;\n      var dataIndex = symbolEl.hoverDataIdx;\n      if (dataIndex >= 0) {\n        // Provide dataIndex for tooltip\n        ecData.dataIndex = dataIndex + (symbolEl.startIndex || 0);\n      }\n    });\n  };\n  LargeSymbolDraw.prototype.remove = function () {\n    this._clear();\n  };\n  LargeSymbolDraw.prototype._clear = function () {\n    this._newAdded = [];\n    this.group.removeAll();\n  };\n  return LargeSymbolDraw;\n}();\nexport default LargeSymbolDraw;", "map": {"version": 3, "names": ["__extends", "graphic", "createSymbol", "getECData", "BOOST_SIZE_THRESHOLD", "LargeSymbolPathShape", "LargeSymbolPath", "_super", "opts", "_this", "call", "_off", "hoverDataIdx", "prototype", "getDefaultShape", "reset", "notClear", "buildPath", "path", "shape", "points", "size", "symbolProxy", "symbolProxyShape", "ctx", "getContext", "canBoost", "softClipShape", "i", "_ctx", "length", "x", "y", "isNaN", "contain", "width", "height", "incremental", "afterBrush", "fillRect", "findDataIndex", "w", "Math", "max", "h", "idx", "x0", "y0", "localPos", "transformCoordToLocal", "rect", "getBoundingRect", "dataIdx", "_rect", "minX", "Infinity", "minY", "maxX", "maxY", "min", "BoundingRect", "Path", "LargeSymbolDraw", "group", "Group", "updateData", "data", "opt", "_clear", "symbolEl", "_create", "setShape", "getLayout", "_setCommon", "updateLayout", "<PERSON><PERSON><PERSON><PERSON>", "child", "startIndex", "len", "endIndex", "byteOffset", "Float32Array", "buffer", "incrementalPrepareUpdate", "incrementalUpdate", "taskParams", "lastAdded", "_newAdded", "oldPoints", "old<PERSON>en", "newPoints", "set", "end", "start", "eachRendered", "cb", "cursor", "ignoreCoarsePointer", "add", "push", "hostModel", "getVisual", "Array", "clipShape", "setColor", "extrude<PERSON>hadow", "useStyle", "getModel", "getItemStyle", "globalStyle", "visualColor", "fill", "ecData", "seriesIndex", "on", "e", "dataIndex", "remove", "removeAll"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/chart/helper/LargeSymbolDraw.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global Float32Array */\n// TODO Batch by color\nimport * as graphic from '../../util/graphic.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { getECData } from '../../util/innerStore.js';\nvar BOOST_SIZE_THRESHOLD = 4;\nvar LargeSymbolPathShape = /** @class */function () {\n  function LargeSymbolPathShape() {}\n  return LargeSymbolPathShape;\n}();\nvar LargeSymbolPath = /** @class */function (_super) {\n  __extends(LargeSymbolPath, _super);\n  function LargeSymbolPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this._off = 0;\n    _this.hoverDataIdx = -1;\n    return _this;\n  }\n  LargeSymbolPath.prototype.getDefaultShape = function () {\n    return new LargeSymbolPathShape();\n  };\n  LargeSymbolPath.prototype.reset = function () {\n    this.notClear = false;\n    this._off = 0;\n  };\n  LargeSymbolPath.prototype.buildPath = function (path, shape) {\n    var points = shape.points;\n    var size = shape.size;\n    var symbolProxy = this.symbolProxy;\n    var symbolProxyShape = symbolProxy.shape;\n    var ctx = path.getContext ? path.getContext() : path;\n    var canBoost = ctx && size[0] < BOOST_SIZE_THRESHOLD;\n    var softClipShape = this.softClipShape;\n    var i;\n    // Do draw in afterBrush.\n    if (canBoost) {\n      this._ctx = ctx;\n      return;\n    }\n    this._ctx = null;\n    for (i = this._off; i < points.length;) {\n      var x = points[i++];\n      var y = points[i++];\n      if (isNaN(x) || isNaN(y)) {\n        continue;\n      }\n      if (softClipShape && !softClipShape.contain(x, y)) {\n        continue;\n      }\n      symbolProxyShape.x = x - size[0] / 2;\n      symbolProxyShape.y = y - size[1] / 2;\n      symbolProxyShape.width = size[0];\n      symbolProxyShape.height = size[1];\n      symbolProxy.buildPath(path, symbolProxyShape, true);\n    }\n    if (this.incremental) {\n      this._off = i;\n      this.notClear = true;\n    }\n  };\n  LargeSymbolPath.prototype.afterBrush = function () {\n    var shape = this.shape;\n    var points = shape.points;\n    var size = shape.size;\n    var ctx = this._ctx;\n    var softClipShape = this.softClipShape;\n    var i;\n    if (!ctx) {\n      return;\n    }\n    // PENDING If style or other canvas status changed?\n    for (i = this._off; i < points.length;) {\n      var x = points[i++];\n      var y = points[i++];\n      if (isNaN(x) || isNaN(y)) {\n        continue;\n      }\n      if (softClipShape && !softClipShape.contain(x, y)) {\n        continue;\n      }\n      // fillRect is faster than building a rect path and draw.\n      // And it support light globalCompositeOperation.\n      ctx.fillRect(x - size[0] / 2, y - size[1] / 2, size[0], size[1]);\n    }\n    if (this.incremental) {\n      this._off = i;\n      this.notClear = true;\n    }\n  };\n  LargeSymbolPath.prototype.findDataIndex = function (x, y) {\n    // TODO ???\n    // Consider transform\n    var shape = this.shape;\n    var points = shape.points;\n    var size = shape.size;\n    var w = Math.max(size[0], 4);\n    var h = Math.max(size[1], 4);\n    // Not consider transform\n    // Treat each element as a rect\n    // top down traverse\n    for (var idx = points.length / 2 - 1; idx >= 0; idx--) {\n      var i = idx * 2;\n      var x0 = points[i] - w / 2;\n      var y0 = points[i + 1] - h / 2;\n      if (x >= x0 && y >= y0 && x <= x0 + w && y <= y0 + h) {\n        return idx;\n      }\n    }\n    return -1;\n  };\n  LargeSymbolPath.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    x = localPos[0];\n    y = localPos[1];\n    if (rect.contain(x, y)) {\n      // Cache found data index.\n      var dataIdx = this.hoverDataIdx = this.findDataIndex(x, y);\n      return dataIdx >= 0;\n    }\n    this.hoverDataIdx = -1;\n    return false;\n  };\n  LargeSymbolPath.prototype.getBoundingRect = function () {\n    // Ignore stroke for large symbol draw.\n    var rect = this._rect;\n    if (!rect) {\n      var shape = this.shape;\n      var points = shape.points;\n      var size = shape.size;\n      var w = size[0];\n      var h = size[1];\n      var minX = Infinity;\n      var minY = Infinity;\n      var maxX = -Infinity;\n      var maxY = -Infinity;\n      for (var i = 0; i < points.length;) {\n        var x = points[i++];\n        var y = points[i++];\n        minX = Math.min(x, minX);\n        maxX = Math.max(x, maxX);\n        minY = Math.min(y, minY);\n        maxY = Math.max(y, maxY);\n      }\n      rect = this._rect = new graphic.BoundingRect(minX - w / 2, minY - h / 2, maxX - minX + w, maxY - minY + h);\n    }\n    return rect;\n  };\n  return LargeSymbolPath;\n}(graphic.Path);\nvar LargeSymbolDraw = /** @class */function () {\n  function LargeSymbolDraw() {\n    this.group = new graphic.Group();\n  }\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  LargeSymbolDraw.prototype.updateData = function (data, opt) {\n    this._clear();\n    var symbolEl = this._create();\n    symbolEl.setShape({\n      points: data.getLayout('points')\n    });\n    this._setCommon(symbolEl, data, opt);\n  };\n  LargeSymbolDraw.prototype.updateLayout = function (data) {\n    var points = data.getLayout('points');\n    this.group.eachChild(function (child) {\n      if (child.startIndex != null) {\n        var len = (child.endIndex - child.startIndex) * 2;\n        var byteOffset = child.startIndex * 4 * 2;\n        points = new Float32Array(points.buffer, byteOffset, len);\n      }\n      child.setShape('points', points);\n      // Reset draw cursor.\n      child.reset();\n    });\n  };\n  LargeSymbolDraw.prototype.incrementalPrepareUpdate = function (data) {\n    this._clear();\n  };\n  LargeSymbolDraw.prototype.incrementalUpdate = function (taskParams, data, opt) {\n    var lastAdded = this._newAdded[0];\n    var points = data.getLayout('points');\n    var oldPoints = lastAdded && lastAdded.shape.points;\n    // Merging the exists. Each element has 1e4 points.\n    // Consider the performance balance between too much elements and too much points in one shape(may affect hover optimization)\n    if (oldPoints && oldPoints.length < 2e4) {\n      var oldLen = oldPoints.length;\n      var newPoints = new Float32Array(oldLen + points.length);\n      // Concat two array\n      newPoints.set(oldPoints);\n      newPoints.set(points, oldLen);\n      // Update endIndex\n      lastAdded.endIndex = taskParams.end;\n      lastAdded.setShape({\n        points: newPoints\n      });\n    } else {\n      // Clear\n      this._newAdded = [];\n      var symbolEl = this._create();\n      symbolEl.startIndex = taskParams.start;\n      symbolEl.endIndex = taskParams.end;\n      symbolEl.incremental = true;\n      symbolEl.setShape({\n        points: points\n      });\n      this._setCommon(symbolEl, data, opt);\n    }\n  };\n  LargeSymbolDraw.prototype.eachRendered = function (cb) {\n    this._newAdded[0] && cb(this._newAdded[0]);\n  };\n  LargeSymbolDraw.prototype._create = function () {\n    var symbolEl = new LargeSymbolPath({\n      cursor: 'default'\n    });\n    symbolEl.ignoreCoarsePointer = true;\n    this.group.add(symbolEl);\n    this._newAdded.push(symbolEl);\n    return symbolEl;\n  };\n  LargeSymbolDraw.prototype._setCommon = function (symbolEl, data, opt) {\n    var hostModel = data.hostModel;\n    opt = opt || {};\n    var size = data.getVisual('symbolSize');\n    symbolEl.setShape('size', size instanceof Array ? size : [size, size]);\n    symbolEl.softClipShape = opt.clipShape || null;\n    // Create symbolProxy to build path for each data\n    symbolEl.symbolProxy = createSymbol(data.getVisual('symbol'), 0, 0, 0, 0);\n    // Use symbolProxy setColor method\n    symbolEl.setColor = symbolEl.symbolProxy.setColor;\n    var extrudeShadow = symbolEl.shape.size[0] < BOOST_SIZE_THRESHOLD;\n    symbolEl.useStyle(\n    // Draw shadow when doing fillRect is extremely slow.\n    hostModel.getModel('itemStyle').getItemStyle(extrudeShadow ? ['color', 'shadowBlur', 'shadowColor'] : ['color']));\n    var globalStyle = data.getVisual('style');\n    var visualColor = globalStyle && globalStyle.fill;\n    if (visualColor) {\n      symbolEl.setColor(visualColor);\n    }\n    var ecData = getECData(symbolEl);\n    // Enable tooltip\n    // PENDING May have performance issue when path is extremely large\n    ecData.seriesIndex = hostModel.seriesIndex;\n    symbolEl.on('mousemove', function (e) {\n      ecData.dataIndex = null;\n      var dataIndex = symbolEl.hoverDataIdx;\n      if (dataIndex >= 0) {\n        // Provide dataIndex for tooltip\n        ecData.dataIndex = dataIndex + (symbolEl.startIndex || 0);\n      }\n    });\n  };\n  LargeSymbolDraw.prototype.remove = function () {\n    this._clear();\n  };\n  LargeSymbolDraw.prototype._clear = function () {\n    this._newAdded = [];\n    this.group.removeAll();\n  };\n  return LargeSymbolDraw;\n}();\nexport default LargeSymbolDraw;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,IAAIC,oBAAoB,GAAG,CAAC;AAC5B,IAAIC,oBAAoB,GAAG,aAAa,YAAY;EAClD,SAASA,oBAAoBA,CAAA,EAAG,CAAC;EACjC,OAAOA,oBAAoB;AAC7B,CAAC,CAAC,CAAC;AACH,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDP,SAAS,CAACM,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAACE,IAAI,EAAE;IAC7B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,IAAI,CAAC,IAAI,IAAI;IAC3CC,KAAK,CAACE,IAAI,GAAG,CAAC;IACdF,KAAK,CAACG,YAAY,GAAG,CAAC,CAAC;IACvB,OAAOH,KAAK;EACd;EACAH,eAAe,CAACO,SAAS,CAACC,eAAe,GAAG,YAAY;IACtD,OAAO,IAAIT,oBAAoB,CAAC,CAAC;EACnC,CAAC;EACDC,eAAe,CAACO,SAAS,CAACE,KAAK,GAAG,YAAY;IAC5C,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACL,IAAI,GAAG,CAAC;EACf,CAAC;EACDL,eAAe,CAACO,SAAS,CAACI,SAAS,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC3D,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIC,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,IAAIC,gBAAgB,GAAGD,WAAW,CAACH,KAAK;IACxC,IAAIK,GAAG,GAAGN,IAAI,CAACO,UAAU,GAAGP,IAAI,CAACO,UAAU,CAAC,CAAC,GAAGP,IAAI;IACpD,IAAIQ,QAAQ,GAAGF,GAAG,IAAIH,IAAI,CAAC,CAAC,CAAC,GAAGjB,oBAAoB;IACpD,IAAIuB,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIC,CAAC;IACL;IACA,IAAIF,QAAQ,EAAE;MACZ,IAAI,CAACG,IAAI,GAAGL,GAAG;MACf;IACF;IACA,IAAI,CAACK,IAAI,GAAG,IAAI;IAChB,KAAKD,CAAC,GAAG,IAAI,CAACjB,IAAI,EAAEiB,CAAC,GAAGR,MAAM,CAACU,MAAM,GAAG;MACtC,IAAIC,CAAC,GAAGX,MAAM,CAACQ,CAAC,EAAE,CAAC;MACnB,IAAII,CAAC,GAAGZ,MAAM,CAACQ,CAAC,EAAE,CAAC;MACnB,IAAIK,KAAK,CAACF,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,CAAC,EAAE;QACxB;MACF;MACA,IAAIL,aAAa,IAAI,CAACA,aAAa,CAACO,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC,EAAE;QACjD;MACF;MACAT,gBAAgB,CAACQ,CAAC,GAAGA,CAAC,GAAGV,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MACpCE,gBAAgB,CAACS,CAAC,GAAGA,CAAC,GAAGX,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MACpCE,gBAAgB,CAACY,KAAK,GAAGd,IAAI,CAAC,CAAC,CAAC;MAChCE,gBAAgB,CAACa,MAAM,GAAGf,IAAI,CAAC,CAAC,CAAC;MACjCC,WAAW,CAACL,SAAS,CAACC,IAAI,EAAEK,gBAAgB,EAAE,IAAI,CAAC;IACrD;IACA,IAAI,IAAI,CAACc,WAAW,EAAE;MACpB,IAAI,CAAC1B,IAAI,GAAGiB,CAAC;MACb,IAAI,CAACZ,QAAQ,GAAG,IAAI;IACtB;EACF,CAAC;EACDV,eAAe,CAACO,SAAS,CAACyB,UAAU,GAAG,YAAY;IACjD,IAAInB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIG,GAAG,GAAG,IAAI,CAACK,IAAI;IACnB,IAAIF,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIC,CAAC;IACL,IAAI,CAACJ,GAAG,EAAE;MACR;IACF;IACA;IACA,KAAKI,CAAC,GAAG,IAAI,CAACjB,IAAI,EAAEiB,CAAC,GAAGR,MAAM,CAACU,MAAM,GAAG;MACtC,IAAIC,CAAC,GAAGX,MAAM,CAACQ,CAAC,EAAE,CAAC;MACnB,IAAII,CAAC,GAAGZ,MAAM,CAACQ,CAAC,EAAE,CAAC;MACnB,IAAIK,KAAK,CAACF,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,CAAC,EAAE;QACxB;MACF;MACA,IAAIL,aAAa,IAAI,CAACA,aAAa,CAACO,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC,EAAE;QACjD;MACF;MACA;MACA;MACAR,GAAG,CAACe,QAAQ,CAACR,CAAC,GAAGV,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEW,CAAC,GAAGX,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IAClE;IACA,IAAI,IAAI,CAACgB,WAAW,EAAE;MACpB,IAAI,CAAC1B,IAAI,GAAGiB,CAAC;MACb,IAAI,CAACZ,QAAQ,GAAG,IAAI;IACtB;EACF,CAAC;EACDV,eAAe,CAACO,SAAS,CAAC2B,aAAa,GAAG,UAAUT,CAAC,EAAEC,CAAC,EAAE;IACxD;IACA;IACA,IAAIb,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIoB,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACtB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B,IAAIuB,CAAC,GAAGF,IAAI,CAACC,GAAG,CAACtB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B;IACA;IACA;IACA,KAAK,IAAIwB,GAAG,GAAGzB,MAAM,CAACU,MAAM,GAAG,CAAC,GAAG,CAAC,EAAEe,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;MACrD,IAAIjB,CAAC,GAAGiB,GAAG,GAAG,CAAC;MACf,IAAIC,EAAE,GAAG1B,MAAM,CAACQ,CAAC,CAAC,GAAGa,CAAC,GAAG,CAAC;MAC1B,IAAIM,EAAE,GAAG3B,MAAM,CAACQ,CAAC,GAAG,CAAC,CAAC,GAAGgB,CAAC,GAAG,CAAC;MAC9B,IAAIb,CAAC,IAAIe,EAAE,IAAId,CAAC,IAAIe,EAAE,IAAIhB,CAAC,IAAIe,EAAE,GAAGL,CAAC,IAAIT,CAAC,IAAIe,EAAE,GAAGH,CAAC,EAAE;QACpD,OAAOC,GAAG;MACZ;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACDvC,eAAe,CAACO,SAAS,CAACqB,OAAO,GAAG,UAAUH,CAAC,EAAEC,CAAC,EAAE;IAClD,IAAIgB,QAAQ,GAAG,IAAI,CAACC,qBAAqB,CAAClB,CAAC,EAAEC,CAAC,CAAC;IAC/C,IAAIkB,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACjCpB,CAAC,GAAGiB,QAAQ,CAAC,CAAC,CAAC;IACfhB,CAAC,GAAGgB,QAAQ,CAAC,CAAC,CAAC;IACf,IAAIE,IAAI,CAAChB,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC,EAAE;MACtB;MACA,IAAIoB,OAAO,GAAG,IAAI,CAACxC,YAAY,GAAG,IAAI,CAAC4B,aAAa,CAACT,CAAC,EAAEC,CAAC,CAAC;MAC1D,OAAOoB,OAAO,IAAI,CAAC;IACrB;IACA,IAAI,CAACxC,YAAY,GAAG,CAAC,CAAC;IACtB,OAAO,KAAK;EACd,CAAC;EACDN,eAAe,CAACO,SAAS,CAACsC,eAAe,GAAG,YAAY;IACtD;IACA,IAAID,IAAI,GAAG,IAAI,CAACG,KAAK;IACrB,IAAI,CAACH,IAAI,EAAE;MACT,IAAI/B,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;MACzB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;MACrB,IAAIoB,CAAC,GAAGpB,IAAI,CAAC,CAAC,CAAC;MACf,IAAIuB,CAAC,GAAGvB,IAAI,CAAC,CAAC,CAAC;MACf,IAAIiC,IAAI,GAAGC,QAAQ;MACnB,IAAIC,IAAI,GAAGD,QAAQ;MACnB,IAAIE,IAAI,GAAG,CAACF,QAAQ;MACpB,IAAIG,IAAI,GAAG,CAACH,QAAQ;MACpB,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACU,MAAM,GAAG;QAClC,IAAIC,CAAC,GAAGX,MAAM,CAACQ,CAAC,EAAE,CAAC;QACnB,IAAII,CAAC,GAAGZ,MAAM,CAACQ,CAAC,EAAE,CAAC;QACnB0B,IAAI,GAAGZ,IAAI,CAACiB,GAAG,CAAC5B,CAAC,EAAEuB,IAAI,CAAC;QACxBG,IAAI,GAAGf,IAAI,CAACC,GAAG,CAACZ,CAAC,EAAE0B,IAAI,CAAC;QACxBD,IAAI,GAAGd,IAAI,CAACiB,GAAG,CAAC3B,CAAC,EAAEwB,IAAI,CAAC;QACxBE,IAAI,GAAGhB,IAAI,CAACC,GAAG,CAACX,CAAC,EAAE0B,IAAI,CAAC;MAC1B;MACAR,IAAI,GAAG,IAAI,CAACG,KAAK,GAAG,IAAIpD,OAAO,CAAC2D,YAAY,CAACN,IAAI,GAAGb,CAAC,GAAG,CAAC,EAAEe,IAAI,GAAGZ,CAAC,GAAG,CAAC,EAAEa,IAAI,GAAGH,IAAI,GAAGb,CAAC,EAAEiB,IAAI,GAAGF,IAAI,GAAGZ,CAAC,CAAC;IAC5G;IACA,OAAOM,IAAI;EACb,CAAC;EACD,OAAO5C,eAAe;AACxB,CAAC,CAACL,OAAO,CAAC4D,IAAI,CAAC;AACf,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG;IACzB,IAAI,CAACC,KAAK,GAAG,IAAI9D,OAAO,CAAC+D,KAAK,CAAC,CAAC;EAClC;EACA;AACF;AACA;EACEF,eAAe,CAACjD,SAAS,CAACoD,UAAU,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IAC1D,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAIC,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC7BD,QAAQ,CAACE,QAAQ,CAAC;MAChBnD,MAAM,EAAE8C,IAAI,CAACM,SAAS,CAAC,QAAQ;IACjC,CAAC,CAAC;IACF,IAAI,CAACC,UAAU,CAACJ,QAAQ,EAAEH,IAAI,EAAEC,GAAG,CAAC;EACtC,CAAC;EACDL,eAAe,CAACjD,SAAS,CAAC6D,YAAY,GAAG,UAAUR,IAAI,EAAE;IACvD,IAAI9C,MAAM,GAAG8C,IAAI,CAACM,SAAS,CAAC,QAAQ,CAAC;IACrC,IAAI,CAACT,KAAK,CAACY,SAAS,CAAC,UAAUC,KAAK,EAAE;MACpC,IAAIA,KAAK,CAACC,UAAU,IAAI,IAAI,EAAE;QAC5B,IAAIC,GAAG,GAAG,CAACF,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACC,UAAU,IAAI,CAAC;QACjD,IAAIG,UAAU,GAAGJ,KAAK,CAACC,UAAU,GAAG,CAAC,GAAG,CAAC;QACzCzD,MAAM,GAAG,IAAI6D,YAAY,CAAC7D,MAAM,CAAC8D,MAAM,EAAEF,UAAU,EAAEF,GAAG,CAAC;MAC3D;MACAF,KAAK,CAACL,QAAQ,CAAC,QAAQ,EAAEnD,MAAM,CAAC;MAChC;MACAwD,KAAK,CAAC7D,KAAK,CAAC,CAAC;IACf,CAAC,CAAC;EACJ,CAAC;EACD+C,eAAe,CAACjD,SAAS,CAACsE,wBAAwB,GAAG,UAAUjB,IAAI,EAAE;IACnE,IAAI,CAACE,MAAM,CAAC,CAAC;EACf,CAAC;EACDN,eAAe,CAACjD,SAAS,CAACuE,iBAAiB,GAAG,UAAUC,UAAU,EAAEnB,IAAI,EAAEC,GAAG,EAAE;IAC7E,IAAImB,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IACjC,IAAInE,MAAM,GAAG8C,IAAI,CAACM,SAAS,CAAC,QAAQ,CAAC;IACrC,IAAIgB,SAAS,GAAGF,SAAS,IAAIA,SAAS,CAACnE,KAAK,CAACC,MAAM;IACnD;IACA;IACA,IAAIoE,SAAS,IAAIA,SAAS,CAAC1D,MAAM,GAAG,GAAG,EAAE;MACvC,IAAI2D,MAAM,GAAGD,SAAS,CAAC1D,MAAM;MAC7B,IAAI4D,SAAS,GAAG,IAAIT,YAAY,CAACQ,MAAM,GAAGrE,MAAM,CAACU,MAAM,CAAC;MACxD;MACA4D,SAAS,CAACC,GAAG,CAACH,SAAS,CAAC;MACxBE,SAAS,CAACC,GAAG,CAACvE,MAAM,EAAEqE,MAAM,CAAC;MAC7B;MACAH,SAAS,CAACP,QAAQ,GAAGM,UAAU,CAACO,GAAG;MACnCN,SAAS,CAACf,QAAQ,CAAC;QACjBnD,MAAM,EAAEsE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACH,SAAS,GAAG,EAAE;MACnB,IAAIlB,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC7BD,QAAQ,CAACQ,UAAU,GAAGQ,UAAU,CAACQ,KAAK;MACtCxB,QAAQ,CAACU,QAAQ,GAAGM,UAAU,CAACO,GAAG;MAClCvB,QAAQ,CAAChC,WAAW,GAAG,IAAI;MAC3BgC,QAAQ,CAACE,QAAQ,CAAC;QAChBnD,MAAM,EAAEA;MACV,CAAC,CAAC;MACF,IAAI,CAACqD,UAAU,CAACJ,QAAQ,EAAEH,IAAI,EAAEC,GAAG,CAAC;IACtC;EACF,CAAC;EACDL,eAAe,CAACjD,SAAS,CAACiF,YAAY,GAAG,UAAUC,EAAE,EAAE;IACrD,IAAI,CAACR,SAAS,CAAC,CAAC,CAAC,IAAIQ,EAAE,CAAC,IAAI,CAACR,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC;EACDzB,eAAe,CAACjD,SAAS,CAACyD,OAAO,GAAG,YAAY;IAC9C,IAAID,QAAQ,GAAG,IAAI/D,eAAe,CAAC;MACjC0F,MAAM,EAAE;IACV,CAAC,CAAC;IACF3B,QAAQ,CAAC4B,mBAAmB,GAAG,IAAI;IACnC,IAAI,CAAClC,KAAK,CAACmC,GAAG,CAAC7B,QAAQ,CAAC;IACxB,IAAI,CAACkB,SAAS,CAACY,IAAI,CAAC9B,QAAQ,CAAC;IAC7B,OAAOA,QAAQ;EACjB,CAAC;EACDP,eAAe,CAACjD,SAAS,CAAC4D,UAAU,GAAG,UAAUJ,QAAQ,EAAEH,IAAI,EAAEC,GAAG,EAAE;IACpE,IAAIiC,SAAS,GAAGlC,IAAI,CAACkC,SAAS;IAC9BjC,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,IAAI9C,IAAI,GAAG6C,IAAI,CAACmC,SAAS,CAAC,YAAY,CAAC;IACvChC,QAAQ,CAACE,QAAQ,CAAC,MAAM,EAAElD,IAAI,YAAYiF,KAAK,GAAGjF,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC,CAAC;IACtEgD,QAAQ,CAAC1C,aAAa,GAAGwC,GAAG,CAACoC,SAAS,IAAI,IAAI;IAC9C;IACAlC,QAAQ,CAAC/C,WAAW,GAAGpB,YAAY,CAACgE,IAAI,CAACmC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzE;IACAhC,QAAQ,CAACmC,QAAQ,GAAGnC,QAAQ,CAAC/C,WAAW,CAACkF,QAAQ;IACjD,IAAIC,aAAa,GAAGpC,QAAQ,CAAClD,KAAK,CAACE,IAAI,CAAC,CAAC,CAAC,GAAGjB,oBAAoB;IACjEiE,QAAQ,CAACqC,QAAQ;IACjB;IACAN,SAAS,CAACO,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAACH,aAAa,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IACjH,IAAII,WAAW,GAAG3C,IAAI,CAACmC,SAAS,CAAC,OAAO,CAAC;IACzC,IAAIS,WAAW,GAAGD,WAAW,IAAIA,WAAW,CAACE,IAAI;IACjD,IAAID,WAAW,EAAE;MACfzC,QAAQ,CAACmC,QAAQ,CAACM,WAAW,CAAC;IAChC;IACA,IAAIE,MAAM,GAAG7G,SAAS,CAACkE,QAAQ,CAAC;IAChC;IACA;IACA2C,MAAM,CAACC,WAAW,GAAGb,SAAS,CAACa,WAAW;IAC1C5C,QAAQ,CAAC6C,EAAE,CAAC,WAAW,EAAE,UAAUC,CAAC,EAAE;MACpCH,MAAM,CAACI,SAAS,GAAG,IAAI;MACvB,IAAIA,SAAS,GAAG/C,QAAQ,CAACzD,YAAY;MACrC,IAAIwG,SAAS,IAAI,CAAC,EAAE;QAClB;QACAJ,MAAM,CAACI,SAAS,GAAGA,SAAS,IAAI/C,QAAQ,CAACQ,UAAU,IAAI,CAAC,CAAC;MAC3D;IACF,CAAC,CAAC;EACJ,CAAC;EACDf,eAAe,CAACjD,SAAS,CAACwG,MAAM,GAAG,YAAY;IAC7C,IAAI,CAACjD,MAAM,CAAC,CAAC;EACf,CAAC;EACDN,eAAe,CAACjD,SAAS,CAACuD,MAAM,GAAG,YAAY;IAC7C,IAAI,CAACmB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACxB,KAAK,CAACuD,SAAS,CAAC,CAAC;EACxB,CAAC;EACD,OAAOxD,eAAe;AACxB,CAAC,CAAC,CAAC;AACH,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}