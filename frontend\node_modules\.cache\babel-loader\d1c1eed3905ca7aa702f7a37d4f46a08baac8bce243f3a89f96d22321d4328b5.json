{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM\\\\frontend\\\\src\\\\pages\\\\Order\\\\OrderList.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { Table, Card, Button, Input, Select, Space, Tag, Typography, Row, Col, Statistic, Progress, Tooltip, Badge } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, EyeOutlined, ReloadOutlined, ExportOutlined, ShoppingCartOutlined } from '@ant-design/icons';\nimport { selectFilteredOrders, selectOrderLoading, selectOrderFilters, selectOrderPagination, setSearchKeyword, setFilters, setPagination, resetFilters } from '../../store/slices/orderSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst OrderList = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const orders = useSelector(selectFilteredOrders);\n  const loading = useSelector(selectOrderLoading);\n  const filters = useSelector(selectOrderFilters);\n  const pagination = useSelector(selectOrderPagination);\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'pending': 'orange',\n      'in_progress': 'blue',\n      'completed': 'green',\n      'cancelled': 'red'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      'pending': '待处理',\n      'in_progress': '进行中',\n      'completed': '已完成',\n      'cancelled': '已取消'\n    };\n    return texts[status] || status;\n  };\n  const getPaymentStatusColor = status => {\n    const colors = {\n      'unpaid': 'red',\n      'partial': 'orange',\n      'paid': 'green'\n    };\n    return colors[status] || 'default';\n  };\n  const getPaymentStatusText = status => {\n    const texts = {\n      'unpaid': '未付款',\n      'partial': '部分付款',\n      'paid': '已付款'\n    };\n    return texts[status] || status;\n  };\n  const columns = [{\n    title: '订单编号',\n    dataIndex: 'id',\n    key: 'id',\n    width: 150,\n    fixed: 'left',\n    render: text => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      style: {\n        padding: 0,\n        fontWeight: 'bold'\n      },\n      onClick: () => navigate(`/orders/${text}`),\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户名称',\n    dataIndex: 'customerName',\n    key: 'customerName',\n    width: 200\n  }, {\n    title: '订单标题',\n    dataIndex: 'title',\n    key: 'title',\n    width: 250,\n    ellipsis: true\n  }, {\n    title: '订单金额',\n    dataIndex: 'amount',\n    key: 'amount',\n    width: 120,\n    render: amount => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#1890ff'\n      },\n      children: formatCurrency(amount)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '关联BOM',\n    key: 'bom',\n    width: 150,\n    render: (_, record) => record.bomId ? /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '12px',\n          color: '#1890ff'\n        },\n        children: record.bomId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '11px',\n          color: '#8c8c8c'\n        },\n        children: record.bomName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      style: {\n        fontSize: '12px'\n      },\n      children: \"\\u672A\\u5173\\u8054BOM\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '利润情况',\n    key: 'profit',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '12px',\n          color: '#52c41a'\n        },\n        children: [\"\\u5229\\u6DA6: \", formatCurrency(record.actualProfit || 0)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '12px'\n        },\n        children: [\"\\u5229\\u6DA6\\u7387: \", record.actualProfitMargin || 0, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '订单状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '付款状态',\n    dataIndex: 'paymentStatus',\n    key: 'paymentStatus',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getPaymentStatusColor(status),\n      children: getPaymentStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '进度',\n    dataIndex: 'progress',\n    key: 'progress',\n    width: 120,\n    render: progress => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: progress,\n      size: \"small\",\n      strokeColor: progress === 100 ? '#52c41a' : '#1890ff'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '业务员',\n    dataIndex: 'salesperson',\n    key: 'salesperson',\n    width: 100\n  }, {\n    title: '交付日期',\n    dataIndex: 'deliveryDate',\n    key: 'deliveryDate',\n    width: 100\n  }, {\n    title: '操作',\n    key: 'actions',\n    width: 120,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => navigate(`/orders/${record.id}`)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 21\n          }, this),\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 计算统计数据\n  const stats = {\n    total: orders.length,\n    pending: orders.filter(o => o.status === 'pending').length,\n    inProgress: orders.filter(o => o.status === 'in_progress').length,\n    completed: orders.filter(o => o.status === 'completed').length,\n    totalAmount: orders.reduce((sum, o) => sum + o.amount, 0),\n    totalProfit: orders.reduce((sum, o) => sum + (o.actualProfit || 0), 0)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: \"\\u8BA2\\u5355\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5237\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 40\n          }, this),\n          children: \"\\u65B0\\u5EFA\\u8BA2\\u5355\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BA2\\u5355\\u603B\\u6570\",\n            value: stats.total,\n            suffix: \"\\u4E2A\",\n            prefix: /*#__PURE__*/_jsxDEV(ShoppingCartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\",\n            value: stats.pending,\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FDB\\u884C\\u4E2D\",\n            value: stats.inProgress,\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: stats.completed,\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BA2\\u5355\\u603B\\u989D\",\n            value: stats.totalAmount,\n            formatter: value => formatCurrency(value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5229\\u6DA6\",\n            value: stats.totalProfit,\n            formatter: value => formatCurrency(value),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Input.Search, {\n            placeholder: \"\\u641C\\u7D22\\u8BA2\\u5355\\u53F7\\u3001\\u5BA2\\u6237\\u540D\\u79F0\\u3001\\u6807\\u9898\",\n            allowClear: true,\n            onSearch: value => dispatch(setSearchKeyword(value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: filters.status,\n            onChange: value => dispatch(setFilters({\n              status: value\n            })),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"pending\",\n              children: \"\\u5F85\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"in_progress\",\n              children: \"\\u8FDB\\u884C\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"completed\",\n              children: \"\\u5DF2\\u5B8C\\u6210\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"cancelled\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u4ED8\\u6B3E\\u72B6\\u6001\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: filters.paymentStatus,\n            onChange: value => dispatch(setFilters({\n              paymentStatus: value\n            })),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"unpaid\",\n              children: \"\\u672A\\u4ED8\\u6B3E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"partial\",\n              children: \"\\u90E8\\u5206\\u4ED8\\u6B3E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"paid\",\n              children: \"\\u5DF2\\u4ED8\\u6B3E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => dispatch(resetFilters()),\n            children: \"\\u91CD\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: orders,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          ...pagination,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        },\n        onChange: paginationConfig => dispatch(setPagination({\n          current: paginationConfig.current,\n          pageSize: paginationConfig.pageSize\n        })),\n        scroll: {\n          x: 1400\n        },\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 261,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderList, \"ha/4+5E0AxKPs4qRTc2/ch/qjTA=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector, useSelector, useSelector];\n});\n_c = OrderList;\nexport default OrderList;\nvar _c;\n$RefreshReg$(_c, \"OrderList\");", "map": {"version": 3, "names": ["React", "useState", "useSelector", "useDispatch", "useNavigate", "Table", "Card", "<PERSON><PERSON>", "Input", "Select", "Space", "Tag", "Typography", "Row", "Col", "Statistic", "Progress", "<PERSON><PERSON><PERSON>", "Badge", "PlusOutlined", "SearchOutlined", "EditOutlined", "EyeOutlined", "ReloadOutlined", "ExportOutlined", "ShoppingCartOutlined", "selectFilteredOrders", "selectOrderLoading", "selectOrderFilters", "selectOrderPagination", "setSearchKeyword", "setFilters", "setPagination", "resetFilters", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "OrderList", "_s", "dispatch", "navigate", "orders", "loading", "filters", "pagination", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "getStatusColor", "status", "colors", "getStatusText", "texts", "getPaymentStatusColor", "getPaymentStatusText", "columns", "title", "dataIndex", "key", "width", "fixed", "render", "text", "type", "padding", "fontWeight", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ellipsis", "amount", "strong", "color", "_", "record", "bomId", "direction", "size", "fontSize", "bom<PERSON>ame", "actualProfit", "actualProfitMargin", "progress", "percent", "strokeColor", "icon", "id", "stats", "total", "length", "pending", "filter", "o", "inProgress", "completed", "totalAmount", "reduce", "sum", "totalProfit", "marginBottom", "display", "justifyContent", "alignItems", "level", "margin", "gutter", "xs", "sm", "lg", "suffix", "prefix", "formatter", "valueStyle", "md", "Search", "placeholder", "allowClear", "onSearch", "onChange", "paymentStatus", "dataSource", "<PERSON><PERSON><PERSON>", "showSizeChanger", "showQuickJumper", "showTotal", "range", "paginationConfig", "current", "pageSize", "scroll", "x", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/pages/Order/OrderList.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Card,\n  Button,\n  Input,\n  Select,\n  Space,\n  Tag,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  ExportOutlined,\n  ShoppingCartOutlined\n} from '@ant-design/icons';\n\nimport {\n  selectFilteredOrders,\n  selectOrderLoading,\n  selectOrderFilters,\n  selectOrderPagination,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters\n} from '../../store/slices/orderSlice';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nconst OrderList = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const orders = useSelector(selectFilteredOrders);\n  const loading = useSelector(selectOrderLoading);\n  const filters = useSelector(selectOrderFilters);\n  const pagination = useSelector(selectOrderPagination);\n\n  const formatCurrency = (value) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      'pending': 'orange',\n      'in_progress': 'blue',\n      'completed': 'green',\n      'cancelled': 'red'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      'pending': '待处理',\n      'in_progress': '进行中',\n      'completed': '已完成',\n      'cancelled': '已取消'\n    };\n    return texts[status] || status;\n  };\n\n  const getPaymentStatusColor = (status) => {\n    const colors = {\n      'unpaid': 'red',\n      'partial': 'orange',\n      'paid': 'green'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getPaymentStatusText = (status) => {\n    const texts = {\n      'unpaid': '未付款',\n      'partial': '部分付款',\n      'paid': '已付款'\n    };\n    return texts[status] || status;\n  };\n\n  const columns = [\n    {\n      title: '订单编号',\n      dataIndex: 'id',\n      key: 'id',\n      width: 150,\n      fixed: 'left',\n      render: (text) => (\n        <Button \n          type=\"link\" \n          style={{ padding: 0, fontWeight: 'bold' }}\n          onClick={() => navigate(`/orders/${text}`)}\n        >\n          {text}\n        </Button>\n      )\n    },\n    {\n      title: '客户名称',\n      dataIndex: 'customerName',\n      key: 'customerName',\n      width: 200\n    },\n    {\n      title: '订单标题',\n      dataIndex: 'title',\n      key: 'title',\n      width: 250,\n      ellipsis: true\n    },\n    {\n      title: '订单金额',\n      dataIndex: 'amount',\n      key: 'amount',\n      width: 120,\n      render: (amount) => (\n        <Text strong style={{ color: '#1890ff' }}>\n          {formatCurrency(amount)}\n        </Text>\n      )\n    },\n    {\n      title: '关联BOM',\n      key: 'bom',\n      width: 150,\n      render: (_, record) => (\n        record.bomId ? (\n          <Space direction=\"vertical\" size={0}>\n            <Text style={{ fontSize: '12px', color: '#1890ff' }}>\n              {record.bomId}\n            </Text>\n            <Text style={{ fontSize: '11px', color: '#8c8c8c' }}>\n              {record.bomName}\n            </Text>\n          </Space>\n        ) : (\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            未关联BOM\n          </Text>\n        )\n      )\n    },\n    {\n      title: '利润情况',\n      key: 'profit',\n      width: 150,\n      render: (_, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ fontSize: '12px', color: '#52c41a' }}>\n            利润: {formatCurrency(record.actualProfit || 0)}\n          </Text>\n          <Text style={{ fontSize: '12px' }}>\n            利润率: {record.actualProfitMargin || 0}%\n          </Text>\n        </Space>\n      )\n    },\n    {\n      title: '订单状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      )\n    },\n    {\n      title: '付款状态',\n      dataIndex: 'paymentStatus',\n      key: 'paymentStatus',\n      width: 100,\n      render: (status) => (\n        <Tag color={getPaymentStatusColor(status)}>\n          {getPaymentStatusText(status)}\n        </Tag>\n      )\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      width: 120,\n      render: (progress) => (\n        <Progress \n          percent={progress} \n          size=\"small\" \n          strokeColor={progress === 100 ? '#52c41a' : '#1890ff'}\n        />\n      )\n    },\n    {\n      title: '业务员',\n      dataIndex: 'salesperson',\n      key: 'salesperson',\n      width: 100\n    },\n    {\n      title: '交付日期',\n      dataIndex: 'deliveryDate',\n      key: 'deliveryDate',\n      width: 100\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 120,\n      fixed: 'right',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button \n              type=\"text\" \n              icon={<EyeOutlined />} \n              size=\"small\"\n              onClick={() => navigate(`/orders/${record.id}`)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button \n              type=\"text\" \n              icon={<EditOutlined />} \n              size=\"small\"\n            />\n          </Tooltip>\n        </Space>\n      )\n    }\n  ];\n\n  // 计算统计数据\n  const stats = {\n    total: orders.length,\n    pending: orders.filter(o => o.status === 'pending').length,\n    inProgress: orders.filter(o => o.status === 'in_progress').length,\n    completed: orders.filter(o => o.status === 'completed').length,\n    totalAmount: orders.reduce((sum, o) => sum + o.amount, 0),\n    totalProfit: orders.reduce((sum, o) => sum + (o.actualProfit || 0), 0)\n  };\n\n  return (\n    <div>\n      {/* 页面标题和操作 */}\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Title level={2} style={{ margin: 0 }}>订单管理</Title>\n        <Space>\n          <Button icon={<ReloadOutlined />}>刷新</Button>\n          <Button icon={<ExportOutlined />}>导出</Button>\n          <Button type=\"primary\" icon={<PlusOutlined />}>\n            新建订单\n          </Button>\n        </Space>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"订单总数\" \n              value={stats.total} \n              suffix=\"个\"\n              prefix={<ShoppingCartOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"待处理\" value={stats.pending} suffix=\"个\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"进行中\" value={stats.inProgress} suffix=\"个\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"已完成\" value={stats.completed} suffix=\"个\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"订单总额\" \n              value={stats.totalAmount} \n              formatter={(value) => formatCurrency(value)}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"总利润\" \n              value={stats.totalProfit} \n              formatter={(value) => formatCurrency(value)}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索和筛选 */}\n      <Card style={{ marginBottom: '16px' }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Input.Search\n              placeholder=\"搜索订单号、客户名称、标题\"\n              allowClear\n              onSearch={(value) => dispatch(setSearchKeyword(value))}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Select\n              placeholder=\"订单状态\"\n              allowClear\n              style={{ width: '100%' }}\n              value={filters.status}\n              onChange={(value) => dispatch(setFilters({ status: value }))}\n            >\n              <Option value=\"pending\">待处理</Option>\n              <Option value=\"in_progress\">进行中</Option>\n              <Option value=\"completed\">已完成</Option>\n              <Option value=\"cancelled\">已取消</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Select\n              placeholder=\"付款状态\"\n              allowClear\n              style={{ width: '100%' }}\n              value={filters.paymentStatus}\n              onChange={(value) => dispatch(setFilters({ paymentStatus: value }))}\n            >\n              <Option value=\"unpaid\">未付款</Option>\n              <Option value=\"partial\">部分付款</Option>\n              <Option value=\"paid\">已付款</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Button onClick={() => dispatch(resetFilters())}>重置</Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 订单列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={orders}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            ...pagination,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => \n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n          }}\n          onChange={(paginationConfig) => dispatch(setPagination({\n            current: paginationConfig.current,\n            pageSize: paginationConfig.pageSize\n          }))}\n          scroll={{ x: 1400 }}\n          size=\"small\"\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default OrderList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,oBAAoB,QACf,mBAAmB;AAE1B,SACEC,oBAAoB,EACpBC,kBAAkB,EAClBC,kBAAkB,EAClBC,qBAAqB,EACrBC,gBAAgB,EAChBC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGzB,UAAU;AAClC,MAAM;EAAE0B;AAAO,CAAC,GAAG7B,MAAM;AAEzB,MAAM8B,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,MAAM,GAAGzC,WAAW,CAACwB,oBAAoB,CAAC;EAChD,MAAMkB,OAAO,GAAG1C,WAAW,CAACyB,kBAAkB,CAAC;EAC/C,MAAMkB,OAAO,GAAG3C,WAAW,CAAC0B,kBAAkB,CAAC;EAC/C,MAAMkB,UAAU,GAAG5C,WAAW,CAAC2B,qBAAqB,CAAC;EAErD,MAAMkB,cAAc,GAAIC,KAAK,IAAK;IAChC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACP,KAAK,CAAC;EAClB,CAAC;EAED,MAAMQ,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,MAAM,GAAG;MACb,SAAS,EAAE,QAAQ;MACnB,aAAa,EAAE,MAAM;MACrB,WAAW,EAAE,OAAO;MACpB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAME,aAAa,GAAIF,MAAM,IAAK;IAChC,MAAMG,KAAK,GAAG;MACZ,SAAS,EAAE,KAAK;MAChB,aAAa,EAAE,KAAK;MACpB,WAAW,EAAE,KAAK;MAClB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,KAAK,CAACH,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMI,qBAAqB,GAAIJ,MAAM,IAAK;IACxC,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,KAAK;MACf,SAAS,EAAE,QAAQ;MACnB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMK,oBAAoB,GAAIL,MAAM,IAAK;IACvC,MAAMG,KAAK,GAAG;MACZ,QAAQ,EAAE,KAAK;MACf,SAAS,EAAE,MAAM;MACjB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,KAAK,CAACH,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMM,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAGC,IAAI,iBACXnC,OAAA,CAAC5B,MAAM;MACLgE,IAAI,EAAC,MAAM;MACXpB,KAAK,EAAE;QAAEqB,OAAO,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAO,CAAE;MAC1CC,OAAO,EAAEA,CAAA,KAAMhC,QAAQ,CAAC,WAAW4B,IAAI,EAAE,CAAE;MAAAK,QAAA,EAE1CL;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAEZ,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVa,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGY,MAAM,iBACb9C,OAAA,CAACE,IAAI;MAAC6C,MAAM;MAAC/B,KAAK,EAAE;QAAEgC,KAAK,EAAE;MAAU,CAAE;MAAAR,QAAA,EACtC5B,cAAc,CAACkC,MAAM;IAAC;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAEV,CAAC,EACD;IACEf,KAAK,EAAE,OAAO;IACdE,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,KAChBA,MAAM,CAACC,KAAK,gBACVnD,OAAA,CAACzB,KAAK;MAAC6E,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAb,QAAA,gBAClCxC,OAAA,CAACE,IAAI;QAACc,KAAK,EAAE;UAAEsC,QAAQ,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EACjDU,MAAM,CAACC;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACP5C,OAAA,CAACE,IAAI;QAACc,KAAK,EAAE;UAAEsC,QAAQ,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EACjDU,MAAM,CAACK;MAAO;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,gBAER5C,OAAA,CAACE,IAAI;MAACkC,IAAI,EAAC,WAAW;MAACpB,KAAK,EAAE;QAAEsC,QAAQ,EAAE;MAAO,CAAE;MAAAd,QAAA,EAAC;IAEpD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAGZ,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChBlD,OAAA,CAACzB,KAAK;MAAC6E,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAb,QAAA,gBAClCxC,OAAA,CAACE,IAAI;QAACc,KAAK,EAAE;UAAEsC,QAAQ,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,GAAC,gBAC/C,EAAC5B,cAAc,CAACsC,MAAM,CAACM,YAAY,IAAI,CAAC,CAAC;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eACP5C,OAAA,CAACE,IAAI;QAACc,KAAK,EAAE;UAAEsC,QAAQ,EAAE;QAAO,CAAE;QAAAd,QAAA,GAAC,sBAC5B,EAACU,MAAM,CAACO,kBAAkB,IAAI,CAAC,EAAC,GACvC;MAAA;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGZ,MAAM,iBACbtB,OAAA,CAACxB,GAAG;MAACwE,KAAK,EAAE3B,cAAc,CAACC,MAAM,CAAE;MAAAkB,QAAA,EAChChB,aAAa,CAACF,MAAM;IAAC;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGZ,MAAM,iBACbtB,OAAA,CAACxB,GAAG;MAACwE,KAAK,EAAEtB,qBAAqB,CAACJ,MAAM,CAAE;MAAAkB,QAAA,EACvCb,oBAAoB,CAACL,MAAM;IAAC;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B;EAET,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGwB,QAAQ,iBACf1D,OAAA,CAACnB,QAAQ;MACP8E,OAAO,EAAED,QAAS;MAClBL,IAAI,EAAC,OAAO;MACZO,WAAW,EAAEF,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG;IAAU;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD;EAEL,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChBlD,OAAA,CAACzB,KAAK;MAAC8E,IAAI,EAAC,OAAO;MAAAb,QAAA,gBACjBxC,OAAA,CAAClB,OAAO;QAAC+C,KAAK,EAAC,0BAAM;QAAAW,QAAA,eACnBxC,OAAA,CAAC5B,MAAM;UACLgE,IAAI,EAAC,MAAM;UACXyB,IAAI,eAAE7D,OAAA,CAACb,WAAW;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBS,IAAI,EAAC,OAAO;UACZd,OAAO,EAAEA,CAAA,KAAMhC,QAAQ,CAAC,WAAW2C,MAAM,CAACY,EAAE,EAAE;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV5C,OAAA,CAAClB,OAAO;QAAC+C,KAAK,EAAC,cAAI;QAAAW,QAAA,eACjBxC,OAAA,CAAC5B,MAAM;UACLgE,IAAI,EAAC,MAAM;UACXyB,IAAI,eAAE7D,OAAA,CAACd,YAAY;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBS,IAAI,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;;EAED;EACA,MAAMmB,KAAK,GAAG;IACZC,KAAK,EAAExD,MAAM,CAACyD,MAAM;IACpBC,OAAO,EAAE1D,MAAM,CAAC2D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,MAAM,KAAK,SAAS,CAAC,CAAC2C,MAAM;IAC1DI,UAAU,EAAE7D,MAAM,CAAC2D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,MAAM,KAAK,aAAa,CAAC,CAAC2C,MAAM;IACjEK,SAAS,EAAE9D,MAAM,CAAC2D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,MAAM,KAAK,WAAW,CAAC,CAAC2C,MAAM;IAC9DM,WAAW,EAAE/D,MAAM,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KAAKK,GAAG,GAAGL,CAAC,CAACtB,MAAM,EAAE,CAAC,CAAC;IACzD4B,WAAW,EAAElE,MAAM,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KAAKK,GAAG,IAAIL,CAAC,CAACZ,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC;EACvE,CAAC;EAED,oBACExD,OAAA;IAAAwC,QAAA,gBAEExC,OAAA;MAAKgB,KAAK,EAAE;QAAE2D,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAtC,QAAA,gBAC3GxC,OAAA,CAACC,KAAK;QAAC8E,KAAK,EAAE,CAAE;QAAC/D,KAAK,EAAE;UAAEgE,MAAM,EAAE;QAAE,CAAE;QAAAxC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnD5C,OAAA,CAACzB,KAAK;QAAAiE,QAAA,gBACJxC,OAAA,CAAC5B,MAAM;UAACyF,IAAI,eAAE7D,OAAA,CAACZ,cAAc;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7C5C,OAAA,CAAC5B,MAAM;UAACyF,IAAI,eAAE7D,OAAA,CAACX,cAAc;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7C5C,OAAA,CAAC5B,MAAM;UAACgE,IAAI,EAAC,SAAS;UAACyB,IAAI,eAAE7D,OAAA,CAAChB,YAAY;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN5C,OAAA,CAACtB,GAAG;MAACuG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACjE,KAAK,EAAE;QAAE2D,YAAY,EAAE;MAAO,CAAE;MAAAnC,QAAA,gBACrDxC,OAAA,CAACrB,GAAG;QAACuG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5C,QAAA,eACxBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAACpB,SAAS;YACRiD,KAAK,EAAC,0BAAM;YACZhB,KAAK,EAAEkD,KAAK,CAACC,KAAM;YACnBqB,MAAM,EAAC,QAAG;YACVC,MAAM,eAAEtF,OAAA,CAACV,oBAAoB;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAACrB,GAAG;QAACuG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5C,QAAA,eACxBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAACpB,SAAS;YAACiD,KAAK,EAAC,oBAAK;YAAChB,KAAK,EAAEkD,KAAK,CAACG,OAAQ;YAACmB,MAAM,EAAC;UAAG;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAACrB,GAAG;QAACuG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5C,QAAA,eACxBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAACpB,SAAS;YAACiD,KAAK,EAAC,oBAAK;YAAChB,KAAK,EAAEkD,KAAK,CAACM,UAAW;YAACgB,MAAM,EAAC;UAAG;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAACrB,GAAG;QAACuG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5C,QAAA,eACxBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAACpB,SAAS;YAACiD,KAAK,EAAC,oBAAK;YAAChB,KAAK,EAAEkD,KAAK,CAACO,SAAU;YAACe,MAAM,EAAC;UAAG;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAACrB,GAAG;QAACuG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5C,QAAA,eACxBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAACpB,SAAS;YACRiD,KAAK,EAAC,0BAAM;YACZhB,KAAK,EAAEkD,KAAK,CAACQ,WAAY;YACzBgB,SAAS,EAAG1E,KAAK,IAAKD,cAAc,CAACC,KAAK;UAAE;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAACrB,GAAG;QAACuG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5C,QAAA,eACxBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAACpB,SAAS;YACRiD,KAAK,EAAC,oBAAK;YACXhB,KAAK,EAAEkD,KAAK,CAACW,WAAY;YACzBa,SAAS,EAAG1E,KAAK,IAAKD,cAAc,CAACC,KAAK,CAAE;YAC5C2E,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA,CAAC7B,IAAI;MAAC6C,KAAK,EAAE;QAAE2D,YAAY,EAAE;MAAO,CAAE;MAAAnC,QAAA,eACpCxC,OAAA,CAACtB,GAAG;QAACuG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAzC,QAAA,gBACpBxC,OAAA,CAACrB,GAAG;UAACuG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA5C,QAAA,eAChCxC,OAAA,CAAC3B,KAAK,CAACqH,MAAM;YACXC,WAAW,EAAC,gFAAe;YAC3BC,UAAU;YACVC,QAAQ,EAAGhF,KAAK,IAAKP,QAAQ,CAACX,gBAAgB,CAACkB,KAAK,CAAC,CAAE;YACvDG,KAAK,EAAE;cAAEgB,KAAK,EAAE;YAAO;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5C,OAAA,CAACrB,GAAG;UAACuG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA5C,QAAA,eAC/BxC,OAAA,CAAC1B,MAAM;YACLqH,WAAW,EAAC,0BAAM;YAClBC,UAAU;YACV5E,KAAK,EAAE;cAAEgB,KAAK,EAAE;YAAO,CAAE;YACzBnB,KAAK,EAAEH,OAAO,CAACY,MAAO;YACtBwE,QAAQ,EAAGjF,KAAK,IAAKP,QAAQ,CAACV,UAAU,CAAC;cAAE0B,MAAM,EAAET;YAAM,CAAC,CAAC,CAAE;YAAA2B,QAAA,gBAE7DxC,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,SAAS;cAAA2B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC5C,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,aAAa;cAAA2B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC5C,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,WAAW;cAAA2B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC5C,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,WAAW;cAAA2B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5C,OAAA,CAACrB,GAAG;UAACuG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA5C,QAAA,eAC/BxC,OAAA,CAAC1B,MAAM;YACLqH,WAAW,EAAC,0BAAM;YAClBC,UAAU;YACV5E,KAAK,EAAE;cAAEgB,KAAK,EAAE;YAAO,CAAE;YACzBnB,KAAK,EAAEH,OAAO,CAACqF,aAAc;YAC7BD,QAAQ,EAAGjF,KAAK,IAAKP,QAAQ,CAACV,UAAU,CAAC;cAAEmG,aAAa,EAAElF;YAAM,CAAC,CAAC,CAAE;YAAA2B,QAAA,gBAEpExC,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,QAAQ;cAAA2B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC5C,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,SAAS;cAAA2B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC5C,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,MAAM;cAAA2B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5C,OAAA,CAACrB,GAAG;UAACuG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA5C,QAAA,eAC/BxC,OAAA,CAAC5B,MAAM;YAACmE,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAACR,YAAY,CAAC,CAAC,CAAE;YAAA0C,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP5C,OAAA,CAAC7B,IAAI;MAAAqE,QAAA,eACHxC,OAAA,CAAC9B,KAAK;QACJ0D,OAAO,EAAEA,OAAQ;QACjBoE,UAAU,EAAExF,MAAO;QACnByF,MAAM,EAAC,IAAI;QACXxF,OAAO,EAAEA,OAAQ;QACjBE,UAAU,EAAE;UACV,GAAGA,UAAU;UACbuF,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACpC,KAAK,EAAEqC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQrC,KAAK;QAC1C,CAAE;QACF8B,QAAQ,EAAGQ,gBAAgB,IAAKhG,QAAQ,CAACT,aAAa,CAAC;UACrD0G,OAAO,EAAED,gBAAgB,CAACC,OAAO;UACjCC,QAAQ,EAAEF,gBAAgB,CAACE;QAC7B,CAAC,CAAC,CAAE;QACJC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBrD,IAAI,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACvC,EAAA,CA1VID,SAAS;EAAA,QACIpC,WAAW,EACXC,WAAW,EACbF,WAAW,EACVA,WAAW,EACXA,WAAW,EACRA,WAAW;AAAA;AAAA4I,EAAA,GAN1BvG,SAAS;AA4Vf,eAAeA,SAAS;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}