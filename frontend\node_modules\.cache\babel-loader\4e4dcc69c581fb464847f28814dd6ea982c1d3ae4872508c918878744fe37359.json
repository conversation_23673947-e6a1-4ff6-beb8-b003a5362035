{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport authSlice from './slices/authSlice';\nimport customerSlice from './slices/customerSlice';\nimport orderSlice from './slices/orderSlice';\nimport quotationSlice from './slices/quotationSlice';\nimport receivableSlice from './slices/receivableSlice';\nimport dashboardSlice from './slices/dashboardSlice';\nimport bomSlice from './slices/bomSlice';\nconst store = configureStore({\n  reducer: {\n    auth: authSlice,\n    customer: customerSlice,\n    order: orderSlice,\n    quotation: quotationSlice,\n    receivable: receivableSlice,\n    dashboard: dashboardSlice,\n    bom: bomSlice\n  },\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: {\n      ignoredActions: ['persist/PERSIST']\n    }\n  })\n});\nexport default store;", "map": {"version": 3, "names": ["configureStore", "authSlice", "customerSlice", "orderSlice", "quotationSlice", "receivableSlice", "dashboardSlice", "bomSlice", "store", "reducer", "auth", "customer", "order", "quotation", "receivable", "dashboard", "bom", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/store/index.js"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport authSlice from './slices/authSlice';\nimport customerSlice from './slices/customerSlice';\nimport orderSlice from './slices/orderSlice';\nimport quotationSlice from './slices/quotationSlice';\nimport receivableSlice from './slices/receivableSlice';\nimport dashboardSlice from './slices/dashboardSlice';\nimport bomSlice from './slices/bomSlice';\n\nconst store = configureStore({\n  reducer: {\n    auth: authSlice,\n    customer: customerSlice,\n    order: orderSlice,\n    quotation: quotationSlice,\n    receivable: receivableSlice,\n    dashboard: dashboardSlice,\n    bom: bomSlice,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST'],\n      },\n    }),\n});\n\nexport default store;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,QAAQ,MAAM,mBAAmB;AAExC,MAAMC,KAAK,GAAGR,cAAc,CAAC;EAC3BS,OAAO,EAAE;IACPC,IAAI,EAAET,SAAS;IACfU,QAAQ,EAAET,aAAa;IACvBU,KAAK,EAAET,UAAU;IACjBU,SAAS,EAAET,cAAc;IACzBU,UAAU,EAAET,eAAe;IAC3BU,SAAS,EAAET,cAAc;IACzBU,GAAG,EAAET;EACP,CAAC;EACDU,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAAC;IACnBC,iBAAiB,EAAE;MACjBC,cAAc,EAAE,CAAC,iBAAiB;IACpC;EACF,CAAC;AACL,CAAC,CAAC;AAEF,eAAeZ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}