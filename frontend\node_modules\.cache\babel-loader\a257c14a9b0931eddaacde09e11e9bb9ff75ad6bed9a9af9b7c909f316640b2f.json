{"ast": null, "code": "import BoundingRect from '../core/BoundingRect.js';\nimport LRU from '../core/LRU.js';\nimport { DEFAULT_FONT, platformApi } from '../core/platform.js';\nvar textWidthCache = {};\nexport function getWidth(text, font) {\n  font = font || DEFAULT_FONT;\n  var cacheOfFont = textWidthCache[font];\n  if (!cacheOfFont) {\n    cacheOfFont = textWidthCache[font] = new LRU(500);\n  }\n  var width = cacheOfFont.get(text);\n  if (width == null) {\n    width = platformApi.measureText(text, font).width;\n    cacheOfFont.put(text, width);\n  }\n  return width;\n}\nexport function innerGetBoundingRect(text, font, textAlign, textBaseline) {\n  var width = getWidth(text, font);\n  var height = getLineHeight(font);\n  var x = adjustTextX(0, width, textAlign);\n  var y = adjustTextY(0, height, textBaseline);\n  var rect = new BoundingRect(x, y, width, height);\n  return rect;\n}\nexport function getBoundingRect(text, font, textAlign, textBaseline) {\n  var textLines = ((text || '') + '').split('\\n');\n  var len = textLines.length;\n  if (len === 1) {\n    return innerGetBoundingRect(textLines[0], font, textAlign, textBaseline);\n  } else {\n    var uniondRect = new BoundingRect(0, 0, 0, 0);\n    for (var i = 0; i < textLines.length; i++) {\n      var rect = innerGetBoundingRect(textLines[i], font, textAlign, textBaseline);\n      i === 0 ? uniondRect.copy(rect) : uniondRect.union(rect);\n    }\n    return uniondRect;\n  }\n}\nexport function adjustTextX(x, width, textAlign) {\n  if (textAlign === 'right') {\n    x -= width;\n  } else if (textAlign === 'center') {\n    x -= width / 2;\n  }\n  return x;\n}\nexport function adjustTextY(y, height, verticalAlign) {\n  if (verticalAlign === 'middle') {\n    y -= height / 2;\n  } else if (verticalAlign === 'bottom') {\n    y -= height;\n  }\n  return y;\n}\nexport function getLineHeight(font) {\n  return getWidth('国', font);\n}\nexport function measureText(text, font) {\n  return platformApi.measureText(text, font);\n}\nexport function parsePercent(value, maxValue) {\n  if (typeof value === 'string') {\n    if (value.lastIndexOf('%') >= 0) {\n      return parseFloat(value) / 100 * maxValue;\n    }\n    return parseFloat(value);\n  }\n  return value;\n}\nexport function calculateTextPosition(out, opts, rect) {\n  var textPosition = opts.position || 'inside';\n  var distance = opts.distance != null ? opts.distance : 5;\n  var height = rect.height;\n  var width = rect.width;\n  var halfHeight = height / 2;\n  var x = rect.x;\n  var y = rect.y;\n  var textAlign = 'left';\n  var textVerticalAlign = 'top';\n  if (textPosition instanceof Array) {\n    x += parsePercent(textPosition[0], rect.width);\n    y += parsePercent(textPosition[1], rect.height);\n    textAlign = null;\n    textVerticalAlign = null;\n  } else {\n    switch (textPosition) {\n      case 'left':\n        x -= distance;\n        y += halfHeight;\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'right':\n        x += distance + width;\n        y += halfHeight;\n        textVerticalAlign = 'middle';\n        break;\n      case 'top':\n        x += width / 2;\n        y -= distance;\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'bottom':\n        x += width / 2;\n        y += height + distance;\n        textAlign = 'center';\n        break;\n      case 'inside':\n        x += width / 2;\n        y += halfHeight;\n        textAlign = 'center';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideLeft':\n        x += distance;\n        y += halfHeight;\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideRight':\n        x += width - distance;\n        y += halfHeight;\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideTop':\n        x += width / 2;\n        y += distance;\n        textAlign = 'center';\n        break;\n      case 'insideBottom':\n        x += width / 2;\n        y += height - distance;\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideTopLeft':\n        x += distance;\n        y += distance;\n        break;\n      case 'insideTopRight':\n        x += width - distance;\n        y += distance;\n        textAlign = 'right';\n        break;\n      case 'insideBottomLeft':\n        x += distance;\n        y += height - distance;\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideBottomRight':\n        x += width - distance;\n        y += height - distance;\n        textAlign = 'right';\n        textVerticalAlign = 'bottom';\n        break;\n    }\n  }\n  out = out || {};\n  out.x = x;\n  out.y = y;\n  out.align = textAlign;\n  out.verticalAlign = textVerticalAlign;\n  return out;\n}", "map": {"version": 3, "names": ["BoundingRect", "LRU", "DEFAULT_FONT", "platformApi", "textWidthCache", "getWidth", "text", "font", "cacheOfFont", "width", "get", "measureText", "put", "innerGetBoundingRect", "textAlign", "textBaseline", "height", "getLineHeight", "x", "adjustTextX", "y", "adjustTextY", "rect", "getBoundingRect", "textLines", "split", "len", "length", "uniondRect", "i", "copy", "union", "verticalAlign", "parsePercent", "value", "maxValue", "lastIndexOf", "parseFloat", "calculateTextPosition", "out", "opts", "textPosition", "position", "distance", "halfHeight", "textVerticalAlign", "Array", "align"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/zrender/lib/contain/text.js"], "sourcesContent": ["import BoundingRect from '../core/BoundingRect.js';\nimport LRU from '../core/LRU.js';\nimport { DEFAULT_FONT, platformApi } from '../core/platform.js';\nvar textWidthCache = {};\nexport function getWidth(text, font) {\n    font = font || DEFAULT_FONT;\n    var cacheOfFont = textWidthCache[font];\n    if (!cacheOfFont) {\n        cacheOfFont = textWidthCache[font] = new LRU(500);\n    }\n    var width = cacheOfFont.get(text);\n    if (width == null) {\n        width = platformApi.measureText(text, font).width;\n        cacheOfFont.put(text, width);\n    }\n    return width;\n}\nexport function innerGetBoundingRect(text, font, textAlign, textBaseline) {\n    var width = getWidth(text, font);\n    var height = getLineHeight(font);\n    var x = adjustTextX(0, width, textAlign);\n    var y = adjustTextY(0, height, textBaseline);\n    var rect = new BoundingRect(x, y, width, height);\n    return rect;\n}\nexport function getBoundingRect(text, font, textAlign, textBaseline) {\n    var textLines = ((text || '') + '').split('\\n');\n    var len = textLines.length;\n    if (len === 1) {\n        return innerGetBoundingRect(textLines[0], font, textAlign, textBaseline);\n    }\n    else {\n        var uniondRect = new BoundingRect(0, 0, 0, 0);\n        for (var i = 0; i < textLines.length; i++) {\n            var rect = innerGetBoundingRect(textLines[i], font, textAlign, textBaseline);\n            i === 0 ? uniondRect.copy(rect) : uniondRect.union(rect);\n        }\n        return uniondRect;\n    }\n}\nexport function adjustTextX(x, width, textAlign) {\n    if (textAlign === 'right') {\n        x -= width;\n    }\n    else if (textAlign === 'center') {\n        x -= width / 2;\n    }\n    return x;\n}\nexport function adjustTextY(y, height, verticalAlign) {\n    if (verticalAlign === 'middle') {\n        y -= height / 2;\n    }\n    else if (verticalAlign === 'bottom') {\n        y -= height;\n    }\n    return y;\n}\nexport function getLineHeight(font) {\n    return getWidth('国', font);\n}\nexport function measureText(text, font) {\n    return platformApi.measureText(text, font);\n}\nexport function parsePercent(value, maxValue) {\n    if (typeof value === 'string') {\n        if (value.lastIndexOf('%') >= 0) {\n            return parseFloat(value) / 100 * maxValue;\n        }\n        return parseFloat(value);\n    }\n    return value;\n}\nexport function calculateTextPosition(out, opts, rect) {\n    var textPosition = opts.position || 'inside';\n    var distance = opts.distance != null ? opts.distance : 5;\n    var height = rect.height;\n    var width = rect.width;\n    var halfHeight = height / 2;\n    var x = rect.x;\n    var y = rect.y;\n    var textAlign = 'left';\n    var textVerticalAlign = 'top';\n    if (textPosition instanceof Array) {\n        x += parsePercent(textPosition[0], rect.width);\n        y += parsePercent(textPosition[1], rect.height);\n        textAlign = null;\n        textVerticalAlign = null;\n    }\n    else {\n        switch (textPosition) {\n            case 'left':\n                x -= distance;\n                y += halfHeight;\n                textAlign = 'right';\n                textVerticalAlign = 'middle';\n                break;\n            case 'right':\n                x += distance + width;\n                y += halfHeight;\n                textVerticalAlign = 'middle';\n                break;\n            case 'top':\n                x += width / 2;\n                y -= distance;\n                textAlign = 'center';\n                textVerticalAlign = 'bottom';\n                break;\n            case 'bottom':\n                x += width / 2;\n                y += height + distance;\n                textAlign = 'center';\n                break;\n            case 'inside':\n                x += width / 2;\n                y += halfHeight;\n                textAlign = 'center';\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideLeft':\n                x += distance;\n                y += halfHeight;\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideRight':\n                x += width - distance;\n                y += halfHeight;\n                textAlign = 'right';\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideTop':\n                x += width / 2;\n                y += distance;\n                textAlign = 'center';\n                break;\n            case 'insideBottom':\n                x += width / 2;\n                y += height - distance;\n                textAlign = 'center';\n                textVerticalAlign = 'bottom';\n                break;\n            case 'insideTopLeft':\n                x += distance;\n                y += distance;\n                break;\n            case 'insideTopRight':\n                x += width - distance;\n                y += distance;\n                textAlign = 'right';\n                break;\n            case 'insideBottomLeft':\n                x += distance;\n                y += height - distance;\n                textVerticalAlign = 'bottom';\n                break;\n            case 'insideBottomRight':\n                x += width - distance;\n                y += height - distance;\n                textAlign = 'right';\n                textVerticalAlign = 'bottom';\n                break;\n        }\n    }\n    out = out || {};\n    out.x = x;\n    out.y = y;\n    out.align = textAlign;\n    out.verticalAlign = textVerticalAlign;\n    return out;\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,yBAAyB;AAClD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,YAAY,EAAEC,WAAW,QAAQ,qBAAqB;AAC/D,IAAIC,cAAc,GAAG,CAAC,CAAC;AACvB,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACjCA,IAAI,GAAGA,IAAI,IAAIL,YAAY;EAC3B,IAAIM,WAAW,GAAGJ,cAAc,CAACG,IAAI,CAAC;EACtC,IAAI,CAACC,WAAW,EAAE;IACdA,WAAW,GAAGJ,cAAc,CAACG,IAAI,CAAC,GAAG,IAAIN,GAAG,CAAC,GAAG,CAAC;EACrD;EACA,IAAIQ,KAAK,GAAGD,WAAW,CAACE,GAAG,CAACJ,IAAI,CAAC;EACjC,IAAIG,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGN,WAAW,CAACQ,WAAW,CAACL,IAAI,EAAEC,IAAI,CAAC,CAACE,KAAK;IACjDD,WAAW,CAACI,GAAG,CAACN,IAAI,EAAEG,KAAK,CAAC;EAChC;EACA,OAAOA,KAAK;AAChB;AACA,OAAO,SAASI,oBAAoBA,CAACP,IAAI,EAAEC,IAAI,EAAEO,SAAS,EAAEC,YAAY,EAAE;EACtE,IAAIN,KAAK,GAAGJ,QAAQ,CAACC,IAAI,EAAEC,IAAI,CAAC;EAChC,IAAIS,MAAM,GAAGC,aAAa,CAACV,IAAI,CAAC;EAChC,IAAIW,CAAC,GAAGC,WAAW,CAAC,CAAC,EAAEV,KAAK,EAAEK,SAAS,CAAC;EACxC,IAAIM,CAAC,GAAGC,WAAW,CAAC,CAAC,EAAEL,MAAM,EAAED,YAAY,CAAC;EAC5C,IAAIO,IAAI,GAAG,IAAItB,YAAY,CAACkB,CAAC,EAAEE,CAAC,EAAEX,KAAK,EAAEO,MAAM,CAAC;EAChD,OAAOM,IAAI;AACf;AACA,OAAO,SAASC,eAAeA,CAACjB,IAAI,EAAEC,IAAI,EAAEO,SAAS,EAAEC,YAAY,EAAE;EACjE,IAAIS,SAAS,GAAG,CAAC,CAAClB,IAAI,IAAI,EAAE,IAAI,EAAE,EAAEmB,KAAK,CAAC,IAAI,CAAC;EAC/C,IAAIC,GAAG,GAAGF,SAAS,CAACG,MAAM;EAC1B,IAAID,GAAG,KAAK,CAAC,EAAE;IACX,OAAOb,oBAAoB,CAACW,SAAS,CAAC,CAAC,CAAC,EAAEjB,IAAI,EAAEO,SAAS,EAAEC,YAAY,CAAC;EAC5E,CAAC,MACI;IACD,IAAIa,UAAU,GAAG,IAAI5B,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;MACvC,IAAIP,IAAI,GAAGT,oBAAoB,CAACW,SAAS,CAACK,CAAC,CAAC,EAAEtB,IAAI,EAAEO,SAAS,EAAEC,YAAY,CAAC;MAC5Ec,CAAC,KAAK,CAAC,GAAGD,UAAU,CAACE,IAAI,CAACR,IAAI,CAAC,GAAGM,UAAU,CAACG,KAAK,CAACT,IAAI,CAAC;IAC5D;IACA,OAAOM,UAAU;EACrB;AACJ;AACA,OAAO,SAAST,WAAWA,CAACD,CAAC,EAAET,KAAK,EAAEK,SAAS,EAAE;EAC7C,IAAIA,SAAS,KAAK,OAAO,EAAE;IACvBI,CAAC,IAAIT,KAAK;EACd,CAAC,MACI,IAAIK,SAAS,KAAK,QAAQ,EAAE;IAC7BI,CAAC,IAAIT,KAAK,GAAG,CAAC;EAClB;EACA,OAAOS,CAAC;AACZ;AACA,OAAO,SAASG,WAAWA,CAACD,CAAC,EAAEJ,MAAM,EAAEgB,aAAa,EAAE;EAClD,IAAIA,aAAa,KAAK,QAAQ,EAAE;IAC5BZ,CAAC,IAAIJ,MAAM,GAAG,CAAC;EACnB,CAAC,MACI,IAAIgB,aAAa,KAAK,QAAQ,EAAE;IACjCZ,CAAC,IAAIJ,MAAM;EACf;EACA,OAAOI,CAAC;AACZ;AACA,OAAO,SAASH,aAAaA,CAACV,IAAI,EAAE;EAChC,OAAOF,QAAQ,CAAC,GAAG,EAAEE,IAAI,CAAC;AAC9B;AACA,OAAO,SAASI,WAAWA,CAACL,IAAI,EAAEC,IAAI,EAAE;EACpC,OAAOJ,WAAW,CAACQ,WAAW,CAACL,IAAI,EAAEC,IAAI,CAAC;AAC9C;AACA,OAAO,SAAS0B,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC1C,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC3B,IAAIA,KAAK,CAACE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MAC7B,OAAOC,UAAU,CAACH,KAAK,CAAC,GAAG,GAAG,GAAGC,QAAQ;IAC7C;IACA,OAAOE,UAAU,CAACH,KAAK,CAAC;EAC5B;EACA,OAAOA,KAAK;AAChB;AACA,OAAO,SAASI,qBAAqBA,CAACC,GAAG,EAAEC,IAAI,EAAElB,IAAI,EAAE;EACnD,IAAImB,YAAY,GAAGD,IAAI,CAACE,QAAQ,IAAI,QAAQ;EAC5C,IAAIC,QAAQ,GAAGH,IAAI,CAACG,QAAQ,IAAI,IAAI,GAAGH,IAAI,CAACG,QAAQ,GAAG,CAAC;EACxD,IAAI3B,MAAM,GAAGM,IAAI,CAACN,MAAM;EACxB,IAAIP,KAAK,GAAGa,IAAI,CAACb,KAAK;EACtB,IAAImC,UAAU,GAAG5B,MAAM,GAAG,CAAC;EAC3B,IAAIE,CAAC,GAAGI,IAAI,CAACJ,CAAC;EACd,IAAIE,CAAC,GAAGE,IAAI,CAACF,CAAC;EACd,IAAIN,SAAS,GAAG,MAAM;EACtB,IAAI+B,iBAAiB,GAAG,KAAK;EAC7B,IAAIJ,YAAY,YAAYK,KAAK,EAAE;IAC/B5B,CAAC,IAAIe,YAAY,CAACQ,YAAY,CAAC,CAAC,CAAC,EAAEnB,IAAI,CAACb,KAAK,CAAC;IAC9CW,CAAC,IAAIa,YAAY,CAACQ,YAAY,CAAC,CAAC,CAAC,EAAEnB,IAAI,CAACN,MAAM,CAAC;IAC/CF,SAAS,GAAG,IAAI;IAChB+B,iBAAiB,GAAG,IAAI;EAC5B,CAAC,MACI;IACD,QAAQJ,YAAY;MAChB,KAAK,MAAM;QACPvB,CAAC,IAAIyB,QAAQ;QACbvB,CAAC,IAAIwB,UAAU;QACf9B,SAAS,GAAG,OAAO;QACnB+B,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,OAAO;QACR3B,CAAC,IAAIyB,QAAQ,GAAGlC,KAAK;QACrBW,CAAC,IAAIwB,UAAU;QACfC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,KAAK;QACN3B,CAAC,IAAIT,KAAK,GAAG,CAAC;QACdW,CAAC,IAAIuB,QAAQ;QACb7B,SAAS,GAAG,QAAQ;QACpB+B,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,QAAQ;QACT3B,CAAC,IAAIT,KAAK,GAAG,CAAC;QACdW,CAAC,IAAIJ,MAAM,GAAG2B,QAAQ;QACtB7B,SAAS,GAAG,QAAQ;QACpB;MACJ,KAAK,QAAQ;QACTI,CAAC,IAAIT,KAAK,GAAG,CAAC;QACdW,CAAC,IAAIwB,UAAU;QACf9B,SAAS,GAAG,QAAQ;QACpB+B,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,YAAY;QACb3B,CAAC,IAAIyB,QAAQ;QACbvB,CAAC,IAAIwB,UAAU;QACfC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,aAAa;QACd3B,CAAC,IAAIT,KAAK,GAAGkC,QAAQ;QACrBvB,CAAC,IAAIwB,UAAU;QACf9B,SAAS,GAAG,OAAO;QACnB+B,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,WAAW;QACZ3B,CAAC,IAAIT,KAAK,GAAG,CAAC;QACdW,CAAC,IAAIuB,QAAQ;QACb7B,SAAS,GAAG,QAAQ;QACpB;MACJ,KAAK,cAAc;QACfI,CAAC,IAAIT,KAAK,GAAG,CAAC;QACdW,CAAC,IAAIJ,MAAM,GAAG2B,QAAQ;QACtB7B,SAAS,GAAG,QAAQ;QACpB+B,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,eAAe;QAChB3B,CAAC,IAAIyB,QAAQ;QACbvB,CAAC,IAAIuB,QAAQ;QACb;MACJ,KAAK,gBAAgB;QACjBzB,CAAC,IAAIT,KAAK,GAAGkC,QAAQ;QACrBvB,CAAC,IAAIuB,QAAQ;QACb7B,SAAS,GAAG,OAAO;QACnB;MACJ,KAAK,kBAAkB;QACnBI,CAAC,IAAIyB,QAAQ;QACbvB,CAAC,IAAIJ,MAAM,GAAG2B,QAAQ;QACtBE,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,mBAAmB;QACpB3B,CAAC,IAAIT,KAAK,GAAGkC,QAAQ;QACrBvB,CAAC,IAAIJ,MAAM,GAAG2B,QAAQ;QACtB7B,SAAS,GAAG,OAAO;QACnB+B,iBAAiB,GAAG,QAAQ;QAC5B;IACR;EACJ;EACAN,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACfA,GAAG,CAACrB,CAAC,GAAGA,CAAC;EACTqB,GAAG,CAACnB,CAAC,GAAGA,CAAC;EACTmB,GAAG,CAACQ,KAAK,GAAGjC,SAAS;EACrByB,GAAG,CAACP,aAAa,GAAGa,iBAAiB;EACrC,OAAON,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}