{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM\\\\frontend\\\\src\\\\pages\\\\Quotation\\\\QuotationList.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { Table, Card, Button, Input, Select, Space, Tag, Typography, Row, Col, Statistic, Modal, message, Tooltip, Progress } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined, ExportOutlined, FileTextOutlined, SwapOutlined } from '@ant-design/icons';\nimport { selectFilteredQuotations, selectQuotationLoading, selectQuotationFilters, selectQuotationPagination, setSearchKeyword, setFilters, setPagination, resetFilters, deleteQuotation, convertToOrder } from '../../store/slices/quotationSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst QuotationList = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const quotations = useSelector(selectFilteredQuotations);\n  const loading = useSelector(selectQuotationLoading);\n  const filters = useSelector(selectQuotationFilters);\n  const pagination = useSelector(selectQuotationPagination);\n  const handleSearch = value => {\n    dispatch(setSearchKeyword(value));\n    dispatch(setPagination({\n      current: 1\n    }));\n  };\n  const handleFilterChange = (key, value) => {\n    dispatch(setFilters({\n      [key]: value\n    }));\n    dispatch(setPagination({\n      current: 1\n    }));\n  };\n  const handleTableChange = paginationConfig => {\n    dispatch(setPagination({\n      current: paginationConfig.current,\n      pageSize: paginationConfig.pageSize\n    }));\n  };\n  const handleDelete = quotation => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除报价单\"${quotation.id}\"吗？`,\n      onOk: () => {\n        dispatch(deleteQuotation(quotation.id));\n        message.success('删除成功');\n      }\n    });\n  };\n  const handleConvertToOrder = quotation => {\n    Modal.confirm({\n      title: '转换为订单',\n      content: `确定要将报价单\"${quotation.id}\"转换为订单吗？`,\n      onOk: () => {\n        const orderId = `ORD-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`;\n        dispatch(convertToOrder({\n          quotationId: quotation.id,\n          orderId\n        }));\n        message.success('转换成功');\n      }\n    });\n  };\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'draft': 'orange',\n      'pending': 'blue',\n      'accepted': 'green',\n      'rejected': 'red',\n      'expired': 'gray'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      'draft': '草稿',\n      'pending': '待确认',\n      'accepted': '已接受',\n      'rejected': '已拒绝',\n      'expired': '已过期'\n    };\n    return texts[status] || status;\n  };\n  const columns = [{\n    title: '报价单号',\n    dataIndex: 'id',\n    key: 'id',\n    width: 150,\n    fixed: 'left',\n    render: text => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      style: {\n        padding: 0,\n        fontWeight: 'bold'\n      },\n      onClick: () => navigate(`/quotations/${text}`),\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户名称',\n    dataIndex: 'customerName',\n    key: 'customerName',\n    width: 200\n  }, {\n    title: '报价标题',\n    dataIndex: 'title',\n    key: 'title',\n    width: 250,\n    ellipsis: true\n  }, {\n    title: '报价金额',\n    dataIndex: 'totalAmount',\n    key: 'totalAmount',\n    width: 120,\n    render: amount => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#1890ff'\n      },\n      children: formatCurrency(amount)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '关联BOM',\n    key: 'bom',\n    width: 150,\n    render: (_, record) => record.bomId ? /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '12px',\n          color: '#1890ff'\n        },\n        children: record.bomId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '11px',\n          color: '#8c8c8c'\n        },\n        children: record.bomName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      style: {\n        fontSize: '12px'\n      },\n      children: \"\\u672A\\u5173\\u8054BOM\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '预估利润',\n    key: 'profit',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '12px',\n          color: '#52c41a'\n        },\n        children: formatCurrency(record.estimatedProfit)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '12px'\n        },\n        children: [\"\\u5229\\u6DA6\\u7387: \", record.profitMargin, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '业务员',\n    dataIndex: 'salesperson',\n    key: 'salesperson',\n    width: 100\n  }, {\n    title: '有效期',\n    dataIndex: 'validUntil',\n    key: 'validUntil',\n    width: 100,\n    render: date => {\n      const isExpired = new Date(date) < new Date();\n      return /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: isExpired ? '#ff4d4f' : 'inherit'\n        },\n        children: date\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createTime',\n    key: 'createTime',\n    width: 100\n  }, {\n    title: '操作',\n    key: 'actions',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => navigate(`/quotations/${record.id}`)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          disabled: record.status === 'accepted'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), record.status === 'pending' && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u8F6C\\u4E3A\\u8BA2\\u5355\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(SwapOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 23\n          }, this),\n          size: \"small\",\n          onClick: () => handleConvertToOrder(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          danger: true,\n          onClick: () => handleDelete(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 计算统计数据\n  const stats = {\n    total: quotations.length,\n    pending: quotations.filter(q => q.status === 'pending').length,\n    accepted: quotations.filter(q => q.status === 'accepted').length,\n    totalAmount: quotations.reduce((sum, q) => sum + q.totalAmount, 0),\n    totalProfit: quotations.reduce((sum, q) => sum + q.estimatedProfit, 0)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: \"\\u62A5\\u4EF7\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5237\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 40\n          }, this),\n          onClick: () => navigate('/quotations/new'),\n          children: \"\\u65B0\\u5EFA\\u62A5\\u4EF7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u62A5\\u4EF7\\u603B\\u6570\",\n            value: stats.total,\n            suffix: \"\\u4E2A\",\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u786E\\u8BA4\",\n            value: stats.pending,\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u63A5\\u53D7\",\n            value: stats.accepted,\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u62A5\\u4EF7\\u603B\\u989D\",\n            value: stats.totalAmount,\n            formatter: value => formatCurrency(value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9884\\u4F30\\u5229\\u6DA6\",\n            value: stats.totalProfit,\n            formatter: value => formatCurrency(value),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Input.Search, {\n            placeholder: \"\\u641C\\u7D22\\u62A5\\u4EF7\\u5355\\u53F7\\u3001\\u5BA2\\u6237\\u540D\\u79F0\\u3001\\u6807\\u9898\",\n            allowClear: true,\n            onSearch: handleSearch,\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u72B6\\u6001\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: filters.status,\n            onChange: value => handleFilterChange('status', value),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"draft\",\n              children: \"\\u8349\\u7A3F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"pending\",\n              children: \"\\u5F85\\u786E\\u8BA4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"accepted\",\n              children: \"\\u5DF2\\u63A5\\u53D7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"rejected\",\n              children: \"\\u5DF2\\u62D2\\u7EDD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"expired\",\n              children: \"\\u5DF2\\u8FC7\\u671F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u4E1A\\u52A1\\u5458\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: filters.salesperson,\n            onChange: value => handleFilterChange('salesperson', value),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u674E\\u4E1A\\u52A1\",\n              children: \"\\u674E\\u4E1A\\u52A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u5F20\\u4E1A\\u52A1\",\n              children: \"\\u5F20\\u4E1A\\u52A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u738B\\u4E1A\\u52A1\",\n              children: \"\\u738B\\u4E1A\\u52A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => dispatch(resetFilters()),\n            children: \"\\u91CD\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: quotations,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          ...pagination,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        },\n        onChange: handleTableChange,\n        scroll: {\n          x: 1400\n        },\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 299,\n    columnNumber: 5\n  }, this);\n};\n_s(QuotationList, \"kZmH36RzNMx5GgALGeBnVg+DEgs=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector, useSelector, useSelector];\n});\n_c = QuotationList;\nexport default QuotationList;\nvar _c;\n$RefreshReg$(_c, \"QuotationList\");", "map": {"version": 3, "names": ["React", "useState", "useSelector", "useDispatch", "useNavigate", "Table", "Card", "<PERSON><PERSON>", "Input", "Select", "Space", "Tag", "Typography", "Row", "Col", "Statistic", "Modal", "message", "<PERSON><PERSON><PERSON>", "Progress", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ReloadOutlined", "ExportOutlined", "FileTextOutlined", "SwapOutlined", "selectFilteredQuotations", "selectQuotationLoading", "selectQuotationFilters", "selectQuotationPagination", "setSearchKeyword", "setFilters", "setPagination", "resetFilters", "deleteQuotation", "convertToOrder", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "QuotationList", "_s", "dispatch", "navigate", "quotations", "loading", "filters", "pagination", "handleSearch", "value", "current", "handleFilterChange", "key", "handleTableChange", "paginationConfig", "pageSize", "handleDelete", "quotation", "confirm", "title", "content", "id", "onOk", "success", "handleConvertToOrder", "orderId", "Date", "getFullYear", "String", "Math", "floor", "random", "padStart", "quotationId", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "getStatusColor", "status", "colors", "getStatusText", "texts", "columns", "dataIndex", "width", "fixed", "render", "text", "type", "padding", "fontWeight", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ellipsis", "amount", "strong", "color", "_", "record", "bomId", "direction", "size", "fontSize", "bom<PERSON>ame", "estimatedProfit", "profitMargin", "date", "isExpired", "icon", "disabled", "danger", "stats", "total", "length", "pending", "filter", "q", "accepted", "totalAmount", "reduce", "sum", "totalProfit", "marginBottom", "display", "justifyContent", "alignItems", "level", "margin", "gutter", "xs", "sm", "lg", "suffix", "prefix", "formatter", "valueStyle", "md", "Search", "placeholder", "allowClear", "onSearch", "onChange", "salesperson", "dataSource", "<PERSON><PERSON><PERSON>", "showSizeChanger", "showQuickJumper", "showTotal", "range", "scroll", "x", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/pages/Quotation/QuotationList.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Card,\n  Button,\n  Input,\n  Select,\n  Space,\n  Tag,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Modal,\n  message,\n  Tooltip,\n  Progress\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  ExportOutlined,\n  FileTextOutlined,\n  SwapOutlined\n} from '@ant-design/icons';\n\nimport {\n  selectFilteredQuotations,\n  selectQuotationLoading,\n  selectQuotationFilters,\n  selectQuotationPagination,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters,\n  deleteQuotation,\n  convertToOrder\n} from '../../store/slices/quotationSlice';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nconst QuotationList = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const quotations = useSelector(selectFilteredQuotations);\n  const loading = useSelector(selectQuotationLoading);\n  const filters = useSelector(selectQuotationFilters);\n  const pagination = useSelector(selectQuotationPagination);\n\n  const handleSearch = (value) => {\n    dispatch(setSearchKeyword(value));\n    dispatch(setPagination({ current: 1 }));\n  };\n\n  const handleFilterChange = (key, value) => {\n    dispatch(setFilters({ [key]: value }));\n    dispatch(setPagination({ current: 1 }));\n  };\n\n  const handleTableChange = (paginationConfig) => {\n    dispatch(setPagination({\n      current: paginationConfig.current,\n      pageSize: paginationConfig.pageSize\n    }));\n  };\n\n  const handleDelete = (quotation) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除报价单\"${quotation.id}\"吗？`,\n      onOk: () => {\n        dispatch(deleteQuotation(quotation.id));\n        message.success('删除成功');\n      }\n    });\n  };\n\n  const handleConvertToOrder = (quotation) => {\n    Modal.confirm({\n      title: '转换为订单',\n      content: `确定要将报价单\"${quotation.id}\"转换为订单吗？`,\n      onOk: () => {\n        const orderId = `ORD-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`;\n        dispatch(convertToOrder({ quotationId: quotation.id, orderId }));\n        message.success('转换成功');\n      }\n    });\n  };\n\n  const formatCurrency = (value) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      'draft': 'orange',\n      'pending': 'blue',\n      'accepted': 'green',\n      'rejected': 'red',\n      'expired': 'gray'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      'draft': '草稿',\n      'pending': '待确认',\n      'accepted': '已接受',\n      'rejected': '已拒绝',\n      'expired': '已过期'\n    };\n    return texts[status] || status;\n  };\n\n  const columns = [\n    {\n      title: '报价单号',\n      dataIndex: 'id',\n      key: 'id',\n      width: 150,\n      fixed: 'left',\n      render: (text) => (\n        <Button \n          type=\"link\" \n          style={{ padding: 0, fontWeight: 'bold' }}\n          onClick={() => navigate(`/quotations/${text}`)}\n        >\n          {text}\n        </Button>\n      )\n    },\n    {\n      title: '客户名称',\n      dataIndex: 'customerName',\n      key: 'customerName',\n      width: 200\n    },\n    {\n      title: '报价标题',\n      dataIndex: 'title',\n      key: 'title',\n      width: 250,\n      ellipsis: true\n    },\n    {\n      title: '报价金额',\n      dataIndex: 'totalAmount',\n      key: 'totalAmount',\n      width: 120,\n      render: (amount) => (\n        <Text strong style={{ color: '#1890ff' }}>\n          {formatCurrency(amount)}\n        </Text>\n      )\n    },\n    {\n      title: '关联BOM',\n      key: 'bom',\n      width: 150,\n      render: (_, record) => (\n        record.bomId ? (\n          <Space direction=\"vertical\" size={0}>\n            <Text style={{ fontSize: '12px', color: '#1890ff' }}>\n              {record.bomId}\n            </Text>\n            <Text style={{ fontSize: '11px', color: '#8c8c8c' }}>\n              {record.bomName}\n            </Text>\n          </Space>\n        ) : (\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            未关联BOM\n          </Text>\n        )\n      )\n    },\n    {\n      title: '预估利润',\n      key: 'profit',\n      width: 150,\n      render: (_, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ fontSize: '12px', color: '#52c41a' }}>\n            {formatCurrency(record.estimatedProfit)}\n          </Text>\n          <Text style={{ fontSize: '12px' }}>\n            利润率: {record.profitMargin}%\n          </Text>\n        </Space>\n      )\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      )\n    },\n    {\n      title: '业务员',\n      dataIndex: 'salesperson',\n      key: 'salesperson',\n      width: 100\n    },\n    {\n      title: '有效期',\n      dataIndex: 'validUntil',\n      key: 'validUntil',\n      width: 100,\n      render: (date) => {\n        const isExpired = new Date(date) < new Date();\n        return (\n          <Text style={{ color: isExpired ? '#ff4d4f' : 'inherit' }}>\n            {date}\n          </Text>\n        );\n      }\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      key: 'createTime',\n      width: 100\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 150,\n      fixed: 'right',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button \n              type=\"text\" \n              icon={<EyeOutlined />} \n              size=\"small\"\n              onClick={() => navigate(`/quotations/${record.id}`)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button \n              type=\"text\" \n              icon={<EditOutlined />} \n              size=\"small\"\n              disabled={record.status === 'accepted'}\n            />\n          </Tooltip>\n          {record.status === 'pending' && (\n            <Tooltip title=\"转为订单\">\n              <Button \n                type=\"text\" \n                icon={<SwapOutlined />} \n                size=\"small\"\n                onClick={() => handleConvertToOrder(record)}\n              />\n            </Tooltip>\n          )}\n          <Tooltip title=\"删除\">\n            <Button \n              type=\"text\" \n              icon={<DeleteOutlined />} \n              size=\"small\"\n              danger\n              onClick={() => handleDelete(record)}\n            />\n          </Tooltip>\n        </Space>\n      )\n    }\n  ];\n\n  // 计算统计数据\n  const stats = {\n    total: quotations.length,\n    pending: quotations.filter(q => q.status === 'pending').length,\n    accepted: quotations.filter(q => q.status === 'accepted').length,\n    totalAmount: quotations.reduce((sum, q) => sum + q.totalAmount, 0),\n    totalProfit: quotations.reduce((sum, q) => sum + q.estimatedProfit, 0)\n  };\n\n  return (\n    <div>\n      {/* 页面标题和操作 */}\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Title level={2} style={{ margin: 0 }}>报价管理</Title>\n        <Space>\n          <Button icon={<ReloadOutlined />}>刷新</Button>\n          <Button icon={<ExportOutlined />}>导出</Button>\n          <Button type=\"primary\" icon={<PlusOutlined />} onClick={() => navigate('/quotations/new')}>\n            新建报价\n          </Button>\n        </Space>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"报价总数\" \n              value={stats.total} \n              suffix=\"个\"\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"待确认\" value={stats.pending} suffix=\"个\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"已接受\" value={stats.accepted} suffix=\"个\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"报价总额\" \n              value={stats.totalAmount} \n              formatter={(value) => formatCurrency(value)}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"预估利润\" \n              value={stats.totalProfit} \n              formatter={(value) => formatCurrency(value)}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索和筛选 */}\n      <Card style={{ marginBottom: '16px' }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Input.Search\n              placeholder=\"搜索报价单号、客户名称、标题\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Select\n              placeholder=\"状态\"\n              allowClear\n              style={{ width: '100%' }}\n              value={filters.status}\n              onChange={(value) => handleFilterChange('status', value)}\n            >\n              <Option value=\"draft\">草稿</Option>\n              <Option value=\"pending\">待确认</Option>\n              <Option value=\"accepted\">已接受</Option>\n              <Option value=\"rejected\">已拒绝</Option>\n              <Option value=\"expired\">已过期</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Select\n              placeholder=\"业务员\"\n              allowClear\n              style={{ width: '100%' }}\n              value={filters.salesperson}\n              onChange={(value) => handleFilterChange('salesperson', value)}\n            >\n              <Option value=\"李业务\">李业务</Option>\n              <Option value=\"张业务\">张业务</Option>\n              <Option value=\"王业务\">王业务</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Button onClick={() => dispatch(resetFilters())}>重置</Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 报价单列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={quotations}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            ...pagination,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => \n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n          }}\n          onChange={handleTableChange}\n          scroll={{ x: 1400 }}\n          size=\"small\"\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default QuotationList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,YAAY,QACP,mBAAmB;AAE1B,SACEC,wBAAwB,EACxBC,sBAAsB,EACtBC,sBAAsB,EACtBC,yBAAyB,EACzBC,gBAAgB,EAChBC,UAAU,EACVC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfC,cAAc,QACT,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG9B,UAAU;AAClC,MAAM;EAAE+B;AAAO,CAAC,GAAGlC,MAAM;AAEzB,MAAMmC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,UAAU,GAAG9C,WAAW,CAAC2B,wBAAwB,CAAC;EACxD,MAAMoB,OAAO,GAAG/C,WAAW,CAAC4B,sBAAsB,CAAC;EACnD,MAAMoB,OAAO,GAAGhD,WAAW,CAAC6B,sBAAsB,CAAC;EACnD,MAAMoB,UAAU,GAAGjD,WAAW,CAAC8B,yBAAyB,CAAC;EAEzD,MAAMoB,YAAY,GAAIC,KAAK,IAAK;IAC9BP,QAAQ,CAACb,gBAAgB,CAACoB,KAAK,CAAC,CAAC;IACjCP,QAAQ,CAACX,aAAa,CAAC;MAAEmB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACC,GAAG,EAAEH,KAAK,KAAK;IACzCP,QAAQ,CAACZ,UAAU,CAAC;MAAE,CAACsB,GAAG,GAAGH;IAAM,CAAC,CAAC,CAAC;IACtCP,QAAQ,CAACX,aAAa,CAAC;MAAEmB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,MAAMG,iBAAiB,GAAIC,gBAAgB,IAAK;IAC9CZ,QAAQ,CAACX,aAAa,CAAC;MACrBmB,OAAO,EAAEI,gBAAgB,CAACJ,OAAO;MACjCK,QAAQ,EAAED,gBAAgB,CAACC;IAC7B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,YAAY,GAAIC,SAAS,IAAK;IAClC7C,KAAK,CAAC8C,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,YAAYH,SAAS,CAACI,EAAE,KAAK;MACtCC,IAAI,EAAEA,CAAA,KAAM;QACVpB,QAAQ,CAACT,eAAe,CAACwB,SAAS,CAACI,EAAE,CAAC,CAAC;QACvChD,OAAO,CAACkD,OAAO,CAAC,MAAM,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,oBAAoB,GAAIP,SAAS,IAAK;IAC1C7C,KAAK,CAAC8C,OAAO,CAAC;MACZC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,WAAWH,SAAS,CAACI,EAAE,UAAU;MAC1CC,IAAI,EAAEA,CAAA,KAAM;QACV,MAAMG,OAAO,GAAG,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC9G9B,QAAQ,CAACR,cAAc,CAAC;UAAEuC,WAAW,EAAEhB,SAAS,CAACI,EAAE;UAAEI;QAAQ,CAAC,CAAC,CAAC;QAChEpD,OAAO,CAACkD,OAAO,CAAC,MAAM,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,cAAc,GAAIzB,KAAK,IAAK;IAChC,OAAO,IAAI0B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAAChC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMiC,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,MAAM,GAAG;MACb,OAAO,EAAE,QAAQ;MACjB,SAAS,EAAE,MAAM;MACjB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,KAAK;MACjB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAME,aAAa,GAAIF,MAAM,IAAK;IAChC,MAAMG,KAAK,GAAG;MACZ,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,KAAK;MACjB,UAAU,EAAE,KAAK;MACjB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,KAAK,CAACH,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMI,OAAO,GAAG,CACd;IACE5B,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,IAAI;IACfpC,GAAG,EAAE,IAAI;IACTqC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAGC,IAAI,iBACXxD,OAAA,CAACjC,MAAM;MACL0F,IAAI,EAAC,MAAM;MACXhB,KAAK,EAAE;QAAEiB,OAAO,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAO,CAAE;MAC1CC,OAAO,EAAEA,CAAA,KAAMrD,QAAQ,CAAC,eAAeiD,IAAI,EAAE,CAAE;MAAAK,QAAA,EAE9CL;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAEZ,CAAC,EACD;IACE1C,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,cAAc;IACzBpC,GAAG,EAAE,cAAc;IACnBqC,KAAK,EAAE;EACT,CAAC,EACD;IACE9B,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,OAAO;IAClBpC,GAAG,EAAE,OAAO;IACZqC,KAAK,EAAE,GAAG;IACVa,QAAQ,EAAE;EACZ,CAAC,EACD;IACE3C,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,aAAa;IACxBpC,GAAG,EAAE,aAAa;IAClBqC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGY,MAAM,iBACbnE,OAAA,CAACE,IAAI;MAACkE,MAAM;MAAC3B,KAAK,EAAE;QAAE4B,KAAK,EAAE;MAAU,CAAE;MAAAR,QAAA,EACtCvB,cAAc,CAAC6B,MAAM;IAAC;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAEV,CAAC,EACD;IACE1C,KAAK,EAAE,OAAO;IACdP,GAAG,EAAE,KAAK;IACVqC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,KAChBA,MAAM,CAACC,KAAK,gBACVxE,OAAA,CAAC9B,KAAK;MAACuG,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAb,QAAA,gBAClC7D,OAAA,CAACE,IAAI;QAACuC,KAAK,EAAE;UAAEkC,QAAQ,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EACjDU,MAAM,CAACC;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACPjE,OAAA,CAACE,IAAI;QAACuC,KAAK,EAAE;UAAEkC,QAAQ,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EACjDU,MAAM,CAACK;MAAO;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,gBAERjE,OAAA,CAACE,IAAI;MAACuD,IAAI,EAAC,WAAW;MAAChB,KAAK,EAAE;QAAEkC,QAAQ,EAAE;MAAO,CAAE;MAAAd,QAAA,EAAC;IAEpD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAGZ,CAAC,EACD;IACE1C,KAAK,EAAE,MAAM;IACbP,GAAG,EAAE,QAAQ;IACbqC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChBvE,OAAA,CAAC9B,KAAK;MAACuG,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAb,QAAA,gBAClC7D,OAAA,CAACE,IAAI;QAACuC,KAAK,EAAE;UAAEkC,QAAQ,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EACjDvB,cAAc,CAACiC,MAAM,CAACM,eAAe;MAAC;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACPjE,OAAA,CAACE,IAAI;QAACuC,KAAK,EAAE;UAAEkC,QAAQ,EAAE;QAAO,CAAE;QAAAd,QAAA,GAAC,sBAC5B,EAACU,MAAM,CAACO,YAAY,EAAC,GAC5B;MAAA;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACE1C,KAAK,EAAE,IAAI;IACX6B,SAAS,EAAE,QAAQ;IACnBpC,GAAG,EAAE,QAAQ;IACbqC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGR,MAAM,iBACb/C,OAAA,CAAC7B,GAAG;MAACkG,KAAK,EAAEvB,cAAc,CAACC,MAAM,CAAE;MAAAc,QAAA,EAChCZ,aAAa,CAACF,MAAM;IAAC;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACE1C,KAAK,EAAE,KAAK;IACZ6B,SAAS,EAAE,aAAa;IACxBpC,GAAG,EAAE,aAAa;IAClBqC,KAAK,EAAE;EACT,CAAC,EACD;IACE9B,KAAK,EAAE,KAAK;IACZ6B,SAAS,EAAE,YAAY;IACvBpC,GAAG,EAAE,YAAY;IACjBqC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGwB,IAAI,IAAK;MAChB,MAAMC,SAAS,GAAG,IAAIlD,IAAI,CAACiD,IAAI,CAAC,GAAG,IAAIjD,IAAI,CAAC,CAAC;MAC7C,oBACE9B,OAAA,CAACE,IAAI;QAACuC,KAAK,EAAE;UAAE4B,KAAK,EAAEW,SAAS,GAAG,SAAS,GAAG;QAAU,CAAE;QAAAnB,QAAA,EACvDkB;MAAI;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEX;EACF,CAAC,EACD;IACE1C,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,YAAY;IACvBpC,GAAG,EAAE,YAAY;IACjBqC,KAAK,EAAE;EACT,CAAC,EACD;IACE9B,KAAK,EAAE,IAAI;IACXP,GAAG,EAAE,SAAS;IACdqC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChBvE,OAAA,CAAC9B,KAAK;MAACwG,IAAI,EAAC,OAAO;MAAAb,QAAA,gBACjB7D,OAAA,CAACtB,OAAO;QAAC6C,KAAK,EAAC,0BAAM;QAAAsC,QAAA,eACnB7D,OAAA,CAACjC,MAAM;UACL0F,IAAI,EAAC,MAAM;UACXwB,IAAI,eAAEjF,OAAA,CAAChB,WAAW;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBS,IAAI,EAAC,OAAO;UACZd,OAAO,EAAEA,CAAA,KAAMrD,QAAQ,CAAC,eAAegE,MAAM,CAAC9C,EAAE,EAAE;QAAE;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVjE,OAAA,CAACtB,OAAO;QAAC6C,KAAK,EAAC,cAAI;QAAAsC,QAAA,eACjB7D,OAAA,CAACjC,MAAM;UACL0F,IAAI,EAAC,MAAM;UACXwB,IAAI,eAAEjF,OAAA,CAAClB,YAAY;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBS,IAAI,EAAC,OAAO;UACZQ,QAAQ,EAAEX,MAAM,CAACxB,MAAM,KAAK;QAAW;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EACTM,MAAM,CAACxB,MAAM,KAAK,SAAS,iBAC1B/C,OAAA,CAACtB,OAAO;QAAC6C,KAAK,EAAC,0BAAM;QAAAsC,QAAA,eACnB7D,OAAA,CAACjC,MAAM;UACL0F,IAAI,EAAC,MAAM;UACXwB,IAAI,eAAEjF,OAAA,CAACZ,YAAY;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBS,IAAI,EAAC,OAAO;UACZd,OAAO,EAAEA,CAAA,KAAMhC,oBAAoB,CAAC2C,MAAM;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CACV,eACDjE,OAAA,CAACtB,OAAO;QAAC6C,KAAK,EAAC,cAAI;QAAAsC,QAAA,eACjB7D,OAAA,CAACjC,MAAM;UACL0F,IAAI,EAAC,MAAM;UACXwB,IAAI,eAAEjF,OAAA,CAACjB,cAAc;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBS,IAAI,EAAC,OAAO;UACZS,MAAM;UACNvB,OAAO,EAAEA,CAAA,KAAMxC,YAAY,CAACmD,MAAM;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;;EAED;EACA,MAAMmB,KAAK,GAAG;IACZC,KAAK,EAAE7E,UAAU,CAAC8E,MAAM;IACxBC,OAAO,EAAE/E,UAAU,CAACgF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1C,MAAM,KAAK,SAAS,CAAC,CAACuC,MAAM;IAC9DI,QAAQ,EAAElF,UAAU,CAACgF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1C,MAAM,KAAK,UAAU,CAAC,CAACuC,MAAM;IAChEK,WAAW,EAAEnF,UAAU,CAACoF,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAG,GAAGJ,CAAC,CAACE,WAAW,EAAE,CAAC,CAAC;IAClEG,WAAW,EAAEtF,UAAU,CAACoF,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAG,GAAGJ,CAAC,CAACZ,eAAe,EAAE,CAAC;EACvE,CAAC;EAED,oBACE7E,OAAA;IAAA6D,QAAA,gBAEE7D,OAAA;MAAKyC,KAAK,EAAE;QAAEsD,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAArC,QAAA,gBAC3G7D,OAAA,CAACC,KAAK;QAACkG,KAAK,EAAE,CAAE;QAAC1D,KAAK,EAAE;UAAE2D,MAAM,EAAE;QAAE,CAAE;QAAAvC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnDjE,OAAA,CAAC9B,KAAK;QAAA2F,QAAA,gBACJ7D,OAAA,CAACjC,MAAM;UAACkH,IAAI,eAAEjF,OAAA,CAACf,cAAc;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CjE,OAAA,CAACjC,MAAM;UAACkH,IAAI,eAAEjF,OAAA,CAACd,cAAc;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CjE,OAAA,CAACjC,MAAM;UAAC0F,IAAI,EAAC,SAAS;UAACwB,IAAI,eAAEjF,OAAA,CAACpB,YAAY;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACL,OAAO,EAAEA,CAAA,KAAMrD,QAAQ,CAAC,iBAAiB,CAAE;UAAAsD,QAAA,EAAC;QAE3F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjE,OAAA,CAAC3B,GAAG;MAACgI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC5D,KAAK,EAAE;QAAEsD,YAAY,EAAE;MAAO,CAAE;MAAAlC,QAAA,gBACrD7D,OAAA,CAAC1B,GAAG;QAACgI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB7D,OAAA,CAAClC,IAAI;UAAA+F,QAAA,eACH7D,OAAA,CAACzB,SAAS;YACRgD,KAAK,EAAC,0BAAM;YACZV,KAAK,EAAEuE,KAAK,CAACC,KAAM;YACnBoB,MAAM,EAAC,QAAG;YACVC,MAAM,eAAE1G,OAAA,CAACb,gBAAgB;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjE,OAAA,CAAC1B,GAAG;QAACgI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB7D,OAAA,CAAClC,IAAI;UAAA+F,QAAA,eACH7D,OAAA,CAACzB,SAAS;YAACgD,KAAK,EAAC,oBAAK;YAACV,KAAK,EAAEuE,KAAK,CAACG,OAAQ;YAACkB,MAAM,EAAC;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjE,OAAA,CAAC1B,GAAG;QAACgI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB7D,OAAA,CAAClC,IAAI;UAAA+F,QAAA,eACH7D,OAAA,CAACzB,SAAS;YAACgD,KAAK,EAAC,oBAAK;YAACV,KAAK,EAAEuE,KAAK,CAACM,QAAS;YAACe,MAAM,EAAC;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjE,OAAA,CAAC1B,GAAG;QAACgI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB7D,OAAA,CAAClC,IAAI;UAAA+F,QAAA,eACH7D,OAAA,CAACzB,SAAS;YACRgD,KAAK,EAAC,0BAAM;YACZV,KAAK,EAAEuE,KAAK,CAACO,WAAY;YACzBgB,SAAS,EAAG9F,KAAK,IAAKyB,cAAc,CAACzB,KAAK;UAAE;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjE,OAAA,CAAC1B,GAAG;QAACgI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB7D,OAAA,CAAClC,IAAI;UAAA+F,QAAA,eACH7D,OAAA,CAACzB,SAAS;YACRgD,KAAK,EAAC,0BAAM;YACZV,KAAK,EAAEuE,KAAK,CAACU,WAAY;YACzBa,SAAS,EAAG9F,KAAK,IAAKyB,cAAc,CAACzB,KAAK,CAAE;YAC5C+F,UAAU,EAAE;cAAEvC,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjE,OAAA,CAAClC,IAAI;MAAC2E,KAAK,EAAE;QAAEsD,YAAY,EAAE;MAAO,CAAE;MAAAlC,QAAA,eACpC7D,OAAA,CAAC3B,GAAG;QAACgI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAxC,QAAA,gBACpB7D,OAAA,CAAC1B,GAAG;UAACgI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA3C,QAAA,eAChC7D,OAAA,CAAChC,KAAK,CAAC8I,MAAM;YACXC,WAAW,EAAC,sFAAgB;YAC5BC,UAAU;YACVC,QAAQ,EAAErG,YAAa;YACvB6B,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAO;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjE,OAAA,CAAC1B,GAAG;UAACgI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA3C,QAAA,eAC/B7D,OAAA,CAAC/B,MAAM;YACL8I,WAAW,EAAC,cAAI;YAChBC,UAAU;YACVvE,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAO,CAAE;YACzBxC,KAAK,EAAEH,OAAO,CAACqC,MAAO;YACtBmE,QAAQ,EAAGrG,KAAK,IAAKE,kBAAkB,CAAC,QAAQ,EAAEF,KAAK,CAAE;YAAAgD,QAAA,gBAEzD7D,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,OAAO;cAAAgD,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCjE,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,SAAS;cAAAgD,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCjE,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,UAAU;cAAAgD,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCjE,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,UAAU;cAAAgD,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCjE,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,SAAS;cAAAgD,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjE,OAAA,CAAC1B,GAAG;UAACgI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA3C,QAAA,eAC/B7D,OAAA,CAAC/B,MAAM;YACL8I,WAAW,EAAC,oBAAK;YACjBC,UAAU;YACVvE,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAO,CAAE;YACzBxC,KAAK,EAAEH,OAAO,CAACyG,WAAY;YAC3BD,QAAQ,EAAGrG,KAAK,IAAKE,kBAAkB,CAAC,aAAa,EAAEF,KAAK,CAAE;YAAAgD,QAAA,gBAE9D7D,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,oBAAK;cAAAgD,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCjE,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,oBAAK;cAAAgD,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCjE,OAAA,CAACG,MAAM;cAACU,KAAK,EAAC,oBAAK;cAAAgD,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjE,OAAA,CAAC1B,GAAG;UAACgI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA3C,QAAA,eAC/B7D,OAAA,CAACjC,MAAM;YAAC6F,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAACV,YAAY,CAAC,CAAC,CAAE;YAAAiE,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPjE,OAAA,CAAClC,IAAI;MAAA+F,QAAA,eACH7D,OAAA,CAACnC,KAAK;QACJsF,OAAO,EAAEA,OAAQ;QACjBiE,UAAU,EAAE5G,UAAW;QACvB6G,MAAM,EAAC,IAAI;QACX5G,OAAO,EAAEA,OAAQ;QACjBE,UAAU,EAAE;UACV,GAAGA,UAAU;UACb2G,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACnC,KAAK,EAAEoC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQpC,KAAK;QAC1C,CAAE;QACF6B,QAAQ,EAAEjG,iBAAkB;QAC5ByG,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBjD,IAAI,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5D,EAAA,CApXID,aAAa;EAAA,QACAzC,WAAW,EACXC,WAAW,EACTF,WAAW,EACdA,WAAW,EACXA,WAAW,EACRA,WAAW;AAAA;AAAAkK,EAAA,GAN1BxH,aAAa;AAsXnB,eAAeA,aAAa;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}