{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n// 模拟用户数据\nconst mockUsers = [{\n  id: 1,\n  username: 'admin',\n  password: '123456',\n  name: '系统管理员',\n  role: 'admin',\n  avatar: null,\n  phone: '13800138000',\n  email: '<EMAIL>'\n}, {\n  id: 2,\n  username: 'boss',\n  password: '123456',\n  name: '张总',\n  role: 'boss',\n  avatar: null,\n  phone: '13800138001',\n  email: '<EMAIL>'\n}, {\n  id: 3,\n  username: 'sales',\n  password: '123456',\n  name: '李业务',\n  role: 'sales',\n  avatar: null,\n  phone: '13800138002',\n  email: '<EMAIL>'\n}, {\n  id: 4,\n  username: 'finance',\n  password: '123456',\n  name: '王会计',\n  role: 'finance',\n  avatar: null,\n  phone: '13800138003',\n  email: '<EMAIL>'\n}, {\n  id: 5,\n  username: 'production',\n  password: '123456',\n  name: '赵生产',\n  role: 'production',\n  avatar: null,\n  phone: '13800138004',\n  email: '<EMAIL>'\n}];\n\n// 角色权限配置\nconst rolePermissions = {\n  admin: ['*'],\n  // 所有权限\n  boss: ['dashboard:view', 'customer:view', 'order:view', 'quotation:view', 'receivable:view', 'bom:view', 'material:view', 'profit:view', 'report:view', 'settings:manage'],\n  sales: ['dashboard:view', 'customer:manage', 'quotation:manage', 'order:view', 'receivable:view', 'bom:view', 'material:view'],\n  finance: ['dashboard:view', 'order:view', 'receivable:manage', 'profit:view', 'report:view'],\n  production: ['dashboard:view', 'order:manage', 'cost:manage', 'bom:manage', 'material:manage']\n};\nconst initialState = {\n  isAuthenticated: false,\n  user: null,\n  token: null,\n  permissions: [],\n  loading: false,\n  error: null\n};\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    loginStart: state => {\n      state.loading = true;\n      state.error = null;\n    },\n    loginSuccess: (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = true;\n      state.user = action.payload.user;\n      state.token = action.payload.token;\n      state.permissions = rolePermissions[action.payload.user.role] || [];\n      state.error = null;\n\n      // 保存到localStorage\n      localStorage.setItem('token', action.payload.token);\n      localStorage.setItem('user', JSON.stringify(action.payload.user));\n    },\n    loginFailure: (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.permissions = [];\n      state.error = action.payload;\n    },\n    logout: state => {\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.permissions = [];\n      state.error = null;\n\n      // 清除localStorage\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    initializeAuth: state => {\n      const token = localStorage.getItem('token');\n      const user = localStorage.getItem('user');\n      if (token && user) {\n        try {\n          const userData = JSON.parse(user);\n          state.isAuthenticated = true;\n          state.user = userData;\n          state.token = token;\n          state.permissions = rolePermissions[userData.role] || [];\n        } catch (error) {\n          // 清除无效数据\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n    }\n  }\n});\n\n// 模拟登录异步操作\nexport const login = credentials => async dispatch => {\n  dispatch(loginStart());\n  try {\n    // 模拟API调用延迟\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const user = mockUsers.find(u => u.username === credentials.username && u.password === credentials.password);\n    if (user) {\n      const token = `mock_token_${user.id}_${Date.now()}`;\n      const {\n        password,\n        ...userWithoutPassword\n      } = user;\n      dispatch(loginSuccess({\n        user: userWithoutPassword,\n        token\n      }));\n    } else {\n      dispatch(loginFailure('用户名或密码错误'));\n    }\n  } catch (error) {\n    dispatch(loginFailure('登录失败，请重试'));\n  }\n};\nexport const {\n  loginStart,\n  loginSuccess,\n  loginFailure,\n  logout,\n  clearError,\n  initializeAuth\n} = authSlice.actions;\n\n// 选择器\nexport const selectAuth = state => state.auth;\nexport const selectUser = state => state.auth.user;\nexport const selectPermissions = state => state.auth.permissions;\nexport const selectIsAuthenticated = state => state.auth.isAuthenticated;\n\n// 权限检查函数\nexport const hasPermission = (permissions, permission) => {\n  if (!permissions || permissions.length === 0) return false;\n  if (permissions.includes('*')) return true;\n  return permissions.includes(permission);\n};\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "mockUsers", "id", "username", "password", "name", "role", "avatar", "phone", "email", "rolePermissions", "admin", "boss", "sales", "finance", "production", "initialState", "isAuthenticated", "user", "token", "permissions", "loading", "error", "authSlice", "reducers", "loginStart", "state", "loginSuccess", "action", "payload", "localStorage", "setItem", "JSON", "stringify", "loginFailure", "logout", "removeItem", "clearError", "initializeAuth", "getItem", "userData", "parse", "login", "credentials", "dispatch", "Promise", "resolve", "setTimeout", "find", "u", "Date", "now", "userWithoutPassword", "actions", "selectAuth", "auth", "selectUser", "selectPermissions", "selectIsAuthenticated", "hasPermission", "permission", "length", "includes", "reducer"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/store/slices/authSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n// 模拟用户数据\nconst mockUsers = [\n  {\n    id: 1,\n    username: 'admin',\n    password: '123456',\n    name: '系统管理员',\n    role: 'admin',\n    avatar: null,\n    phone: '13800138000',\n    email: '<EMAIL>'\n  },\n  {\n    id: 2,\n    username: 'boss',\n    password: '123456',\n    name: '张总',\n    role: 'boss',\n    avatar: null,\n    phone: '13800138001',\n    email: '<EMAIL>'\n  },\n  {\n    id: 3,\n    username: 'sales',\n    password: '123456',\n    name: '李业务',\n    role: 'sales',\n    avatar: null,\n    phone: '13800138002',\n    email: '<EMAIL>'\n  },\n  {\n    id: 4,\n    username: 'finance',\n    password: '123456',\n    name: '王会计',\n    role: 'finance',\n    avatar: null,\n    phone: '13800138003',\n    email: '<EMAIL>'\n  },\n  {\n    id: 5,\n    username: 'production',\n    password: '123456',\n    name: '赵生产',\n    role: 'production',\n    avatar: null,\n    phone: '13800138004',\n    email: '<EMAIL>'\n  }\n];\n\n// 角色权限配置\nconst rolePermissions = {\n  admin: ['*'], // 所有权限\n  boss: [\n    'dashboard:view',\n    'customer:view',\n    'order:view',\n    'quotation:view',\n    'receivable:view',\n    'bom:view',\n    'material:view',\n    'profit:view',\n    'report:view',\n    'settings:manage'\n  ],\n  sales: [\n    'dashboard:view',\n    'customer:manage',\n    'quotation:manage',\n    'order:view',\n    'receivable:view',\n    'bom:view',\n    'material:view'\n  ],\n  finance: [\n    'dashboard:view',\n    'order:view',\n    'receivable:manage',\n    'profit:view',\n    'report:view'\n  ],\n  production: [\n    'dashboard:view',\n    'order:manage',\n    'cost:manage',\n    'bom:manage',\n    'material:manage'\n  ]\n};\n\nconst initialState = {\n  isAuthenticated: false,\n  user: null,\n  token: null,\n  permissions: [],\n  loading: false,\n  error: null\n};\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    loginStart: (state) => {\n      state.loading = true;\n      state.error = null;\n    },\n    loginSuccess: (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = true;\n      state.user = action.payload.user;\n      state.token = action.payload.token;\n      state.permissions = rolePermissions[action.payload.user.role] || [];\n      state.error = null;\n      \n      // 保存到localStorage\n      localStorage.setItem('token', action.payload.token);\n      localStorage.setItem('user', JSON.stringify(action.payload.user));\n    },\n    loginFailure: (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.permissions = [];\n      state.error = action.payload;\n    },\n    logout: (state) => {\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.permissions = [];\n      state.error = null;\n      \n      // 清除localStorage\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    initializeAuth: (state) => {\n      const token = localStorage.getItem('token');\n      const user = localStorage.getItem('user');\n      \n      if (token && user) {\n        try {\n          const userData = JSON.parse(user);\n          state.isAuthenticated = true;\n          state.user = userData;\n          state.token = token;\n          state.permissions = rolePermissions[userData.role] || [];\n        } catch (error) {\n          // 清除无效数据\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n    }\n  }\n});\n\n// 模拟登录异步操作\nexport const login = (credentials) => async (dispatch) => {\n  dispatch(loginStart());\n  \n  try {\n    // 模拟API调用延迟\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    const user = mockUsers.find(\n      u => u.username === credentials.username && u.password === credentials.password\n    );\n    \n    if (user) {\n      const token = `mock_token_${user.id}_${Date.now()}`;\n      const { password, ...userWithoutPassword } = user;\n      \n      dispatch(loginSuccess({\n        user: userWithoutPassword,\n        token\n      }));\n    } else {\n      dispatch(loginFailure('用户名或密码错误'));\n    }\n  } catch (error) {\n    dispatch(loginFailure('登录失败，请重试'));\n  }\n};\n\nexport const { \n  loginStart, \n  loginSuccess, \n  loginFailure, \n  logout, \n  clearError, \n  initializeAuth \n} = authSlice.actions;\n\n// 选择器\nexport const selectAuth = (state) => state.auth;\nexport const selectUser = (state) => state.auth.user;\nexport const selectPermissions = (state) => state.auth.permissions;\nexport const selectIsAuthenticated = (state) => state.auth.isAuthenticated;\n\n// 权限检查函数\nexport const hasPermission = (permissions, permission) => {\n  if (!permissions || permissions.length === 0) return false;\n  if (permissions.includes('*')) return true;\n  return permissions.includes(permission);\n};\n\nexport default authSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA,MAAMC,SAAS,GAAG,CAChB;EACEC,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE;AACT,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE;AACT,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE;AACT,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,SAAS;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE;AACT,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,QAAQ;EAClBC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE;AACT,CAAC,CACF;;AAED;AACA,MAAMC,eAAe,GAAG;EACtBC,KAAK,EAAE,CAAC,GAAG,CAAC;EAAE;EACdC,IAAI,EAAE,CACJ,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,UAAU,EACV,eAAe,EACf,aAAa,EACb,aAAa,EACb,iBAAiB,CAClB;EACDC,KAAK,EAAE,CACL,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,YAAY,EACZ,iBAAiB,EACjB,UAAU,EACV,eAAe,CAChB;EACDC,OAAO,EAAE,CACP,gBAAgB,EAChB,YAAY,EACZ,mBAAmB,EACnB,aAAa,EACb,aAAa,CACd;EACDC,UAAU,EAAE,CACV,gBAAgB,EAChB,cAAc,EACd,aAAa,EACb,YAAY,EACZ,iBAAiB;AAErB,CAAC;AAED,MAAMC,YAAY,GAAG;EACnBC,eAAe,EAAE,KAAK;EACtBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,EAAE;EACfC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,SAAS,GAAGvB,WAAW,CAAC;EAC5BK,IAAI,EAAE,MAAM;EACZW,YAAY;EACZQ,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACL,OAAO,GAAG,IAAI;MACpBK,KAAK,CAACJ,KAAK,GAAG,IAAI;IACpB,CAAC;IACDK,YAAY,EAAEA,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC/BF,KAAK,CAACL,OAAO,GAAG,KAAK;MACrBK,KAAK,CAACT,eAAe,GAAG,IAAI;MAC5BS,KAAK,CAACR,IAAI,GAAGU,MAAM,CAACC,OAAO,CAACX,IAAI;MAChCQ,KAAK,CAACP,KAAK,GAAGS,MAAM,CAACC,OAAO,CAACV,KAAK;MAClCO,KAAK,CAACN,WAAW,GAAGV,eAAe,CAACkB,MAAM,CAACC,OAAO,CAACX,IAAI,CAACZ,IAAI,CAAC,IAAI,EAAE;MACnEoB,KAAK,CAACJ,KAAK,GAAG,IAAI;;MAElB;MACAQ,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,MAAM,CAACC,OAAO,CAACV,KAAK,CAAC;MACnDW,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACC,OAAO,CAACX,IAAI,CAAC,CAAC;IACnE,CAAC;IACDgB,YAAY,EAAEA,CAACR,KAAK,EAAEE,MAAM,KAAK;MAC/BF,KAAK,CAACL,OAAO,GAAG,KAAK;MACrBK,KAAK,CAACT,eAAe,GAAG,KAAK;MAC7BS,KAAK,CAACR,IAAI,GAAG,IAAI;MACjBQ,KAAK,CAACP,KAAK,GAAG,IAAI;MAClBO,KAAK,CAACN,WAAW,GAAG,EAAE;MACtBM,KAAK,CAACJ,KAAK,GAAGM,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDM,MAAM,EAAGT,KAAK,IAAK;MACjBA,KAAK,CAACT,eAAe,GAAG,KAAK;MAC7BS,KAAK,CAACR,IAAI,GAAG,IAAI;MACjBQ,KAAK,CAACP,KAAK,GAAG,IAAI;MAClBO,KAAK,CAACN,WAAW,GAAG,EAAE;MACtBM,KAAK,CAACJ,KAAK,GAAG,IAAI;;MAElB;MACAQ,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;MAChCN,YAAY,CAACM,UAAU,CAAC,MAAM,CAAC;IACjC,CAAC;IACDC,UAAU,EAAGX,KAAK,IAAK;MACrBA,KAAK,CAACJ,KAAK,GAAG,IAAI;IACpB,CAAC;IACDgB,cAAc,EAAGZ,KAAK,IAAK;MACzB,MAAMP,KAAK,GAAGW,YAAY,CAACS,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMrB,IAAI,GAAGY,YAAY,CAACS,OAAO,CAAC,MAAM,CAAC;MAEzC,IAAIpB,KAAK,IAAID,IAAI,EAAE;QACjB,IAAI;UACF,MAAMsB,QAAQ,GAAGR,IAAI,CAACS,KAAK,CAACvB,IAAI,CAAC;UACjCQ,KAAK,CAACT,eAAe,GAAG,IAAI;UAC5BS,KAAK,CAACR,IAAI,GAAGsB,QAAQ;UACrBd,KAAK,CAACP,KAAK,GAAGA,KAAK;UACnBO,KAAK,CAACN,WAAW,GAAGV,eAAe,CAAC8B,QAAQ,CAAClC,IAAI,CAAC,IAAI,EAAE;QAC1D,CAAC,CAAC,OAAOgB,KAAK,EAAE;UACd;UACAQ,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;UAChCN,YAAY,CAACM,UAAU,CAAC,MAAM,CAAC;QACjC;MACF;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMM,KAAK,GAAIC,WAAW,IAAK,MAAOC,QAAQ,IAAK;EACxDA,QAAQ,CAACnB,UAAU,CAAC,CAAC,CAAC;EAEtB,IAAI;IACF;IACA,MAAM,IAAIoB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,MAAM5B,IAAI,GAAGjB,SAAS,CAAC+C,IAAI,CACzBC,CAAC,IAAIA,CAAC,CAAC9C,QAAQ,KAAKwC,WAAW,CAACxC,QAAQ,IAAI8C,CAAC,CAAC7C,QAAQ,KAAKuC,WAAW,CAACvC,QACzE,CAAC;IAED,IAAIc,IAAI,EAAE;MACR,MAAMC,KAAK,GAAG,cAAcD,IAAI,CAAChB,EAAE,IAAIgD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACnD,MAAM;QAAE/C,QAAQ;QAAE,GAAGgD;MAAoB,CAAC,GAAGlC,IAAI;MAEjD0B,QAAQ,CAACjB,YAAY,CAAC;QACpBT,IAAI,EAAEkC,mBAAmB;QACzBjC;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLyB,QAAQ,CAACV,YAAY,CAAC,UAAU,CAAC,CAAC;IACpC;EACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdsB,QAAQ,CAACV,YAAY,CAAC,UAAU,CAAC,CAAC;EACpC;AACF,CAAC;AAED,OAAO,MAAM;EACXT,UAAU;EACVE,YAAY;EACZO,YAAY;EACZC,MAAM;EACNE,UAAU;EACVC;AACF,CAAC,GAAGf,SAAS,CAAC8B,OAAO;;AAErB;AACA,OAAO,MAAMC,UAAU,GAAI5B,KAAK,IAAKA,KAAK,CAAC6B,IAAI;AAC/C,OAAO,MAAMC,UAAU,GAAI9B,KAAK,IAAKA,KAAK,CAAC6B,IAAI,CAACrC,IAAI;AACpD,OAAO,MAAMuC,iBAAiB,GAAI/B,KAAK,IAAKA,KAAK,CAAC6B,IAAI,CAACnC,WAAW;AAClE,OAAO,MAAMsC,qBAAqB,GAAIhC,KAAK,IAAKA,KAAK,CAAC6B,IAAI,CAACtC,eAAe;;AAE1E;AACA,OAAO,MAAM0C,aAAa,GAAGA,CAACvC,WAAW,EAAEwC,UAAU,KAAK;EACxD,IAAI,CAACxC,WAAW,IAAIA,WAAW,CAACyC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;EAC1D,IAAIzC,WAAW,CAAC0C,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI;EAC1C,OAAO1C,WAAW,CAAC0C,QAAQ,CAACF,UAAU,CAAC;AACzC,CAAC;AAED,eAAerC,SAAS,CAACwC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}