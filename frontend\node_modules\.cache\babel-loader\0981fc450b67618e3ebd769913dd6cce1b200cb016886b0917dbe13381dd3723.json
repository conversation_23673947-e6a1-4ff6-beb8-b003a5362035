{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM\\\\frontend\\\\src\\\\pages\\\\BOM\\\\MaterialList.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { Table, Card, Button, Input, Select, Space, Tag, Typography, Row, Col, Statistic, Modal, Form, InputNumber, message, Tooltip, Badge } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, ExportOutlined, DatabaseOutlined, WarningOutlined } from '@ant-design/icons';\nimport { selectFilteredMaterials, selectBOMLoading, selectBOMFilters, setSearchKeyword, setFilters, resetFilters, addMaterial, updateMaterial, deleteMaterial } from '../../store/slices/bomSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst MaterialList = () => {\n  _s();\n  const dispatch = useDispatch();\n  const materials = useSelector(selectFilteredMaterials);\n  const loading = useSelector(selectBOMLoading);\n  const filters = useSelector(selectBOMFilters);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingMaterial, setEditingMaterial] = useState(null);\n  const [form] = Form.useForm();\n  const handleSearch = value => {\n    dispatch(setSearchKeyword(value));\n  };\n  const handleFilterChange = (key, value) => {\n    dispatch(setFilters({\n      [key]: value\n    }));\n  };\n  const handleAdd = () => {\n    setEditingMaterial(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = material => {\n    setEditingMaterial(material);\n    form.setFieldsValue(material);\n    setModalVisible(true);\n  };\n  const handleDelete = material => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除物料\"${material.name}\"吗？`,\n      onOk: () => {\n        dispatch(deleteMaterial(material.id));\n        message.success('删除成功');\n      }\n    });\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingMaterial) {\n        dispatch(updateMaterial({\n          ...editingMaterial,\n          ...values\n        }));\n        message.success('更新成功');\n      } else {\n        dispatch(addMaterial(values));\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(value);\n  };\n  const getCategoryColor = category => {\n    const colors = {\n      'CPU': 'red',\n      '主板': 'blue',\n      '内存': 'green',\n      '存储': 'orange',\n      '显卡': 'purple',\n      '机箱': 'cyan',\n      '电源': 'gold'\n    };\n    return colors[category] || 'default';\n  };\n  const getStatusColor = status => {\n    return status === 'active' ? 'success' : 'error';\n  };\n  const getStockStatus = (current, min) => {\n    if (current <= min) return {\n      status: 'error',\n      text: '库存不足'\n    };\n    if (current <= min * 2) return {\n      status: 'warning',\n      text: '库存偏低'\n    };\n    return {\n      status: 'success',\n      text: '库存正常'\n    };\n  };\n  const columns = [{\n    title: '物料编码',\n    dataIndex: 'code',\n    key: 'code',\n    width: 150,\n    fixed: 'left',\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#1890ff'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物料名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 200,\n    ellipsis: true\n  }, {\n    title: '规格型号',\n    dataIndex: 'specification',\n    key: 'specification',\n    width: 200,\n    ellipsis: true\n  }, {\n    title: '分类',\n    dataIndex: 'category',\n    key: 'category',\n    width: 100,\n    render: category => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getCategoryColor(category),\n      children: category\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '单位',\n    dataIndex: 'unit',\n    key: 'unit',\n    width: 60\n  }, {\n    title: '标准成本',\n    dataIndex: 'standardCost',\n    key: 'standardCost',\n    width: 100,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '当前成本',\n    dataIndex: 'currentCost',\n    key: 'currentCost',\n    width: 100,\n    render: (cost, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        style: {\n          color: '#52c41a'\n        },\n        children: formatCurrency(cost)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), cost !== record.standardCost && /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: '12px',\n          color: '#8c8c8c'\n        },\n        children: [\"\\u5DEE\\u5F02: \", formatCurrency(cost - record.standardCost)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '库存情况',\n    key: 'stock',\n    width: 120,\n    render: (_, record) => {\n      const stockStatus = getStockStatus(record.stockQuantity, record.minStock);\n      return /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: 0,\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: [\"\\u73B0\\u5B58: \", record.stockQuantity]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n          status: stockStatus.status,\n          text: stockStatus.text,\n          style: {\n            fontSize: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '供应商',\n    dataIndex: 'supplier',\n    key: 'supplier',\n    width: 120\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: getStatusColor(status),\n      text: status === 'active' ? '启用' : '停用'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createTime',\n    key: 'createTime',\n    width: 100\n  }, {\n    title: '操作',\n    key: 'actions',\n    width: 120,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          danger: true,\n          onClick: () => handleDelete(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 计算统计数据\n  const stats = {\n    total: materials.length,\n    active: materials.filter(m => m.status === 'active').length,\n    lowStock: materials.filter(m => m.stockQuantity <= m.minStock).length,\n    categories: new Set(materials.map(m => m.category)).size,\n    totalValue: materials.reduce((sum, m) => sum + m.currentCost * m.stockQuantity, 0)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: \"\\u7269\\u6599\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5237\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 40\n          }, this),\n          onClick: handleAdd,\n          children: \"\\u65B0\\u589E\\u7269\\u6599\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7269\\u6599\\u603B\\u6570\",\n            value: stats.total,\n            suffix: \"\\u4E2A\",\n            prefix: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u542F\\u7528\\u4E2D\",\n            value: stats.active,\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E93\\u5B58\\u4E0D\\u8DB3\",\n            value: stats.lowStock,\n            suffix: \"\\u4E2A\",\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: stats.lowStock > 0 ? '#ff4d4f' : '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7269\\u6599\\u5206\\u7C7B\",\n            value: stats.categories,\n            suffix: \"\\u7C7B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E93\\u5B58\\u603B\\u503C\",\n            value: stats.totalValue,\n            formatter: value => formatCurrency(value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Input.Search, {\n            placeholder: \"\\u641C\\u7D22\\u7269\\u6599\\u7F16\\u7801\\u3001\\u540D\\u79F0\\u3001\\u89C4\\u683C\",\n            allowClear: true,\n            onSearch: handleSearch,\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u5206\\u7C7B\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: filters.category,\n            onChange: value => handleFilterChange('category', value),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"CPU\",\n              children: \"CPU\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u4E3B\\u677F\",\n              children: \"\\u4E3B\\u677F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u5185\\u5B58\",\n              children: \"\\u5185\\u5B58\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u5B58\\u50A8\",\n              children: \"\\u5B58\\u50A8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u663E\\u5361\",\n              children: \"\\u663E\\u5361\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u673A\\u7BB1\",\n              children: \"\\u673A\\u7BB1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u7535\\u6E90\",\n              children: \"\\u7535\\u6E90\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u72B6\\u6001\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: filters.status,\n            onChange: value => handleFilterChange('status', value),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"active\",\n              children: \"\\u542F\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"inactive\",\n              children: \"\\u505C\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => dispatch(resetFilters()),\n            children: \"\\u91CD\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: materials,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        },\n        scroll: {\n          x: 1400\n        },\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingMaterial ? '编辑物料' : '新增物料',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      width: 800,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          status: 'active',\n          unit: '个',\n          minStock: 10,\n          stockQuantity: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"\\u7269\\u6599\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入物料编码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u7F16\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u7269\\u6599\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入物料名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"specification\",\n          label: \"\\u89C4\\u683C\\u578B\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u89C4\\u683C\\u578B\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"category\",\n              label: \"\\u5206\\u7C7B\",\n              rules: [{\n                required: true,\n                message: '请选择分类'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5206\\u7C7B\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"CPU\",\n                  children: \"CPU\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u4E3B\\u677F\",\n                  children: \"\\u4E3B\\u677F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u5185\\u5B58\",\n                  children: \"\\u5185\\u5B58\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u5B58\\u50A8\",\n                  children: \"\\u5B58\\u50A8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u663E\\u5361\",\n                  children: \"\\u663E\\u5361\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u673A\\u7BB1\",\n                  children: \"\\u673A\\u7BB1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u7535\\u6E90\",\n                  children: \"\\u7535\\u6E90\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"unit\",\n              label: \"\\u5355\\u4F4D\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u4E2A\",\n                  children: \"\\u4E2A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u5957\",\n                  children: \"\\u5957\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u53F0\",\n                  children: \"\\u53F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u5757\",\n                  children: \"\\u5757\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u6761\",\n                  children: \"\\u6761\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"active\",\n                  children: \"\\u542F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"inactive\",\n                  children: \"\\u505C\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"standardCost\",\n              label: \"\\u6807\\u51C6\\u6210\\u672C\",\n              rules: [{\n                required: true,\n                message: '请输入标准成本'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                precision: 2,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u51C6\\u6210\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"currentCost\",\n              label: \"\\u5F53\\u524D\\u6210\\u672C\",\n              rules: [{\n                required: true,\n                message: '请输入当前成本'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                precision: 2,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5F53\\u524D\\u6210\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"stockQuantity\",\n              label: \"\\u5E93\\u5B58\\u6570\\u91CF\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5E93\\u5B58\\u6570\\u91CF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"minStock\",\n              label: \"\\u6700\\u4F4E\\u5E93\\u5B58\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u6700\\u4F4E\\u5E93\\u5B58\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"supplier\",\n              label: \"\\u4F9B\\u5E94\\u5546\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F9B\\u5E94\\u5546\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 283,\n    columnNumber: 5\n  }, this);\n};\n_s(MaterialList, \"32pW/EGlJkkBpiQuiNmyLI9uJWs=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector, Form.useForm];\n});\n_c = MaterialList;\nexport default MaterialList;\nvar _c;\n$RefreshReg$(_c, \"MaterialList\");", "map": {"version": 3, "names": ["React", "useState", "useSelector", "useDispatch", "Table", "Card", "<PERSON><PERSON>", "Input", "Select", "Space", "Tag", "Typography", "Row", "Col", "Statistic", "Modal", "Form", "InputNumber", "message", "<PERSON><PERSON><PERSON>", "Badge", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "ReloadOutlined", "ExportOutlined", "DatabaseOutlined", "WarningOutlined", "selectFilteredMaterials", "selectBOMLoading", "selectBOMFilters", "setSearchKeyword", "setFilters", "resetFilters", "addMaterial", "updateMaterial", "deleteMaterial", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "MaterialList", "_s", "dispatch", "materials", "loading", "filters", "modalVisible", "setModalVisible", "editingMaterial", "setEditingMaterial", "form", "useForm", "handleSearch", "value", "handleFilterChange", "key", "handleAdd", "resetFields", "handleEdit", "material", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "confirm", "title", "content", "name", "onOk", "id", "success", "handleModalOk", "values", "validateFields", "error", "console", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "getCategoryColor", "category", "colors", "getStatusColor", "status", "getStockStatus", "current", "min", "text", "columns", "dataIndex", "width", "fixed", "render", "strong", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ellipsis", "cost", "record", "direction", "size", "standardCost", "fontSize", "_", "stockStatus", "stockQuantity", "minStock", "type", "icon", "onClick", "danger", "stats", "total", "length", "active", "filter", "m", "lowStock", "categories", "Set", "map", "totalValue", "reduce", "sum", "currentCost", "marginBottom", "display", "justifyContent", "alignItems", "level", "margin", "gutter", "xs", "sm", "lg", "suffix", "prefix", "valueStyle", "formatter", "md", "Search", "placeholder", "allowClear", "onSearch", "onChange", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "scroll", "x", "open", "onCancel", "destroyOnClose", "layout", "initialValues", "unit", "span", "<PERSON><PERSON>", "label", "rules", "required", "precision", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/pages/BOM/MaterialList.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport {\n  Table,\n  Card,\n  Button,\n  Input,\n  Select,\n  Space,\n  Tag,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Modal,\n  Form,\n  InputNumber,\n  message,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  ExportOutlined,\n  DatabaseOutlined,\n  WarningOutlined\n} from '@ant-design/icons';\n\nimport {\n  selectFilteredMaterials,\n  selectBOMLoading,\n  selectBOMFilters,\n  setSearchKeyword,\n  setFilters,\n  resetFilters,\n  addMaterial,\n  updateMaterial,\n  deleteMaterial\n} from '../../store/slices/bomSlice';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nconst MaterialList = () => {\n  const dispatch = useDispatch();\n  const materials = useSelector(selectFilteredMaterials);\n  const loading = useSelector(selectBOMLoading);\n  const filters = useSelector(selectBOMFilters);\n\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingMaterial, setEditingMaterial] = useState(null);\n  const [form] = Form.useForm();\n\n  const handleSearch = (value) => {\n    dispatch(setSearchKeyword(value));\n  };\n\n  const handleFilterChange = (key, value) => {\n    dispatch(setFilters({ [key]: value }));\n  };\n\n  const handleAdd = () => {\n    setEditingMaterial(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (material) => {\n    setEditingMaterial(material);\n    form.setFieldsValue(material);\n    setModalVisible(true);\n  };\n\n  const handleDelete = (material) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除物料\"${material.name}\"吗？`,\n      onOk: () => {\n        dispatch(deleteMaterial(material.id));\n        message.success('删除成功');\n      }\n    });\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingMaterial) {\n        dispatch(updateMaterial({ ...editingMaterial, ...values }));\n        message.success('更新成功');\n      } else {\n        dispatch(addMaterial(values));\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const formatCurrency = (value) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(value);\n  };\n\n  const getCategoryColor = (category) => {\n    const colors = {\n      'CPU': 'red',\n      '主板': 'blue',\n      '内存': 'green',\n      '存储': 'orange',\n      '显卡': 'purple',\n      '机箱': 'cyan',\n      '电源': 'gold'\n    };\n    return colors[category] || 'default';\n  };\n\n  const getStatusColor = (status) => {\n    return status === 'active' ? 'success' : 'error';\n  };\n\n  const getStockStatus = (current, min) => {\n    if (current <= min) return { status: 'error', text: '库存不足' };\n    if (current <= min * 2) return { status: 'warning', text: '库存偏低' };\n    return { status: 'success', text: '库存正常' };\n  };\n\n  const columns = [\n    {\n      title: '物料编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 150,\n      fixed: 'left',\n      render: (text) => (\n        <Text strong style={{ color: '#1890ff' }}>{text}</Text>\n      )\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 200,\n      ellipsis: true\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 200,\n      ellipsis: true\n    },\n    {\n      title: '分类',\n      dataIndex: 'category',\n      key: 'category',\n      width: 100,\n      render: (category) => (\n        <Tag color={getCategoryColor(category)}>{category}</Tag>\n      )\n    },\n    {\n      title: '单位',\n      dataIndex: 'unit',\n      key: 'unit',\n      width: 60\n    },\n    {\n      title: '标准成本',\n      dataIndex: 'standardCost',\n      key: 'standardCost',\n      width: 100,\n      render: (cost) => formatCurrency(cost)\n    },\n    {\n      title: '当前成本',\n      dataIndex: 'currentCost',\n      key: 'currentCost',\n      width: 100,\n      render: (cost, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong style={{ color: '#52c41a' }}>\n            {formatCurrency(cost)}\n          </Text>\n          {cost !== record.standardCost && (\n            <Text style={{ fontSize: '12px', color: '#8c8c8c' }}>\n              差异: {formatCurrency(cost - record.standardCost)}\n            </Text>\n          )}\n        </Space>\n      )\n    },\n    {\n      title: '库存情况',\n      key: 'stock',\n      width: 120,\n      render: (_, record) => {\n        const stockStatus = getStockStatus(record.stockQuantity, record.minStock);\n        return (\n          <Space direction=\"vertical\" size={0}>\n            <Text>现存: {record.stockQuantity}</Text>\n            <Badge \n              status={stockStatus.status} \n              text={stockStatus.text}\n              style={{ fontSize: '12px' }}\n            />\n          </Space>\n        );\n      }\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 120\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status) => (\n        <Badge \n          status={getStatusColor(status)} \n          text={status === 'active' ? '启用' : '停用'}\n        />\n      )\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      key: 'createTime',\n      width: 100\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 120,\n      fixed: 'right',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Tooltip title=\"编辑\">\n            <Button \n              type=\"text\" \n              icon={<EditOutlined />} \n              size=\"small\"\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button \n              type=\"text\" \n              icon={<DeleteOutlined />} \n              size=\"small\"\n              danger\n              onClick={() => handleDelete(record)}\n            />\n          </Tooltip>\n        </Space>\n      )\n    }\n  ];\n\n  // 计算统计数据\n  const stats = {\n    total: materials.length,\n    active: materials.filter(m => m.status === 'active').length,\n    lowStock: materials.filter(m => m.stockQuantity <= m.minStock).length,\n    categories: new Set(materials.map(m => m.category)).size,\n    totalValue: materials.reduce((sum, m) => sum + (m.currentCost * m.stockQuantity), 0)\n  };\n\n  return (\n    <div>\n      {/* 页面标题和操作 */}\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Title level={2} style={{ margin: 0 }}>物料管理</Title>\n        <Space>\n          <Button icon={<ReloadOutlined />}>刷新</Button>\n          <Button icon={<ExportOutlined />}>导出</Button>\n          <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAdd}>\n            新增物料\n          </Button>\n        </Space>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"物料总数\" \n              value={stats.total} \n              suffix=\"个\"\n              prefix={<DatabaseOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"启用中\" value={stats.active} suffix=\"个\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"库存不足\" \n              value={stats.lowStock} \n              suffix=\"个\"\n              prefix={<WarningOutlined />}\n              valueStyle={{ color: stats.lowStock > 0 ? '#ff4d4f' : '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"物料分类\" value={stats.categories} suffix=\"类\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"库存总值\" \n              value={stats.totalValue} \n              formatter={(value) => formatCurrency(value)}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索和筛选 */}\n      <Card style={{ marginBottom: '16px' }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Input.Search\n              placeholder=\"搜索物料编码、名称、规格\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Select\n              placeholder=\"分类\"\n              allowClear\n              style={{ width: '100%' }}\n              value={filters.category}\n              onChange={(value) => handleFilterChange('category', value)}\n            >\n              <Option value=\"CPU\">CPU</Option>\n              <Option value=\"主板\">主板</Option>\n              <Option value=\"内存\">内存</Option>\n              <Option value=\"存储\">存储</Option>\n              <Option value=\"显卡\">显卡</Option>\n              <Option value=\"机箱\">机箱</Option>\n              <Option value=\"电源\">电源</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Select\n              placeholder=\"状态\"\n              allowClear\n              style={{ width: '100%' }}\n              value={filters.status}\n              onChange={(value) => handleFilterChange('status', value)}\n            >\n              <Option value=\"active\">启用</Option>\n              <Option value=\"inactive\">停用</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Button onClick={() => dispatch(resetFilters())}>重置</Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 物料列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={materials}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => \n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n          }}\n          scroll={{ x: 1400 }}\n          size=\"small\"\n        />\n      </Card>\n\n      {/* 新增/编辑物料弹窗 */}\n      <Modal\n        title={editingMaterial ? '编辑物料' : '新增物料'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n        width={800}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            status: 'active',\n            unit: '个',\n            minStock: 10,\n            stockQuantity: 0\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"code\"\n                label=\"物料编码\"\n                rules={[{ required: true, message: '请输入物料编码' }]}\n              >\n                <Input placeholder=\"请输入物料编码\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"物料名称\"\n                rules={[{ required: true, message: '请输入物料名称' }]}\n              >\n                <Input placeholder=\"请输入物料名称\" />\n              </Form.Item>\n            </Col>\n          </Row>\n          <Form.Item\n            name=\"specification\"\n            label=\"规格型号\"\n          >\n            <Input placeholder=\"请输入规格型号\" />\n          </Form.Item>\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"分类\"\n                rules={[{ required: true, message: '请选择分类' }]}\n              >\n                <Select placeholder=\"请选择分类\">\n                  <Option value=\"CPU\">CPU</Option>\n                  <Option value=\"主板\">主板</Option>\n                  <Option value=\"内存\">内存</Option>\n                  <Option value=\"存储\">存储</Option>\n                  <Option value=\"显卡\">显卡</Option>\n                  <Option value=\"机箱\">机箱</Option>\n                  <Option value=\"电源\">电源</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"unit\"\n                label=\"单位\"\n              >\n                <Select>\n                  <Option value=\"个\">个</Option>\n                  <Option value=\"套\">套</Option>\n                  <Option value=\"台\">台</Option>\n                  <Option value=\"块\">块</Option>\n                  <Option value=\"条\">条</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n              >\n                <Select>\n                  <Option value=\"active\">启用</Option>\n                  <Option value=\"inactive\">停用</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"standardCost\"\n                label=\"标准成本\"\n                rules={[{ required: true, message: '请输入标准成本' }]}\n              >\n                <InputNumber\n                  min={0}\n                  precision={2}\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入标准成本\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"currentCost\"\n                label=\"当前成本\"\n                rules={[{ required: true, message: '请输入当前成本' }]}\n              >\n                <InputNumber\n                  min={0}\n                  precision={2}\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入当前成本\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"stockQuantity\"\n                label=\"库存数量\"\n              >\n                <InputNumber\n                  min={0}\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入库存数量\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"minStock\"\n                label=\"最低库存\"\n              >\n                <InputNumber\n                  min={0}\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入最低库存\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"supplier\"\n                label=\"供应商\"\n              >\n                <Input placeholder=\"请输入供应商\" />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default MaterialList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,OAAO,EACPC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,QACV,mBAAmB;AAE1B,SACEC,uBAAuB,EACvBC,gBAAgB,EAChBC,gBAAgB,EAChBC,gBAAgB,EAChBC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,cAAc,QACT,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG9B,UAAU;AAClC,MAAM;EAAE+B;AAAO,CAAC,GAAGlC,MAAM;AAEzB,MAAMmC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,SAAS,GAAG5C,WAAW,CAAC2B,uBAAuB,CAAC;EACtD,MAAMkB,OAAO,GAAG7C,WAAW,CAAC4B,gBAAgB,CAAC;EAC7C,MAAMkB,OAAO,GAAG9C,WAAW,CAAC6B,gBAAgB,CAAC;EAE7C,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoD,IAAI,CAAC,GAAGrC,IAAI,CAACsC,OAAO,CAAC,CAAC;EAE7B,MAAMC,YAAY,GAAIC,KAAK,IAAK;IAC9BX,QAAQ,CAACb,gBAAgB,CAACwB,KAAK,CAAC,CAAC;EACnC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACC,GAAG,EAAEF,KAAK,KAAK;IACzCX,QAAQ,CAACZ,UAAU,CAAC;MAAE,CAACyB,GAAG,GAAGF;IAAM,CAAC,CAAC,CAAC;EACxC,CAAC;EAED,MAAMG,SAAS,GAAGA,CAAA,KAAM;IACtBP,kBAAkB,CAAC,IAAI,CAAC;IACxBC,IAAI,CAACO,WAAW,CAAC,CAAC;IAClBV,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMW,UAAU,GAAIC,QAAQ,IAAK;IAC/BV,kBAAkB,CAACU,QAAQ,CAAC;IAC5BT,IAAI,CAACU,cAAc,CAACD,QAAQ,CAAC;IAC7BZ,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMc,YAAY,GAAIF,QAAQ,IAAK;IACjC/C,KAAK,CAACkD,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,WAAWL,QAAQ,CAACM,IAAI,KAAK;MACtCC,IAAI,EAAEA,CAAA,KAAM;QACVxB,QAAQ,CAACR,cAAc,CAACyB,QAAQ,CAACQ,EAAE,CAAC,CAAC;QACrCpD,OAAO,CAACqD,OAAO,CAAC,MAAM,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMpB,IAAI,CAACqB,cAAc,CAAC,CAAC;MAC1C,IAAIvB,eAAe,EAAE;QACnBN,QAAQ,CAACT,cAAc,CAAC;UAAE,GAAGe,eAAe;UAAE,GAAGsB;QAAO,CAAC,CAAC,CAAC;QAC3DvD,OAAO,CAACqD,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL1B,QAAQ,CAACV,WAAW,CAACsC,MAAM,CAAC,CAAC;QAC7BvD,OAAO,CAACqD,OAAO,CAAC,MAAM,CAAC;MACzB;MACArB,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAME,cAAc,GAAIrB,KAAK,IAAK;IAChC,OAAO,IAAIsB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAC;EAClB,CAAC;EAED,MAAM6B,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,MAAMC,MAAM,GAAG;MACb,KAAK,EAAE,KAAK;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,OAAO;MACb,IAAI,EAAE,QAAQ;MACd,IAAI,EAAE,QAAQ;MACd,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE;IACR,CAAC;IACD,OAAOA,MAAM,CAACD,QAAQ,CAAC,IAAI,SAAS;EACtC,CAAC;EAED,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAOA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAO;EAClD,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACC,OAAO,EAAEC,GAAG,KAAK;IACvC,IAAID,OAAO,IAAIC,GAAG,EAAE,OAAO;MAAEH,MAAM,EAAE,OAAO;MAAEI,IAAI,EAAE;IAAO,CAAC;IAC5D,IAAIF,OAAO,IAAIC,GAAG,GAAG,CAAC,EAAE,OAAO;MAAEH,MAAM,EAAE,SAAS;MAAEI,IAAI,EAAE;IAAO,CAAC;IAClE,OAAO;MAAEJ,MAAM,EAAE,SAAS;MAAEI,IAAI,EAAE;IAAO,CAAC;EAC5C,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACE5B,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,MAAM;IACjBrC,GAAG,EAAE,MAAM;IACXsC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAGL,IAAI,iBACXtD,OAAA,CAACE,IAAI;MAAC0D,MAAM;MAACnB,KAAK,EAAE;QAAEoB,KAAK,EAAE;MAAU,CAAE;MAAAC,QAAA,EAAER;IAAI;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAE1D,CAAC,EACD;IACEvC,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,MAAM;IACjBrC,GAAG,EAAE,MAAM;IACXsC,KAAK,EAAE,GAAG;IACVU,QAAQ,EAAE;EACZ,CAAC,EACD;IACExC,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,eAAe;IAC1BrC,GAAG,EAAE,eAAe;IACpBsC,KAAK,EAAE,GAAG;IACVU,QAAQ,EAAE;EACZ,CAAC,EACD;IACExC,KAAK,EAAE,IAAI;IACX6B,SAAS,EAAE,UAAU;IACrBrC,GAAG,EAAE,UAAU;IACfsC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGZ,QAAQ,iBACf/C,OAAA,CAAC7B,GAAG;MAAC0F,KAAK,EAAEf,gBAAgB,CAACC,QAAQ,CAAE;MAAAe,QAAA,EAAEf;IAAQ;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAE3D,CAAC,EACD;IACEvC,KAAK,EAAE,IAAI;IACX6B,SAAS,EAAE,MAAM;IACjBrC,GAAG,EAAE,MAAM;IACXsC,KAAK,EAAE;EACT,CAAC,EACD;IACE9B,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,cAAc;IACzBrC,GAAG,EAAE,cAAc;IACnBsC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGS,IAAI,IAAK9B,cAAc,CAAC8B,IAAI;EACvC,CAAC,EACD;IACEzC,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,aAAa;IACxBrC,GAAG,EAAE,aAAa;IAClBsC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACS,IAAI,EAAEC,MAAM,kBACnBrE,OAAA,CAAC9B,KAAK;MAACoG,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAT,QAAA,gBAClC9D,OAAA,CAACE,IAAI;QAAC0D,MAAM;QAACnB,KAAK,EAAE;UAAEoB,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EACtCxB,cAAc,CAAC8B,IAAI;MAAC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,EACNE,IAAI,KAAKC,MAAM,CAACG,YAAY,iBAC3BxE,OAAA,CAACE,IAAI;QAACuC,KAAK,EAAE;UAAEgC,QAAQ,EAAE,MAAM;UAAEZ,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,GAAC,gBAC/C,EAACxB,cAAc,CAAC8B,IAAI,GAAGC,MAAM,CAACG,YAAY,CAAC;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,EACD;IACEvC,KAAK,EAAE,MAAM;IACbR,GAAG,EAAE,OAAO;IACZsC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACe,CAAC,EAAEL,MAAM,KAAK;MACrB,MAAMM,WAAW,GAAGxB,cAAc,CAACkB,MAAM,CAACO,aAAa,EAAEP,MAAM,CAACQ,QAAQ,CAAC;MACzE,oBACE7E,OAAA,CAAC9B,KAAK;QAACoG,SAAS,EAAC,UAAU;QAACC,IAAI,EAAE,CAAE;QAAAT,QAAA,gBAClC9D,OAAA,CAACE,IAAI;UAAA4D,QAAA,GAAC,gBAAI,EAACO,MAAM,CAACO,aAAa;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvClE,OAAA,CAACnB,KAAK;UACJqE,MAAM,EAAEyB,WAAW,CAACzB,MAAO;UAC3BI,IAAI,EAAEqB,WAAW,CAACrB,IAAK;UACvBb,KAAK,EAAE;YAAEgC,QAAQ,EAAE;UAAO;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;EACF,CAAC,EACD;IACEvC,KAAK,EAAE,KAAK;IACZ6B,SAAS,EAAE,UAAU;IACrBrC,GAAG,EAAE,UAAU;IACfsC,KAAK,EAAE;EACT,CAAC,EACD;IACE9B,KAAK,EAAE,IAAI;IACX6B,SAAS,EAAE,QAAQ;IACnBrC,GAAG,EAAE,QAAQ;IACbsC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGT,MAAM,iBACblD,OAAA,CAACnB,KAAK;MACJqE,MAAM,EAAED,cAAc,CAACC,MAAM,CAAE;MAC/BI,IAAI,EAAEJ,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACEvC,KAAK,EAAE,MAAM;IACb6B,SAAS,EAAE,YAAY;IACvBrC,GAAG,EAAE,YAAY;IACjBsC,KAAK,EAAE;EACT,CAAC,EACD;IACE9B,KAAK,EAAE,IAAI;IACXR,GAAG,EAAE,SAAS;IACdsC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAEA,CAACe,CAAC,EAAEL,MAAM,kBAChBrE,OAAA,CAAC9B,KAAK;MAACqG,IAAI,EAAC,OAAO;MAAAT,QAAA,gBACjB9D,OAAA,CAACpB,OAAO;QAAC+C,KAAK,EAAC,cAAI;QAAAmC,QAAA,eACjB9D,OAAA,CAACjC,MAAM;UACL+G,IAAI,EAAC,MAAM;UACXC,IAAI,eAAE/E,OAAA,CAAChB,YAAY;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBK,IAAI,EAAC,OAAO;UACZS,OAAO,EAAEA,CAAA,KAAM1D,UAAU,CAAC+C,MAAM;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlE,OAAA,CAACpB,OAAO;QAAC+C,KAAK,EAAC,cAAI;QAAAmC,QAAA,eACjB9D,OAAA,CAACjC,MAAM;UACL+G,IAAI,EAAC,MAAM;UACXC,IAAI,eAAE/E,OAAA,CAACf,cAAc;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBK,IAAI,EAAC,OAAO;UACZU,MAAM;UACND,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAAC4C,MAAM;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;;EAED;EACA,MAAMgB,KAAK,GAAG;IACZC,KAAK,EAAE5E,SAAS,CAAC6E,MAAM;IACvBC,MAAM,EAAE9E,SAAS,CAAC+E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAK,QAAQ,CAAC,CAACkC,MAAM;IAC3DI,QAAQ,EAAEjF,SAAS,CAAC+E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACX,aAAa,IAAIW,CAAC,CAACV,QAAQ,CAAC,CAACO,MAAM;IACrEK,UAAU,EAAE,IAAIC,GAAG,CAACnF,SAAS,CAACoF,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAACwB,IAAI;IACxDqB,UAAU,EAAErF,SAAS,CAACsF,MAAM,CAAC,CAACC,GAAG,EAAEP,CAAC,KAAKO,GAAG,GAAIP,CAAC,CAACQ,WAAW,GAAGR,CAAC,CAACX,aAAc,EAAE,CAAC;EACrF,CAAC;EAED,oBACE5E,OAAA;IAAA8D,QAAA,gBAEE9D,OAAA;MAAKyC,KAAK,EAAE;QAAEuD,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAArC,QAAA,gBAC3G9D,OAAA,CAACC,KAAK;QAACmG,KAAK,EAAE,CAAE;QAAC3D,KAAK,EAAE;UAAE4D,MAAM,EAAE;QAAE,CAAE;QAAAvC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnDlE,OAAA,CAAC9B,KAAK;QAAA4F,QAAA,gBACJ9D,OAAA,CAACjC,MAAM;UAACgH,IAAI,eAAE/E,OAAA,CAACd,cAAc;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7ClE,OAAA,CAACjC,MAAM;UAACgH,IAAI,eAAE/E,OAAA,CAACb,cAAc;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7ClE,OAAA,CAACjC,MAAM;UAAC+G,IAAI,EAAC,SAAS;UAACC,IAAI,eAAE/E,OAAA,CAAClB,YAAY;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACc,OAAO,EAAE5D,SAAU;UAAA0C,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNlE,OAAA,CAAC3B,GAAG;MAACiI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC7D,KAAK,EAAE;QAAEuD,YAAY,EAAE;MAAO,CAAE;MAAAlC,QAAA,gBACrD9D,OAAA,CAAC1B,GAAG;QAACiI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB9D,OAAA,CAAClC,IAAI;UAAAgG,QAAA,eACH9D,OAAA,CAACzB,SAAS;YACRoD,KAAK,EAAC,0BAAM;YACZV,KAAK,EAAEiE,KAAK,CAACC,KAAM;YACnBuB,MAAM,EAAC,QAAG;YACVC,MAAM,eAAE3G,OAAA,CAACZ,gBAAgB;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAAC1B,GAAG;QAACiI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB9D,OAAA,CAAClC,IAAI;UAAAgG,QAAA,eACH9D,OAAA,CAACzB,SAAS;YAACoD,KAAK,EAAC,oBAAK;YAACV,KAAK,EAAEiE,KAAK,CAACG,MAAO;YAACqB,MAAM,EAAC;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAAC1B,GAAG;QAACiI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB9D,OAAA,CAAClC,IAAI;UAAAgG,QAAA,eACH9D,OAAA,CAACzB,SAAS;YACRoD,KAAK,EAAC,0BAAM;YACZV,KAAK,EAAEiE,KAAK,CAACM,QAAS;YACtBkB,MAAM,EAAC,QAAG;YACVC,MAAM,eAAE3G,OAAA,CAACX,eAAe;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B0C,UAAU,EAAE;cAAE/C,KAAK,EAAEqB,KAAK,CAACM,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAAC1B,GAAG;QAACiI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB9D,OAAA,CAAClC,IAAI;UAAAgG,QAAA,eACH9D,OAAA,CAACzB,SAAS;YAACoD,KAAK,EAAC,0BAAM;YAACV,KAAK,EAAEiE,KAAK,CAACO,UAAW;YAACiB,MAAM,EAAC;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAAC1B,GAAG;QAACiI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACxB9D,OAAA,CAAClC,IAAI;UAAAgG,QAAA,eACH9D,OAAA,CAACzB,SAAS;YACRoD,KAAK,EAAC,0BAAM;YACZV,KAAK,EAAEiE,KAAK,CAACU,UAAW;YACxBiB,SAAS,EAAG5F,KAAK,IAAKqB,cAAc,CAACrB,KAAK;UAAE;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA,CAAClC,IAAI;MAAC2E,KAAK,EAAE;QAAEuD,YAAY,EAAE;MAAO,CAAE;MAAAlC,QAAA,eACpC9D,OAAA,CAAC3B,GAAG;QAACiI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAxC,QAAA,gBACpB9D,OAAA,CAAC1B,GAAG;UAACiI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA3C,QAAA,eAChC9D,OAAA,CAAChC,KAAK,CAAC+I,MAAM;YACXC,WAAW,EAAC,0EAAc;YAC1BC,UAAU;YACVC,QAAQ,EAAElG,YAAa;YACvByB,KAAK,EAAE;cAAEgB,KAAK,EAAE;YAAO;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlE,OAAA,CAAC1B,GAAG;UAACiI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA3C,QAAA,eAC/B9D,OAAA,CAAC/B,MAAM;YACL+I,WAAW,EAAC,cAAI;YAChBC,UAAU;YACVxE,KAAK,EAAE;cAAEgB,KAAK,EAAE;YAAO,CAAE;YACzBxC,KAAK,EAAER,OAAO,CAACsC,QAAS;YACxBoE,QAAQ,EAAGlG,KAAK,IAAKC,kBAAkB,CAAC,UAAU,EAAED,KAAK,CAAE;YAAA6C,QAAA,gBAE3D9D,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,KAAK;cAAA6C,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChClE,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,cAAI;cAAA6C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,cAAI;cAAA6C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,cAAI;cAAA6C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,cAAI;cAAA6C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,cAAI;cAAA6C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,cAAI;cAAA6C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlE,OAAA,CAAC1B,GAAG;UAACiI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA3C,QAAA,eAC/B9D,OAAA,CAAC/B,MAAM;YACL+I,WAAW,EAAC,cAAI;YAChBC,UAAU;YACVxE,KAAK,EAAE;cAAEgB,KAAK,EAAE;YAAO,CAAE;YACzBxC,KAAK,EAAER,OAAO,CAACyC,MAAO;YACtBiE,QAAQ,EAAGlG,KAAK,IAAKC,kBAAkB,CAAC,QAAQ,EAAED,KAAK,CAAE;YAAA6C,QAAA,gBAEzD9D,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,QAAQ;cAAA6C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClClE,OAAA,CAACG,MAAM;cAACc,KAAK,EAAC,UAAU;cAAA6C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlE,OAAA,CAAC1B,GAAG;UAACiI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACM,EAAE,EAAE,CAAE;UAACL,EAAE,EAAE,CAAE;UAAA3C,QAAA,eAC/B9D,OAAA,CAACjC,MAAM;YAACiH,OAAO,EAAEA,CAAA,KAAM1E,QAAQ,CAACX,YAAY,CAAC,CAAC,CAAE;YAAAmE,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPlE,OAAA,CAAClC,IAAI;MAAAgG,QAAA,eACH9D,OAAA,CAACnC,KAAK;QACJ0F,OAAO,EAAEA,OAAQ;QACjB6D,UAAU,EAAE7G,SAAU;QACtB8G,MAAM,EAAC,IAAI;QACX7G,OAAO,EAAEA,OAAQ;QACjB8G,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACtC,KAAK,EAAEuC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQvC,KAAK;QAC1C,CAAE;QACFwC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBrD,IAAI,EAAC;MAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPlE,OAAA,CAACxB,KAAK;MACJmD,KAAK,EAAEf,eAAe,GAAG,MAAM,GAAG,MAAO;MACzCiH,IAAI,EAAEnH,YAAa;MACnBoB,IAAI,EAAEG,aAAc;MACpB6F,QAAQ,EAAEA,CAAA,KAAMnH,eAAe,CAAC,KAAK,CAAE;MACvC8C,KAAK,EAAE,GAAI;MACXsE,cAAc;MAAAjE,QAAA,eAEd9D,OAAA,CAACvB,IAAI;QACHqC,IAAI,EAAEA,IAAK;QACXkH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb/E,MAAM,EAAE,QAAQ;UAChBgF,IAAI,EAAE,GAAG;UACTrD,QAAQ,EAAE,EAAE;UACZD,aAAa,EAAE;QACjB,CAAE;QAAAd,QAAA,gBAEF9D,OAAA,CAAC3B,GAAG;UAACiI,MAAM,EAAE,EAAG;UAAAxC,QAAA,gBACd9D,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,EAAG;YAAArE,QAAA,eACZ9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,MAAM;cACXwG,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAmF,QAAA,eAEhD9D,OAAA,CAAChC,KAAK;gBAACgJ,WAAW,EAAC;cAAS;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,EAAG;YAAArE,QAAA,eACZ9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,MAAM;cACXwG,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAmF,QAAA,eAEhD9D,OAAA,CAAChC,KAAK;gBAACgJ,WAAW,EAAC;cAAS;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlE,OAAA,CAACvB,IAAI,CAAC2J,IAAI;UACRvG,IAAI,EAAC,eAAe;UACpBwG,KAAK,EAAC,0BAAM;UAAAvE,QAAA,eAEZ9D,OAAA,CAAChC,KAAK;YAACgJ,WAAW,EAAC;UAAS;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZlE,OAAA,CAAC3B,GAAG;UAACiI,MAAM,EAAE,EAAG;UAAAxC,QAAA,gBACd9D,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,CAAE;YAAArE,QAAA,eACX9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,UAAU;cACfwG,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAmF,QAAA,eAE9C9D,OAAA,CAAC/B,MAAM;gBAAC+I,WAAW,EAAC,gCAAO;gBAAAlD,QAAA,gBACzB9D,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,KAAK;kBAAA6C,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,cAAI;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,cAAI;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,cAAI;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,cAAI;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,cAAI;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,cAAI;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,CAAE;YAAArE,QAAA,eACX9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,MAAM;cACXwG,KAAK,EAAC,cAAI;cAAAvE,QAAA,eAEV9D,OAAA,CAAC/B,MAAM;gBAAA6F,QAAA,gBACL9D,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,QAAG;kBAAA6C,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,QAAG;kBAAA6C,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,QAAG;kBAAA6C,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,QAAG;kBAAA6C,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BlE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,QAAG;kBAAA6C,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,CAAE;YAAArE,QAAA,eACX9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,QAAQ;cACbwG,KAAK,EAAC,cAAI;cAAAvE,QAAA,eAEV9D,OAAA,CAAC/B,MAAM;gBAAA6F,QAAA,gBACL9D,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,QAAQ;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClE,OAAA,CAACG,MAAM;kBAACc,KAAK,EAAC,UAAU;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlE,OAAA,CAAC3B,GAAG;UAACiI,MAAM,EAAE,EAAG;UAAAxC,QAAA,gBACd9D,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,EAAG;YAAArE,QAAA,eACZ9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,cAAc;cACnBwG,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAmF,QAAA,eAEhD9D,OAAA,CAACtB,WAAW;gBACV2E,GAAG,EAAE,CAAE;gBACPmF,SAAS,EAAE,CAAE;gBACb/F,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBACzBuD,WAAW,EAAC;cAAS;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,EAAG;YAAArE,QAAA,eACZ9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,aAAa;cAClBwG,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAmF,QAAA,eAEhD9D,OAAA,CAACtB,WAAW;gBACV2E,GAAG,EAAE,CAAE;gBACPmF,SAAS,EAAE,CAAE;gBACb/F,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBACzBuD,WAAW,EAAC;cAAS;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlE,OAAA,CAAC3B,GAAG;UAACiI,MAAM,EAAE,EAAG;UAAAxC,QAAA,gBACd9D,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,CAAE;YAAArE,QAAA,eACX9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,eAAe;cACpBwG,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAEZ9D,OAAA,CAACtB,WAAW;gBACV2E,GAAG,EAAE,CAAE;gBACPZ,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBACzBuD,WAAW,EAAC;cAAS;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,CAAE;YAAArE,QAAA,eACX9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,UAAU;cACfwG,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAEZ9D,OAAA,CAACtB,WAAW;gBACV2E,GAAG,EAAE,CAAE;gBACPZ,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBACzBuD,WAAW,EAAC;cAAS;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAAC1B,GAAG;YAAC6J,IAAI,EAAE,CAAE;YAAArE,QAAA,eACX9D,OAAA,CAACvB,IAAI,CAAC2J,IAAI;cACRvG,IAAI,EAAC,UAAU;cACfwG,KAAK,EAAC,oBAAK;cAAAvE,QAAA,eAEX9D,OAAA,CAAChC,KAAK;gBAACgJ,WAAW,EAAC;cAAQ;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAjgBID,YAAY;EAAA,QACCxC,WAAW,EACVD,WAAW,EACbA,WAAW,EACXA,WAAW,EAIZc,IAAI,CAACsC,OAAO;AAAA;AAAA0H,EAAA,GARvBrI,YAAY;AAmgBlB,eAAeA,YAAY;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}