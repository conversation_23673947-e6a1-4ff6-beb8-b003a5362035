{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport * as modelUtil from '../../util/model.js';\nimport * as graphicUtil from '../../util/graphic.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport { parsePercent } from '../../util/number.js';\nimport ComponentView from '../../view/Component.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { isEC4CompatibleStyle, convertFromEC4CompatibleStyle } from '../../util/styleCompat.js';\nimport { applyLeaveTransition, applyUpdateTransition, isTransitionAll, updateLeaveTo } from '../../animation/customGraphicTransition.js';\nimport { updateProps } from '../../animation/basicTransition.js';\nimport { applyKeyframeAnimation, stopPreviousKeyframeAnimationAndRestore } from '../../animation/customGraphicKeyframeAnimation.js';\nvar nonShapeGraphicElements = {\n  // Reserved but not supported in graphic component.\n  path: null,\n  compoundPath: null,\n  // Supported in graphic component.\n  group: graphicUtil.Group,\n  image: graphicUtil.Image,\n  text: graphicUtil.Text\n};\nexport var inner = modelUtil.makeInner();\n// ------------------------\n// View\n// ------------------------\nvar GraphicComponentView = /** @class */function (_super) {\n  __extends(GraphicComponentView, _super);\n  function GraphicComponentView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphicComponentView.type;\n    return _this;\n  }\n  GraphicComponentView.prototype.init = function () {\n    this._elMap = zrUtil.createHashMap();\n  };\n  GraphicComponentView.prototype.render = function (graphicModel, ecModel, api) {\n    // Having leveraged between use cases and algorithm complexity, a very\n    // simple layout mechanism is used:\n    // The size(width/height) can be determined by itself or its parent (not\n    // implemented yet), but can not by its children. (Top-down travel)\n    // The location(x/y) can be determined by the bounding rect of itself\n    // (can including its descendants or not) and the size of its parent.\n    // (Bottom-up travel)\n    // When `chart.clear()` or `chart.setOption({...}, true)` with the same id,\n    // view will be reused.\n    if (graphicModel !== this._lastGraphicModel) {\n      this._clear();\n    }\n    this._lastGraphicModel = graphicModel;\n    this._updateElements(graphicModel);\n    this._relocate(graphicModel, api);\n  };\n  /**\r\n   * Update graphic elements.\r\n   */\n  GraphicComponentView.prototype._updateElements = function (graphicModel) {\n    var elOptionsToUpdate = graphicModel.useElOptionsToUpdate();\n    if (!elOptionsToUpdate) {\n      return;\n    }\n    var elMap = this._elMap;\n    var rootGroup = this.group;\n    var globalZ = graphicModel.get('z');\n    var globalZLevel = graphicModel.get('zlevel');\n    // Top-down tranverse to assign graphic settings to each elements.\n    zrUtil.each(elOptionsToUpdate, function (elOption) {\n      var id = modelUtil.convertOptionIdName(elOption.id, null);\n      var elExisting = id != null ? elMap.get(id) : null;\n      var parentId = modelUtil.convertOptionIdName(elOption.parentId, null);\n      var targetElParent = parentId != null ? elMap.get(parentId) : rootGroup;\n      var elType = elOption.type;\n      var elOptionStyle = elOption.style;\n      if (elType === 'text' && elOptionStyle) {\n        // In top/bottom mode, textVerticalAlign should not be used, which cause\n        // inaccurately locating.\n        if (elOption.hv && elOption.hv[1]) {\n          elOptionStyle.textVerticalAlign = elOptionStyle.textBaseline = elOptionStyle.verticalAlign = elOptionStyle.align = null;\n        }\n      }\n      var textContentOption = elOption.textContent;\n      var textConfig = elOption.textConfig;\n      if (elOptionStyle && isEC4CompatibleStyle(elOptionStyle, elType, !!textConfig, !!textContentOption)) {\n        var convertResult = convertFromEC4CompatibleStyle(elOptionStyle, elType, true);\n        if (!textConfig && convertResult.textConfig) {\n          textConfig = elOption.textConfig = convertResult.textConfig;\n        }\n        if (!textContentOption && convertResult.textContent) {\n          textContentOption = convertResult.textContent;\n        }\n      }\n      // Remove unnecessary props to avoid potential problems.\n      var elOptionCleaned = getCleanedElOption(elOption);\n      // For simple, do not support parent change, otherwise reorder is needed.\n      if (process.env.NODE_ENV !== 'production') {\n        elExisting && zrUtil.assert(targetElParent === elExisting.parent, 'Changing parent is not supported.');\n      }\n      var $action = elOption.$action || 'merge';\n      var isMerge = $action === 'merge';\n      var isReplace = $action === 'replace';\n      if (isMerge) {\n        var isInit = !elExisting;\n        var el_1 = elExisting;\n        if (isInit) {\n          el_1 = createEl(id, targetElParent, elOption.type, elMap);\n        } else {\n          el_1 && (inner(el_1).isNew = false);\n          // Stop and restore before update any other attributes.\n          stopPreviousKeyframeAnimationAndRestore(el_1);\n        }\n        if (el_1) {\n          applyUpdateTransition(el_1, elOptionCleaned, graphicModel, {\n            isInit: isInit\n          });\n          updateCommonAttrs(el_1, elOption, globalZ, globalZLevel);\n        }\n      } else if (isReplace) {\n        removeEl(elExisting, elOption, elMap, graphicModel);\n        var el_2 = createEl(id, targetElParent, elOption.type, elMap);\n        if (el_2) {\n          applyUpdateTransition(el_2, elOptionCleaned, graphicModel, {\n            isInit: true\n          });\n          updateCommonAttrs(el_2, elOption, globalZ, globalZLevel);\n        }\n      } else if ($action === 'remove') {\n        updateLeaveTo(elExisting, elOption);\n        removeEl(elExisting, elOption, elMap, graphicModel);\n      }\n      var el = elMap.get(id);\n      if (el && textContentOption) {\n        if (isMerge) {\n          var textContentExisting = el.getTextContent();\n          textContentExisting ? textContentExisting.attr(textContentOption) : el.setTextContent(new graphicUtil.Text(textContentOption));\n        } else if (isReplace) {\n          el.setTextContent(new graphicUtil.Text(textContentOption));\n        }\n      }\n      if (el) {\n        var clipPathOption = elOption.clipPath;\n        if (clipPathOption) {\n          var clipPathType = clipPathOption.type;\n          var clipPath = void 0;\n          var isInit = false;\n          if (isMerge) {\n            var oldClipPath = el.getClipPath();\n            isInit = !oldClipPath || inner(oldClipPath).type !== clipPathType;\n            clipPath = isInit ? newEl(clipPathType) : oldClipPath;\n          } else if (isReplace) {\n            isInit = true;\n            clipPath = newEl(clipPathType);\n          }\n          el.setClipPath(clipPath);\n          applyUpdateTransition(clipPath, clipPathOption, graphicModel, {\n            isInit: isInit\n          });\n          applyKeyframeAnimation(clipPath, clipPathOption.keyframeAnimation, graphicModel);\n        }\n        var elInner = inner(el);\n        el.setTextConfig(textConfig);\n        elInner.option = elOption;\n        setEventData(el, graphicModel, elOption);\n        graphicUtil.setTooltipConfig({\n          el: el,\n          componentModel: graphicModel,\n          itemName: el.name,\n          itemTooltipOption: elOption.tooltip\n        });\n        applyKeyframeAnimation(el, elOption.keyframeAnimation, graphicModel);\n      }\n    });\n  };\n  /**\r\n   * Locate graphic elements.\r\n   */\n  GraphicComponentView.prototype._relocate = function (graphicModel, api) {\n    var elOptions = graphicModel.option.elements;\n    var rootGroup = this.group;\n    var elMap = this._elMap;\n    var apiWidth = api.getWidth();\n    var apiHeight = api.getHeight();\n    var xy = ['x', 'y'];\n    // Top-down to calculate percentage width/height of group\n    for (var i = 0; i < elOptions.length; i++) {\n      var elOption = elOptions[i];\n      var id = modelUtil.convertOptionIdName(elOption.id, null);\n      var el = id != null ? elMap.get(id) : null;\n      if (!el || !el.isGroup) {\n        continue;\n      }\n      var parentEl = el.parent;\n      var isParentRoot = parentEl === rootGroup;\n      // Like 'position:absolut' in css, default 0.\n      var elInner = inner(el);\n      var parentElInner = inner(parentEl);\n      elInner.width = parsePercent(elInner.option.width, isParentRoot ? apiWidth : parentElInner.width) || 0;\n      elInner.height = parsePercent(elInner.option.height, isParentRoot ? apiHeight : parentElInner.height) || 0;\n    }\n    // Bottom-up tranvese all elements (consider ec resize) to locate elements.\n    for (var i = elOptions.length - 1; i >= 0; i--) {\n      var elOption = elOptions[i];\n      var id = modelUtil.convertOptionIdName(elOption.id, null);\n      var el = id != null ? elMap.get(id) : null;\n      if (!el) {\n        continue;\n      }\n      var parentEl = el.parent;\n      var parentElInner = inner(parentEl);\n      var containerInfo = parentEl === rootGroup ? {\n        width: apiWidth,\n        height: apiHeight\n      } : {\n        width: parentElInner.width,\n        height: parentElInner.height\n      };\n      // PENDING\n      // Currently, when `bounding: 'all'`, the union bounding rect of the group\n      // does not include the rect of [0, 0, group.width, group.height], which\n      // is probably weird for users. Should we make a break change for it?\n      var layoutPos = {};\n      var layouted = layoutUtil.positionElement(el, elOption, containerInfo, null, {\n        hv: elOption.hv,\n        boundingMode: elOption.bounding\n      }, layoutPos);\n      if (!inner(el).isNew && layouted) {\n        var transition = elOption.transition;\n        var animatePos = {};\n        for (var k = 0; k < xy.length; k++) {\n          var key = xy[k];\n          var val = layoutPos[key];\n          if (transition && (isTransitionAll(transition) || zrUtil.indexOf(transition, key) >= 0)) {\n            animatePos[key] = val;\n          } else {\n            el[key] = val;\n          }\n        }\n        updateProps(el, animatePos, graphicModel, 0);\n      } else {\n        el.attr(layoutPos);\n      }\n    }\n  };\n  /**\r\n   * Clear all elements.\r\n   */\n  GraphicComponentView.prototype._clear = function () {\n    var _this = this;\n    var elMap = this._elMap;\n    elMap.each(function (el) {\n      removeEl(el, inner(el).option, elMap, _this._lastGraphicModel);\n    });\n    this._elMap = zrUtil.createHashMap();\n  };\n  GraphicComponentView.prototype.dispose = function () {\n    this._clear();\n  };\n  GraphicComponentView.type = 'graphic';\n  return GraphicComponentView;\n}(ComponentView);\nexport { GraphicComponentView };\nfunction newEl(graphicType) {\n  if (process.env.NODE_ENV !== 'production') {\n    zrUtil.assert(graphicType, 'graphic type MUST be set');\n  }\n  var Clz = zrUtil.hasOwn(nonShapeGraphicElements, graphicType)\n  // Those graphic elements are not shapes. They should not be\n  // overwritten by users, so do them first.\n  ? nonShapeGraphicElements[graphicType] : graphicUtil.getShapeClass(graphicType);\n  if (process.env.NODE_ENV !== 'production') {\n    zrUtil.assert(Clz, \"graphic type \" + graphicType + \" can not be found\");\n  }\n  var el = new Clz({});\n  inner(el).type = graphicType;\n  return el;\n}\nfunction createEl(id, targetElParent, graphicType, elMap) {\n  var el = newEl(graphicType);\n  targetElParent.add(el);\n  elMap.set(id, el);\n  inner(el).id = id;\n  inner(el).isNew = true;\n  return el;\n}\nfunction removeEl(elExisting, elOption, elMap, graphicModel) {\n  var existElParent = elExisting && elExisting.parent;\n  if (existElParent) {\n    elExisting.type === 'group' && elExisting.traverse(function (el) {\n      removeEl(el, elOption, elMap, graphicModel);\n    });\n    applyLeaveTransition(elExisting, elOption, graphicModel);\n    elMap.removeKey(inner(elExisting).id);\n  }\n}\nfunction updateCommonAttrs(el, elOption, defaultZ, defaultZlevel) {\n  if (!el.isGroup) {\n    zrUtil.each([['cursor', Displayable.prototype.cursor],\n    // We should not support configure z and zlevel in the element level.\n    // But seems we didn't limit it previously. So here still use it to avoid breaking.\n    ['zlevel', defaultZlevel || 0], ['z', defaultZ || 0],\n    // z2 must not be null/undefined, otherwise sort error may occur.\n    ['z2', 0]], function (item) {\n      var prop = item[0];\n      if (zrUtil.hasOwn(elOption, prop)) {\n        el[prop] = zrUtil.retrieve2(elOption[prop], item[1]);\n      } else if (el[prop] == null) {\n        el[prop] = item[1];\n      }\n    });\n  }\n  zrUtil.each(zrUtil.keys(elOption), function (key) {\n    // Assign event handlers.\n    // PENDING: should enumerate all event names or use pattern matching?\n    if (key.indexOf('on') === 0) {\n      var val = elOption[key];\n      el[key] = zrUtil.isFunction(val) ? val : null;\n    }\n  });\n  if (zrUtil.hasOwn(elOption, 'draggable')) {\n    el.draggable = elOption.draggable;\n  }\n  // Other attributes\n  elOption.name != null && (el.name = elOption.name);\n  elOption.id != null && (el.id = elOption.id);\n}\n// Remove unnecessary props to avoid potential problems.\nfunction getCleanedElOption(elOption) {\n  elOption = zrUtil.extend({}, elOption);\n  zrUtil.each(['id', 'parentId', '$action', 'hv', 'bounding', 'textContent', 'clipPath'].concat(layoutUtil.LOCATION_PARAMS), function (name) {\n    delete elOption[name];\n  });\n  return elOption;\n}\nfunction setEventData(el, graphicModel, elOption) {\n  var eventData = getECData(el).eventData;\n  // Simple optimize for large amount of elements that no need event.\n  if (!el.silent && !el.ignore && !eventData) {\n    eventData = getECData(el).eventData = {\n      componentType: 'graphic',\n      componentIndex: graphicModel.componentIndex,\n      name: el.name\n    };\n  }\n  // `elOption.info` enables user to mount some info on\n  // elements and use them in event handlers.\n  if (eventData) {\n    eventData.info = elOption.info;\n  }\n}", "map": {"version": 3, "names": ["__extends", "zrUtil", "Displayable", "modelUtil", "graphicUtil", "layoutUtil", "parsePercent", "ComponentView", "getECData", "isEC4CompatibleStyle", "convertFromEC4CompatibleStyle", "applyLeaveTransition", "applyUpdateTransition", "isTransitionAll", "updateLeaveTo", "updateProps", "applyKeyframeAnimation", "stopPreviousKeyframeAnimationAndRestore", "nonShapeGraphicElements", "path", "compoundPath", "group", "Group", "image", "Image", "text", "Text", "inner", "makeInner", "GraphicComponentView", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "_elMap", "createHashMap", "render", "graphicModel", "ecModel", "api", "_lastGraphicModel", "_clear", "_updateElements", "_relocate", "elOptionsToUpdate", "useElOptionsToUpdate", "elMap", "rootGroup", "globalZ", "get", "globalZLevel", "each", "elOption", "id", "convertOptionIdName", "elExisting", "parentId", "targetElParent", "elType", "elOptionStyle", "style", "hv", "textVerticalAlign", "textBaseline", "verticalAlign", "align", "textContentOption", "textContent", "textConfig", "convertResult", "elOptionCleaned", "getCleanedElOption", "process", "env", "NODE_ENV", "assert", "parent", "$action", "isMerge", "isReplace", "isInit", "el_1", "createEl", "isNew", "updateCommonAttrs", "removeEl", "el_2", "el", "textContentExisting", "getTextContent", "attr", "setTextContent", "clipPathOption", "clipPath", "clipPathType", "old<PERSON><PERSON><PERSON><PERSON>", "getClipPath", "newEl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyframeAnimation", "elInner", "setTextConfig", "option", "setEventData", "setTooltipConfig", "componentModel", "itemName", "name", "itemTooltipOption", "tooltip", "elOptions", "elements", "a<PERSON><PERSON><PERSON><PERSON>", "getWidth", "apiHeight", "getHeight", "xy", "i", "length", "isGroup", "parentEl", "isParentRoot", "parentElInner", "width", "height", "containerInfo", "layoutPos", "layouted", "positionElement", "boundingMode", "bounding", "transition", "animatePos", "k", "key", "val", "indexOf", "dispose", "graphicType", "Clz", "hasOwn", "getShapeClass", "add", "set", "existElParent", "traverse", "<PERSON><PERSON><PERSON>", "defaultZ", "defaultZlevel", "cursor", "item", "prop", "retrieve2", "keys", "isFunction", "draggable", "extend", "concat", "LOCATION_PARAMS", "eventData", "silent", "ignore", "componentType", "componentIndex", "info"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/component/graphic/GraphicView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport * as modelUtil from '../../util/model.js';\nimport * as graphicUtil from '../../util/graphic.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport { parsePercent } from '../../util/number.js';\nimport ComponentView from '../../view/Component.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { isEC4CompatibleStyle, convertFromEC4CompatibleStyle } from '../../util/styleCompat.js';\nimport { applyLeaveTransition, applyUpdateTransition, isTransitionAll, updateLeaveTo } from '../../animation/customGraphicTransition.js';\nimport { updateProps } from '../../animation/basicTransition.js';\nimport { applyKeyframeAnimation, stopPreviousKeyframeAnimationAndRestore } from '../../animation/customGraphicKeyframeAnimation.js';\nvar nonShapeGraphicElements = {\n  // Reserved but not supported in graphic component.\n  path: null,\n  compoundPath: null,\n  // Supported in graphic component.\n  group: graphicUtil.Group,\n  image: graphicUtil.Image,\n  text: graphicUtil.Text\n};\nexport var inner = modelUtil.makeInner();\n// ------------------------\n// View\n// ------------------------\nvar GraphicComponentView = /** @class */function (_super) {\n  __extends(GraphicComponentView, _super);\n  function GraphicComponentView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphicComponentView.type;\n    return _this;\n  }\n  GraphicComponentView.prototype.init = function () {\n    this._elMap = zrUtil.createHashMap();\n  };\n  GraphicComponentView.prototype.render = function (graphicModel, ecModel, api) {\n    // Having leveraged between use cases and algorithm complexity, a very\n    // simple layout mechanism is used:\n    // The size(width/height) can be determined by itself or its parent (not\n    // implemented yet), but can not by its children. (Top-down travel)\n    // The location(x/y) can be determined by the bounding rect of itself\n    // (can including its descendants or not) and the size of its parent.\n    // (Bottom-up travel)\n    // When `chart.clear()` or `chart.setOption({...}, true)` with the same id,\n    // view will be reused.\n    if (graphicModel !== this._lastGraphicModel) {\n      this._clear();\n    }\n    this._lastGraphicModel = graphicModel;\n    this._updateElements(graphicModel);\n    this._relocate(graphicModel, api);\n  };\n  /**\r\n   * Update graphic elements.\r\n   */\n  GraphicComponentView.prototype._updateElements = function (graphicModel) {\n    var elOptionsToUpdate = graphicModel.useElOptionsToUpdate();\n    if (!elOptionsToUpdate) {\n      return;\n    }\n    var elMap = this._elMap;\n    var rootGroup = this.group;\n    var globalZ = graphicModel.get('z');\n    var globalZLevel = graphicModel.get('zlevel');\n    // Top-down tranverse to assign graphic settings to each elements.\n    zrUtil.each(elOptionsToUpdate, function (elOption) {\n      var id = modelUtil.convertOptionIdName(elOption.id, null);\n      var elExisting = id != null ? elMap.get(id) : null;\n      var parentId = modelUtil.convertOptionIdName(elOption.parentId, null);\n      var targetElParent = parentId != null ? elMap.get(parentId) : rootGroup;\n      var elType = elOption.type;\n      var elOptionStyle = elOption.style;\n      if (elType === 'text' && elOptionStyle) {\n        // In top/bottom mode, textVerticalAlign should not be used, which cause\n        // inaccurately locating.\n        if (elOption.hv && elOption.hv[1]) {\n          elOptionStyle.textVerticalAlign = elOptionStyle.textBaseline = elOptionStyle.verticalAlign = elOptionStyle.align = null;\n        }\n      }\n      var textContentOption = elOption.textContent;\n      var textConfig = elOption.textConfig;\n      if (elOptionStyle && isEC4CompatibleStyle(elOptionStyle, elType, !!textConfig, !!textContentOption)) {\n        var convertResult = convertFromEC4CompatibleStyle(elOptionStyle, elType, true);\n        if (!textConfig && convertResult.textConfig) {\n          textConfig = elOption.textConfig = convertResult.textConfig;\n        }\n        if (!textContentOption && convertResult.textContent) {\n          textContentOption = convertResult.textContent;\n        }\n      }\n      // Remove unnecessary props to avoid potential problems.\n      var elOptionCleaned = getCleanedElOption(elOption);\n      // For simple, do not support parent change, otherwise reorder is needed.\n      if (process.env.NODE_ENV !== 'production') {\n        elExisting && zrUtil.assert(targetElParent === elExisting.parent, 'Changing parent is not supported.');\n      }\n      var $action = elOption.$action || 'merge';\n      var isMerge = $action === 'merge';\n      var isReplace = $action === 'replace';\n      if (isMerge) {\n        var isInit = !elExisting;\n        var el_1 = elExisting;\n        if (isInit) {\n          el_1 = createEl(id, targetElParent, elOption.type, elMap);\n        } else {\n          el_1 && (inner(el_1).isNew = false);\n          // Stop and restore before update any other attributes.\n          stopPreviousKeyframeAnimationAndRestore(el_1);\n        }\n        if (el_1) {\n          applyUpdateTransition(el_1, elOptionCleaned, graphicModel, {\n            isInit: isInit\n          });\n          updateCommonAttrs(el_1, elOption, globalZ, globalZLevel);\n        }\n      } else if (isReplace) {\n        removeEl(elExisting, elOption, elMap, graphicModel);\n        var el_2 = createEl(id, targetElParent, elOption.type, elMap);\n        if (el_2) {\n          applyUpdateTransition(el_2, elOptionCleaned, graphicModel, {\n            isInit: true\n          });\n          updateCommonAttrs(el_2, elOption, globalZ, globalZLevel);\n        }\n      } else if ($action === 'remove') {\n        updateLeaveTo(elExisting, elOption);\n        removeEl(elExisting, elOption, elMap, graphicModel);\n      }\n      var el = elMap.get(id);\n      if (el && textContentOption) {\n        if (isMerge) {\n          var textContentExisting = el.getTextContent();\n          textContentExisting ? textContentExisting.attr(textContentOption) : el.setTextContent(new graphicUtil.Text(textContentOption));\n        } else if (isReplace) {\n          el.setTextContent(new graphicUtil.Text(textContentOption));\n        }\n      }\n      if (el) {\n        var clipPathOption = elOption.clipPath;\n        if (clipPathOption) {\n          var clipPathType = clipPathOption.type;\n          var clipPath = void 0;\n          var isInit = false;\n          if (isMerge) {\n            var oldClipPath = el.getClipPath();\n            isInit = !oldClipPath || inner(oldClipPath).type !== clipPathType;\n            clipPath = isInit ? newEl(clipPathType) : oldClipPath;\n          } else if (isReplace) {\n            isInit = true;\n            clipPath = newEl(clipPathType);\n          }\n          el.setClipPath(clipPath);\n          applyUpdateTransition(clipPath, clipPathOption, graphicModel, {\n            isInit: isInit\n          });\n          applyKeyframeAnimation(clipPath, clipPathOption.keyframeAnimation, graphicModel);\n        }\n        var elInner = inner(el);\n        el.setTextConfig(textConfig);\n        elInner.option = elOption;\n        setEventData(el, graphicModel, elOption);\n        graphicUtil.setTooltipConfig({\n          el: el,\n          componentModel: graphicModel,\n          itemName: el.name,\n          itemTooltipOption: elOption.tooltip\n        });\n        applyKeyframeAnimation(el, elOption.keyframeAnimation, graphicModel);\n      }\n    });\n  };\n  /**\r\n   * Locate graphic elements.\r\n   */\n  GraphicComponentView.prototype._relocate = function (graphicModel, api) {\n    var elOptions = graphicModel.option.elements;\n    var rootGroup = this.group;\n    var elMap = this._elMap;\n    var apiWidth = api.getWidth();\n    var apiHeight = api.getHeight();\n    var xy = ['x', 'y'];\n    // Top-down to calculate percentage width/height of group\n    for (var i = 0; i < elOptions.length; i++) {\n      var elOption = elOptions[i];\n      var id = modelUtil.convertOptionIdName(elOption.id, null);\n      var el = id != null ? elMap.get(id) : null;\n      if (!el || !el.isGroup) {\n        continue;\n      }\n      var parentEl = el.parent;\n      var isParentRoot = parentEl === rootGroup;\n      // Like 'position:absolut' in css, default 0.\n      var elInner = inner(el);\n      var parentElInner = inner(parentEl);\n      elInner.width = parsePercent(elInner.option.width, isParentRoot ? apiWidth : parentElInner.width) || 0;\n      elInner.height = parsePercent(elInner.option.height, isParentRoot ? apiHeight : parentElInner.height) || 0;\n    }\n    // Bottom-up tranvese all elements (consider ec resize) to locate elements.\n    for (var i = elOptions.length - 1; i >= 0; i--) {\n      var elOption = elOptions[i];\n      var id = modelUtil.convertOptionIdName(elOption.id, null);\n      var el = id != null ? elMap.get(id) : null;\n      if (!el) {\n        continue;\n      }\n      var parentEl = el.parent;\n      var parentElInner = inner(parentEl);\n      var containerInfo = parentEl === rootGroup ? {\n        width: apiWidth,\n        height: apiHeight\n      } : {\n        width: parentElInner.width,\n        height: parentElInner.height\n      };\n      // PENDING\n      // Currently, when `bounding: 'all'`, the union bounding rect of the group\n      // does not include the rect of [0, 0, group.width, group.height], which\n      // is probably weird for users. Should we make a break change for it?\n      var layoutPos = {};\n      var layouted = layoutUtil.positionElement(el, elOption, containerInfo, null, {\n        hv: elOption.hv,\n        boundingMode: elOption.bounding\n      }, layoutPos);\n      if (!inner(el).isNew && layouted) {\n        var transition = elOption.transition;\n        var animatePos = {};\n        for (var k = 0; k < xy.length; k++) {\n          var key = xy[k];\n          var val = layoutPos[key];\n          if (transition && (isTransitionAll(transition) || zrUtil.indexOf(transition, key) >= 0)) {\n            animatePos[key] = val;\n          } else {\n            el[key] = val;\n          }\n        }\n        updateProps(el, animatePos, graphicModel, 0);\n      } else {\n        el.attr(layoutPos);\n      }\n    }\n  };\n  /**\r\n   * Clear all elements.\r\n   */\n  GraphicComponentView.prototype._clear = function () {\n    var _this = this;\n    var elMap = this._elMap;\n    elMap.each(function (el) {\n      removeEl(el, inner(el).option, elMap, _this._lastGraphicModel);\n    });\n    this._elMap = zrUtil.createHashMap();\n  };\n  GraphicComponentView.prototype.dispose = function () {\n    this._clear();\n  };\n  GraphicComponentView.type = 'graphic';\n  return GraphicComponentView;\n}(ComponentView);\nexport { GraphicComponentView };\nfunction newEl(graphicType) {\n  if (process.env.NODE_ENV !== 'production') {\n    zrUtil.assert(graphicType, 'graphic type MUST be set');\n  }\n  var Clz = zrUtil.hasOwn(nonShapeGraphicElements, graphicType)\n  // Those graphic elements are not shapes. They should not be\n  // overwritten by users, so do them first.\n  ? nonShapeGraphicElements[graphicType] : graphicUtil.getShapeClass(graphicType);\n  if (process.env.NODE_ENV !== 'production') {\n    zrUtil.assert(Clz, \"graphic type \" + graphicType + \" can not be found\");\n  }\n  var el = new Clz({});\n  inner(el).type = graphicType;\n  return el;\n}\nfunction createEl(id, targetElParent, graphicType, elMap) {\n  var el = newEl(graphicType);\n  targetElParent.add(el);\n  elMap.set(id, el);\n  inner(el).id = id;\n  inner(el).isNew = true;\n  return el;\n}\nfunction removeEl(elExisting, elOption, elMap, graphicModel) {\n  var existElParent = elExisting && elExisting.parent;\n  if (existElParent) {\n    elExisting.type === 'group' && elExisting.traverse(function (el) {\n      removeEl(el, elOption, elMap, graphicModel);\n    });\n    applyLeaveTransition(elExisting, elOption, graphicModel);\n    elMap.removeKey(inner(elExisting).id);\n  }\n}\nfunction updateCommonAttrs(el, elOption, defaultZ, defaultZlevel) {\n  if (!el.isGroup) {\n    zrUtil.each([['cursor', Displayable.prototype.cursor],\n    // We should not support configure z and zlevel in the element level.\n    // But seems we didn't limit it previously. So here still use it to avoid breaking.\n    ['zlevel', defaultZlevel || 0], ['z', defaultZ || 0],\n    // z2 must not be null/undefined, otherwise sort error may occur.\n    ['z2', 0]], function (item) {\n      var prop = item[0];\n      if (zrUtil.hasOwn(elOption, prop)) {\n        el[prop] = zrUtil.retrieve2(elOption[prop], item[1]);\n      } else if (el[prop] == null) {\n        el[prop] = item[1];\n      }\n    });\n  }\n  zrUtil.each(zrUtil.keys(elOption), function (key) {\n    // Assign event handlers.\n    // PENDING: should enumerate all event names or use pattern matching?\n    if (key.indexOf('on') === 0) {\n      var val = elOption[key];\n      el[key] = zrUtil.isFunction(val) ? val : null;\n    }\n  });\n  if (zrUtil.hasOwn(elOption, 'draggable')) {\n    el.draggable = elOption.draggable;\n  }\n  // Other attributes\n  elOption.name != null && (el.name = elOption.name);\n  elOption.id != null && (el.id = elOption.id);\n}\n// Remove unnecessary props to avoid potential problems.\nfunction getCleanedElOption(elOption) {\n  elOption = zrUtil.extend({}, elOption);\n  zrUtil.each(['id', 'parentId', '$action', 'hv', 'bounding', 'textContent', 'clipPath'].concat(layoutUtil.LOCATION_PARAMS), function (name) {\n    delete elOption[name];\n  });\n  return elOption;\n}\nfunction setEventData(el, graphicModel, elOption) {\n  var eventData = getECData(el).eventData;\n  // Simple optimize for large amount of elements that no need event.\n  if (!el.silent && !el.ignore && !eventData) {\n    eventData = getECData(el).eventData = {\n      componentType: 'graphic',\n      componentIndex: graphicModel.componentIndex,\n      name: el.name\n    };\n  }\n  // `elOption.info` enables user to mount some info on\n  // elements and use them in event handlers.\n  if (eventData) {\n    eventData.info = elOption.info;\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,OAAO,KAAKC,WAAW,MAAM,uBAAuB;AACpD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,oBAAoB,EAAEC,6BAA6B,QAAQ,2BAA2B;AAC/F,SAASC,oBAAoB,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,aAAa,QAAQ,4CAA4C;AACxI,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,sBAAsB,EAAEC,uCAAuC,QAAQ,mDAAmD;AACnI,IAAIC,uBAAuB,GAAG;EAC5B;EACAC,IAAI,EAAE,IAAI;EACVC,YAAY,EAAE,IAAI;EAClB;EACAC,KAAK,EAAEjB,WAAW,CAACkB,KAAK;EACxBC,KAAK,EAAEnB,WAAW,CAACoB,KAAK;EACxBC,IAAI,EAAErB,WAAW,CAACsB;AACpB,CAAC;AACD,OAAO,IAAIC,KAAK,GAAGxB,SAAS,CAACyB,SAAS,CAAC,CAAC;AACxC;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACxD9B,SAAS,CAAC6B,oBAAoB,EAAEC,MAAM,CAAC;EACvC,SAASD,oBAAoBA,CAAA,EAAG;IAC9B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,oBAAoB,CAACK,IAAI;IACtC,OAAOH,KAAK;EACd;EACAF,oBAAoB,CAACM,SAAS,CAACC,IAAI,GAAG,YAAY;IAChD,IAAI,CAACC,MAAM,GAAGpC,MAAM,CAACqC,aAAa,CAAC,CAAC;EACtC,CAAC;EACDT,oBAAoB,CAACM,SAAS,CAACI,MAAM,GAAG,UAAUC,YAAY,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAC5E;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIF,YAAY,KAAK,IAAI,CAACG,iBAAiB,EAAE;MAC3C,IAAI,CAACC,MAAM,CAAC,CAAC;IACf;IACA,IAAI,CAACD,iBAAiB,GAAGH,YAAY;IACrC,IAAI,CAACK,eAAe,CAACL,YAAY,CAAC;IAClC,IAAI,CAACM,SAAS,CAACN,YAAY,EAAEE,GAAG,CAAC;EACnC,CAAC;EACD;AACF;AACA;EACEb,oBAAoB,CAACM,SAAS,CAACU,eAAe,GAAG,UAAUL,YAAY,EAAE;IACvE,IAAIO,iBAAiB,GAAGP,YAAY,CAACQ,oBAAoB,CAAC,CAAC;IAC3D,IAAI,CAACD,iBAAiB,EAAE;MACtB;IACF;IACA,IAAIE,KAAK,GAAG,IAAI,CAACZ,MAAM;IACvB,IAAIa,SAAS,GAAG,IAAI,CAAC7B,KAAK;IAC1B,IAAI8B,OAAO,GAAGX,YAAY,CAACY,GAAG,CAAC,GAAG,CAAC;IACnC,IAAIC,YAAY,GAAGb,YAAY,CAACY,GAAG,CAAC,QAAQ,CAAC;IAC7C;IACAnD,MAAM,CAACqD,IAAI,CAACP,iBAAiB,EAAE,UAAUQ,QAAQ,EAAE;MACjD,IAAIC,EAAE,GAAGrD,SAAS,CAACsD,mBAAmB,CAACF,QAAQ,CAACC,EAAE,EAAE,IAAI,CAAC;MACzD,IAAIE,UAAU,GAAGF,EAAE,IAAI,IAAI,GAAGP,KAAK,CAACG,GAAG,CAACI,EAAE,CAAC,GAAG,IAAI;MAClD,IAAIG,QAAQ,GAAGxD,SAAS,CAACsD,mBAAmB,CAACF,QAAQ,CAACI,QAAQ,EAAE,IAAI,CAAC;MACrE,IAAIC,cAAc,GAAGD,QAAQ,IAAI,IAAI,GAAGV,KAAK,CAACG,GAAG,CAACO,QAAQ,CAAC,GAAGT,SAAS;MACvE,IAAIW,MAAM,GAAGN,QAAQ,CAACrB,IAAI;MAC1B,IAAI4B,aAAa,GAAGP,QAAQ,CAACQ,KAAK;MAClC,IAAIF,MAAM,KAAK,MAAM,IAAIC,aAAa,EAAE;QACtC;QACA;QACA,IAAIP,QAAQ,CAACS,EAAE,IAAIT,QAAQ,CAACS,EAAE,CAAC,CAAC,CAAC,EAAE;UACjCF,aAAa,CAACG,iBAAiB,GAAGH,aAAa,CAACI,YAAY,GAAGJ,aAAa,CAACK,aAAa,GAAGL,aAAa,CAACM,KAAK,GAAG,IAAI;QACzH;MACF;MACA,IAAIC,iBAAiB,GAAGd,QAAQ,CAACe,WAAW;MAC5C,IAAIC,UAAU,GAAGhB,QAAQ,CAACgB,UAAU;MACpC,IAAIT,aAAa,IAAIrD,oBAAoB,CAACqD,aAAa,EAAED,MAAM,EAAE,CAAC,CAACU,UAAU,EAAE,CAAC,CAACF,iBAAiB,CAAC,EAAE;QACnG,IAAIG,aAAa,GAAG9D,6BAA6B,CAACoD,aAAa,EAAED,MAAM,EAAE,IAAI,CAAC;QAC9E,IAAI,CAACU,UAAU,IAAIC,aAAa,CAACD,UAAU,EAAE;UAC3CA,UAAU,GAAGhB,QAAQ,CAACgB,UAAU,GAAGC,aAAa,CAACD,UAAU;QAC7D;QACA,IAAI,CAACF,iBAAiB,IAAIG,aAAa,CAACF,WAAW,EAAE;UACnDD,iBAAiB,GAAGG,aAAa,CAACF,WAAW;QAC/C;MACF;MACA;MACA,IAAIG,eAAe,GAAGC,kBAAkB,CAACnB,QAAQ,CAAC;MAClD;MACA,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCnB,UAAU,IAAIzD,MAAM,CAAC6E,MAAM,CAAClB,cAAc,KAAKF,UAAU,CAACqB,MAAM,EAAE,mCAAmC,CAAC;MACxG;MACA,IAAIC,OAAO,GAAGzB,QAAQ,CAACyB,OAAO,IAAI,OAAO;MACzC,IAAIC,OAAO,GAAGD,OAAO,KAAK,OAAO;MACjC,IAAIE,SAAS,GAAGF,OAAO,KAAK,SAAS;MACrC,IAAIC,OAAO,EAAE;QACX,IAAIE,MAAM,GAAG,CAACzB,UAAU;QACxB,IAAI0B,IAAI,GAAG1B,UAAU;QACrB,IAAIyB,MAAM,EAAE;UACVC,IAAI,GAAGC,QAAQ,CAAC7B,EAAE,EAAEI,cAAc,EAAEL,QAAQ,CAACrB,IAAI,EAAEe,KAAK,CAAC;QAC3D,CAAC,MAAM;UACLmC,IAAI,KAAKzD,KAAK,CAACyD,IAAI,CAAC,CAACE,KAAK,GAAG,KAAK,CAAC;UACnC;UACArE,uCAAuC,CAACmE,IAAI,CAAC;QAC/C;QACA,IAAIA,IAAI,EAAE;UACRxE,qBAAqB,CAACwE,IAAI,EAAEX,eAAe,EAAEjC,YAAY,EAAE;YACzD2C,MAAM,EAAEA;UACV,CAAC,CAAC;UACFI,iBAAiB,CAACH,IAAI,EAAE7B,QAAQ,EAAEJ,OAAO,EAAEE,YAAY,CAAC;QAC1D;MACF,CAAC,MAAM,IAAI6B,SAAS,EAAE;QACpBM,QAAQ,CAAC9B,UAAU,EAAEH,QAAQ,EAAEN,KAAK,EAAET,YAAY,CAAC;QACnD,IAAIiD,IAAI,GAAGJ,QAAQ,CAAC7B,EAAE,EAAEI,cAAc,EAAEL,QAAQ,CAACrB,IAAI,EAAEe,KAAK,CAAC;QAC7D,IAAIwC,IAAI,EAAE;UACR7E,qBAAqB,CAAC6E,IAAI,EAAEhB,eAAe,EAAEjC,YAAY,EAAE;YACzD2C,MAAM,EAAE;UACV,CAAC,CAAC;UACFI,iBAAiB,CAACE,IAAI,EAAElC,QAAQ,EAAEJ,OAAO,EAAEE,YAAY,CAAC;QAC1D;MACF,CAAC,MAAM,IAAI2B,OAAO,KAAK,QAAQ,EAAE;QAC/BlE,aAAa,CAAC4C,UAAU,EAAEH,QAAQ,CAAC;QACnCiC,QAAQ,CAAC9B,UAAU,EAAEH,QAAQ,EAAEN,KAAK,EAAET,YAAY,CAAC;MACrD;MACA,IAAIkD,EAAE,GAAGzC,KAAK,CAACG,GAAG,CAACI,EAAE,CAAC;MACtB,IAAIkC,EAAE,IAAIrB,iBAAiB,EAAE;QAC3B,IAAIY,OAAO,EAAE;UACX,IAAIU,mBAAmB,GAAGD,EAAE,CAACE,cAAc,CAAC,CAAC;UAC7CD,mBAAmB,GAAGA,mBAAmB,CAACE,IAAI,CAACxB,iBAAiB,CAAC,GAAGqB,EAAE,CAACI,cAAc,CAAC,IAAI1F,WAAW,CAACsB,IAAI,CAAC2C,iBAAiB,CAAC,CAAC;QAChI,CAAC,MAAM,IAAIa,SAAS,EAAE;UACpBQ,EAAE,CAACI,cAAc,CAAC,IAAI1F,WAAW,CAACsB,IAAI,CAAC2C,iBAAiB,CAAC,CAAC;QAC5D;MACF;MACA,IAAIqB,EAAE,EAAE;QACN,IAAIK,cAAc,GAAGxC,QAAQ,CAACyC,QAAQ;QACtC,IAAID,cAAc,EAAE;UAClB,IAAIE,YAAY,GAAGF,cAAc,CAAC7D,IAAI;UACtC,IAAI8D,QAAQ,GAAG,KAAK,CAAC;UACrB,IAAIb,MAAM,GAAG,KAAK;UAClB,IAAIF,OAAO,EAAE;YACX,IAAIiB,WAAW,GAAGR,EAAE,CAACS,WAAW,CAAC,CAAC;YAClChB,MAAM,GAAG,CAACe,WAAW,IAAIvE,KAAK,CAACuE,WAAW,CAAC,CAAChE,IAAI,KAAK+D,YAAY;YACjED,QAAQ,GAAGb,MAAM,GAAGiB,KAAK,CAACH,YAAY,CAAC,GAAGC,WAAW;UACvD,CAAC,MAAM,IAAIhB,SAAS,EAAE;YACpBC,MAAM,GAAG,IAAI;YACba,QAAQ,GAAGI,KAAK,CAACH,YAAY,CAAC;UAChC;UACAP,EAAE,CAACW,WAAW,CAACL,QAAQ,CAAC;UACxBpF,qBAAqB,CAACoF,QAAQ,EAAED,cAAc,EAAEvD,YAAY,EAAE;YAC5D2C,MAAM,EAAEA;UACV,CAAC,CAAC;UACFnE,sBAAsB,CAACgF,QAAQ,EAAED,cAAc,CAACO,iBAAiB,EAAE9D,YAAY,CAAC;QAClF;QACA,IAAI+D,OAAO,GAAG5E,KAAK,CAAC+D,EAAE,CAAC;QACvBA,EAAE,CAACc,aAAa,CAACjC,UAAU,CAAC;QAC5BgC,OAAO,CAACE,MAAM,GAAGlD,QAAQ;QACzBmD,YAAY,CAAChB,EAAE,EAAElD,YAAY,EAAEe,QAAQ,CAAC;QACxCnD,WAAW,CAACuG,gBAAgB,CAAC;UAC3BjB,EAAE,EAAEA,EAAE;UACNkB,cAAc,EAAEpE,YAAY;UAC5BqE,QAAQ,EAAEnB,EAAE,CAACoB,IAAI;UACjBC,iBAAiB,EAAExD,QAAQ,CAACyD;QAC9B,CAAC,CAAC;QACFhG,sBAAsB,CAAC0E,EAAE,EAAEnC,QAAQ,CAAC+C,iBAAiB,EAAE9D,YAAY,CAAC;MACtE;IACF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACEX,oBAAoB,CAACM,SAAS,CAACW,SAAS,GAAG,UAAUN,YAAY,EAAEE,GAAG,EAAE;IACtE,IAAIuE,SAAS,GAAGzE,YAAY,CAACiE,MAAM,CAACS,QAAQ;IAC5C,IAAIhE,SAAS,GAAG,IAAI,CAAC7B,KAAK;IAC1B,IAAI4B,KAAK,GAAG,IAAI,CAACZ,MAAM;IACvB,IAAI8E,QAAQ,GAAGzE,GAAG,CAAC0E,QAAQ,CAAC,CAAC;IAC7B,IAAIC,SAAS,GAAG3E,GAAG,CAAC4E,SAAS,CAAC,CAAC;IAC/B,IAAIC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IACnB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,SAAS,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAIjE,QAAQ,GAAG0D,SAAS,CAACO,CAAC,CAAC;MAC3B,IAAIhE,EAAE,GAAGrD,SAAS,CAACsD,mBAAmB,CAACF,QAAQ,CAACC,EAAE,EAAE,IAAI,CAAC;MACzD,IAAIkC,EAAE,GAAGlC,EAAE,IAAI,IAAI,GAAGP,KAAK,CAACG,GAAG,CAACI,EAAE,CAAC,GAAG,IAAI;MAC1C,IAAI,CAACkC,EAAE,IAAI,CAACA,EAAE,CAACgC,OAAO,EAAE;QACtB;MACF;MACA,IAAIC,QAAQ,GAAGjC,EAAE,CAACX,MAAM;MACxB,IAAI6C,YAAY,GAAGD,QAAQ,KAAKzE,SAAS;MACzC;MACA,IAAIqD,OAAO,GAAG5E,KAAK,CAAC+D,EAAE,CAAC;MACvB,IAAImC,aAAa,GAAGlG,KAAK,CAACgG,QAAQ,CAAC;MACnCpB,OAAO,CAACuB,KAAK,GAAGxH,YAAY,CAACiG,OAAO,CAACE,MAAM,CAACqB,KAAK,EAAEF,YAAY,GAAGT,QAAQ,GAAGU,aAAa,CAACC,KAAK,CAAC,IAAI,CAAC;MACtGvB,OAAO,CAACwB,MAAM,GAAGzH,YAAY,CAACiG,OAAO,CAACE,MAAM,CAACsB,MAAM,EAAEH,YAAY,GAAGP,SAAS,GAAGQ,aAAa,CAACE,MAAM,CAAC,IAAI,CAAC;IAC5G;IACA;IACA,KAAK,IAAIP,CAAC,GAAGP,SAAS,CAACQ,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,IAAIjE,QAAQ,GAAG0D,SAAS,CAACO,CAAC,CAAC;MAC3B,IAAIhE,EAAE,GAAGrD,SAAS,CAACsD,mBAAmB,CAACF,QAAQ,CAACC,EAAE,EAAE,IAAI,CAAC;MACzD,IAAIkC,EAAE,GAAGlC,EAAE,IAAI,IAAI,GAAGP,KAAK,CAACG,GAAG,CAACI,EAAE,CAAC,GAAG,IAAI;MAC1C,IAAI,CAACkC,EAAE,EAAE;QACP;MACF;MACA,IAAIiC,QAAQ,GAAGjC,EAAE,CAACX,MAAM;MACxB,IAAI8C,aAAa,GAAGlG,KAAK,CAACgG,QAAQ,CAAC;MACnC,IAAIK,aAAa,GAAGL,QAAQ,KAAKzE,SAAS,GAAG;QAC3C4E,KAAK,EAAEX,QAAQ;QACfY,MAAM,EAAEV;MACV,CAAC,GAAG;QACFS,KAAK,EAAED,aAAa,CAACC,KAAK;QAC1BC,MAAM,EAAEF,aAAa,CAACE;MACxB,CAAC;MACD;MACA;MACA;MACA;MACA,IAAIE,SAAS,GAAG,CAAC,CAAC;MAClB,IAAIC,QAAQ,GAAG7H,UAAU,CAAC8H,eAAe,CAACzC,EAAE,EAAEnC,QAAQ,EAAEyE,aAAa,EAAE,IAAI,EAAE;QAC3EhE,EAAE,EAAET,QAAQ,CAACS,EAAE;QACfoE,YAAY,EAAE7E,QAAQ,CAAC8E;MACzB,CAAC,EAAEJ,SAAS,CAAC;MACb,IAAI,CAACtG,KAAK,CAAC+D,EAAE,CAAC,CAACJ,KAAK,IAAI4C,QAAQ,EAAE;QAChC,IAAII,UAAU,GAAG/E,QAAQ,CAAC+E,UAAU;QACpC,IAAIC,UAAU,GAAG,CAAC,CAAC;QACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,EAAE,CAACE,MAAM,EAAEe,CAAC,EAAE,EAAE;UAClC,IAAIC,GAAG,GAAGlB,EAAE,CAACiB,CAAC,CAAC;UACf,IAAIE,GAAG,GAAGT,SAAS,CAACQ,GAAG,CAAC;UACxB,IAAIH,UAAU,KAAKzH,eAAe,CAACyH,UAAU,CAAC,IAAIrI,MAAM,CAAC0I,OAAO,CAACL,UAAU,EAAEG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;YACvFF,UAAU,CAACE,GAAG,CAAC,GAAGC,GAAG;UACvB,CAAC,MAAM;YACLhD,EAAE,CAAC+C,GAAG,CAAC,GAAGC,GAAG;UACf;QACF;QACA3H,WAAW,CAAC2E,EAAE,EAAE6C,UAAU,EAAE/F,YAAY,EAAE,CAAC,CAAC;MAC9C,CAAC,MAAM;QACLkD,EAAE,CAACG,IAAI,CAACoC,SAAS,CAAC;MACpB;IACF;EACF,CAAC;EACD;AACF;AACA;EACEpG,oBAAoB,CAACM,SAAS,CAACS,MAAM,GAAG,YAAY;IAClD,IAAIb,KAAK,GAAG,IAAI;IAChB,IAAIkB,KAAK,GAAG,IAAI,CAACZ,MAAM;IACvBY,KAAK,CAACK,IAAI,CAAC,UAAUoC,EAAE,EAAE;MACvBF,QAAQ,CAACE,EAAE,EAAE/D,KAAK,CAAC+D,EAAE,CAAC,CAACe,MAAM,EAAExD,KAAK,EAAElB,KAAK,CAACY,iBAAiB,CAAC;IAChE,CAAC,CAAC;IACF,IAAI,CAACN,MAAM,GAAGpC,MAAM,CAACqC,aAAa,CAAC,CAAC;EACtC,CAAC;EACDT,oBAAoB,CAACM,SAAS,CAACyG,OAAO,GAAG,YAAY;IACnD,IAAI,CAAChG,MAAM,CAAC,CAAC;EACf,CAAC;EACDf,oBAAoB,CAACK,IAAI,GAAG,SAAS;EACrC,OAAOL,oBAAoB;AAC7B,CAAC,CAACtB,aAAa,CAAC;AAChB,SAASsB,oBAAoB;AAC7B,SAASuE,KAAKA,CAACyC,WAAW,EAAE;EAC1B,IAAIlE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC5E,MAAM,CAAC6E,MAAM,CAAC+D,WAAW,EAAE,0BAA0B,CAAC;EACxD;EACA,IAAIC,GAAG,GAAG7I,MAAM,CAAC8I,MAAM,CAAC7H,uBAAuB,EAAE2H,WAAW;EAC5D;EACA;EAAA,EACE3H,uBAAuB,CAAC2H,WAAW,CAAC,GAAGzI,WAAW,CAAC4I,aAAa,CAACH,WAAW,CAAC;EAC/E,IAAIlE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC5E,MAAM,CAAC6E,MAAM,CAACgE,GAAG,EAAE,eAAe,GAAGD,WAAW,GAAG,mBAAmB,CAAC;EACzE;EACA,IAAInD,EAAE,GAAG,IAAIoD,GAAG,CAAC,CAAC,CAAC,CAAC;EACpBnH,KAAK,CAAC+D,EAAE,CAAC,CAACxD,IAAI,GAAG2G,WAAW;EAC5B,OAAOnD,EAAE;AACX;AACA,SAASL,QAAQA,CAAC7B,EAAE,EAAEI,cAAc,EAAEiF,WAAW,EAAE5F,KAAK,EAAE;EACxD,IAAIyC,EAAE,GAAGU,KAAK,CAACyC,WAAW,CAAC;EAC3BjF,cAAc,CAACqF,GAAG,CAACvD,EAAE,CAAC;EACtBzC,KAAK,CAACiG,GAAG,CAAC1F,EAAE,EAAEkC,EAAE,CAAC;EACjB/D,KAAK,CAAC+D,EAAE,CAAC,CAAClC,EAAE,GAAGA,EAAE;EACjB7B,KAAK,CAAC+D,EAAE,CAAC,CAACJ,KAAK,GAAG,IAAI;EACtB,OAAOI,EAAE;AACX;AACA,SAASF,QAAQA,CAAC9B,UAAU,EAAEH,QAAQ,EAAEN,KAAK,EAAET,YAAY,EAAE;EAC3D,IAAI2G,aAAa,GAAGzF,UAAU,IAAIA,UAAU,CAACqB,MAAM;EACnD,IAAIoE,aAAa,EAAE;IACjBzF,UAAU,CAACxB,IAAI,KAAK,OAAO,IAAIwB,UAAU,CAAC0F,QAAQ,CAAC,UAAU1D,EAAE,EAAE;MAC/DF,QAAQ,CAACE,EAAE,EAAEnC,QAAQ,EAAEN,KAAK,EAAET,YAAY,CAAC;IAC7C,CAAC,CAAC;IACF7B,oBAAoB,CAAC+C,UAAU,EAAEH,QAAQ,EAAEf,YAAY,CAAC;IACxDS,KAAK,CAACoG,SAAS,CAAC1H,KAAK,CAAC+B,UAAU,CAAC,CAACF,EAAE,CAAC;EACvC;AACF;AACA,SAAS+B,iBAAiBA,CAACG,EAAE,EAAEnC,QAAQ,EAAE+F,QAAQ,EAAEC,aAAa,EAAE;EAChE,IAAI,CAAC7D,EAAE,CAACgC,OAAO,EAAE;IACfzH,MAAM,CAACqD,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAEpD,WAAW,CAACiC,SAAS,CAACqH,MAAM,CAAC;IACrD;IACA;IACA,CAAC,QAAQ,EAAED,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,EAAED,QAAQ,IAAI,CAAC,CAAC;IACpD;IACA,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,UAAUG,IAAI,EAAE;MAC1B,IAAIC,IAAI,GAAGD,IAAI,CAAC,CAAC,CAAC;MAClB,IAAIxJ,MAAM,CAAC8I,MAAM,CAACxF,QAAQ,EAAEmG,IAAI,CAAC,EAAE;QACjChE,EAAE,CAACgE,IAAI,CAAC,GAAGzJ,MAAM,CAAC0J,SAAS,CAACpG,QAAQ,CAACmG,IAAI,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,MAAM,IAAI/D,EAAE,CAACgE,IAAI,CAAC,IAAI,IAAI,EAAE;QAC3BhE,EAAE,CAACgE,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;EACJ;EACAxJ,MAAM,CAACqD,IAAI,CAACrD,MAAM,CAAC2J,IAAI,CAACrG,QAAQ,CAAC,EAAE,UAAUkF,GAAG,EAAE;IAChD;IACA;IACA,IAAIA,GAAG,CAACE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MAC3B,IAAID,GAAG,GAAGnF,QAAQ,CAACkF,GAAG,CAAC;MACvB/C,EAAE,CAAC+C,GAAG,CAAC,GAAGxI,MAAM,CAAC4J,UAAU,CAACnB,GAAG,CAAC,GAAGA,GAAG,GAAG,IAAI;IAC/C;EACF,CAAC,CAAC;EACF,IAAIzI,MAAM,CAAC8I,MAAM,CAACxF,QAAQ,EAAE,WAAW,CAAC,EAAE;IACxCmC,EAAE,CAACoE,SAAS,GAAGvG,QAAQ,CAACuG,SAAS;EACnC;EACA;EACAvG,QAAQ,CAACuD,IAAI,IAAI,IAAI,KAAKpB,EAAE,CAACoB,IAAI,GAAGvD,QAAQ,CAACuD,IAAI,CAAC;EAClDvD,QAAQ,CAACC,EAAE,IAAI,IAAI,KAAKkC,EAAE,CAAClC,EAAE,GAAGD,QAAQ,CAACC,EAAE,CAAC;AAC9C;AACA;AACA,SAASkB,kBAAkBA,CAACnB,QAAQ,EAAE;EACpCA,QAAQ,GAAGtD,MAAM,CAAC8J,MAAM,CAAC,CAAC,CAAC,EAAExG,QAAQ,CAAC;EACtCtD,MAAM,CAACqD,IAAI,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC0G,MAAM,CAAC3J,UAAU,CAAC4J,eAAe,CAAC,EAAE,UAAUnD,IAAI,EAAE;IACzI,OAAOvD,QAAQ,CAACuD,IAAI,CAAC;EACvB,CAAC,CAAC;EACF,OAAOvD,QAAQ;AACjB;AACA,SAASmD,YAAYA,CAAChB,EAAE,EAAElD,YAAY,EAAEe,QAAQ,EAAE;EAChD,IAAI2G,SAAS,GAAG1J,SAAS,CAACkF,EAAE,CAAC,CAACwE,SAAS;EACvC;EACA,IAAI,CAACxE,EAAE,CAACyE,MAAM,IAAI,CAACzE,EAAE,CAAC0E,MAAM,IAAI,CAACF,SAAS,EAAE;IAC1CA,SAAS,GAAG1J,SAAS,CAACkF,EAAE,CAAC,CAACwE,SAAS,GAAG;MACpCG,aAAa,EAAE,SAAS;MACxBC,cAAc,EAAE9H,YAAY,CAAC8H,cAAc;MAC3CxD,IAAI,EAAEpB,EAAE,CAACoB;IACX,CAAC;EACH;EACA;EACA;EACA,IAAIoD,SAAS,EAAE;IACbA,SAAS,CAACK,IAAI,GAAGhH,QAAQ,CAACgH,IAAI;EAChC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}