{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../../coord/Axis.js';\n/**\r\n * Extend axis 2d\r\n */\nvar TimelineAxis = /** @class */function (_super) {\n  __extends(TimelineAxis, _super);\n  function TimelineAxis(dim, scale, coordExtent, axisType) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    _this.type = axisType || 'value';\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TimelineAxis.prototype.getLabelModel = function () {\n    // Force override\n    return this.model.getModel('label');\n  };\n  /**\r\n   * @override\r\n   */\n  TimelineAxis.prototype.isHorizontal = function () {\n    return this.model.get('orient') === 'horizontal';\n  };\n  return TimelineAxis;\n}(Axis);\nexport default TimelineAxis;", "map": {"version": 3, "names": ["__extends", "Axis", "TimelineAxis", "_super", "dim", "scale", "coordExtent", "axisType", "_this", "call", "type", "prototype", "getLabelModel", "model", "getModel", "isHorizontal", "get"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/component/timeline/TimelineAxis.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../../coord/Axis.js';\n/**\r\n * Extend axis 2d\r\n */\nvar TimelineAxis = /** @class */function (_super) {\n  __extends(TimelineAxis, _super);\n  function TimelineAxis(dim, scale, coordExtent, axisType) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    _this.type = axisType || 'value';\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TimelineAxis.prototype.getLabelModel = function () {\n    // Force override\n    return this.model.getModel('label');\n  };\n  /**\r\n   * @override\r\n   */\n  TimelineAxis.prototype.isHorizontal = function () {\n    return this.model.get('orient') === 'horizontal';\n  };\n  return TimelineAxis;\n}(Axis);\nexport default TimelineAxis;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,qBAAqB;AACtC;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDH,SAAS,CAACE,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAACE,GAAG,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE;IACvD,IAAIC,KAAK,GAAGL,MAAM,CAACM,IAAI,CAAC,IAAI,EAAEL,GAAG,EAAEC,KAAK,EAAEC,WAAW,CAAC,IAAI,IAAI;IAC9DE,KAAK,CAACE,IAAI,GAAGH,QAAQ,IAAI,OAAO;IAChC,OAAOC,KAAK;EACd;EACA;AACF;AACA;EACEN,YAAY,CAACS,SAAS,CAACC,aAAa,GAAG,YAAY;IACjD;IACA,OAAO,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC;EACrC,CAAC;EACD;AACF;AACA;EACEZ,YAAY,CAACS,SAAS,CAACI,YAAY,GAAG,YAAY;IAChD,OAAO,IAAI,CAACF,KAAK,CAACG,GAAG,CAAC,QAAQ,CAAC,KAAK,YAAY;EAClD,CAAC;EACD,OAAOd,YAAY;AACrB,CAAC,CAACD,IAAI,CAAC;AACP,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}