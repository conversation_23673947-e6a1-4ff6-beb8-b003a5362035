import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Card,
  Row,
  Col,
  Descriptions,
  Button,
  Space,
  Typography,
  Tag,
  Table,
  Statistic,
  Modal,
  Form,
  Select,
  InputNumber,
  message,
  Tooltip,
  Tree
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  PlusOutlined,
  DeleteOutlined,
  CalculatorOutlined,
  AppstoreOutlined,
  SaveOutlined
} from '@ant-design/icons';

import {
  selectBOMs,
  selectMaterials,
  setCurrentBOM,
  updateBOM,
  calculateBOMCost
} from '../../store/slices/bomSlice';

const { Title, Text } = Typography;
const { Option } = Select;

const BOMDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const boms = useSelector(selectBOMs);
  const materials = useSelector(selectMaterials);

  const [bom, setBOM] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    const foundBOM = boms.find(b => b.id === id);
    if (foundBOM) {
      setBOM(foundBOM);
      dispatch(setCurrentBOM(foundBOM));
    }
  }, [id, boms, dispatch]);

  if (!bom) {
    return <div>BOM不存在</div>;
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getStatusColor = (status) => {
    const colors = {
      'active': 'green',
      'inactive': 'red',
      'draft': 'orange'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      'active': '启用',
      'inactive': '停用',
      'draft': '草稿'
    };
    return texts[status] || status;
  };

  const getTypeColor = (type) => {
    const colors = {
      'product': 'blue',
      'component': 'green',
      'material': 'orange'
    };
    return colors[type] || 'default';
  };

  const getTypeText = (type) => {
    const texts = {
      'product': '产品',
      'component': '组件',
      'material': '物料'
    };
    return texts[type] || type;
  };

  const handleAddItem = () => {
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditItem = (item) => {
    form.setFieldsValue(item);
    setModalVisible(true);
  };

  const handleDeleteItem = (itemId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个物料项吗？',
      onOk: () => {
        const updatedBOM = {
          ...bom,
          items: bom.items.filter(item => item.id !== itemId)
        };
        setBOM(updatedBOM);
        dispatch(calculateBOMCost({ bomId: bom.id }));
        message.success('删除成功');
      }
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const selectedMaterial = materials.find(m => m.id === values.materialId);
      
      if (!selectedMaterial) {
        message.error('请选择有效的物料');
        return;
      }

      const newItem = {
        id: Math.max(...bom.items.map(i => i.id), 0) + 1,
        materialId: selectedMaterial.id,
        materialCode: selectedMaterial.code,
        materialName: selectedMaterial.name,
        specification: selectedMaterial.specification,
        quantity: values.quantity,
        unit: selectedMaterial.unit,
        unitCost: selectedMaterial.currentCost,
        totalCost: values.quantity * selectedMaterial.currentCost,
        level: 1,
        parentId: null,
        children: []
      };

      const updatedBOM = {
        ...bom,
        items: [...bom.items, newItem],
        totalCost: bom.totalCost + newItem.totalCost
      };

      setBOM(updatedBOM);
      setModalVisible(false);
      message.success('添加成功');
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleSave = () => {
    dispatch(updateBOM(bom));
    setEditMode(false);
    message.success('保存成功');
  };

  const columns = [
    {
      title: '物料编码',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 150
    },
    {
      title: '物料名称',
      dataIndex: 'materialName',
      key: 'materialName',
      width: 200
    },
    {
      title: '规格型号',
      dataIndex: 'specification',
      key: 'specification',
      width: 200,
      ellipsis: true
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      render: (quantity, record) => `${quantity} ${record.unit}`
    },
    {
      title: '单价',
      dataIndex: 'unitCost',
      key: 'unitCost',
      width: 100,
      render: (cost) => formatCurrency(cost)
    },
    {
      title: '小计',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 100,
      render: (cost) => (
        <Text strong style={{ color: '#1890ff' }}>
          {formatCurrency(cost)}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEditItem(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
              onClick={() => handleDeleteItem(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 计算统计数据
  const stats = {
    itemCount: bom.items?.length || 0,
    totalCost: bom.totalCost || 0,
    avgCost: bom.items?.length > 0 ? bom.totalCost / bom.items.length : 0,
    materialTypes: new Set(bom.items?.map(item => {
      const material = materials.find(m => m.id === item.materialId);
      return material?.category;
    }).filter(Boolean)).size
  };

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/bom')}
            style={{ marginRight: '16px' }}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            {bom.name}
          </Title>
        </div>
        <Space>
          {editMode ? (
            <>
              <Button onClick={() => setEditMode(false)}>取消</Button>
              <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
                保存
              </Button>
            </>
          ) : (
            <Button icon={<EditOutlined />} onClick={() => setEditMode(true)}>
              编辑
            </Button>
          )}
        </Space>
      </div>

      {/* BOM基本信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={16}>
          <Card title="基本信息">
            <Descriptions column={{ xs: 1, sm: 2, md: 2 }}>
              <Descriptions.Item label="BOM编码">
                {bom.code}
              </Descriptions.Item>
              <Descriptions.Item label="BOM名称">
                {bom.name}
              </Descriptions.Item>
              <Descriptions.Item label="版本">
                {bom.version}
              </Descriptions.Item>
              <Descriptions.Item label="类型">
                <Tag color={getTypeColor(bom.type)}>
                  {getTypeText(bom.type)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(bom.status)}>
                  {getStatusText(bom.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建人">
                {bom.createdBy}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {bom.createTime}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {bom.updateTime}
              </Descriptions.Item>
              <Descriptions.Item label="描述" span={2}>
                {bom.description || '无'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="成本统计">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="物料项数"
                  value={stats.itemCount}
                  suffix="项"
                  prefix={<AppstoreOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="物料类别"
                  value={stats.materialTypes}
                  suffix="类"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="总成本"
                  value={stats.totalCost}
                  formatter={(value) => formatCurrency(value)}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="平均成本"
                  value={stats.avgCost}
                  formatter={(value) => formatCurrency(value)}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* BOM明细 */}
      <Card 
        title="BOM明细"
        extra={
          <Space>
            <Button 
              icon={<CalculatorOutlined />}
              onClick={() => dispatch(calculateBOMCost({ bomId: bom.id }))}
            >
              重新计算
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleAddItem}
            >
              添加物料
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={bom.items || []}
          rowKey="id"
          pagination={false}
          size="small"
          summary={(pageData) => {
            const totalCost = pageData.reduce((sum, item) => sum + item.totalCost, 0);
            return (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={5}>
                  <Text strong>合计</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <Text strong style={{ color: '#1890ff' }}>
                    {formatCurrency(totalCost)}
                  </Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={2} />
              </Table.Summary.Row>
            );
          }}
        />
      </Card>

      {/* 添加物料弹窗 */}
      <Modal
        title="添加物料"
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="materialId"
            label="选择物料"
            rules={[{ required: true, message: '请选择物料' }]}
          >
            <Select
              placeholder="请选择物料"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {materials.map(material => (
                <Option key={material.id} value={material.id}>
                  {material.code} - {material.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="quantity"
            label="数量"
            rules={[{ required: true, message: '请输入数量' }]}
          >
            <InputNumber
              min={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder="请输入数量"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BOMDetail;
