{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport axisDefault from './axisDefault.js';\nimport { getLayoutParams, mergeLayoutParam, fetchLayoutMode } from '../util/layout.js';\nimport OrdinalMeta from '../data/OrdinalMeta.js';\nimport { AXIS_TYPES } from './axisCommonTypes.js';\nimport { each, merge } from 'zrender/lib/core/util.js';\n/**\r\n * Generate sub axis model class\r\n * @param axisName 'x' 'y' 'radius' 'angle' 'parallel' ...\r\n */\nexport default function axisModelCreator(registers, axisName, BaseAxisModelClass, extraDefaultOption) {\n  each(AXIS_TYPES, function (v, axisType) {\n    var defaultOption = merge(merge({}, axisDefault[axisType], true), extraDefaultOption, true);\n    var AxisModel = /** @class */function (_super) {\n      __extends(AxisModel, _super);\n      function AxisModel() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = axisName + 'Axis.' + axisType;\n        return _this;\n      }\n      AxisModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n        var layoutMode = fetchLayoutMode(this);\n        var inputPositionParams = layoutMode ? getLayoutParams(option) : {};\n        var themeModel = ecModel.getTheme();\n        merge(option, themeModel.get(axisType + 'Axis'));\n        merge(option, this.getDefaultOption());\n        option.type = getAxisType(option);\n        if (layoutMode) {\n          mergeLayoutParam(option, inputPositionParams, layoutMode);\n        }\n      };\n      AxisModel.prototype.optionUpdated = function () {\n        var thisOption = this.option;\n        if (thisOption.type === 'category') {\n          this.__ordinalMeta = OrdinalMeta.createByAxisModel(this);\n        }\n      };\n      /**\r\n       * Should not be called before all of 'getInitailData' finished.\r\n       * Because categories are collected during initializing data.\r\n       */\n      AxisModel.prototype.getCategories = function (rawData) {\n        var option = this.option;\n        // FIXME\n        // warning if called before all of 'getInitailData' finished.\n        if (option.type === 'category') {\n          if (rawData) {\n            return option.data;\n          }\n          return this.__ordinalMeta.categories;\n        }\n      };\n      AxisModel.prototype.getOrdinalMeta = function () {\n        return this.__ordinalMeta;\n      };\n      AxisModel.type = axisName + 'Axis.' + axisType;\n      AxisModel.defaultOption = defaultOption;\n      return AxisModel;\n    }(BaseAxisModelClass);\n    registers.registerComponentModel(AxisModel);\n  });\n  registers.registerSubTypeDefaulter(axisName + 'Axis', getAxisType);\n}\nfunction getAxisType(option) {\n  // Default axis with data is category axis\n  return option.type || (option.data ? 'category' : 'value');\n}", "map": {"version": 3, "names": ["__extends", "axisDefault", "getLayoutParams", "mergeLayoutParam", "fetchLayoutMode", "OrdinalMeta", "AXIS_TYPES", "each", "merge", "axisModelCreator", "registers", "axisName", "BaseAxisModelClass", "extraDefaultOption", "v", "axisType", "defaultOption", "AxisModel", "_super", "_this", "apply", "arguments", "type", "prototype", "mergeDefaultAndTheme", "option", "ecModel", "layoutMode", "inputPositionParams", "themeModel", "getTheme", "get", "getDefaultOption", "getAxisType", "optionUpdated", "thisOption", "__ordinalMeta", "createByAxisModel", "getCategories", "rawData", "data", "categories", "getOrdinalMeta", "registerComponentModel", "registerSubTypeDefaulter"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/coord/axisModelCreator.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport axisDefault from './axisDefault.js';\nimport { getLayoutParams, mergeLayoutParam, fetchLayoutMode } from '../util/layout.js';\nimport OrdinalMeta from '../data/OrdinalMeta.js';\nimport { AXIS_TYPES } from './axisCommonTypes.js';\nimport { each, merge } from 'zrender/lib/core/util.js';\n/**\r\n * Generate sub axis model class\r\n * @param axisName 'x' 'y' 'radius' 'angle' 'parallel' ...\r\n */\nexport default function axisModelCreator(registers, axisName, BaseAxisModelClass, extraDefaultOption) {\n  each(AXIS_TYPES, function (v, axisType) {\n    var defaultOption = merge(merge({}, axisDefault[axisType], true), extraDefaultOption, true);\n    var AxisModel = /** @class */function (_super) {\n      __extends(AxisModel, _super);\n      function AxisModel() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = axisName + 'Axis.' + axisType;\n        return _this;\n      }\n      AxisModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n        var layoutMode = fetchLayoutMode(this);\n        var inputPositionParams = layoutMode ? getLayoutParams(option) : {};\n        var themeModel = ecModel.getTheme();\n        merge(option, themeModel.get(axisType + 'Axis'));\n        merge(option, this.getDefaultOption());\n        option.type = getAxisType(option);\n        if (layoutMode) {\n          mergeLayoutParam(option, inputPositionParams, layoutMode);\n        }\n      };\n      AxisModel.prototype.optionUpdated = function () {\n        var thisOption = this.option;\n        if (thisOption.type === 'category') {\n          this.__ordinalMeta = OrdinalMeta.createByAxisModel(this);\n        }\n      };\n      /**\r\n       * Should not be called before all of 'getInitailData' finished.\r\n       * Because categories are collected during initializing data.\r\n       */\n      AxisModel.prototype.getCategories = function (rawData) {\n        var option = this.option;\n        // FIXME\n        // warning if called before all of 'getInitailData' finished.\n        if (option.type === 'category') {\n          if (rawData) {\n            return option.data;\n          }\n          return this.__ordinalMeta.categories;\n        }\n      };\n      AxisModel.prototype.getOrdinalMeta = function () {\n        return this.__ordinalMeta;\n      };\n      AxisModel.type = axisName + 'Axis.' + axisType;\n      AxisModel.defaultOption = defaultOption;\n      return AxisModel;\n    }(BaseAxisModelClass);\n    registers.registerComponentModel(AxisModel);\n  });\n  registers.registerSubTypeDefaulter(axisName + 'Axis', getAxisType);\n}\nfunction getAxisType(option) {\n  // Default axis with data is category axis\n  return option.type || (option.data ? 'category' : 'value');\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,eAAe,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,mBAAmB;AACtF,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,IAAI,EAAEC,KAAK,QAAQ,0BAA0B;AACtD;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAE;EACpGN,IAAI,CAACD,UAAU,EAAE,UAAUQ,CAAC,EAAEC,QAAQ,EAAE;IACtC,IAAIC,aAAa,GAAGR,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC,EAAEP,WAAW,CAACc,QAAQ,CAAC,EAAE,IAAI,CAAC,EAAEF,kBAAkB,EAAE,IAAI,CAAC;IAC3F,IAAII,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;MAC7ClB,SAAS,CAACiB,SAAS,EAAEC,MAAM,CAAC;MAC5B,SAASD,SAASA,CAAA,EAAG;QACnB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;QACpEF,KAAK,CAACG,IAAI,GAAGX,QAAQ,GAAG,OAAO,GAAGI,QAAQ;QAC1C,OAAOI,KAAK;MACd;MACAF,SAAS,CAACM,SAAS,CAACC,oBAAoB,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;QACpE,IAAIC,UAAU,GAAGvB,eAAe,CAAC,IAAI,CAAC;QACtC,IAAIwB,mBAAmB,GAAGD,UAAU,GAAGzB,eAAe,CAACuB,MAAM,CAAC,GAAG,CAAC,CAAC;QACnE,IAAII,UAAU,GAAGH,OAAO,CAACI,QAAQ,CAAC,CAAC;QACnCtB,KAAK,CAACiB,MAAM,EAAEI,UAAU,CAACE,GAAG,CAAChB,QAAQ,GAAG,MAAM,CAAC,CAAC;QAChDP,KAAK,CAACiB,MAAM,EAAE,IAAI,CAACO,gBAAgB,CAAC,CAAC,CAAC;QACtCP,MAAM,CAACH,IAAI,GAAGW,WAAW,CAACR,MAAM,CAAC;QACjC,IAAIE,UAAU,EAAE;UACdxB,gBAAgB,CAACsB,MAAM,EAAEG,mBAAmB,EAAED,UAAU,CAAC;QAC3D;MACF,CAAC;MACDV,SAAS,CAACM,SAAS,CAACW,aAAa,GAAG,YAAY;QAC9C,IAAIC,UAAU,GAAG,IAAI,CAACV,MAAM;QAC5B,IAAIU,UAAU,CAACb,IAAI,KAAK,UAAU,EAAE;UAClC,IAAI,CAACc,aAAa,GAAG/B,WAAW,CAACgC,iBAAiB,CAAC,IAAI,CAAC;QAC1D;MACF,CAAC;MACD;AACN;AACA;AACA;MACMpB,SAAS,CAACM,SAAS,CAACe,aAAa,GAAG,UAAUC,OAAO,EAAE;QACrD,IAAId,MAAM,GAAG,IAAI,CAACA,MAAM;QACxB;QACA;QACA,IAAIA,MAAM,CAACH,IAAI,KAAK,UAAU,EAAE;UAC9B,IAAIiB,OAAO,EAAE;YACX,OAAOd,MAAM,CAACe,IAAI;UACpB;UACA,OAAO,IAAI,CAACJ,aAAa,CAACK,UAAU;QACtC;MACF,CAAC;MACDxB,SAAS,CAACM,SAAS,CAACmB,cAAc,GAAG,YAAY;QAC/C,OAAO,IAAI,CAACN,aAAa;MAC3B,CAAC;MACDnB,SAAS,CAACK,IAAI,GAAGX,QAAQ,GAAG,OAAO,GAAGI,QAAQ;MAC9CE,SAAS,CAACD,aAAa,GAAGA,aAAa;MACvC,OAAOC,SAAS;IAClB,CAAC,CAACL,kBAAkB,CAAC;IACrBF,SAAS,CAACiC,sBAAsB,CAAC1B,SAAS,CAAC;EAC7C,CAAC,CAAC;EACFP,SAAS,CAACkC,wBAAwB,CAACjC,QAAQ,GAAG,MAAM,EAAEsB,WAAW,CAAC;AACpE;AACA,SAASA,WAAWA,CAACR,MAAM,EAAE;EAC3B;EACA,OAAOA,MAAM,CAACH,IAAI,KAAKG,MAAM,CAACe,IAAI,GAAG,UAAU,GAAG,OAAO,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}