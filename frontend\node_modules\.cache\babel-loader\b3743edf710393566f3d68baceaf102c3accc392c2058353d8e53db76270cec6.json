{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM\\\\frontend\\\\src\\\\components\\\\Layout\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Badge, Button, Typography, Space, Drawer } from 'antd';\nimport { DashboardOutlined, UserOutlined, FileTextOutlined, ShoppingCartOutlined, MoneyCollectOutlined, BarChartOutlined, QuestionCircleOutlined, SettingOutlined, LogoutOutlined, BellOutlined, MenuOutlined, TeamOutlined, AppstoreOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport { logout, selectUser, hasPermission, selectPermissions } from '../../store/slices/authSlice';\nimport { selectUnreadAlerts } from '../../store/slices/dashboardSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst {\n  Text\n} = Typography;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const user = useSelector(selectUser);\n  const permissions = useSelector(selectPermissions);\n  const unreadAlerts = useSelector(selectUnreadAlerts);\n  const [collapsed, setCollapsed] = useState(false);\n  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);\n\n  // 菜单配置\n  const menuItems = [{\n    key: '/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this),\n    label: '经营仪表板',\n    permission: 'dashboard:view'\n  }, {\n    key: '/customers',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 13\n    }, this),\n    label: '客户管理',\n    permission: 'customer:view'\n  }, {\n    key: '/quotations',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    label: '报价管理',\n    permission: 'quotation:view'\n  }, {\n    key: '/orders',\n    icon: /*#__PURE__*/_jsxDEV(ShoppingCartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this),\n    label: '订单管理',\n    permission: 'order:view'\n  }, {\n    key: '/receivables',\n    icon: /*#__PURE__*/_jsxDEV(MoneyCollectOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this),\n    label: '应收款管理',\n    permission: 'receivable:view'\n  }, {\n    key: 'bom',\n    icon: /*#__PURE__*/_jsxDEV(AppstoreOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this),\n    label: 'BOM管理',\n    permission: 'bom:view',\n    children: [{\n      key: '/bom',\n      label: 'BOM清单',\n      permission: 'bom:view'\n    }, {\n      key: '/materials',\n      label: '物料管理',\n      permission: 'material:view'\n    }]\n  }, {\n    key: 'analysis',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this),\n    label: '分析报表',\n    permission: 'report:view',\n    children: [{\n      key: '/analysis/profit',\n      label: '利润分析',\n      permission: 'profit:view'\n    }, {\n      key: '/analysis/performance',\n      label: '绩效管理',\n      permission: 'profit:view'\n    }, {\n      key: '/analysis/business',\n      label: '经营分析',\n      permission: 'report:view'\n    }, {\n      key: '/analysis/financial',\n      label: '财务报表',\n      permission: 'report:view'\n    }]\n  }, {\n    key: '/help',\n    icon: /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this),\n    label: '帮助中心',\n    permission: '*'\n  }, {\n    key: '/settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 13\n    }, this),\n    label: '系统设置',\n    permission: 'settings:manage'\n  }];\n\n  // 过滤有权限的菜单项\n  const filterMenuItems = items => {\n    return items.filter(item => {\n      if (!hasPermission(permissions, item.permission)) {\n        return false;\n      }\n      if (item.children) {\n        item.children = filterMenuItems(item.children);\n        return item.children.length > 0;\n      }\n      return true;\n    });\n  };\n  const filteredMenuItems = filterMenuItems(menuItems);\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n    setMobileMenuVisible(false);\n  };\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 13\n    }, this),\n    label: '个人信息'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this),\n    label: '系统设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: handleLogout\n  }];\n  const getRoleDisplayName = role => {\n    const roleMap = {\n      admin: '系统管理员',\n      boss: '老板/高管',\n      sales: '业务员',\n      finance: '财务人员',\n      production: '生产人员'\n    };\n    return roleMap[role] || role;\n  };\n\n  // 获取当前选中的菜单项\n  const getSelectedKeys = () => {\n    const path = location.pathname;\n    // 处理分析报表的子菜单\n    if (path.startsWith('/analysis/')) {\n      return [path];\n    }\n    return [path];\n  };\n\n  // 获取展开的菜单项\n  const getOpenKeys = () => {\n    const path = location.pathname;\n    if (path.startsWith('/analysis/')) {\n      return ['analysis'];\n    }\n    return [];\n  };\n  const siderContent = /*#__PURE__*/_jsxDEV(Menu, {\n    theme: \"dark\",\n    mode: \"inline\",\n    selectedKeys: getSelectedKeys(),\n    defaultOpenKeys: getOpenKeys(),\n    items: filteredMenuItems,\n    onClick: handleMenuClick,\n    style: {\n      borderRight: 0\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      className: \"desktop-only\",\n      style: {\n        overflow: 'auto',\n        height: '100vh',\n        position: 'fixed',\n        left: 0,\n        top: 0,\n        bottom: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '64px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '16px',\n          fontWeight: 'bold',\n          borderBottom: '1px solid #001529'\n        },\n        children: collapsed ? 'SME' : '经营管理系统'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), siderContent]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      title: \"\\u83DC\\u5355\",\n      placement: \"left\",\n      onClose: () => setMobileMenuVisible(false),\n      open: mobileMenuVisible,\n      className: \"mobile-only\",\n      bodyStyle: {\n        padding: 0\n      },\n      children: siderContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      style: {\n        marginLeft: collapsed ? 80 : 200\n      },\n      className: \"desktop-only\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 24px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderBottom: '1px solid #f0f0f0',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(MenuOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 50\n          }, this),\n          onClick: () => setCollapsed(!collapsed),\n          style: {\n            fontSize: '16px',\n            width: 64,\n            height: 64\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Badge, {\n            count: unreadAlerts.length,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 23\n              }, this),\n              style: {\n                fontSize: '16px'\n              },\n              onClick: () => navigate('/dashboard')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            arrow: true,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: user === null || user === void 0 ? void 0 : user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: getRoleDisplayName(user === null || user === void 0 ? void 0 : user.role)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '24px',\n          padding: '24px',\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)'\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      className: \"mobile-only\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 16px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderBottom: '1px solid #f0f0f0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(MenuOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 19\n          }, this),\n          onClick: () => setMobileMenuVisible(true),\n          style: {\n            fontSize: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            fontSize: '16px'\n          },\n          children: \"\\u7ECF\\u8425\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Badge, {\n            count: unreadAlerts.length,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 23\n              }, this),\n              style: {\n                fontSize: '16px'\n              },\n              onClick: () => navigate('/dashboard')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            arrow: true,\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 29\n              }, this),\n              style: {\n                cursor: 'pointer'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '16px',\n          padding: '16px',\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 96px)'\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 232,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"I3c8s/JltJEgqVAkquHxC7GxMl8=\", false, function () {\n  return [useDispatch, useNavigate, useLocation, useSelector, useSelector, useSelector];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useSelector", "useDispatch", "useNavigate", "useLocation", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "Badge", "<PERSON><PERSON>", "Typography", "Space", "Drawer", "DashboardOutlined", "UserOutlined", "FileTextOutlined", "ShoppingCartOutlined", "MoneyCollectOutlined", "BarChartOutlined", "QuestionCircleOutlined", "SettingOutlined", "LogoutOutlined", "BellOutlined", "MenuOutlined", "TeamOutlined", "AppstoreOutlined", "DatabaseOutlined", "logout", "selectUser", "hasPermission", "selectPermissions", "selectUnreadAlerts", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Text", "children", "_s", "dispatch", "navigate", "location", "user", "permissions", "unread<PERSON><PERSON><PERSON>", "collapsed", "setCollapsed", "mobileMenuVisible", "setMobileMenuVisible", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "permission", "filterMenuItems", "items", "filter", "item", "length", "filteredMenuItems", "handleMenuClick", "handleLogout", "userMenuItems", "type", "onClick", "getRoleDisplayName", "role", "roleMap", "admin", "boss", "sales", "finance", "production", "getSelectedKeys", "path", "pathname", "startsWith", "get<PERSON><PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theme", "mode", "<PERSON><PERSON><PERSON><PERSON>", "defaultOpenKeys", "style", "borderRight", "minHeight", "trigger", "collapsible", "className", "overflow", "height", "position", "left", "top", "bottom", "display", "alignItems", "justifyContent", "color", "fontSize", "fontWeight", "borderBottom", "title", "placement", "onClose", "open", "bodyStyle", "padding", "marginLeft", "background", "zIndex", "width", "size", "count", "menu", "arrow", "cursor", "strong", "name", "margin", "borderRadius", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/components/Layout/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Layout as AntLayout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Badge,\n  Button,\n  Typography,\n  Space,\n  Drawer\n} from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  FileTextOutlined,\n  ShoppingCartOutlined,\n  MoneyCollectOutlined,\n  BarChartOutlined,\n  QuestionCircleOutlined,\n  SettingOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  MenuOutlined,\n  TeamOutlined,\n  AppstoreOutlined,\n  DatabaseOutlined\n} from '@ant-design/icons';\n\nimport { logout, selectUser, hasPermission, selectPermissions } from '../../store/slices/authSlice';\nimport { selectUnreadAlerts } from '../../store/slices/dashboardSlice';\n\nconst { Header, Sider, Content } = AntLayout;\nconst { Text } = Typography;\n\nconst Layout = ({ children }) => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const user = useSelector(selectUser);\n  const permissions = useSelector(selectPermissions);\n  const unreadAlerts = useSelector(selectUnreadAlerts);\n  \n  const [collapsed, setCollapsed] = useState(false);\n  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);\n\n  // 菜单配置\n  const menuItems = [\n    {\n      key: '/dashboard',\n      icon: <DashboardOutlined />,\n      label: '经营仪表板',\n      permission: 'dashboard:view'\n    },\n    {\n      key: '/customers',\n      icon: <TeamOutlined />,\n      label: '客户管理',\n      permission: 'customer:view'\n    },\n    {\n      key: '/quotations',\n      icon: <FileTextOutlined />,\n      label: '报价管理',\n      permission: 'quotation:view'\n    },\n    {\n      key: '/orders',\n      icon: <ShoppingCartOutlined />,\n      label: '订单管理',\n      permission: 'order:view'\n    },\n    {\n      key: '/receivables',\n      icon: <MoneyCollectOutlined />,\n      label: '应收款管理',\n      permission: 'receivable:view'\n    },\n    {\n      key: 'bom',\n      icon: <AppstoreOutlined />,\n      label: 'BOM管理',\n      permission: 'bom:view',\n      children: [\n        {\n          key: '/bom',\n          label: 'BOM清单',\n          permission: 'bom:view'\n        },\n        {\n          key: '/materials',\n          label: '物料管理',\n          permission: 'material:view'\n        }\n      ]\n    },\n    {\n      key: 'analysis',\n      icon: <BarChartOutlined />,\n      label: '分析报表',\n      permission: 'report:view',\n      children: [\n        {\n          key: '/analysis/profit',\n          label: '利润分析',\n          permission: 'profit:view'\n        },\n        {\n          key: '/analysis/performance',\n          label: '绩效管理',\n          permission: 'profit:view'\n        },\n        {\n          key: '/analysis/business',\n          label: '经营分析',\n          permission: 'report:view'\n        },\n        {\n          key: '/analysis/financial',\n          label: '财务报表',\n          permission: 'report:view'\n        }\n      ]\n    },\n    {\n      key: '/help',\n      icon: <QuestionCircleOutlined />,\n      label: '帮助中心',\n      permission: '*'\n    },\n    {\n      key: '/settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n      permission: 'settings:manage'\n    }\n  ];\n\n  // 过滤有权限的菜单项\n  const filterMenuItems = (items) => {\n    return items.filter(item => {\n      if (!hasPermission(permissions, item.permission)) {\n        return false;\n      }\n      if (item.children) {\n        item.children = filterMenuItems(item.children);\n        return item.children.length > 0;\n      }\n      return true;\n    });\n  };\n\n  const filteredMenuItems = filterMenuItems(menuItems);\n\n  const handleMenuClick = ({ key }) => {\n    navigate(key);\n    setMobileMenuVisible(false);\n  };\n\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人信息'\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '系统设置'\n    },\n    {\n      type: 'divider'\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout\n    }\n  ];\n\n  const getRoleDisplayName = (role) => {\n    const roleMap = {\n      admin: '系统管理员',\n      boss: '老板/高管',\n      sales: '业务员',\n      finance: '财务人员',\n      production: '生产人员'\n    };\n    return roleMap[role] || role;\n  };\n\n  // 获取当前选中的菜单项\n  const getSelectedKeys = () => {\n    const path = location.pathname;\n    // 处理分析报表的子菜单\n    if (path.startsWith('/analysis/')) {\n      return [path];\n    }\n    return [path];\n  };\n\n  // 获取展开的菜单项\n  const getOpenKeys = () => {\n    const path = location.pathname;\n    if (path.startsWith('/analysis/')) {\n      return ['analysis'];\n    }\n    return [];\n  };\n\n  const siderContent = (\n    <Menu\n      theme=\"dark\"\n      mode=\"inline\"\n      selectedKeys={getSelectedKeys()}\n      defaultOpenKeys={getOpenKeys()}\n      items={filteredMenuItems}\n      onClick={handleMenuClick}\n      style={{ borderRight: 0 }}\n    />\n  );\n\n  return (\n    <AntLayout style={{ minHeight: '100vh' }}>\n      {/* 桌面端侧边栏 */}\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={collapsed}\n        className=\"desktop-only\"\n        style={{\n          overflow: 'auto',\n          height: '100vh',\n          position: 'fixed',\n          left: 0,\n          top: 0,\n          bottom: 0,\n        }}\n      >\n        <div style={{\n          height: '64px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '16px',\n          fontWeight: 'bold',\n          borderBottom: '1px solid #001529'\n        }}>\n          {collapsed ? 'SME' : '经营管理系统'}\n        </div>\n        {siderContent}\n      </Sider>\n\n      {/* 移动端抽屉菜单 */}\n      <Drawer\n        title=\"菜单\"\n        placement=\"left\"\n        onClose={() => setMobileMenuVisible(false)}\n        open={mobileMenuVisible}\n        className=\"mobile-only\"\n        bodyStyle={{ padding: 0 }}\n      >\n        {siderContent}\n      </Drawer>\n\n      <AntLayout style={{ marginLeft: collapsed ? 80 : 200 }} className=\"desktop-only\">\n        <Header style={{\n          padding: '0 24px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderBottom: '1px solid #f0f0f0',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100\n        }}>\n          <Button\n            type=\"text\"\n            icon={collapsed ? <MenuOutlined /> : <MenuOutlined />}\n            onClick={() => setCollapsed(!collapsed)}\n            style={{ fontSize: '16px', width: 64, height: 64 }}\n          />\n\n          <Space size=\"large\">\n            <Badge count={unreadAlerts.length} size=\"small\">\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                style={{ fontSize: '16px' }}\n                onClick={() => navigate('/dashboard')}\n              />\n            </Badge>\n\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <div>\n                  <Text strong>{user?.name}</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    {getRoleDisplayName(user?.role)}\n                  </Text>\n                </div>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        <Content style={{\n          margin: '24px',\n          padding: '24px',\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)'\n        }}>\n          {children}\n        </Content>\n      </AntLayout>\n\n      {/* 移动端布局 */}\n      <AntLayout className=\"mobile-only\">\n        <Header style={{\n          padding: '0 16px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderBottom: '1px solid #f0f0f0'\n        }}>\n          <Button\n            type=\"text\"\n            icon={<MenuOutlined />}\n            onClick={() => setMobileMenuVisible(true)}\n            style={{ fontSize: '16px' }}\n          />\n\n          <Text strong style={{ fontSize: '16px' }}>经营管理系统</Text>\n\n          <Space>\n            <Badge count={unreadAlerts.length} size=\"small\">\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                style={{ fontSize: '16px' }}\n                onClick={() => navigate('/dashboard')}\n              />\n            </Badge>\n\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Avatar icon={<UserOutlined />} style={{ cursor: 'pointer' }} />\n            </Dropdown>\n          </Space>\n        </Header>\n\n        <Content style={{\n          margin: '16px',\n          padding: '16px',\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 96px)'\n        }}>\n          {children}\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,IAAIC,SAAS,EACnBC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,MAAM,QACD,MAAM;AACb,SACEC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,oBAAoB,EACpBC,gBAAgB,EAChBC,sBAAsB,EACtBC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,QACX,mBAAmB;AAE1B,SAASC,MAAM,EAAEC,UAAU,EAAEC,aAAa,EAAEC,iBAAiB,QAAQ,8BAA8B;AACnG,SAASC,kBAAkB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGhC,SAAS;AAC5C,MAAM;EAAEiC;AAAK,CAAC,GAAG3B,UAAU;AAE3B,MAAMP,MAAM,GAAGA,CAAC;EAAEmC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,IAAI,GAAG5C,WAAW,CAAC6B,UAAU,CAAC;EACpC,MAAMgB,WAAW,GAAG7C,WAAW,CAAC+B,iBAAiB,CAAC;EAClD,MAAMe,YAAY,GAAG9C,WAAW,CAACgC,kBAAkB,CAAC;EAEpD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAMoD,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAEnB,OAAA,CAACpB,iBAAiB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEP,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAEnB,OAAA,CAACT,YAAY;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEP,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAEnB,OAAA,CAAClB,gBAAgB;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEP,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEnB,OAAA,CAACjB,oBAAoB;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC9BC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEP,GAAG,EAAE,cAAc;IACnBC,IAAI,eAAEnB,OAAA,CAAChB,oBAAoB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC9BC,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEP,GAAG,EAAE,KAAK;IACVC,IAAI,eAAEnB,OAAA,CAACR,gBAAgB;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,UAAU;IACtBpB,QAAQ,EAAE,CACR;MACEa,GAAG,EAAE,MAAM;MACXM,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEP,GAAG,EAAE,YAAY;MACjBM,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC,EACD;IACEP,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEnB,OAAA,CAACf,gBAAgB;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE,aAAa;IACzBpB,QAAQ,EAAE,CACR;MACEa,GAAG,EAAE,kBAAkB;MACvBM,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE;IACd,CAAC,EACD;MACEP,GAAG,EAAE,uBAAuB;MAC5BM,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE;IACd,CAAC,EACD;MACEP,GAAG,EAAE,oBAAoB;MACzBM,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE;IACd,CAAC,EACD;MACEP,GAAG,EAAE,qBAAqB;MAC1BM,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC,EACD;IACEP,GAAG,EAAE,OAAO;IACZC,IAAI,eAAEnB,OAAA,CAACd,sBAAsB;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChCC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEP,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEnB,OAAA,CAACb,eAAe;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE;EACd,CAAC,CACF;;EAED;EACA,MAAMC,eAAe,GAAIC,KAAK,IAAK;IACjC,OAAOA,KAAK,CAACC,MAAM,CAACC,IAAI,IAAI;MAC1B,IAAI,CAACjC,aAAa,CAACe,WAAW,EAAEkB,IAAI,CAACJ,UAAU,CAAC,EAAE;QAChD,OAAO,KAAK;MACd;MACA,IAAII,IAAI,CAACxB,QAAQ,EAAE;QACjBwB,IAAI,CAACxB,QAAQ,GAAGqB,eAAe,CAACG,IAAI,CAACxB,QAAQ,CAAC;QAC9C,OAAOwB,IAAI,CAACxB,QAAQ,CAACyB,MAAM,GAAG,CAAC;MACjC;MACA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAGL,eAAe,CAACT,SAAS,CAAC;EAEpD,MAAMe,eAAe,GAAGA,CAAC;IAAEd;EAAI,CAAC,KAAK;IACnCV,QAAQ,CAACU,GAAG,CAAC;IACbF,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzB1B,QAAQ,CAACb,MAAM,CAAC,CAAC,CAAC;IAClBc,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAM0B,aAAa,GAAG,CACpB;IACEhB,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEnB,OAAA,CAACnB,YAAY;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEnB,OAAA,CAACb,eAAe;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEW,IAAI,EAAE;EACR,CAAC,EACD;IACEjB,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEnB,OAAA,CAACZ,cAAc;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbY,OAAO,EAAEH;EACX,CAAC,CACF;EAED,MAAMI,kBAAkB,GAAIC,IAAI,IAAK;IACnC,MAAMC,OAAO,GAAG;MACdC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAC;IACD,OAAOL,OAAO,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC9B,CAAC;;EAED;EACA,MAAMO,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,IAAI,GAAGrC,QAAQ,CAACsC,QAAQ;IAC9B;IACA,IAAID,IAAI,CAACE,UAAU,CAAC,YAAY,CAAC,EAAE;MACjC,OAAO,CAACF,IAAI,CAAC;IACf;IACA,OAAO,CAACA,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMH,IAAI,GAAGrC,QAAQ,CAACsC,QAAQ;IAC9B,IAAID,IAAI,CAACE,UAAU,CAAC,YAAY,CAAC,EAAE;MACjC,OAAO,CAAC,UAAU,CAAC;IACrB;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAME,YAAY,gBAChBlD,OAAA,CAAC5B,IAAI;IACH+E,KAAK,EAAC,MAAM;IACZC,IAAI,EAAC,QAAQ;IACbC,YAAY,EAAER,eAAe,CAAC,CAAE;IAChCS,eAAe,EAAEL,WAAW,CAAC,CAAE;IAC/BtB,KAAK,EAAEI,iBAAkB;IACzBK,OAAO,EAAEJ,eAAgB;IACzBuB,KAAK,EAAE;MAAEC,WAAW,EAAE;IAAE;EAAE;IAAApC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3B,CACF;EAED,oBACEvB,OAAA,CAAC7B,SAAS;IAACoF,KAAK,EAAE;MAAEE,SAAS,EAAE;IAAQ,CAAE;IAAApD,QAAA,gBAEvCL,OAAA,CAACE,KAAK;MACJwD,OAAO,EAAE,IAAK;MACdC,WAAW;MACX9C,SAAS,EAAEA,SAAU;MACrB+C,SAAS,EAAC,cAAc;MACxBL,KAAK,EAAE;QACLM,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE,OAAO;QACjBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE;MACV,CAAE;MAAA7D,QAAA,gBAEFL,OAAA;QAAKuD,KAAK,EAAE;UACVO,MAAM,EAAE,MAAM;UACdK,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE;QAChB,CAAE;QAAApE,QAAA,EACCQ,SAAS,GAAG,KAAK,GAAG;MAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,EACL2B,YAAY;IAAA;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRvB,OAAA,CAACrB,MAAM;MACL+F,KAAK,EAAC,cAAI;MACVC,SAAS,EAAC,MAAM;MAChBC,OAAO,EAAEA,CAAA,KAAM5D,oBAAoB,CAAC,KAAK,CAAE;MAC3C6D,IAAI,EAAE9D,iBAAkB;MACxB6C,SAAS,EAAC,aAAa;MACvBkB,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAAA1E,QAAA,EAEzB6C;IAAY;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAETvB,OAAA,CAAC7B,SAAS;MAACoF,KAAK,EAAE;QAAEyB,UAAU,EAAEnE,SAAS,GAAG,EAAE,GAAG;MAAI,CAAE;MAAC+C,SAAS,EAAC,cAAc;MAAAvD,QAAA,gBAC9EL,OAAA,CAACC,MAAM;QAACsD,KAAK,EAAE;UACbwB,OAAO,EAAE,QAAQ;UACjBE,UAAU,EAAE,MAAM;UAClBd,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BI,YAAY,EAAE,mBAAmB;UACjCV,QAAQ,EAAE,QAAQ;UAClBE,GAAG,EAAE,CAAC;UACNiB,MAAM,EAAE;QACV,CAAE;QAAA7E,QAAA,gBACAL,OAAA,CAACxB,MAAM;UACL2D,IAAI,EAAC,MAAM;UACXhB,IAAI,EAAEN,SAAS,gBAAGb,OAAA,CAACV,YAAY;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvB,OAAA,CAACV,YAAY;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtDa,OAAO,EAAEA,CAAA,KAAMtB,YAAY,CAAC,CAACD,SAAS,CAAE;UACxC0C,KAAK,EAAE;YAAEgB,QAAQ,EAAE,MAAM;YAAEY,KAAK,EAAE,EAAE;YAAErB,MAAM,EAAE;UAAG;QAAE;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAEFvB,OAAA,CAACtB,KAAK;UAAC0G,IAAI,EAAC,OAAO;UAAA/E,QAAA,gBACjBL,OAAA,CAACzB,KAAK;YAAC8G,KAAK,EAAEzE,YAAY,CAACkB,MAAO;YAACsD,IAAI,EAAC,OAAO;YAAA/E,QAAA,eAC7CL,OAAA,CAACxB,MAAM;cACL2D,IAAI,EAAC,MAAM;cACXhB,IAAI,eAAEnB,OAAA,CAACX,YAAY;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBgC,KAAK,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAC5BnC,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAAC,YAAY;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAERvB,OAAA,CAAC1B,QAAQ;YACPgH,IAAI,EAAE;cAAE3D,KAAK,EAAEO;YAAc,CAAE;YAC/ByC,SAAS,EAAC,aAAa;YACvBY,KAAK;YAAAlF,QAAA,eAELL,OAAA,CAACtB,KAAK;cAAC6E,KAAK,EAAE;gBAAEiC,MAAM,EAAE;cAAU,CAAE;cAAAnF,QAAA,gBAClCL,OAAA,CAAC3B,MAAM;gBAAC8C,IAAI,eAAEnB,OAAA,CAACnB,YAAY;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCvB,OAAA;gBAAAK,QAAA,gBACEL,OAAA,CAACI,IAAI;kBAACqF,MAAM;kBAAApF,QAAA,EAAEK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF;gBAAI;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChCvB,OAAA;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvB,OAAA,CAACI,IAAI;kBAAC+B,IAAI,EAAC,WAAW;kBAACoB,KAAK,EAAE;oBAAEgB,QAAQ,EAAE;kBAAO,CAAE;kBAAAlE,QAAA,EAChDgC,kBAAkB,CAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,IAAI;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETvB,OAAA,CAACG,OAAO;QAACoD,KAAK,EAAE;UACdoC,MAAM,EAAE,MAAM;UACdZ,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,MAAM;UAClBW,YAAY,EAAE,KAAK;UACnBnC,SAAS,EAAE;QACb,CAAE;QAAApD,QAAA,EACCA;MAAQ;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGZvB,OAAA,CAAC7B,SAAS;MAACyF,SAAS,EAAC,aAAa;MAAAvD,QAAA,gBAChCL,OAAA,CAACC,MAAM;QAACsD,KAAK,EAAE;UACbwB,OAAO,EAAE,QAAQ;UACjBE,UAAU,EAAE,MAAM;UAClBd,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BI,YAAY,EAAE;QAChB,CAAE;QAAApE,QAAA,gBACAL,OAAA,CAACxB,MAAM;UACL2D,IAAI,EAAC,MAAM;UACXhB,IAAI,eAAEnB,OAAA,CAACV,YAAY;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBa,OAAO,EAAEA,CAAA,KAAMpB,oBAAoB,CAAC,IAAI,CAAE;UAC1CuC,KAAK,EAAE;YAAEgB,QAAQ,EAAE;UAAO;QAAE;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFvB,OAAA,CAACI,IAAI;UAACqF,MAAM;UAAClC,KAAK,EAAE;YAAEgB,QAAQ,EAAE;UAAO,CAAE;UAAAlE,QAAA,EAAC;QAAM;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEvDvB,OAAA,CAACtB,KAAK;UAAA2B,QAAA,gBACJL,OAAA,CAACzB,KAAK;YAAC8G,KAAK,EAAEzE,YAAY,CAACkB,MAAO;YAACsD,IAAI,EAAC,OAAO;YAAA/E,QAAA,eAC7CL,OAAA,CAACxB,MAAM;cACL2D,IAAI,EAAC,MAAM;cACXhB,IAAI,eAAEnB,OAAA,CAACX,YAAY;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBgC,KAAK,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAC5BnC,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAAC,YAAY;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAERvB,OAAA,CAAC1B,QAAQ;YACPgH,IAAI,EAAE;cAAE3D,KAAK,EAAEO;YAAc,CAAE;YAC/ByC,SAAS,EAAC,aAAa;YACvBY,KAAK;YAAAlF,QAAA,eAELL,OAAA,CAAC3B,MAAM;cAAC8C,IAAI,eAAEnB,OAAA,CAACnB,YAAY;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACgC,KAAK,EAAE;gBAAEiC,MAAM,EAAE;cAAU;YAAE;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETvB,OAAA,CAACG,OAAO;QAACoD,KAAK,EAAE;UACdoC,MAAM,EAAE,MAAM;UACdZ,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,MAAM;UAClBW,YAAY,EAAE,KAAK;UACnBnC,SAAS,EAAE;QACb,CAAE;QAAApD,QAAA,EACCA;MAAQ;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACjB,EAAA,CA3VIpC,MAAM;EAAA,QACOH,WAAW,EACXC,WAAW,EACXC,WAAW,EACfH,WAAW,EACJA,WAAW,EACVA,WAAW;AAAA;AAAA+H,EAAA,GAN5B3H,MAAM;AA6VZ,eAAeA,MAAM;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}