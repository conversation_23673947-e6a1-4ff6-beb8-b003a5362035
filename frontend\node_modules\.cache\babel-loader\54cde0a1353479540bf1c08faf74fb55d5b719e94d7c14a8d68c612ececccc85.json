{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport function layout(axisModel, opt) {\n  opt = opt || {};\n  var single = axisModel.coordinateSystem;\n  var axis = axisModel.axis;\n  var layout = {};\n  var axisPosition = axis.position;\n  var orient = axis.orient;\n  var rect = single.getRect();\n  var rectBound = [rect.x, rect.x + rect.width, rect.y, rect.y + rect.height];\n  var positionMap = {\n    horizontal: {\n      top: rectBound[2],\n      bottom: rectBound[3]\n    },\n    vertical: {\n      left: rectBound[0],\n      right: rectBound[1]\n    }\n  };\n  layout.position = [orient === 'vertical' ? positionMap.vertical[axisPosition] : rectBound[0], orient === 'horizontal' ? positionMap.horizontal[axisPosition] : rectBound[3]];\n  var r = {\n    horizontal: 0,\n    vertical: 1\n  };\n  layout.rotation = Math.PI / 2 * r[orient];\n  var directionMap = {\n    top: -1,\n    bottom: 1,\n    right: 1,\n    left: -1\n  };\n  layout.labelDirection = layout.tickDirection = layout.nameDirection = directionMap[axisPosition];\n  if (axisModel.get(['axisTick', 'inside'])) {\n    layout.tickDirection = -layout.tickDirection;\n  }\n  if (zrUtil.retrieve(opt.labelInside, axisModel.get(['axisLabel', 'inside']))) {\n    layout.labelDirection = -layout.labelDirection;\n  }\n  var labelRotation = opt.rotate;\n  labelRotation == null && (labelRotation = axisModel.get(['axisLabel', 'rotate']));\n  layout.labelRotation = axisPosition === 'top' ? -labelRotation : labelRotation;\n  layout.z2 = 1;\n  return layout;\n}", "map": {"version": 3, "names": ["zrUtil", "layout", "axisModel", "opt", "single", "coordinateSystem", "axis", "axisPosition", "position", "orient", "rect", "getRect", "rectBound", "x", "width", "y", "height", "positionMap", "horizontal", "top", "bottom", "vertical", "left", "right", "r", "rotation", "Math", "PI", "directionMap", "labelDirection", "tickDirection", "nameDirection", "get", "retrieve", "labelInside", "labelRotation", "rotate", "z2"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/coord/single/singleAxisHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport function layout(axisModel, opt) {\n  opt = opt || {};\n  var single = axisModel.coordinateSystem;\n  var axis = axisModel.axis;\n  var layout = {};\n  var axisPosition = axis.position;\n  var orient = axis.orient;\n  var rect = single.getRect();\n  var rectBound = [rect.x, rect.x + rect.width, rect.y, rect.y + rect.height];\n  var positionMap = {\n    horizontal: {\n      top: rectBound[2],\n      bottom: rectBound[3]\n    },\n    vertical: {\n      left: rectBound[0],\n      right: rectBound[1]\n    }\n  };\n  layout.position = [orient === 'vertical' ? positionMap.vertical[axisPosition] : rectBound[0], orient === 'horizontal' ? positionMap.horizontal[axisPosition] : rectBound[3]];\n  var r = {\n    horizontal: 0,\n    vertical: 1\n  };\n  layout.rotation = Math.PI / 2 * r[orient];\n  var directionMap = {\n    top: -1,\n    bottom: 1,\n    right: 1,\n    left: -1\n  };\n  layout.labelDirection = layout.tickDirection = layout.nameDirection = directionMap[axisPosition];\n  if (axisModel.get(['axisTick', 'inside'])) {\n    layout.tickDirection = -layout.tickDirection;\n  }\n  if (zrUtil.retrieve(opt.labelInside, axisModel.get(['axisLabel', 'inside']))) {\n    layout.labelDirection = -layout.labelDirection;\n  }\n  var labelRotation = opt.rotate;\n  labelRotation == null && (labelRotation = axisModel.get(['axisLabel', 'rotate']));\n  layout.labelRotation = axisPosition === 'top' ? -labelRotation : labelRotation;\n  layout.z2 = 1;\n  return layout;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,SAASC,MAAMA,CAACC,SAAS,EAAEC,GAAG,EAAE;EACrCA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIC,MAAM,GAAGF,SAAS,CAACG,gBAAgB;EACvC,IAAIC,IAAI,GAAGJ,SAAS,CAACI,IAAI;EACzB,IAAIL,MAAM,GAAG,CAAC,CAAC;EACf,IAAIM,YAAY,GAAGD,IAAI,CAACE,QAAQ;EAChC,IAAIC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACxB,IAAIC,IAAI,GAAGN,MAAM,CAACO,OAAO,CAAC,CAAC;EAC3B,IAAIC,SAAS,GAAG,CAACF,IAAI,CAACG,CAAC,EAAEH,IAAI,CAACG,CAAC,GAAGH,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAACK,CAAC,EAAEL,IAAI,CAACK,CAAC,GAAGL,IAAI,CAACM,MAAM,CAAC;EAC3E,IAAIC,WAAW,GAAG;IAChBC,UAAU,EAAE;MACVC,GAAG,EAAEP,SAAS,CAAC,CAAC,CAAC;MACjBQ,MAAM,EAAER,SAAS,CAAC,CAAC;IACrB,CAAC;IACDS,QAAQ,EAAE;MACRC,IAAI,EAAEV,SAAS,CAAC,CAAC,CAAC;MAClBW,KAAK,EAAEX,SAAS,CAAC,CAAC;IACpB;EACF,CAAC;EACDX,MAAM,CAACO,QAAQ,GAAG,CAACC,MAAM,KAAK,UAAU,GAAGQ,WAAW,CAACI,QAAQ,CAACd,YAAY,CAAC,GAAGK,SAAS,CAAC,CAAC,CAAC,EAAEH,MAAM,KAAK,YAAY,GAAGQ,WAAW,CAACC,UAAU,CAACX,YAAY,CAAC,GAAGK,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5K,IAAIY,CAAC,GAAG;IACNN,UAAU,EAAE,CAAC;IACbG,QAAQ,EAAE;EACZ,CAAC;EACDpB,MAAM,CAACwB,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGH,CAAC,CAACf,MAAM,CAAC;EACzC,IAAImB,YAAY,GAAG;IACjBT,GAAG,EAAE,CAAC,CAAC;IACPC,MAAM,EAAE,CAAC;IACTG,KAAK,EAAE,CAAC;IACRD,IAAI,EAAE,CAAC;EACT,CAAC;EACDrB,MAAM,CAAC4B,cAAc,GAAG5B,MAAM,CAAC6B,aAAa,GAAG7B,MAAM,CAAC8B,aAAa,GAAGH,YAAY,CAACrB,YAAY,CAAC;EAChG,IAAIL,SAAS,CAAC8B,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,EAAE;IACzC/B,MAAM,CAAC6B,aAAa,GAAG,CAAC7B,MAAM,CAAC6B,aAAa;EAC9C;EACA,IAAI9B,MAAM,CAACiC,QAAQ,CAAC9B,GAAG,CAAC+B,WAAW,EAAEhC,SAAS,CAAC8B,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE;IAC5E/B,MAAM,CAAC4B,cAAc,GAAG,CAAC5B,MAAM,CAAC4B,cAAc;EAChD;EACA,IAAIM,aAAa,GAAGhC,GAAG,CAACiC,MAAM;EAC9BD,aAAa,IAAI,IAAI,KAAKA,aAAa,GAAGjC,SAAS,CAAC8B,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;EACjF/B,MAAM,CAACkC,aAAa,GAAG5B,YAAY,KAAK,KAAK,GAAG,CAAC4B,aAAa,GAAGA,aAAa;EAC9ElC,MAAM,CAACoC,EAAE,GAAG,CAAC;EACb,OAAOpC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}