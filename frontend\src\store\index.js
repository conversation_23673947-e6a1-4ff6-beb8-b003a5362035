import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import customerSlice from './slices/customerSlice';
import orderSlice from './slices/orderSlice';
import quotationSlice from './slices/quotationSlice';
import receivableSlice from './slices/receivableSlice';
import dashboardSlice from './slices/dashboardSlice';
import bomSlice from './slices/bomSlice';

const store = configureStore({
  reducer: {
    auth: authSlice,
    customer: customerSlice,
    order: orderSlice,
    quotation: quotationSlice,
    receivable: receivableSlice,
    dashboard: dashboardSlice,
    bom: bomSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export default store;
