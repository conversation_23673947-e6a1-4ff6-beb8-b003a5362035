{"ast": null, "code": "import PathProxy from '../core/PathProxy.js';\nimport * as line from './line.js';\nimport * as cubic from './cubic.js';\nimport * as quadratic from './quadratic.js';\nimport * as arc from './arc.js';\nimport * as curve from '../core/curve.js';\nimport windingLine from './windingLine.js';\nvar CMD = PathProxy.CMD;\nvar PI2 = Math.PI * 2;\nvar EPSILON = 1e-4;\nfunction isAroundEqual(a, b) {\n  return Math.abs(a - b) < EPSILON;\n}\nvar roots = [-1, -1, -1];\nvar extrema = [-1, -1];\nfunction swapExtrema() {\n  var tmp = extrema[0];\n  extrema[0] = extrema[1];\n  extrema[1] = tmp;\n}\nfunction windingCubic(x0, y0, x1, y1, x2, y2, x3, y3, x, y) {\n  if (y > y0 && y > y1 && y > y2 && y > y3 || y < y0 && y < y1 && y < y2 && y < y3) {\n    return 0;\n  }\n  var nRoots = curve.cubicRootAt(y0, y1, y2, y3, y, roots);\n  if (nRoots === 0) {\n    return 0;\n  } else {\n    var w = 0;\n    var nExtrema = -1;\n    var y0_ = void 0;\n    var y1_ = void 0;\n    for (var i = 0; i < nRoots; i++) {\n      var t = roots[i];\n      var unit = t === 0 || t === 1 ? 0.5 : 1;\n      var x_ = curve.cubicAt(x0, x1, x2, x3, t);\n      if (x_ < x) {\n        continue;\n      }\n      if (nExtrema < 0) {\n        nExtrema = curve.cubicExtrema(y0, y1, y2, y3, extrema);\n        if (extrema[1] < extrema[0] && nExtrema > 1) {\n          swapExtrema();\n        }\n        y0_ = curve.cubicAt(y0, y1, y2, y3, extrema[0]);\n        if (nExtrema > 1) {\n          y1_ = curve.cubicAt(y0, y1, y2, y3, extrema[1]);\n        }\n      }\n      if (nExtrema === 2) {\n        if (t < extrema[0]) {\n          w += y0_ < y0 ? unit : -unit;\n        } else if (t < extrema[1]) {\n          w += y1_ < y0_ ? unit : -unit;\n        } else {\n          w += y3 < y1_ ? unit : -unit;\n        }\n      } else {\n        if (t < extrema[0]) {\n          w += y0_ < y0 ? unit : -unit;\n        } else {\n          w += y3 < y0_ ? unit : -unit;\n        }\n      }\n    }\n    return w;\n  }\n}\nfunction windingQuadratic(x0, y0, x1, y1, x2, y2, x, y) {\n  if (y > y0 && y > y1 && y > y2 || y < y0 && y < y1 && y < y2) {\n    return 0;\n  }\n  var nRoots = curve.quadraticRootAt(y0, y1, y2, y, roots);\n  if (nRoots === 0) {\n    return 0;\n  } else {\n    var t = curve.quadraticExtremum(y0, y1, y2);\n    if (t >= 0 && t <= 1) {\n      var w = 0;\n      var y_ = curve.quadraticAt(y0, y1, y2, t);\n      for (var i = 0; i < nRoots; i++) {\n        var unit = roots[i] === 0 || roots[i] === 1 ? 0.5 : 1;\n        var x_ = curve.quadraticAt(x0, x1, x2, roots[i]);\n        if (x_ < x) {\n          continue;\n        }\n        if (roots[i] < t) {\n          w += y_ < y0 ? unit : -unit;\n        } else {\n          w += y2 < y_ ? unit : -unit;\n        }\n      }\n      return w;\n    } else {\n      var unit = roots[0] === 0 || roots[0] === 1 ? 0.5 : 1;\n      var x_ = curve.quadraticAt(x0, x1, x2, roots[0]);\n      if (x_ < x) {\n        return 0;\n      }\n      return y2 < y0 ? unit : -unit;\n    }\n  }\n}\nfunction windingArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y) {\n  y -= cy;\n  if (y > r || y < -r) {\n    return 0;\n  }\n  var tmp = Math.sqrt(r * r - y * y);\n  roots[0] = -tmp;\n  roots[1] = tmp;\n  var dTheta = Math.abs(startAngle - endAngle);\n  if (dTheta < 1e-4) {\n    return 0;\n  }\n  if (dTheta >= PI2 - 1e-4) {\n    startAngle = 0;\n    endAngle = PI2;\n    var dir = anticlockwise ? 1 : -1;\n    if (x >= roots[0] + cx && x <= roots[1] + cx) {\n      return dir;\n    } else {\n      return 0;\n    }\n  }\n  if (startAngle > endAngle) {\n    var tmp_1 = startAngle;\n    startAngle = endAngle;\n    endAngle = tmp_1;\n  }\n  if (startAngle < 0) {\n    startAngle += PI2;\n    endAngle += PI2;\n  }\n  var w = 0;\n  for (var i = 0; i < 2; i++) {\n    var x_ = roots[i];\n    if (x_ + cx > x) {\n      var angle = Math.atan2(y, x_);\n      var dir = anticlockwise ? 1 : -1;\n      if (angle < 0) {\n        angle = PI2 + angle;\n      }\n      if (angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle) {\n        if (angle > Math.PI / 2 && angle < Math.PI * 1.5) {\n          dir = -dir;\n        }\n        w += dir;\n      }\n    }\n  }\n  return w;\n}\nfunction containPath(path, lineWidth, isStroke, x, y) {\n  var data = path.data;\n  var len = path.len();\n  var w = 0;\n  var xi = 0;\n  var yi = 0;\n  var x0 = 0;\n  var y0 = 0;\n  var x1;\n  var y1;\n  for (var i = 0; i < len;) {\n    var cmd = data[i++];\n    var isFirst = i === 1;\n    if (cmd === CMD.M && i > 1) {\n      if (!isStroke) {\n        w += windingLine(xi, yi, x0, y0, x, y);\n      }\n    }\n    if (isFirst) {\n      xi = data[i];\n      yi = data[i + 1];\n      x0 = xi;\n      y0 = yi;\n    }\n    switch (cmd) {\n      case CMD.M:\n        x0 = data[i++];\n        y0 = data[i++];\n        xi = x0;\n        yi = y0;\n        break;\n      case CMD.L:\n        if (isStroke) {\n          if (line.containStroke(xi, yi, data[i], data[i + 1], lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingLine(xi, yi, data[i], data[i + 1], x, y) || 0;\n        }\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.C:\n        if (isStroke) {\n          if (cubic.containStroke(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y) || 0;\n        }\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.Q:\n        if (isStroke) {\n          if (quadratic.containStroke(xi, yi, data[i++], data[i++], data[i], data[i + 1], lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y) || 0;\n        }\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.A:\n        var cx = data[i++];\n        var cy = data[i++];\n        var rx = data[i++];\n        var ry = data[i++];\n        var theta = data[i++];\n        var dTheta = data[i++];\n        i += 1;\n        var anticlockwise = !!(1 - data[i++]);\n        x1 = Math.cos(theta) * rx + cx;\n        y1 = Math.sin(theta) * ry + cy;\n        if (!isFirst) {\n          w += windingLine(xi, yi, x1, y1, x, y);\n        } else {\n          x0 = x1;\n          y0 = y1;\n        }\n        var _x = (x - cx) * ry / rx + cx;\n        if (isStroke) {\n          if (arc.containStroke(cx, cy, ry, theta, theta + dTheta, anticlockwise, lineWidth, _x, y)) {\n            return true;\n          }\n        } else {\n          w += windingArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y);\n        }\n        xi = Math.cos(theta + dTheta) * rx + cx;\n        yi = Math.sin(theta + dTheta) * ry + cy;\n        break;\n      case CMD.R:\n        x0 = xi = data[i++];\n        y0 = yi = data[i++];\n        var width = data[i++];\n        var height = data[i++];\n        x1 = x0 + width;\n        y1 = y0 + height;\n        if (isStroke) {\n          if (line.containStroke(x0, y0, x1, y0, lineWidth, x, y) || line.containStroke(x1, y0, x1, y1, lineWidth, x, y) || line.containStroke(x1, y1, x0, y1, lineWidth, x, y) || line.containStroke(x0, y1, x0, y0, lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingLine(x1, y0, x1, y1, x, y);\n          w += windingLine(x0, y1, x0, y0, x, y);\n        }\n        break;\n      case CMD.Z:\n        if (isStroke) {\n          if (line.containStroke(xi, yi, x0, y0, lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingLine(xi, yi, x0, y0, x, y);\n        }\n        xi = x0;\n        yi = y0;\n        break;\n    }\n  }\n  if (!isStroke && !isAroundEqual(yi, y0)) {\n    w += windingLine(xi, yi, x0, y0, x, y) || 0;\n  }\n  return w !== 0;\n}\nexport function contain(pathProxy, x, y) {\n  return containPath(pathProxy, 0, false, x, y);\n}\nexport function containStroke(pathProxy, lineWidth, x, y) {\n  return containPath(pathProxy, lineWidth, true, x, y);\n}", "map": {"version": 3, "names": ["PathProxy", "line", "cubic", "quadratic", "arc", "curve", "windingLine", "CMD", "PI2", "Math", "PI", "EPSILON", "isAroundEqual", "a", "b", "abs", "roots", "extrema", "swapExtrema", "tmp", "windingCubic", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "x", "y", "nRoots", "cubicRootAt", "w", "nExtrema", "y0_", "y1_", "i", "t", "unit", "x_", "cubicAt", "cubicExtrema", "windingQuadratic", "quadraticRootAt", "quadraticExtremum", "y_", "quadraticAt", "windingArc", "cx", "cy", "r", "startAngle", "endAngle", "anticlockwise", "sqrt", "d<PERSON><PERSON><PERSON>", "dir", "tmp_1", "angle", "atan2", "containPath", "path", "lineWidth", "isStroke", "data", "len", "xi", "yi", "cmd", "<PERSON><PERSON><PERSON><PERSON>", "M", "L", "containStroke", "C", "Q", "A", "rx", "ry", "theta", "cos", "sin", "_x", "R", "width", "height", "Z", "contain", "pathProxy"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/zrender/lib/contain/path.js"], "sourcesContent": ["import PathProxy from '../core/PathProxy.js';\nimport * as line from './line.js';\nimport * as cubic from './cubic.js';\nimport * as quadratic from './quadratic.js';\nimport * as arc from './arc.js';\nimport * as curve from '../core/curve.js';\nimport windingLine from './windingLine.js';\nvar CMD = PathProxy.CMD;\nvar PI2 = Math.PI * 2;\nvar EPSILON = 1e-4;\nfunction isAroundEqual(a, b) {\n    return Math.abs(a - b) < EPSILON;\n}\nvar roots = [-1, -1, -1];\nvar extrema = [-1, -1];\nfunction swapExtrema() {\n    var tmp = extrema[0];\n    extrema[0] = extrema[1];\n    extrema[1] = tmp;\n}\nfunction windingCubic(x0, y0, x1, y1, x2, y2, x3, y3, x, y) {\n    if ((y > y0 && y > y1 && y > y2 && y > y3)\n        || (y < y0 && y < y1 && y < y2 && y < y3)) {\n        return 0;\n    }\n    var nRoots = curve.cubicRootAt(y0, y1, y2, y3, y, roots);\n    if (nRoots === 0) {\n        return 0;\n    }\n    else {\n        var w = 0;\n        var nExtrema = -1;\n        var y0_ = void 0;\n        var y1_ = void 0;\n        for (var i = 0; i < nRoots; i++) {\n            var t = roots[i];\n            var unit = (t === 0 || t === 1) ? 0.5 : 1;\n            var x_ = curve.cubicAt(x0, x1, x2, x3, t);\n            if (x_ < x) {\n                continue;\n            }\n            if (nExtrema < 0) {\n                nExtrema = curve.cubicExtrema(y0, y1, y2, y3, extrema);\n                if (extrema[1] < extrema[0] && nExtrema > 1) {\n                    swapExtrema();\n                }\n                y0_ = curve.cubicAt(y0, y1, y2, y3, extrema[0]);\n                if (nExtrema > 1) {\n                    y1_ = curve.cubicAt(y0, y1, y2, y3, extrema[1]);\n                }\n            }\n            if (nExtrema === 2) {\n                if (t < extrema[0]) {\n                    w += y0_ < y0 ? unit : -unit;\n                }\n                else if (t < extrema[1]) {\n                    w += y1_ < y0_ ? unit : -unit;\n                }\n                else {\n                    w += y3 < y1_ ? unit : -unit;\n                }\n            }\n            else {\n                if (t < extrema[0]) {\n                    w += y0_ < y0 ? unit : -unit;\n                }\n                else {\n                    w += y3 < y0_ ? unit : -unit;\n                }\n            }\n        }\n        return w;\n    }\n}\nfunction windingQuadratic(x0, y0, x1, y1, x2, y2, x, y) {\n    if ((y > y0 && y > y1 && y > y2)\n        || (y < y0 && y < y1 && y < y2)) {\n        return 0;\n    }\n    var nRoots = curve.quadraticRootAt(y0, y1, y2, y, roots);\n    if (nRoots === 0) {\n        return 0;\n    }\n    else {\n        var t = curve.quadraticExtremum(y0, y1, y2);\n        if (t >= 0 && t <= 1) {\n            var w = 0;\n            var y_ = curve.quadraticAt(y0, y1, y2, t);\n            for (var i = 0; i < nRoots; i++) {\n                var unit = (roots[i] === 0 || roots[i] === 1) ? 0.5 : 1;\n                var x_ = curve.quadraticAt(x0, x1, x2, roots[i]);\n                if (x_ < x) {\n                    continue;\n                }\n                if (roots[i] < t) {\n                    w += y_ < y0 ? unit : -unit;\n                }\n                else {\n                    w += y2 < y_ ? unit : -unit;\n                }\n            }\n            return w;\n        }\n        else {\n            var unit = (roots[0] === 0 || roots[0] === 1) ? 0.5 : 1;\n            var x_ = curve.quadraticAt(x0, x1, x2, roots[0]);\n            if (x_ < x) {\n                return 0;\n            }\n            return y2 < y0 ? unit : -unit;\n        }\n    }\n}\nfunction windingArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y) {\n    y -= cy;\n    if (y > r || y < -r) {\n        return 0;\n    }\n    var tmp = Math.sqrt(r * r - y * y);\n    roots[0] = -tmp;\n    roots[1] = tmp;\n    var dTheta = Math.abs(startAngle - endAngle);\n    if (dTheta < 1e-4) {\n        return 0;\n    }\n    if (dTheta >= PI2 - 1e-4) {\n        startAngle = 0;\n        endAngle = PI2;\n        var dir = anticlockwise ? 1 : -1;\n        if (x >= roots[0] + cx && x <= roots[1] + cx) {\n            return dir;\n        }\n        else {\n            return 0;\n        }\n    }\n    if (startAngle > endAngle) {\n        var tmp_1 = startAngle;\n        startAngle = endAngle;\n        endAngle = tmp_1;\n    }\n    if (startAngle < 0) {\n        startAngle += PI2;\n        endAngle += PI2;\n    }\n    var w = 0;\n    for (var i = 0; i < 2; i++) {\n        var x_ = roots[i];\n        if (x_ + cx > x) {\n            var angle = Math.atan2(y, x_);\n            var dir = anticlockwise ? 1 : -1;\n            if (angle < 0) {\n                angle = PI2 + angle;\n            }\n            if ((angle >= startAngle && angle <= endAngle)\n                || (angle + PI2 >= startAngle && angle + PI2 <= endAngle)) {\n                if (angle > Math.PI / 2 && angle < Math.PI * 1.5) {\n                    dir = -dir;\n                }\n                w += dir;\n            }\n        }\n    }\n    return w;\n}\nfunction containPath(path, lineWidth, isStroke, x, y) {\n    var data = path.data;\n    var len = path.len();\n    var w = 0;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    var x1;\n    var y1;\n    for (var i = 0; i < len;) {\n        var cmd = data[i++];\n        var isFirst = i === 1;\n        if (cmd === CMD.M && i > 1) {\n            if (!isStroke) {\n                w += windingLine(xi, yi, x0, y0, x, y);\n            }\n        }\n        if (isFirst) {\n            xi = data[i];\n            yi = data[i + 1];\n            x0 = xi;\n            y0 = yi;\n        }\n        switch (cmd) {\n            case CMD.M:\n                x0 = data[i++];\n                y0 = data[i++];\n                xi = x0;\n                yi = y0;\n                break;\n            case CMD.L:\n                if (isStroke) {\n                    if (line.containStroke(xi, yi, data[i], data[i + 1], lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingLine(xi, yi, data[i], data[i + 1], x, y) || 0;\n                }\n                xi = data[i++];\n                yi = data[i++];\n                break;\n            case CMD.C:\n                if (isStroke) {\n                    if (cubic.containStroke(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y) || 0;\n                }\n                xi = data[i++];\n                yi = data[i++];\n                break;\n            case CMD.Q:\n                if (isStroke) {\n                    if (quadratic.containStroke(xi, yi, data[i++], data[i++], data[i], data[i + 1], lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y) || 0;\n                }\n                xi = data[i++];\n                yi = data[i++];\n                break;\n            case CMD.A:\n                var cx = data[i++];\n                var cy = data[i++];\n                var rx = data[i++];\n                var ry = data[i++];\n                var theta = data[i++];\n                var dTheta = data[i++];\n                i += 1;\n                var anticlockwise = !!(1 - data[i++]);\n                x1 = Math.cos(theta) * rx + cx;\n                y1 = Math.sin(theta) * ry + cy;\n                if (!isFirst) {\n                    w += windingLine(xi, yi, x1, y1, x, y);\n                }\n                else {\n                    x0 = x1;\n                    y0 = y1;\n                }\n                var _x = (x - cx) * ry / rx + cx;\n                if (isStroke) {\n                    if (arc.containStroke(cx, cy, ry, theta, theta + dTheta, anticlockwise, lineWidth, _x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y);\n                }\n                xi = Math.cos(theta + dTheta) * rx + cx;\n                yi = Math.sin(theta + dTheta) * ry + cy;\n                break;\n            case CMD.R:\n                x0 = xi = data[i++];\n                y0 = yi = data[i++];\n                var width = data[i++];\n                var height = data[i++];\n                x1 = x0 + width;\n                y1 = y0 + height;\n                if (isStroke) {\n                    if (line.containStroke(x0, y0, x1, y0, lineWidth, x, y)\n                        || line.containStroke(x1, y0, x1, y1, lineWidth, x, y)\n                        || line.containStroke(x1, y1, x0, y1, lineWidth, x, y)\n                        || line.containStroke(x0, y1, x0, y0, lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingLine(x1, y0, x1, y1, x, y);\n                    w += windingLine(x0, y1, x0, y0, x, y);\n                }\n                break;\n            case CMD.Z:\n                if (isStroke) {\n                    if (line.containStroke(xi, yi, x0, y0, lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingLine(xi, yi, x0, y0, x, y);\n                }\n                xi = x0;\n                yi = y0;\n                break;\n        }\n    }\n    if (!isStroke && !isAroundEqual(yi, y0)) {\n        w += windingLine(xi, yi, x0, y0, x, y) || 0;\n    }\n    return w !== 0;\n}\nexport function contain(pathProxy, x, y) {\n    return containPath(pathProxy, 0, false, x, y);\n}\nexport function containStroke(pathProxy, lineWidth, x, y) {\n    return containPath(pathProxy, lineWidth, true, x, y);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,KAAK,MAAM,YAAY;AACnC,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAC3C,OAAO,KAAKC,GAAG,MAAM,UAAU;AAC/B,OAAO,KAAKC,KAAK,MAAM,kBAAkB;AACzC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,IAAIC,GAAG,GAAGP,SAAS,CAACO,GAAG;AACvB,IAAIC,GAAG,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;AACrB,IAAIC,OAAO,GAAG,IAAI;AAClB,SAASC,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOL,IAAI,CAACM,GAAG,CAACF,CAAC,GAAGC,CAAC,CAAC,GAAGH,OAAO;AACpC;AACA,IAAIK,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,SAASC,WAAWA,CAAA,EAAG;EACnB,IAAIC,GAAG,GAAGF,OAAO,CAAC,CAAC,CAAC;EACpBA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EACvBA,OAAO,CAAC,CAAC,CAAC,GAAGE,GAAG;AACpB;AACA,SAASC,YAAYA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACxD,IAAKA,CAAC,GAAGR,EAAE,IAAIQ,CAAC,GAAGN,EAAE,IAAIM,CAAC,GAAGJ,EAAE,IAAII,CAAC,GAAGF,EAAE,IACjCE,CAAC,GAAGR,EAAE,IAAIQ,CAAC,GAAGN,EAAE,IAAIM,CAAC,GAAGJ,EAAE,IAAII,CAAC,GAAGF,EAAG,EAAE;IAC3C,OAAO,CAAC;EACZ;EACA,IAAIG,MAAM,GAAG1B,KAAK,CAAC2B,WAAW,CAACV,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,CAAC,EAAEd,KAAK,CAAC;EACxD,IAAIe,MAAM,KAAK,CAAC,EAAE;IACd,OAAO,CAAC;EACZ,CAAC,MACI;IACD,IAAIE,CAAC,GAAG,CAAC;IACT,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,GAAG,GAAG,KAAK,CAAC;IAChB,IAAIC,GAAG,GAAG,KAAK,CAAC;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAEM,CAAC,EAAE,EAAE;MAC7B,IAAIC,CAAC,GAAGtB,KAAK,CAACqB,CAAC,CAAC;MAChB,IAAIE,IAAI,GAAID,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,GAAI,GAAG,GAAG,CAAC;MACzC,IAAIE,EAAE,GAAGnC,KAAK,CAACoC,OAAO,CAACpB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEW,CAAC,CAAC;MACzC,IAAIE,EAAE,GAAGX,CAAC,EAAE;QACR;MACJ;MACA,IAAIK,QAAQ,GAAG,CAAC,EAAE;QACdA,QAAQ,GAAG7B,KAAK,CAACqC,YAAY,CAACpB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEX,OAAO,CAAC;QACtD,IAAIA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,IAAIiB,QAAQ,GAAG,CAAC,EAAE;UACzChB,WAAW,CAAC,CAAC;QACjB;QACAiB,GAAG,GAAG9B,KAAK,CAACoC,OAAO,CAACnB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEX,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAIiB,QAAQ,GAAG,CAAC,EAAE;UACdE,GAAG,GAAG/B,KAAK,CAACoC,OAAO,CAACnB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEX,OAAO,CAAC,CAAC,CAAC,CAAC;QACnD;MACJ;MACA,IAAIiB,QAAQ,KAAK,CAAC,EAAE;QAChB,IAAII,CAAC,GAAGrB,OAAO,CAAC,CAAC,CAAC,EAAE;UAChBgB,CAAC,IAAIE,GAAG,GAAGb,EAAE,GAAGiB,IAAI,GAAG,CAACA,IAAI;QAChC,CAAC,MACI,IAAID,CAAC,GAAGrB,OAAO,CAAC,CAAC,CAAC,EAAE;UACrBgB,CAAC,IAAIG,GAAG,GAAGD,GAAG,GAAGI,IAAI,GAAG,CAACA,IAAI;QACjC,CAAC,MACI;UACDN,CAAC,IAAIL,EAAE,GAAGQ,GAAG,GAAGG,IAAI,GAAG,CAACA,IAAI;QAChC;MACJ,CAAC,MACI;QACD,IAAID,CAAC,GAAGrB,OAAO,CAAC,CAAC,CAAC,EAAE;UAChBgB,CAAC,IAAIE,GAAG,GAAGb,EAAE,GAAGiB,IAAI,GAAG,CAACA,IAAI;QAChC,CAAC,MACI;UACDN,CAAC,IAAIL,EAAE,GAAGO,GAAG,GAAGI,IAAI,GAAG,CAACA,IAAI;QAChC;MACJ;IACJ;IACA,OAAON,CAAC;EACZ;AACJ;AACA,SAASU,gBAAgBA,CAACtB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,CAAC,EAAEC,CAAC,EAAE;EACpD,IAAKA,CAAC,GAAGR,EAAE,IAAIQ,CAAC,GAAGN,EAAE,IAAIM,CAAC,GAAGJ,EAAE,IACvBI,CAAC,GAAGR,EAAE,IAAIQ,CAAC,GAAGN,EAAE,IAAIM,CAAC,GAAGJ,EAAG,EAAE;IACjC,OAAO,CAAC;EACZ;EACA,IAAIK,MAAM,GAAG1B,KAAK,CAACuC,eAAe,CAACtB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEI,CAAC,EAAEd,KAAK,CAAC;EACxD,IAAIe,MAAM,KAAK,CAAC,EAAE;IACd,OAAO,CAAC;EACZ,CAAC,MACI;IACD,IAAIO,CAAC,GAAGjC,KAAK,CAACwC,iBAAiB,CAACvB,EAAE,EAAEE,EAAE,EAAEE,EAAE,CAAC;IAC3C,IAAIY,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,EAAE;MAClB,IAAIL,CAAC,GAAG,CAAC;MACT,IAAIa,EAAE,GAAGzC,KAAK,CAAC0C,WAAW,CAACzB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEY,CAAC,CAAC;MACzC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAEM,CAAC,EAAE,EAAE;QAC7B,IAAIE,IAAI,GAAIvB,KAAK,CAACqB,CAAC,CAAC,KAAK,CAAC,IAAIrB,KAAK,CAACqB,CAAC,CAAC,KAAK,CAAC,GAAI,GAAG,GAAG,CAAC;QACvD,IAAIG,EAAE,GAAGnC,KAAK,CAAC0C,WAAW,CAAC1B,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAET,KAAK,CAACqB,CAAC,CAAC,CAAC;QAChD,IAAIG,EAAE,GAAGX,CAAC,EAAE;UACR;QACJ;QACA,IAAIb,KAAK,CAACqB,CAAC,CAAC,GAAGC,CAAC,EAAE;UACdL,CAAC,IAAIa,EAAE,GAAGxB,EAAE,GAAGiB,IAAI,GAAG,CAACA,IAAI;QAC/B,CAAC,MACI;UACDN,CAAC,IAAIP,EAAE,GAAGoB,EAAE,GAAGP,IAAI,GAAG,CAACA,IAAI;QAC/B;MACJ;MACA,OAAON,CAAC;IACZ,CAAC,MACI;MACD,IAAIM,IAAI,GAAIvB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAG,GAAG,CAAC;MACvD,IAAIwB,EAAE,GAAGnC,KAAK,CAAC0C,WAAW,CAAC1B,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAET,KAAK,CAAC,CAAC,CAAC,CAAC;MAChD,IAAIwB,EAAE,GAAGX,CAAC,EAAE;QACR,OAAO,CAAC;MACZ;MACA,OAAOH,EAAE,GAAGJ,EAAE,GAAGiB,IAAI,GAAG,CAACA,IAAI;IACjC;EACJ;AACJ;AACA,SAASS,UAAUA,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAEzB,CAAC,EAAEC,CAAC,EAAE;EACtEA,CAAC,IAAIoB,EAAE;EACP,IAAIpB,CAAC,GAAGqB,CAAC,IAAIrB,CAAC,GAAG,CAACqB,CAAC,EAAE;IACjB,OAAO,CAAC;EACZ;EACA,IAAIhC,GAAG,GAAGV,IAAI,CAAC8C,IAAI,CAACJ,CAAC,GAAGA,CAAC,GAAGrB,CAAC,GAAGA,CAAC,CAAC;EAClCd,KAAK,CAAC,CAAC,CAAC,GAAG,CAACG,GAAG;EACfH,KAAK,CAAC,CAAC,CAAC,GAAGG,GAAG;EACd,IAAIqC,MAAM,GAAG/C,IAAI,CAACM,GAAG,CAACqC,UAAU,GAAGC,QAAQ,CAAC;EAC5C,IAAIG,MAAM,GAAG,IAAI,EAAE;IACf,OAAO,CAAC;EACZ;EACA,IAAIA,MAAM,IAAIhD,GAAG,GAAG,IAAI,EAAE;IACtB4C,UAAU,GAAG,CAAC;IACdC,QAAQ,GAAG7C,GAAG;IACd,IAAIiD,GAAG,GAAGH,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,IAAIzB,CAAC,IAAIb,KAAK,CAAC,CAAC,CAAC,GAAGiC,EAAE,IAAIpB,CAAC,IAAIb,KAAK,CAAC,CAAC,CAAC,GAAGiC,EAAE,EAAE;MAC1C,OAAOQ,GAAG;IACd,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;EACA,IAAIL,UAAU,GAAGC,QAAQ,EAAE;IACvB,IAAIK,KAAK,GAAGN,UAAU;IACtBA,UAAU,GAAGC,QAAQ;IACrBA,QAAQ,GAAGK,KAAK;EACpB;EACA,IAAIN,UAAU,GAAG,CAAC,EAAE;IAChBA,UAAU,IAAI5C,GAAG;IACjB6C,QAAQ,IAAI7C,GAAG;EACnB;EACA,IAAIyB,CAAC,GAAG,CAAC;EACT,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxB,IAAIG,EAAE,GAAGxB,KAAK,CAACqB,CAAC,CAAC;IACjB,IAAIG,EAAE,GAAGS,EAAE,GAAGpB,CAAC,EAAE;MACb,IAAI8B,KAAK,GAAGlD,IAAI,CAACmD,KAAK,CAAC9B,CAAC,EAAEU,EAAE,CAAC;MAC7B,IAAIiB,GAAG,GAAGH,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;MAChC,IAAIK,KAAK,GAAG,CAAC,EAAE;QACXA,KAAK,GAAGnD,GAAG,GAAGmD,KAAK;MACvB;MACA,IAAKA,KAAK,IAAIP,UAAU,IAAIO,KAAK,IAAIN,QAAQ,IACrCM,KAAK,GAAGnD,GAAG,IAAI4C,UAAU,IAAIO,KAAK,GAAGnD,GAAG,IAAI6C,QAAS,EAAE;QAC3D,IAAIM,KAAK,GAAGlD,IAAI,CAACC,EAAE,GAAG,CAAC,IAAIiD,KAAK,GAAGlD,IAAI,CAACC,EAAE,GAAG,GAAG,EAAE;UAC9C+C,GAAG,GAAG,CAACA,GAAG;QACd;QACAxB,CAAC,IAAIwB,GAAG;MACZ;IACJ;EACJ;EACA,OAAOxB,CAAC;AACZ;AACA,SAAS4B,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEnC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAImC,IAAI,GAAGH,IAAI,CAACG,IAAI;EACpB,IAAIC,GAAG,GAAGJ,IAAI,CAACI,GAAG,CAAC,CAAC;EACpB,IAAIjC,CAAC,GAAG,CAAC;EACT,IAAIkC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAI/C,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE;EACN,IAAIC,EAAE;EACN,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,GAAG,GAAG;IACtB,IAAIG,GAAG,GAAGJ,IAAI,CAAC5B,CAAC,EAAE,CAAC;IACnB,IAAIiC,OAAO,GAAGjC,CAAC,KAAK,CAAC;IACrB,IAAIgC,GAAG,KAAK9D,GAAG,CAACgE,CAAC,IAAIlC,CAAC,GAAG,CAAC,EAAE;MACxB,IAAI,CAAC2B,QAAQ,EAAE;QACX/B,CAAC,IAAI3B,WAAW,CAAC6D,EAAE,EAAEC,EAAE,EAAE/C,EAAE,EAAEC,EAAE,EAAEO,CAAC,EAAEC,CAAC,CAAC;MAC1C;IACJ;IACA,IAAIwC,OAAO,EAAE;MACTH,EAAE,GAAGF,IAAI,CAAC5B,CAAC,CAAC;MACZ+B,EAAE,GAAGH,IAAI,CAAC5B,CAAC,GAAG,CAAC,CAAC;MAChBhB,EAAE,GAAG8C,EAAE;MACP7C,EAAE,GAAG8C,EAAE;IACX;IACA,QAAQC,GAAG;MACP,KAAK9D,GAAG,CAACgE,CAAC;QACNlD,EAAE,GAAG4C,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACdf,EAAE,GAAG2C,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACd8B,EAAE,GAAG9C,EAAE;QACP+C,EAAE,GAAG9C,EAAE;QACP;MACJ,KAAKf,GAAG,CAACiE,CAAC;QACN,IAAIR,QAAQ,EAAE;UACV,IAAI/D,IAAI,CAACwE,aAAa,CAACN,EAAE,EAAEC,EAAE,EAAEH,IAAI,CAAC5B,CAAC,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,GAAG,CAAC,CAAC,EAAE0B,SAAS,EAAElC,CAAC,EAAEC,CAAC,CAAC,EAAE;YACnE,OAAO,IAAI;UACf;QACJ,CAAC,MACI;UACDG,CAAC,IAAI3B,WAAW,CAAC6D,EAAE,EAAEC,EAAE,EAAEH,IAAI,CAAC5B,CAAC,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,GAAG,CAAC,CAAC,EAAER,CAAC,EAAEC,CAAC,CAAC,IAAI,CAAC;QAC7D;QACAqC,EAAE,GAAGF,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACd+B,EAAE,GAAGH,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACd;MACJ,KAAK9B,GAAG,CAACmE,CAAC;QACN,IAAIV,QAAQ,EAAE;UACV,IAAI9D,KAAK,CAACuE,aAAa,CAACN,EAAE,EAAEC,EAAE,EAAEH,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,GAAG,CAAC,CAAC,EAAE0B,SAAS,EAAElC,CAAC,EAAEC,CAAC,CAAC,EAAE;YAChH,OAAO,IAAI;UACf;QACJ,CAAC,MACI;UACDG,CAAC,IAAIb,YAAY,CAAC+C,EAAE,EAAEC,EAAE,EAAEH,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,GAAG,CAAC,CAAC,EAAER,CAAC,EAAEC,CAAC,CAAC,IAAI,CAAC;QAC1G;QACAqC,EAAE,GAAGF,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACd+B,EAAE,GAAGH,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACd;MACJ,KAAK9B,GAAG,CAACoE,CAAC;QACN,IAAIX,QAAQ,EAAE;UACV,IAAI7D,SAAS,CAACsE,aAAa,CAACN,EAAE,EAAEC,EAAE,EAAEH,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,GAAG,CAAC,CAAC,EAAE0B,SAAS,EAAElC,CAAC,EAAEC,CAAC,CAAC,EAAE;YAC9F,OAAO,IAAI;UACf;QACJ,CAAC,MACI;UACDG,CAAC,IAAIU,gBAAgB,CAACwB,EAAE,EAAEC,EAAE,EAAEH,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,EAAE,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,CAAC,EAAE4B,IAAI,CAAC5B,CAAC,GAAG,CAAC,CAAC,EAAER,CAAC,EAAEC,CAAC,CAAC,IAAI,CAAC;QACxF;QACAqC,EAAE,GAAGF,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACd+B,EAAE,GAAGH,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACd;MACJ,KAAK9B,GAAG,CAACqE,CAAC;QACN,IAAI3B,EAAE,GAAGgB,IAAI,CAAC5B,CAAC,EAAE,CAAC;QAClB,IAAIa,EAAE,GAAGe,IAAI,CAAC5B,CAAC,EAAE,CAAC;QAClB,IAAIwC,EAAE,GAAGZ,IAAI,CAAC5B,CAAC,EAAE,CAAC;QAClB,IAAIyC,EAAE,GAAGb,IAAI,CAAC5B,CAAC,EAAE,CAAC;QAClB,IAAI0C,KAAK,GAAGd,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACrB,IAAImB,MAAM,GAAGS,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACtBA,CAAC,IAAI,CAAC;QACN,IAAIiB,aAAa,GAAG,CAAC,EAAE,CAAC,GAAGW,IAAI,CAAC5B,CAAC,EAAE,CAAC,CAAC;QACrCd,EAAE,GAAGd,IAAI,CAACuE,GAAG,CAACD,KAAK,CAAC,GAAGF,EAAE,GAAG5B,EAAE;QAC9BzB,EAAE,GAAGf,IAAI,CAACwE,GAAG,CAACF,KAAK,CAAC,GAAGD,EAAE,GAAG5B,EAAE;QAC9B,IAAI,CAACoB,OAAO,EAAE;UACVrC,CAAC,IAAI3B,WAAW,CAAC6D,EAAE,EAAEC,EAAE,EAAE7C,EAAE,EAAEC,EAAE,EAAEK,CAAC,EAAEC,CAAC,CAAC;QAC1C,CAAC,MACI;UACDT,EAAE,GAAGE,EAAE;UACPD,EAAE,GAAGE,EAAE;QACX;QACA,IAAI0D,EAAE,GAAG,CAACrD,CAAC,GAAGoB,EAAE,IAAI6B,EAAE,GAAGD,EAAE,GAAG5B,EAAE;QAChC,IAAIe,QAAQ,EAAE;UACV,IAAI5D,GAAG,CAACqE,aAAa,CAACxB,EAAE,EAAEC,EAAE,EAAE4B,EAAE,EAAEC,KAAK,EAAEA,KAAK,GAAGvB,MAAM,EAAEF,aAAa,EAAES,SAAS,EAAEmB,EAAE,EAAEpD,CAAC,CAAC,EAAE;YACvF,OAAO,IAAI;UACf;QACJ,CAAC,MACI;UACDG,CAAC,IAAIe,UAAU,CAACC,EAAE,EAAEC,EAAE,EAAE4B,EAAE,EAAEC,KAAK,EAAEA,KAAK,GAAGvB,MAAM,EAAEF,aAAa,EAAE4B,EAAE,EAAEpD,CAAC,CAAC;QAC5E;QACAqC,EAAE,GAAG1D,IAAI,CAACuE,GAAG,CAACD,KAAK,GAAGvB,MAAM,CAAC,GAAGqB,EAAE,GAAG5B,EAAE;QACvCmB,EAAE,GAAG3D,IAAI,CAACwE,GAAG,CAACF,KAAK,GAAGvB,MAAM,CAAC,GAAGsB,EAAE,GAAG5B,EAAE;QACvC;MACJ,KAAK3C,GAAG,CAAC4E,CAAC;QACN9D,EAAE,GAAG8C,EAAE,GAAGF,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACnBf,EAAE,GAAG8C,EAAE,GAAGH,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACnB,IAAI+C,KAAK,GAAGnB,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACrB,IAAIgD,MAAM,GAAGpB,IAAI,CAAC5B,CAAC,EAAE,CAAC;QACtBd,EAAE,GAAGF,EAAE,GAAG+D,KAAK;QACf5D,EAAE,GAAGF,EAAE,GAAG+D,MAAM;QAChB,IAAIrB,QAAQ,EAAE;UACV,IAAI/D,IAAI,CAACwE,aAAa,CAACpD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAED,EAAE,EAAEyC,SAAS,EAAElC,CAAC,EAAEC,CAAC,CAAC,IAChD7B,IAAI,CAACwE,aAAa,CAAClD,EAAE,EAAED,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEuC,SAAS,EAAElC,CAAC,EAAEC,CAAC,CAAC,IACnD7B,IAAI,CAACwE,aAAa,CAAClD,EAAE,EAAEC,EAAE,EAAEH,EAAE,EAAEG,EAAE,EAAEuC,SAAS,EAAElC,CAAC,EAAEC,CAAC,CAAC,IACnD7B,IAAI,CAACwE,aAAa,CAACpD,EAAE,EAAEG,EAAE,EAAEH,EAAE,EAAEC,EAAE,EAAEyC,SAAS,EAAElC,CAAC,EAAEC,CAAC,CAAC,EAAE;YACxD,OAAO,IAAI;UACf;QACJ,CAAC,MACI;UACDG,CAAC,IAAI3B,WAAW,CAACiB,EAAE,EAAED,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEK,CAAC,EAAEC,CAAC,CAAC;UACtCG,CAAC,IAAI3B,WAAW,CAACe,EAAE,EAAEG,EAAE,EAAEH,EAAE,EAAEC,EAAE,EAAEO,CAAC,EAAEC,CAAC,CAAC;QAC1C;QACA;MACJ,KAAKvB,GAAG,CAAC+E,CAAC;QACN,IAAItB,QAAQ,EAAE;UACV,IAAI/D,IAAI,CAACwE,aAAa,CAACN,EAAE,EAAEC,EAAE,EAAE/C,EAAE,EAAEC,EAAE,EAAEyC,SAAS,EAAElC,CAAC,EAAEC,CAAC,CAAC,EAAE;YACrD,OAAO,IAAI;UACf;QACJ,CAAC,MACI;UACDG,CAAC,IAAI3B,WAAW,CAAC6D,EAAE,EAAEC,EAAE,EAAE/C,EAAE,EAAEC,EAAE,EAAEO,CAAC,EAAEC,CAAC,CAAC;QAC1C;QACAqC,EAAE,GAAG9C,EAAE;QACP+C,EAAE,GAAG9C,EAAE;QACP;IACR;EACJ;EACA,IAAI,CAAC0C,QAAQ,IAAI,CAACpD,aAAa,CAACwD,EAAE,EAAE9C,EAAE,CAAC,EAAE;IACrCW,CAAC,IAAI3B,WAAW,CAAC6D,EAAE,EAAEC,EAAE,EAAE/C,EAAE,EAAEC,EAAE,EAAEO,CAAC,EAAEC,CAAC,CAAC,IAAI,CAAC;EAC/C;EACA,OAAOG,CAAC,KAAK,CAAC;AAClB;AACA,OAAO,SAASsD,OAAOA,CAACC,SAAS,EAAE3D,CAAC,EAAEC,CAAC,EAAE;EACrC,OAAO+B,WAAW,CAAC2B,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE3D,CAAC,EAAEC,CAAC,CAAC;AACjD;AACA,OAAO,SAAS2C,aAAaA,CAACe,SAAS,EAAEzB,SAAS,EAAElC,CAAC,EAAEC,CAAC,EAAE;EACtD,OAAO+B,WAAW,CAAC2B,SAAS,EAAEzB,SAAS,EAAE,IAAI,EAAElC,CAAC,EAAEC,CAAC,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}