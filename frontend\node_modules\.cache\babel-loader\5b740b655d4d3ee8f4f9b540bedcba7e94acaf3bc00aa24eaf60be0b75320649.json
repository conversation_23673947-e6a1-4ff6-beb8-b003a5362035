{"ast": null, "code": "import { cubicSubdivide } from '../core/curve.js';\nimport PathProxy from '../core/PathProxy.js';\nvar CMD = PathProxy.CMD;\nfunction aroundEqual(a, b) {\n  return Math.abs(a - b) < 1e-5;\n}\nexport function pathToBezierCurves(path) {\n  var data = path.data;\n  var len = path.len();\n  var bezierArrayGroups = [];\n  var currentSubpath;\n  var xi = 0;\n  var yi = 0;\n  var x0 = 0;\n  var y0 = 0;\n  function createNewSubpath(x, y) {\n    if (currentSubpath && currentSubpath.length > 2) {\n      bezierArrayGroups.push(currentSubpath);\n    }\n    currentSubpath = [x, y];\n  }\n  function addLine(x0, y0, x1, y1) {\n    if (!(aroundEqual(x0, x1) && aroundEqual(y0, y1))) {\n      currentSubpath.push(x0, y0, x1, y1, x1, y1);\n    }\n  }\n  function addArc(startAngle, endAngle, cx, cy, rx, ry) {\n    var delta = Math.abs(endAngle - startAngle);\n    var len = Math.tan(delta / 4) * 4 / 3;\n    var dir = endAngle < startAngle ? -1 : 1;\n    var c1 = Math.cos(startAngle);\n    var s1 = Math.sin(startAngle);\n    var c2 = Math.cos(endAngle);\n    var s2 = Math.sin(endAngle);\n    var x1 = c1 * rx + cx;\n    var y1 = s1 * ry + cy;\n    var x4 = c2 * rx + cx;\n    var y4 = s2 * ry + cy;\n    var hx = rx * len * dir;\n    var hy = ry * len * dir;\n    currentSubpath.push(x1 - hx * s1, y1 + hy * c1, x4 + hx * s2, y4 - hy * c2, x4, y4);\n  }\n  var x1;\n  var y1;\n  var x2;\n  var y2;\n  for (var i = 0; i < len;) {\n    var cmd = data[i++];\n    var isFirst = i === 1;\n    if (isFirst) {\n      xi = data[i];\n      yi = data[i + 1];\n      x0 = xi;\n      y0 = yi;\n      if (cmd === CMD.L || cmd === CMD.C || cmd === CMD.Q) {\n        currentSubpath = [x0, y0];\n      }\n    }\n    switch (cmd) {\n      case CMD.M:\n        xi = x0 = data[i++];\n        yi = y0 = data[i++];\n        createNewSubpath(x0, y0);\n        break;\n      case CMD.L:\n        x1 = data[i++];\n        y1 = data[i++];\n        addLine(xi, yi, x1, y1);\n        xi = x1;\n        yi = y1;\n        break;\n      case CMD.C:\n        currentSubpath.push(data[i++], data[i++], data[i++], data[i++], xi = data[i++], yi = data[i++]);\n        break;\n      case CMD.Q:\n        x1 = data[i++];\n        y1 = data[i++];\n        x2 = data[i++];\n        y2 = data[i++];\n        currentSubpath.push(xi + 2 / 3 * (x1 - xi), yi + 2 / 3 * (y1 - yi), x2 + 2 / 3 * (x1 - x2), y2 + 2 / 3 * (y1 - y2), x2, y2);\n        xi = x2;\n        yi = y2;\n        break;\n      case CMD.A:\n        var cx = data[i++];\n        var cy = data[i++];\n        var rx = data[i++];\n        var ry = data[i++];\n        var startAngle = data[i++];\n        var endAngle = data[i++] + startAngle;\n        i += 1;\n        var anticlockwise = !data[i++];\n        x1 = Math.cos(startAngle) * rx + cx;\n        y1 = Math.sin(startAngle) * ry + cy;\n        if (isFirst) {\n          x0 = x1;\n          y0 = y1;\n          createNewSubpath(x0, y0);\n        } else {\n          addLine(xi, yi, x1, y1);\n        }\n        xi = Math.cos(endAngle) * rx + cx;\n        yi = Math.sin(endAngle) * ry + cy;\n        var step = (anticlockwise ? -1 : 1) * Math.PI / 2;\n        for (var angle = startAngle; anticlockwise ? angle > endAngle : angle < endAngle; angle += step) {\n          var nextAngle = anticlockwise ? Math.max(angle + step, endAngle) : Math.min(angle + step, endAngle);\n          addArc(angle, nextAngle, cx, cy, rx, ry);\n        }\n        break;\n      case CMD.R:\n        x0 = xi = data[i++];\n        y0 = yi = data[i++];\n        x1 = x0 + data[i++];\n        y1 = y0 + data[i++];\n        createNewSubpath(x1, y0);\n        addLine(x1, y0, x1, y1);\n        addLine(x1, y1, x0, y1);\n        addLine(x0, y1, x0, y0);\n        addLine(x0, y0, x1, y0);\n        break;\n      case CMD.Z:\n        currentSubpath && addLine(xi, yi, x0, y0);\n        xi = x0;\n        yi = y0;\n        break;\n    }\n  }\n  if (currentSubpath && currentSubpath.length > 2) {\n    bezierArrayGroups.push(currentSubpath);\n  }\n  return bezierArrayGroups;\n}\nfunction adpativeBezier(x0, y0, x1, y1, x2, y2, x3, y3, out, scale) {\n  if (aroundEqual(x0, x1) && aroundEqual(y0, y1) && aroundEqual(x2, x3) && aroundEqual(y2, y3)) {\n    out.push(x3, y3);\n    return;\n  }\n  var PIXEL_DISTANCE = 2 / scale;\n  var PIXEL_DISTANCE_SQR = PIXEL_DISTANCE * PIXEL_DISTANCE;\n  var dx = x3 - x0;\n  var dy = y3 - y0;\n  var d = Math.sqrt(dx * dx + dy * dy);\n  dx /= d;\n  dy /= d;\n  var dx1 = x1 - x0;\n  var dy1 = y1 - y0;\n  var dx2 = x2 - x3;\n  var dy2 = y2 - y3;\n  var cp1LenSqr = dx1 * dx1 + dy1 * dy1;\n  var cp2LenSqr = dx2 * dx2 + dy2 * dy2;\n  if (cp1LenSqr < PIXEL_DISTANCE_SQR && cp2LenSqr < PIXEL_DISTANCE_SQR) {\n    out.push(x3, y3);\n    return;\n  }\n  var projLen1 = dx * dx1 + dy * dy1;\n  var projLen2 = -dx * dx2 - dy * dy2;\n  var d1Sqr = cp1LenSqr - projLen1 * projLen1;\n  var d2Sqr = cp2LenSqr - projLen2 * projLen2;\n  if (d1Sqr < PIXEL_DISTANCE_SQR && projLen1 >= 0 && d2Sqr < PIXEL_DISTANCE_SQR && projLen2 >= 0) {\n    out.push(x3, y3);\n    return;\n  }\n  var tmpSegX = [];\n  var tmpSegY = [];\n  cubicSubdivide(x0, x1, x2, x3, 0.5, tmpSegX);\n  cubicSubdivide(y0, y1, y2, y3, 0.5, tmpSegY);\n  adpativeBezier(tmpSegX[0], tmpSegY[0], tmpSegX[1], tmpSegY[1], tmpSegX[2], tmpSegY[2], tmpSegX[3], tmpSegY[3], out, scale);\n  adpativeBezier(tmpSegX[4], tmpSegY[4], tmpSegX[5], tmpSegY[5], tmpSegX[6], tmpSegY[6], tmpSegX[7], tmpSegY[7], out, scale);\n}\nexport function pathToPolygons(path, scale) {\n  var bezierArrayGroups = pathToBezierCurves(path);\n  var polygons = [];\n  scale = scale || 1;\n  for (var i = 0; i < bezierArrayGroups.length; i++) {\n    var beziers = bezierArrayGroups[i];\n    var polygon = [];\n    var x0 = beziers[0];\n    var y0 = beziers[1];\n    polygon.push(x0, y0);\n    for (var k = 2; k < beziers.length;) {\n      var x1 = beziers[k++];\n      var y1 = beziers[k++];\n      var x2 = beziers[k++];\n      var y2 = beziers[k++];\n      var x3 = beziers[k++];\n      var y3 = beziers[k++];\n      adpativeBezier(x0, y0, x1, y1, x2, y2, x3, y3, polygon, scale);\n      x0 = x3;\n      y0 = y3;\n    }\n    polygons.push(polygon);\n  }\n  return polygons;\n}", "map": {"version": 3, "names": ["cubicSubdivide", "PathProxy", "CMD", "aroundEqual", "a", "b", "Math", "abs", "pathToBezierCurves", "path", "data", "len", "bezierArrayGroups", "currentSubpath", "xi", "yi", "x0", "y0", "createNewSubpath", "x", "y", "length", "push", "addLine", "x1", "y1", "addArc", "startAngle", "endAngle", "cx", "cy", "rx", "ry", "delta", "tan", "dir", "c1", "cos", "s1", "sin", "c2", "s2", "x4", "y4", "hx", "hy", "x2", "y2", "i", "cmd", "<PERSON><PERSON><PERSON><PERSON>", "L", "C", "Q", "M", "A", "anticlockwise", "step", "PI", "angle", "nextAngle", "max", "min", "R", "Z", "adpativeBezier", "x3", "y3", "out", "scale", "PIXEL_DISTANCE", "PIXEL_DISTANCE_SQR", "dx", "dy", "d", "sqrt", "dx1", "dy1", "dx2", "dy2", "cp1LenSqr", "cp2LenSqr", "projLen1", "projLen2", "d1Sqr", "d2Sqr", "tmpSegX", "tmpSegY", "pathToPolygons", "polygons", "beziers", "polygon", "k"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/zrender/lib/tool/convertPath.js"], "sourcesContent": ["import { cubicSubdivide } from '../core/curve.js';\nimport PathProxy from '../core/PathProxy.js';\nvar CMD = PathProxy.CMD;\nfunction aroundEqual(a, b) {\n    return Math.abs(a - b) < 1e-5;\n}\nexport function pathToBezierCurves(path) {\n    var data = path.data;\n    var len = path.len();\n    var bezierArrayGroups = [];\n    var currentSubpath;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    function createNewSubpath(x, y) {\n        if (currentSubpath && currentSubpath.length > 2) {\n            bezierArrayGroups.push(currentSubpath);\n        }\n        currentSubpath = [x, y];\n    }\n    function addLine(x0, y0, x1, y1) {\n        if (!(aroundEqual(x0, x1) && aroundEqual(y0, y1))) {\n            currentSubpath.push(x0, y0, x1, y1, x1, y1);\n        }\n    }\n    function addArc(startAngle, endAngle, cx, cy, rx, ry) {\n        var delta = Math.abs(endAngle - startAngle);\n        var len = Math.tan(delta / 4) * 4 / 3;\n        var dir = endAngle < startAngle ? -1 : 1;\n        var c1 = Math.cos(startAngle);\n        var s1 = Math.sin(startAngle);\n        var c2 = Math.cos(endAngle);\n        var s2 = Math.sin(endAngle);\n        var x1 = c1 * rx + cx;\n        var y1 = s1 * ry + cy;\n        var x4 = c2 * rx + cx;\n        var y4 = s2 * ry + cy;\n        var hx = rx * len * dir;\n        var hy = ry * len * dir;\n        currentSubpath.push(x1 - hx * s1, y1 + hy * c1, x4 + hx * s2, y4 - hy * c2, x4, y4);\n    }\n    var x1;\n    var y1;\n    var x2;\n    var y2;\n    for (var i = 0; i < len;) {\n        var cmd = data[i++];\n        var isFirst = i === 1;\n        if (isFirst) {\n            xi = data[i];\n            yi = data[i + 1];\n            x0 = xi;\n            y0 = yi;\n            if (cmd === CMD.L || cmd === CMD.C || cmd === CMD.Q) {\n                currentSubpath = [x0, y0];\n            }\n        }\n        switch (cmd) {\n            case CMD.M:\n                xi = x0 = data[i++];\n                yi = y0 = data[i++];\n                createNewSubpath(x0, y0);\n                break;\n            case CMD.L:\n                x1 = data[i++];\n                y1 = data[i++];\n                addLine(xi, yi, x1, y1);\n                xi = x1;\n                yi = y1;\n                break;\n            case CMD.C:\n                currentSubpath.push(data[i++], data[i++], data[i++], data[i++], xi = data[i++], yi = data[i++]);\n                break;\n            case CMD.Q:\n                x1 = data[i++];\n                y1 = data[i++];\n                x2 = data[i++];\n                y2 = data[i++];\n                currentSubpath.push(xi + 2 / 3 * (x1 - xi), yi + 2 / 3 * (y1 - yi), x2 + 2 / 3 * (x1 - x2), y2 + 2 / 3 * (y1 - y2), x2, y2);\n                xi = x2;\n                yi = y2;\n                break;\n            case CMD.A:\n                var cx = data[i++];\n                var cy = data[i++];\n                var rx = data[i++];\n                var ry = data[i++];\n                var startAngle = data[i++];\n                var endAngle = data[i++] + startAngle;\n                i += 1;\n                var anticlockwise = !data[i++];\n                x1 = Math.cos(startAngle) * rx + cx;\n                y1 = Math.sin(startAngle) * ry + cy;\n                if (isFirst) {\n                    x0 = x1;\n                    y0 = y1;\n                    createNewSubpath(x0, y0);\n                }\n                else {\n                    addLine(xi, yi, x1, y1);\n                }\n                xi = Math.cos(endAngle) * rx + cx;\n                yi = Math.sin(endAngle) * ry + cy;\n                var step = (anticlockwise ? -1 : 1) * Math.PI / 2;\n                for (var angle = startAngle; anticlockwise ? angle > endAngle : angle < endAngle; angle += step) {\n                    var nextAngle = anticlockwise ? Math.max(angle + step, endAngle)\n                        : Math.min(angle + step, endAngle);\n                    addArc(angle, nextAngle, cx, cy, rx, ry);\n                }\n                break;\n            case CMD.R:\n                x0 = xi = data[i++];\n                y0 = yi = data[i++];\n                x1 = x0 + data[i++];\n                y1 = y0 + data[i++];\n                createNewSubpath(x1, y0);\n                addLine(x1, y0, x1, y1);\n                addLine(x1, y1, x0, y1);\n                addLine(x0, y1, x0, y0);\n                addLine(x0, y0, x1, y0);\n                break;\n            case CMD.Z:\n                currentSubpath && addLine(xi, yi, x0, y0);\n                xi = x0;\n                yi = y0;\n                break;\n        }\n    }\n    if (currentSubpath && currentSubpath.length > 2) {\n        bezierArrayGroups.push(currentSubpath);\n    }\n    return bezierArrayGroups;\n}\nfunction adpativeBezier(x0, y0, x1, y1, x2, y2, x3, y3, out, scale) {\n    if (aroundEqual(x0, x1) && aroundEqual(y0, y1) && aroundEqual(x2, x3) && aroundEqual(y2, y3)) {\n        out.push(x3, y3);\n        return;\n    }\n    var PIXEL_DISTANCE = 2 / scale;\n    var PIXEL_DISTANCE_SQR = PIXEL_DISTANCE * PIXEL_DISTANCE;\n    var dx = x3 - x0;\n    var dy = y3 - y0;\n    var d = Math.sqrt(dx * dx + dy * dy);\n    dx /= d;\n    dy /= d;\n    var dx1 = x1 - x0;\n    var dy1 = y1 - y0;\n    var dx2 = x2 - x3;\n    var dy2 = y2 - y3;\n    var cp1LenSqr = dx1 * dx1 + dy1 * dy1;\n    var cp2LenSqr = dx2 * dx2 + dy2 * dy2;\n    if (cp1LenSqr < PIXEL_DISTANCE_SQR && cp2LenSqr < PIXEL_DISTANCE_SQR) {\n        out.push(x3, y3);\n        return;\n    }\n    var projLen1 = dx * dx1 + dy * dy1;\n    var projLen2 = -dx * dx2 - dy * dy2;\n    var d1Sqr = cp1LenSqr - projLen1 * projLen1;\n    var d2Sqr = cp2LenSqr - projLen2 * projLen2;\n    if (d1Sqr < PIXEL_DISTANCE_SQR && projLen1 >= 0\n        && d2Sqr < PIXEL_DISTANCE_SQR && projLen2 >= 0) {\n        out.push(x3, y3);\n        return;\n    }\n    var tmpSegX = [];\n    var tmpSegY = [];\n    cubicSubdivide(x0, x1, x2, x3, 0.5, tmpSegX);\n    cubicSubdivide(y0, y1, y2, y3, 0.5, tmpSegY);\n    adpativeBezier(tmpSegX[0], tmpSegY[0], tmpSegX[1], tmpSegY[1], tmpSegX[2], tmpSegY[2], tmpSegX[3], tmpSegY[3], out, scale);\n    adpativeBezier(tmpSegX[4], tmpSegY[4], tmpSegX[5], tmpSegY[5], tmpSegX[6], tmpSegY[6], tmpSegX[7], tmpSegY[7], out, scale);\n}\nexport function pathToPolygons(path, scale) {\n    var bezierArrayGroups = pathToBezierCurves(path);\n    var polygons = [];\n    scale = scale || 1;\n    for (var i = 0; i < bezierArrayGroups.length; i++) {\n        var beziers = bezierArrayGroups[i];\n        var polygon = [];\n        var x0 = beziers[0];\n        var y0 = beziers[1];\n        polygon.push(x0, y0);\n        for (var k = 2; k < beziers.length;) {\n            var x1 = beziers[k++];\n            var y1 = beziers[k++];\n            var x2 = beziers[k++];\n            var y2 = beziers[k++];\n            var x3 = beziers[k++];\n            var y3 = beziers[k++];\n            adpativeBezier(x0, y0, x1, y1, x2, y2, x3, y3, polygon, scale);\n            x0 = x3;\n            y0 = y3;\n        }\n        polygons.push(polygon);\n    }\n    return polygons;\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,IAAIC,GAAG,GAAGD,SAAS,CAACC,GAAG;AACvB,SAASC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvB,OAAOC,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGC,CAAC,CAAC,GAAG,IAAI;AACjC;AACA,OAAO,SAASG,kBAAkBA,CAACC,IAAI,EAAE;EACrC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;EACpB,IAAIC,GAAG,GAAGF,IAAI,CAACE,GAAG,CAAC,CAAC;EACpB,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,cAAc;EAClB,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,SAASC,gBAAgBA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAC5B,IAAIP,cAAc,IAAIA,cAAc,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC7CT,iBAAiB,CAACU,IAAI,CAACT,cAAc,CAAC;IAC1C;IACAA,cAAc,GAAG,CAACM,CAAC,EAAEC,CAAC,CAAC;EAC3B;EACA,SAASG,OAAOA,CAACP,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAEC,EAAE,EAAE;IAC7B,IAAI,EAAEtB,WAAW,CAACa,EAAE,EAAEQ,EAAE,CAAC,IAAIrB,WAAW,CAACc,EAAE,EAAEQ,EAAE,CAAC,CAAC,EAAE;MAC/CZ,cAAc,CAACS,IAAI,CAACN,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAEC,EAAE,EAAED,EAAE,EAAEC,EAAE,CAAC;IAC/C;EACJ;EACA,SAASC,MAAMA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAClD,IAAIC,KAAK,GAAG3B,IAAI,CAACC,GAAG,CAACqB,QAAQ,GAAGD,UAAU,CAAC;IAC3C,IAAIhB,GAAG,GAAGL,IAAI,CAAC4B,GAAG,CAACD,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACrC,IAAIE,GAAG,GAAGP,QAAQ,GAAGD,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;IACxC,IAAIS,EAAE,GAAG9B,IAAI,CAAC+B,GAAG,CAACV,UAAU,CAAC;IAC7B,IAAIW,EAAE,GAAGhC,IAAI,CAACiC,GAAG,CAACZ,UAAU,CAAC;IAC7B,IAAIa,EAAE,GAAGlC,IAAI,CAAC+B,GAAG,CAACT,QAAQ,CAAC;IAC3B,IAAIa,EAAE,GAAGnC,IAAI,CAACiC,GAAG,CAACX,QAAQ,CAAC;IAC3B,IAAIJ,EAAE,GAAGY,EAAE,GAAGL,EAAE,GAAGF,EAAE;IACrB,IAAIJ,EAAE,GAAGa,EAAE,GAAGN,EAAE,GAAGF,EAAE;IACrB,IAAIY,EAAE,GAAGF,EAAE,GAAGT,EAAE,GAAGF,EAAE;IACrB,IAAIc,EAAE,GAAGF,EAAE,GAAGT,EAAE,GAAGF,EAAE;IACrB,IAAIc,EAAE,GAAGb,EAAE,GAAGpB,GAAG,GAAGwB,GAAG;IACvB,IAAIU,EAAE,GAAGb,EAAE,GAAGrB,GAAG,GAAGwB,GAAG;IACvBtB,cAAc,CAACS,IAAI,CAACE,EAAE,GAAGoB,EAAE,GAAGN,EAAE,EAAEb,EAAE,GAAGoB,EAAE,GAAGT,EAAE,EAAEM,EAAE,GAAGE,EAAE,GAAGH,EAAE,EAAEE,EAAE,GAAGE,EAAE,GAAGL,EAAE,EAAEE,EAAE,EAAEC,EAAE,CAAC;EACvF;EACA,IAAInB,EAAE;EACN,IAAIC,EAAE;EACN,IAAIqB,EAAE;EACN,IAAIC,EAAE;EACN,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,GAAG,GAAG;IACtB,IAAIsC,GAAG,GAAGvC,IAAI,CAACsC,CAAC,EAAE,CAAC;IACnB,IAAIE,OAAO,GAAGF,CAAC,KAAK,CAAC;IACrB,IAAIE,OAAO,EAAE;MACTpC,EAAE,GAAGJ,IAAI,CAACsC,CAAC,CAAC;MACZjC,EAAE,GAAGL,IAAI,CAACsC,CAAC,GAAG,CAAC,CAAC;MAChBhC,EAAE,GAAGF,EAAE;MACPG,EAAE,GAAGF,EAAE;MACP,IAAIkC,GAAG,KAAK/C,GAAG,CAACiD,CAAC,IAAIF,GAAG,KAAK/C,GAAG,CAACkD,CAAC,IAAIH,GAAG,KAAK/C,GAAG,CAACmD,CAAC,EAAE;QACjDxC,cAAc,GAAG,CAACG,EAAE,EAAEC,EAAE,CAAC;MAC7B;IACJ;IACA,QAAQgC,GAAG;MACP,KAAK/C,GAAG,CAACoD,CAAC;QACNxC,EAAE,GAAGE,EAAE,GAAGN,IAAI,CAACsC,CAAC,EAAE,CAAC;QACnBjC,EAAE,GAAGE,EAAE,GAAGP,IAAI,CAACsC,CAAC,EAAE,CAAC;QACnB9B,gBAAgB,CAACF,EAAE,EAAEC,EAAE,CAAC;QACxB;MACJ,KAAKf,GAAG,CAACiD,CAAC;QACN3B,EAAE,GAAGd,IAAI,CAACsC,CAAC,EAAE,CAAC;QACdvB,EAAE,GAAGf,IAAI,CAACsC,CAAC,EAAE,CAAC;QACdzB,OAAO,CAACT,EAAE,EAAEC,EAAE,EAAES,EAAE,EAAEC,EAAE,CAAC;QACvBX,EAAE,GAAGU,EAAE;QACPT,EAAE,GAAGU,EAAE;QACP;MACJ,KAAKvB,GAAG,CAACkD,CAAC;QACNvC,cAAc,CAACS,IAAI,CAACZ,IAAI,CAACsC,CAAC,EAAE,CAAC,EAAEtC,IAAI,CAACsC,CAAC,EAAE,CAAC,EAAEtC,IAAI,CAACsC,CAAC,EAAE,CAAC,EAAEtC,IAAI,CAACsC,CAAC,EAAE,CAAC,EAAElC,EAAE,GAAGJ,IAAI,CAACsC,CAAC,EAAE,CAAC,EAAEjC,EAAE,GAAGL,IAAI,CAACsC,CAAC,EAAE,CAAC,CAAC;QAC/F;MACJ,KAAK9C,GAAG,CAACmD,CAAC;QACN7B,EAAE,GAAGd,IAAI,CAACsC,CAAC,EAAE,CAAC;QACdvB,EAAE,GAAGf,IAAI,CAACsC,CAAC,EAAE,CAAC;QACdF,EAAE,GAAGpC,IAAI,CAACsC,CAAC,EAAE,CAAC;QACdD,EAAE,GAAGrC,IAAI,CAACsC,CAAC,EAAE,CAAC;QACdnC,cAAc,CAACS,IAAI,CAACR,EAAE,GAAG,CAAC,GAAG,CAAC,IAAIU,EAAE,GAAGV,EAAE,CAAC,EAAEC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAIU,EAAE,GAAGV,EAAE,CAAC,EAAE+B,EAAE,GAAG,CAAC,GAAG,CAAC,IAAItB,EAAE,GAAGsB,EAAE,CAAC,EAAEC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAItB,EAAE,GAAGsB,EAAE,CAAC,EAAED,EAAE,EAAEC,EAAE,CAAC;QAC3HjC,EAAE,GAAGgC,EAAE;QACP/B,EAAE,GAAGgC,EAAE;QACP;MACJ,KAAK7C,GAAG,CAACqD,CAAC;QACN,IAAI1B,EAAE,GAAGnB,IAAI,CAACsC,CAAC,EAAE,CAAC;QAClB,IAAIlB,EAAE,GAAGpB,IAAI,CAACsC,CAAC,EAAE,CAAC;QAClB,IAAIjB,EAAE,GAAGrB,IAAI,CAACsC,CAAC,EAAE,CAAC;QAClB,IAAIhB,EAAE,GAAGtB,IAAI,CAACsC,CAAC,EAAE,CAAC;QAClB,IAAIrB,UAAU,GAAGjB,IAAI,CAACsC,CAAC,EAAE,CAAC;QAC1B,IAAIpB,QAAQ,GAAGlB,IAAI,CAACsC,CAAC,EAAE,CAAC,GAAGrB,UAAU;QACrCqB,CAAC,IAAI,CAAC;QACN,IAAIQ,aAAa,GAAG,CAAC9C,IAAI,CAACsC,CAAC,EAAE,CAAC;QAC9BxB,EAAE,GAAGlB,IAAI,CAAC+B,GAAG,CAACV,UAAU,CAAC,GAAGI,EAAE,GAAGF,EAAE;QACnCJ,EAAE,GAAGnB,IAAI,CAACiC,GAAG,CAACZ,UAAU,CAAC,GAAGK,EAAE,GAAGF,EAAE;QACnC,IAAIoB,OAAO,EAAE;UACTlC,EAAE,GAAGQ,EAAE;UACPP,EAAE,GAAGQ,EAAE;UACPP,gBAAgB,CAACF,EAAE,EAAEC,EAAE,CAAC;QAC5B,CAAC,MACI;UACDM,OAAO,CAACT,EAAE,EAAEC,EAAE,EAAES,EAAE,EAAEC,EAAE,CAAC;QAC3B;QACAX,EAAE,GAAGR,IAAI,CAAC+B,GAAG,CAACT,QAAQ,CAAC,GAAGG,EAAE,GAAGF,EAAE;QACjCd,EAAE,GAAGT,IAAI,CAACiC,GAAG,CAACX,QAAQ,CAAC,GAAGI,EAAE,GAAGF,EAAE;QACjC,IAAI2B,IAAI,GAAG,CAACD,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIlD,IAAI,CAACoD,EAAE,GAAG,CAAC;QACjD,KAAK,IAAIC,KAAK,GAAGhC,UAAU,EAAE6B,aAAa,GAAGG,KAAK,GAAG/B,QAAQ,GAAG+B,KAAK,GAAG/B,QAAQ,EAAE+B,KAAK,IAAIF,IAAI,EAAE;UAC7F,IAAIG,SAAS,GAAGJ,aAAa,GAAGlD,IAAI,CAACuD,GAAG,CAACF,KAAK,GAAGF,IAAI,EAAE7B,QAAQ,CAAC,GAC1DtB,IAAI,CAACwD,GAAG,CAACH,KAAK,GAAGF,IAAI,EAAE7B,QAAQ,CAAC;UACtCF,MAAM,CAACiC,KAAK,EAAEC,SAAS,EAAE/B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;QAC5C;QACA;MACJ,KAAK9B,GAAG,CAAC6D,CAAC;QACN/C,EAAE,GAAGF,EAAE,GAAGJ,IAAI,CAACsC,CAAC,EAAE,CAAC;QACnB/B,EAAE,GAAGF,EAAE,GAAGL,IAAI,CAACsC,CAAC,EAAE,CAAC;QACnBxB,EAAE,GAAGR,EAAE,GAAGN,IAAI,CAACsC,CAAC,EAAE,CAAC;QACnBvB,EAAE,GAAGR,EAAE,GAAGP,IAAI,CAACsC,CAAC,EAAE,CAAC;QACnB9B,gBAAgB,CAACM,EAAE,EAAEP,EAAE,CAAC;QACxBM,OAAO,CAACC,EAAE,EAAEP,EAAE,EAAEO,EAAE,EAAEC,EAAE,CAAC;QACvBF,OAAO,CAACC,EAAE,EAAEC,EAAE,EAAET,EAAE,EAAES,EAAE,CAAC;QACvBF,OAAO,CAACP,EAAE,EAAES,EAAE,EAAET,EAAE,EAAEC,EAAE,CAAC;QACvBM,OAAO,CAACP,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAEP,EAAE,CAAC;QACvB;MACJ,KAAKf,GAAG,CAAC8D,CAAC;QACNnD,cAAc,IAAIU,OAAO,CAACT,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;QACzCH,EAAE,GAAGE,EAAE;QACPD,EAAE,GAAGE,EAAE;QACP;IACR;EACJ;EACA,IAAIJ,cAAc,IAAIA,cAAc,CAACQ,MAAM,GAAG,CAAC,EAAE;IAC7CT,iBAAiB,CAACU,IAAI,CAACT,cAAc,CAAC;EAC1C;EACA,OAAOD,iBAAiB;AAC5B;AACA,SAASqD,cAAcA,CAACjD,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAEC,EAAE,EAAEqB,EAAE,EAAEC,EAAE,EAAEmB,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAChE,IAAIlE,WAAW,CAACa,EAAE,EAAEQ,EAAE,CAAC,IAAIrB,WAAW,CAACc,EAAE,EAAEQ,EAAE,CAAC,IAAItB,WAAW,CAAC2C,EAAE,EAAEoB,EAAE,CAAC,IAAI/D,WAAW,CAAC4C,EAAE,EAAEoB,EAAE,CAAC,EAAE;IAC1FC,GAAG,CAAC9C,IAAI,CAAC4C,EAAE,EAAEC,EAAE,CAAC;IAChB;EACJ;EACA,IAAIG,cAAc,GAAG,CAAC,GAAGD,KAAK;EAC9B,IAAIE,kBAAkB,GAAGD,cAAc,GAAGA,cAAc;EACxD,IAAIE,EAAE,GAAGN,EAAE,GAAGlD,EAAE;EAChB,IAAIyD,EAAE,GAAGN,EAAE,GAAGlD,EAAE;EAChB,IAAIyD,CAAC,GAAGpE,IAAI,CAACqE,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;EACpCD,EAAE,IAAIE,CAAC;EACPD,EAAE,IAAIC,CAAC;EACP,IAAIE,GAAG,GAAGpD,EAAE,GAAGR,EAAE;EACjB,IAAI6D,GAAG,GAAGpD,EAAE,GAAGR,EAAE;EACjB,IAAI6D,GAAG,GAAGhC,EAAE,GAAGoB,EAAE;EACjB,IAAIa,GAAG,GAAGhC,EAAE,GAAGoB,EAAE;EACjB,IAAIa,SAAS,GAAGJ,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG;EACrC,IAAII,SAAS,GAAGH,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG;EACrC,IAAIC,SAAS,GAAGT,kBAAkB,IAAIU,SAAS,GAAGV,kBAAkB,EAAE;IAClEH,GAAG,CAAC9C,IAAI,CAAC4C,EAAE,EAAEC,EAAE,CAAC;IAChB;EACJ;EACA,IAAIe,QAAQ,GAAGV,EAAE,GAAGI,GAAG,GAAGH,EAAE,GAAGI,GAAG;EAClC,IAAIM,QAAQ,GAAG,CAACX,EAAE,GAAGM,GAAG,GAAGL,EAAE,GAAGM,GAAG;EACnC,IAAIK,KAAK,GAAGJ,SAAS,GAAGE,QAAQ,GAAGA,QAAQ;EAC3C,IAAIG,KAAK,GAAGJ,SAAS,GAAGE,QAAQ,GAAGA,QAAQ;EAC3C,IAAIC,KAAK,GAAGb,kBAAkB,IAAIW,QAAQ,IAAI,CAAC,IACxCG,KAAK,GAAGd,kBAAkB,IAAIY,QAAQ,IAAI,CAAC,EAAE;IAChDf,GAAG,CAAC9C,IAAI,CAAC4C,EAAE,EAAEC,EAAE,CAAC;IAChB;EACJ;EACA,IAAImB,OAAO,GAAG,EAAE;EAChB,IAAIC,OAAO,GAAG,EAAE;EAChBvF,cAAc,CAACgB,EAAE,EAAEQ,EAAE,EAAEsB,EAAE,EAAEoB,EAAE,EAAE,GAAG,EAAEoB,OAAO,CAAC;EAC5CtF,cAAc,CAACiB,EAAE,EAAEQ,EAAE,EAAEsB,EAAE,EAAEoB,EAAE,EAAE,GAAG,EAAEoB,OAAO,CAAC;EAC5CtB,cAAc,CAACqB,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAEnB,GAAG,EAAEC,KAAK,CAAC;EAC1HJ,cAAc,CAACqB,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAEnB,GAAG,EAAEC,KAAK,CAAC;AAC9H;AACA,OAAO,SAASmB,cAAcA,CAAC/E,IAAI,EAAE4D,KAAK,EAAE;EACxC,IAAIzD,iBAAiB,GAAGJ,kBAAkB,CAACC,IAAI,CAAC;EAChD,IAAIgF,QAAQ,GAAG,EAAE;EACjBpB,KAAK,GAAGA,KAAK,IAAI,CAAC;EAClB,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,iBAAiB,CAACS,MAAM,EAAE2B,CAAC,EAAE,EAAE;IAC/C,IAAI0C,OAAO,GAAG9E,iBAAiB,CAACoC,CAAC,CAAC;IAClC,IAAI2C,OAAO,GAAG,EAAE;IAChB,IAAI3E,EAAE,GAAG0E,OAAO,CAAC,CAAC,CAAC;IACnB,IAAIzE,EAAE,GAAGyE,OAAO,CAAC,CAAC,CAAC;IACnBC,OAAO,CAACrE,IAAI,CAACN,EAAE,EAAEC,EAAE,CAAC;IACpB,KAAK,IAAI2E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACrE,MAAM,GAAG;MACjC,IAAIG,EAAE,GAAGkE,OAAO,CAACE,CAAC,EAAE,CAAC;MACrB,IAAInE,EAAE,GAAGiE,OAAO,CAACE,CAAC,EAAE,CAAC;MACrB,IAAI9C,EAAE,GAAG4C,OAAO,CAACE,CAAC,EAAE,CAAC;MACrB,IAAI7C,EAAE,GAAG2C,OAAO,CAACE,CAAC,EAAE,CAAC;MACrB,IAAI1B,EAAE,GAAGwB,OAAO,CAACE,CAAC,EAAE,CAAC;MACrB,IAAIzB,EAAE,GAAGuB,OAAO,CAACE,CAAC,EAAE,CAAC;MACrB3B,cAAc,CAACjD,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAEC,EAAE,EAAEqB,EAAE,EAAEC,EAAE,EAAEmB,EAAE,EAAEC,EAAE,EAAEwB,OAAO,EAAEtB,KAAK,CAAC;MAC9DrD,EAAE,GAAGkD,EAAE;MACPjD,EAAE,GAAGkD,EAAE;IACX;IACAsB,QAAQ,CAACnE,IAAI,CAACqE,OAAO,CAAC;EAC1B;EACA,OAAOF,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}