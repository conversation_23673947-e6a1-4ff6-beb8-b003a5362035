{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM\\\\frontend\\\\src\\\\pages\\\\BOM\\\\BOMDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { Card, Row, Col, Descriptions, Button, Space, Typography, Tag, Table, Statistic, Modal, Form, Select, InputNumber, message, Tooltip, Tree } from 'antd';\nimport { ArrowLeftOutlined, EditOutlined, PlusOutlined, DeleteOutlined, CalculatorOutlined, AppstoreOutlined, SaveOutlined } from '@ant-design/icons';\nimport { selectBOMs, selectMaterials, setCurrentBOM, updateBOM, calculateBOMCost } from '../../store/slices/bomSlice';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst BOMDetail = () => {\n  _s();\n  var _bom$items, _bom$items2, _bom$items3;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const boms = useSelector(selectBOMs);\n  const materials = useSelector(selectMaterials);\n  const [bom, setBOM] = useState(null);\n  const [editMode, setEditMode] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  useEffect(() => {\n    const foundBOM = boms.find(b => b.id === id);\n    if (foundBOM) {\n      setBOM(foundBOM);\n      dispatch(setCurrentBOM(foundBOM));\n    }\n  }, [id, boms, dispatch]);\n  if (!bom) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"BOM\\u4E0D\\u5B58\\u5728\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 12\n    }, this);\n  }\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'active': 'green',\n      'inactive': 'red',\n      'draft': 'orange'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      'active': '启用',\n      'inactive': '停用',\n      'draft': '草稿'\n    };\n    return texts[status] || status;\n  };\n  const getTypeColor = type => {\n    const colors = {\n      'product': 'blue',\n      'component': 'green',\n      'material': 'orange'\n    };\n    return colors[type] || 'default';\n  };\n  const getTypeText = type => {\n    const texts = {\n      'product': '产品',\n      'component': '组件',\n      'material': '物料'\n    };\n    return texts[type] || type;\n  };\n  const handleAddItem = () => {\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEditItem = item => {\n    form.setFieldsValue(item);\n    setModalVisible(true);\n  };\n  const handleDeleteItem = itemId => {\n    Modal.confirm({\n      title: '确认删除',\n      content: '确定要删除这个物料项吗？',\n      onOk: () => {\n        const updatedBOM = {\n          ...bom,\n          items: bom.items.filter(item => item.id !== itemId)\n        };\n        setBOM(updatedBOM);\n        dispatch(calculateBOMCost({\n          bomId: bom.id\n        }));\n        message.success('删除成功');\n      }\n    });\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      const selectedMaterial = materials.find(m => m.id === values.materialId);\n      if (!selectedMaterial) {\n        message.error('请选择有效的物料');\n        return;\n      }\n      const newItem = {\n        id: Math.max(...bom.items.map(i => i.id), 0) + 1,\n        materialId: selectedMaterial.id,\n        materialCode: selectedMaterial.code,\n        materialName: selectedMaterial.name,\n        specification: selectedMaterial.specification,\n        quantity: values.quantity,\n        unit: selectedMaterial.unit,\n        unitCost: selectedMaterial.currentCost,\n        totalCost: values.quantity * selectedMaterial.currentCost,\n        level: 1,\n        parentId: null,\n        children: []\n      };\n      const updatedBOM = {\n        ...bom,\n        items: [...bom.items, newItem],\n        totalCost: bom.totalCost + newItem.totalCost\n      };\n      setBOM(updatedBOM);\n      setModalVisible(false);\n      message.success('添加成功');\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n  const handleSave = () => {\n    dispatch(updateBOM(bom));\n    setEditMode(false);\n    message.success('保存成功');\n  };\n  const columns = [{\n    title: '物料编码',\n    dataIndex: 'materialCode',\n    key: 'materialCode',\n    width: 150\n  }, {\n    title: '物料名称',\n    dataIndex: 'materialName',\n    key: 'materialName',\n    width: 200\n  }, {\n    title: '规格型号',\n    dataIndex: 'specification',\n    key: 'specification',\n    width: 200,\n    ellipsis: true\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    key: 'quantity',\n    width: 80,\n    render: (quantity, record) => `${quantity} ${record.unit}`\n  }, {\n    title: '单价',\n    dataIndex: 'unitCost',\n    key: 'unitCost',\n    width: 100,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '小计',\n    dataIndex: 'totalCost',\n    key: 'totalCost',\n    width: 100,\n    render: cost => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#1890ff'\n      },\n      children: formatCurrency(cost)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'actions',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => handleEditItem(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          danger: true,\n          onClick: () => handleDeleteItem(record.id)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 计算统计数据\n  const stats = {\n    itemCount: ((_bom$items = bom.items) === null || _bom$items === void 0 ? void 0 : _bom$items.length) || 0,\n    totalCost: bom.totalCost || 0,\n    avgCost: ((_bom$items2 = bom.items) === null || _bom$items2 === void 0 ? void 0 : _bom$items2.length) > 0 ? bom.totalCost / bom.items.length : 0,\n    materialTypes: new Set((_bom$items3 = bom.items) === null || _bom$items3 === void 0 ? void 0 : _bom$items3.map(item => {\n      const material = materials.find(m => m.id === item.materialId);\n      return material === null || material === void 0 ? void 0 : material.category;\n    }).filter(Boolean)).size\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate('/bom'),\n          style: {\n            marginRight: '16px'\n          },\n          children: \"\\u8FD4\\u56DE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            margin: 0\n          },\n          children: bom.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: editMode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setEditMode(false),\n            children: \"\\u53D6\\u6D88\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 44\n            }, this),\n            onClick: handleSave,\n            children: \"\\u4FDD\\u5B58\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 27\n          }, this),\n          onClick: () => setEditMode(true),\n          children: \"\\u7F16\\u8F91\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          children: /*#__PURE__*/_jsxDEV(Descriptions, {\n            column: {\n              xs: 1,\n              sm: 2,\n              md: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"BOM\\u7F16\\u7801\",\n              children: bom.code\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"BOM\\u540D\\u79F0\",\n              children: bom.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7248\\u672C\",\n              children: bom.version\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7C7B\\u578B\",\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: getTypeColor(bom.type),\n                children: getTypeText(bom.type)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: getStatusColor(bom.status),\n                children: getStatusText(bom.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u521B\\u5EFA\\u4EBA\",\n              children: bom.createdBy\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n              children: bom.createTime\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n              children: bom.updateTime\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u63CF\\u8FF0\",\n              span: 2,\n              children: bom.description || '无'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6210\\u672C\\u7EDF\\u8BA1\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [16, 16],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u7269\\u6599\\u9879\\u6570\",\n                value: stats.itemCount,\n                suffix: \"\\u9879\",\n                prefix: /*#__PURE__*/_jsxDEV(AppstoreOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u7269\\u6599\\u7C7B\\u522B\",\n                value: stats.materialTypes,\n                suffix: \"\\u7C7B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u603B\\u6210\\u672C\",\n                value: stats.totalCost,\n                formatter: value => formatCurrency(value),\n                valueStyle: {\n                  color: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5E73\\u5747\\u6210\\u672C\",\n                value: stats.avgCost,\n                formatter: value => formatCurrency(value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"BOM\\u660E\\u7EC6\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(CalculatorOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 21\n          }, this),\n          onClick: () => dispatch(calculateBOMCost({\n            bomId: bom.id\n          })),\n          children: \"\\u91CD\\u65B0\\u8BA1\\u7B97\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 21\n          }, this),\n          onClick: handleAddItem,\n          children: \"\\u6DFB\\u52A0\\u7269\\u6599\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: bom.items || [],\n        rowKey: \"id\",\n        pagination: false,\n        size: \"small\",\n        summary: pageData => {\n          const totalCost = pageData.reduce((sum, item) => sum + item.totalCost, 0);\n          return /*#__PURE__*/_jsxDEV(Table.Summary.Row, {\n            children: [/*#__PURE__*/_jsxDEV(Table.Summary.Cell, {\n              index: 0,\n              colSpan: 5,\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5408\\u8BA1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table.Summary.Cell, {\n              index: 1,\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                style: {\n                  color: '#1890ff'\n                },\n                children: formatCurrency(totalCost)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table.Summary.Cell, {\n              index: 2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u6DFB\\u52A0\\u7269\\u6599\",\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      width: 600,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"materialId\",\n          label: \"\\u9009\\u62E9\\u7269\\u6599\",\n          rules: [{\n            required: true,\n            message: '请选择物料'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7269\\u6599\",\n            showSearch: true,\n            optionFilterProp: \"children\",\n            filterOption: (input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0,\n            children: materials.map(material => /*#__PURE__*/_jsxDEV(Option, {\n              value: material.id,\n              children: [material.code, \" - \", material.name]\n            }, material.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"quantity\",\n          label: \"\\u6570\\u91CF\",\n          rules: [{\n            required: true,\n            message: '请输入数量'\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            min: 0.01,\n            precision: 2,\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u6570\\u91CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 5\n  }, this);\n};\n_s(BOMDetail, \"bLIbWSN4KBS+uupVPtypIsCf6BE=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector, useSelector, Form.useForm];\n});\n_c = BOMDetail;\nexport default BOMDetail;\nvar _c;\n$RefreshReg$(_c, \"BOMDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useSelector", "useDispatch", "Card", "Row", "Col", "Descriptions", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Table", "Statistic", "Modal", "Form", "Select", "InputNumber", "message", "<PERSON><PERSON><PERSON>", "Tree", "ArrowLeftOutlined", "EditOutlined", "PlusOutlined", "DeleteOutlined", "CalculatorOutlined", "AppstoreOutlined", "SaveOutlined", "selectBOMs", "selectMaterials", "setCurrentBOM", "updateBOM", "calculateBOMCost", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Option", "BOMDetail", "_s", "_bom$items", "_bom$items2", "_bom$items3", "id", "navigate", "dispatch", "boms", "materials", "bom", "setBOM", "editMode", "setEditMode", "modalVisible", "setModalVisible", "form", "useForm", "foundBOM", "find", "b", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "getStatusColor", "status", "colors", "getStatusText", "texts", "getTypeColor", "type", "getTypeText", "handleAddItem", "resetFields", "handleEditItem", "item", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDeleteItem", "itemId", "confirm", "title", "content", "onOk", "updatedBOM", "items", "filter", "bomId", "success", "handleModalOk", "values", "validateFields", "selectedMaterial", "m", "materialId", "error", "newItem", "Math", "max", "map", "i", "materialCode", "code", "materialName", "name", "specification", "quantity", "unit", "unitCost", "currentCost", "totalCost", "level", "parentId", "console", "handleSave", "columns", "dataIndex", "key", "width", "ellipsis", "render", "record", "cost", "strong", "color", "_", "size", "icon", "onClick", "danger", "stats", "itemCount", "length", "avgCost", "materialTypes", "Set", "material", "category", "Boolean", "marginBottom", "display", "alignItems", "justifyContent", "marginRight", "margin", "gutter", "xs", "lg", "column", "sm", "md", "<PERSON><PERSON>", "label", "version", "created<PERSON>y", "createTime", "updateTime", "span", "description", "suffix", "prefix", "formatter", "valueStyle", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "summary", "pageData", "reduce", "sum", "Summary", "Cell", "index", "colSpan", "open", "onCancel", "destroyOnClose", "layout", "rules", "required", "placeholder", "showSearch", "optionFilterProp", "filterOption", "input", "option", "toLowerCase", "indexOf", "min", "precision", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/pages/BOM/BOMDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport {\n  Card,\n  Row,\n  Col,\n  Descriptions,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Table,\n  Statistic,\n  Modal,\n  Form,\n  Select,\n  InputNumber,\n  message,\n  Tooltip,\n  Tree\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  EditOutlined,\n  PlusOutlined,\n  DeleteOutlined,\n  CalculatorOutlined,\n  AppstoreOutlined,\n  SaveOutlined\n} from '@ant-design/icons';\n\nimport {\n  selectBOMs,\n  selectMaterials,\n  setCurrentBOM,\n  updateBOM,\n  calculateBOMCost\n} from '../../store/slices/bomSlice';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nconst BOMDetail = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const boms = useSelector(selectBOMs);\n  const materials = useSelector(selectMaterials);\n\n  const [bom, setBOM] = useState(null);\n  const [editMode, setEditMode] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    const foundBOM = boms.find(b => b.id === id);\n    if (foundBOM) {\n      setBOM(foundBOM);\n      dispatch(setCurrentBOM(foundBOM));\n    }\n  }, [id, boms, dispatch]);\n\n  if (!bom) {\n    return <div>BOM不存在</div>;\n  }\n\n  const formatCurrency = (value) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      'active': 'green',\n      'inactive': 'red',\n      'draft': 'orange'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      'active': '启用',\n      'inactive': '停用',\n      'draft': '草稿'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeColor = (type) => {\n    const colors = {\n      'product': 'blue',\n      'component': 'green',\n      'material': 'orange'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      'product': '产品',\n      'component': '组件',\n      'material': '物料'\n    };\n    return texts[type] || type;\n  };\n\n  const handleAddItem = () => {\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditItem = (item) => {\n    form.setFieldsValue(item);\n    setModalVisible(true);\n  };\n\n  const handleDeleteItem = (itemId) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: '确定要删除这个物料项吗？',\n      onOk: () => {\n        const updatedBOM = {\n          ...bom,\n          items: bom.items.filter(item => item.id !== itemId)\n        };\n        setBOM(updatedBOM);\n        dispatch(calculateBOMCost({ bomId: bom.id }));\n        message.success('删除成功');\n      }\n    });\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      const selectedMaterial = materials.find(m => m.id === values.materialId);\n      \n      if (!selectedMaterial) {\n        message.error('请选择有效的物料');\n        return;\n      }\n\n      const newItem = {\n        id: Math.max(...bom.items.map(i => i.id), 0) + 1,\n        materialId: selectedMaterial.id,\n        materialCode: selectedMaterial.code,\n        materialName: selectedMaterial.name,\n        specification: selectedMaterial.specification,\n        quantity: values.quantity,\n        unit: selectedMaterial.unit,\n        unitCost: selectedMaterial.currentCost,\n        totalCost: values.quantity * selectedMaterial.currentCost,\n        level: 1,\n        parentId: null,\n        children: []\n      };\n\n      const updatedBOM = {\n        ...bom,\n        items: [...bom.items, newItem],\n        totalCost: bom.totalCost + newItem.totalCost\n      };\n\n      setBOM(updatedBOM);\n      setModalVisible(false);\n      message.success('添加成功');\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleSave = () => {\n    dispatch(updateBOM(bom));\n    setEditMode(false);\n    message.success('保存成功');\n  };\n\n  const columns = [\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 150\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      width: 200\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 200,\n      ellipsis: true\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n      render: (quantity, record) => `${quantity} ${record.unit}`\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitCost',\n      key: 'unitCost',\n      width: 100,\n      render: (cost) => formatCurrency(cost)\n    },\n    {\n      title: '小计',\n      dataIndex: 'totalCost',\n      key: 'totalCost',\n      width: 100,\n      render: (cost) => (\n        <Text strong style={{ color: '#1890ff' }}>\n          {formatCurrency(cost)}\n        </Text>\n      )\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 120,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Tooltip title=\"编辑\">\n            <Button \n              type=\"text\" \n              icon={<EditOutlined />} \n              size=\"small\"\n              onClick={() => handleEditItem(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button \n              type=\"text\" \n              icon={<DeleteOutlined />} \n              size=\"small\"\n              danger\n              onClick={() => handleDeleteItem(record.id)}\n            />\n          </Tooltip>\n        </Space>\n      )\n    }\n  ];\n\n  // 计算统计数据\n  const stats = {\n    itemCount: bom.items?.length || 0,\n    totalCost: bom.totalCost || 0,\n    avgCost: bom.items?.length > 0 ? bom.totalCost / bom.items.length : 0,\n    materialTypes: new Set(bom.items?.map(item => {\n      const material = materials.find(m => m.id === item.materialId);\n      return material?.category;\n    }).filter(Boolean)).size\n  };\n\n  return (\n    <div>\n      {/* 页面标题 */}\n      <div style={{ marginBottom: '24px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <Button \n            icon={<ArrowLeftOutlined />} \n            onClick={() => navigate('/bom')}\n            style={{ marginRight: '16px' }}\n          >\n            返回\n          </Button>\n          <Title level={2} style={{ margin: 0 }}>\n            {bom.name}\n          </Title>\n        </div>\n        <Space>\n          {editMode ? (\n            <>\n              <Button onClick={() => setEditMode(false)}>取消</Button>\n              <Button type=\"primary\" icon={<SaveOutlined />} onClick={handleSave}>\n                保存\n              </Button>\n            </>\n          ) : (\n            <Button icon={<EditOutlined />} onClick={() => setEditMode(true)}>\n              编辑\n            </Button>\n          )}\n        </Space>\n      </div>\n\n      {/* BOM基本信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col xs={24} lg={16}>\n          <Card title=\"基本信息\">\n            <Descriptions column={{ xs: 1, sm: 2, md: 2 }}>\n              <Descriptions.Item label=\"BOM编码\">\n                {bom.code}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"BOM名称\">\n                {bom.name}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"版本\">\n                {bom.version}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"类型\">\n                <Tag color={getTypeColor(bom.type)}>\n                  {getTypeText(bom.type)}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"状态\">\n                <Tag color={getStatusColor(bom.status)}>\n                  {getStatusText(bom.status)}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"创建人\">\n                {bom.createdBy}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"创建时间\">\n                {bom.createTime}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"更新时间\">\n                {bom.updateTime}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"描述\" span={2}>\n                {bom.description || '无'}\n              </Descriptions.Item>\n            </Descriptions>\n          </Card>\n        </Col>\n\n        <Col xs={24} lg={8}>\n          <Card title=\"成本统计\">\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Statistic\n                  title=\"物料项数\"\n                  value={stats.itemCount}\n                  suffix=\"项\"\n                  prefix={<AppstoreOutlined />}\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"物料类别\"\n                  value={stats.materialTypes}\n                  suffix=\"类\"\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"总成本\"\n                  value={stats.totalCost}\n                  formatter={(value) => formatCurrency(value)}\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"平均成本\"\n                  value={stats.avgCost}\n                  formatter={(value) => formatCurrency(value)}\n                />\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* BOM明细 */}\n      <Card \n        title=\"BOM明细\"\n        extra={\n          <Space>\n            <Button \n              icon={<CalculatorOutlined />}\n              onClick={() => dispatch(calculateBOMCost({ bomId: bom.id }))}\n            >\n              重新计算\n            </Button>\n            <Button \n              type=\"primary\" \n              icon={<PlusOutlined />} \n              onClick={handleAddItem}\n            >\n              添加物料\n            </Button>\n          </Space>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={bom.items || []}\n          rowKey=\"id\"\n          pagination={false}\n          size=\"small\"\n          summary={(pageData) => {\n            const totalCost = pageData.reduce((sum, item) => sum + item.totalCost, 0);\n            return (\n              <Table.Summary.Row>\n                <Table.Summary.Cell index={0} colSpan={5}>\n                  <Text strong>合计</Text>\n                </Table.Summary.Cell>\n                <Table.Summary.Cell index={1}>\n                  <Text strong style={{ color: '#1890ff' }}>\n                    {formatCurrency(totalCost)}\n                  </Text>\n                </Table.Summary.Cell>\n                <Table.Summary.Cell index={2} />\n              </Table.Summary.Row>\n            );\n          }}\n        />\n      </Card>\n\n      {/* 添加物料弹窗 */}\n      <Modal\n        title=\"添加物料\"\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n        width={600}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"materialId\"\n            label=\"选择物料\"\n            rules={[{ required: true, message: '请选择物料' }]}\n          >\n            <Select\n              placeholder=\"请选择物料\"\n              showSearch\n              optionFilterProp=\"children\"\n              filterOption={(input, option) =>\n                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0\n              }\n            >\n              {materials.map(material => (\n                <Option key={material.id} value={material.id}>\n                  {material.code} - {material.name}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n          <Form.Item\n            name=\"quantity\"\n            label=\"数量\"\n            rules={[{ required: true, message: '请输入数量' }]}\n          >\n            <InputNumber\n              min={0.01}\n              precision={2}\n              style={{ width: '100%' }}\n              placeholder=\"请输入数量\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default BOMDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,YAAY,EACZC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,OAAO,EACPC,OAAO,EACPC,IAAI,QACC,MAAM;AACb,SACEC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,YAAY,QACP,mBAAmB;AAE1B,SACEC,UAAU,EACVC,eAAe,EACfC,aAAa,EACbC,SAAS,EACTC,gBAAgB,QACX,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG5B,UAAU;AAClC,MAAM;EAAE6B;AAAO,CAAC,GAAGvB,MAAM;AAEzB,MAAMwB,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA;EACtB,MAAM;IAAEC;EAAG,CAAC,GAAG7C,SAAS,CAAC,CAAC;EAC1B,MAAM8C,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,IAAI,GAAG9C,WAAW,CAAC0B,UAAU,CAAC;EACpC,MAAMqB,SAAS,GAAG/C,WAAW,CAAC2B,eAAe,CAAC;EAE9C,MAAM,CAACqB,GAAG,EAAEC,MAAM,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0D,IAAI,CAAC,GAAGzC,IAAI,CAAC0C,OAAO,CAAC,CAAC;EAE7B1D,SAAS,CAAC,MAAM;IACd,MAAM2D,QAAQ,GAAGV,IAAI,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKA,EAAE,CAAC;IAC5C,IAAIa,QAAQ,EAAE;MACZP,MAAM,CAACO,QAAQ,CAAC;MAChBX,QAAQ,CAACjB,aAAa,CAAC4B,QAAQ,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACb,EAAE,EAAEG,IAAI,EAAED,QAAQ,CAAC,CAAC;EAExB,IAAI,CAACG,GAAG,EAAE;IACR,oBAAOhB,OAAA;MAAA2B,QAAA,EAAK;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1B;EAEA,MAAMC,cAAc,GAAIC,KAAK,IAAK;IAChC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACP,KAAK,CAAC;EAClB,CAAC;EAED,MAAMQ,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,OAAO;MACjB,UAAU,EAAE,KAAK;MACjB,OAAO,EAAE;IACX,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAME,aAAa,GAAIF,MAAM,IAAK;IAChC,MAAMG,KAAK,GAAG;MACZ,QAAQ,EAAE,IAAI;MACd,UAAU,EAAE,IAAI;MAChB,OAAO,EAAE;IACX,CAAC;IACD,OAAOA,KAAK,CAACH,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMI,YAAY,GAAIC,IAAI,IAAK;IAC7B,MAAMJ,MAAM,GAAG;MACb,SAAS,EAAE,MAAM;MACjB,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,MAAM,CAACI,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAMC,WAAW,GAAID,IAAI,IAAK;IAC5B,MAAMF,KAAK,GAAG;MACZ,SAAS,EAAE,IAAI;MACf,WAAW,EAAE,IAAI;MACjB,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,KAAK,CAACE,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B3B,IAAI,CAAC4B,WAAW,CAAC,CAAC;IAClB7B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM8B,cAAc,GAAIC,IAAI,IAAK;IAC/B9B,IAAI,CAAC+B,cAAc,CAACD,IAAI,CAAC;IACzB/B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMiC,gBAAgB,GAAIC,MAAM,IAAK;IACnC3E,KAAK,CAAC4E,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,cAAc;MACvBC,IAAI,EAAEA,CAAA,KAAM;QACV,MAAMC,UAAU,GAAG;UACjB,GAAG5C,GAAG;UACN6C,KAAK,EAAE7C,GAAG,CAAC6C,KAAK,CAACC,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACzC,EAAE,KAAK4C,MAAM;QACpD,CAAC;QACDtC,MAAM,CAAC2C,UAAU,CAAC;QAClB/C,QAAQ,CAACf,gBAAgB,CAAC;UAAEiE,KAAK,EAAE/C,GAAG,CAACL;QAAG,CAAC,CAAC,CAAC;QAC7C3B,OAAO,CAACgF,OAAO,CAAC,MAAM,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM5C,IAAI,CAAC6C,cAAc,CAAC,CAAC;MAC1C,MAAMC,gBAAgB,GAAGrD,SAAS,CAACU,IAAI,CAAC4C,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKuD,MAAM,CAACI,UAAU,CAAC;MAExE,IAAI,CAACF,gBAAgB,EAAE;QACrBpF,OAAO,CAACuF,KAAK,CAAC,UAAU,CAAC;QACzB;MACF;MAEA,MAAMC,OAAO,GAAG;QACd7D,EAAE,EAAE8D,IAAI,CAACC,GAAG,CAAC,GAAG1D,GAAG,CAAC6C,KAAK,CAACc,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;QAChD2D,UAAU,EAAEF,gBAAgB,CAACzD,EAAE;QAC/BkE,YAAY,EAAET,gBAAgB,CAACU,IAAI;QACnCC,YAAY,EAAEX,gBAAgB,CAACY,IAAI;QACnCC,aAAa,EAAEb,gBAAgB,CAACa,aAAa;QAC7CC,QAAQ,EAAEhB,MAAM,CAACgB,QAAQ;QACzBC,IAAI,EAAEf,gBAAgB,CAACe,IAAI;QAC3BC,QAAQ,EAAEhB,gBAAgB,CAACiB,WAAW;QACtCC,SAAS,EAAEpB,MAAM,CAACgB,QAAQ,GAAGd,gBAAgB,CAACiB,WAAW;QACzDE,KAAK,EAAE,CAAC;QACRC,QAAQ,EAAE,IAAI;QACd7D,QAAQ,EAAE;MACZ,CAAC;MAED,MAAMiC,UAAU,GAAG;QACjB,GAAG5C,GAAG;QACN6C,KAAK,EAAE,CAAC,GAAG7C,GAAG,CAAC6C,KAAK,EAAEW,OAAO,CAAC;QAC9Bc,SAAS,EAAEtE,GAAG,CAACsE,SAAS,GAAGd,OAAO,CAACc;MACrC,CAAC;MAEDrE,MAAM,CAAC2C,UAAU,CAAC;MAClBvC,eAAe,CAAC,KAAK,CAAC;MACtBrC,OAAO,CAACgF,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAMmB,UAAU,GAAGA,CAAA,KAAM;IACvB7E,QAAQ,CAAChB,SAAS,CAACmB,GAAG,CAAC,CAAC;IACxBG,WAAW,CAAC,KAAK,CAAC;IAClBnC,OAAO,CAACgF,OAAO,CAAC,MAAM,CAAC;EACzB,CAAC;EAED,MAAM2B,OAAO,GAAG,CACd;IACElC,KAAK,EAAE,MAAM;IACbmC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACErC,KAAK,EAAE,MAAM;IACbmC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACErC,KAAK,EAAE,MAAM;IACbmC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEtC,KAAK,EAAE,IAAI;IACXmC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAEA,CAACd,QAAQ,EAAEe,MAAM,KAAK,GAAGf,QAAQ,IAAIe,MAAM,CAACd,IAAI;EAC1D,CAAC,EACD;IACE1B,KAAK,EAAE,IAAI;IACXmC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGE,IAAI,IAAKlE,cAAc,CAACkE,IAAI;EACvC,CAAC,EACD;IACEzC,KAAK,EAAE,IAAI;IACXmC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGE,IAAI,iBACXlG,OAAA,CAACI,IAAI;MAAC+F,MAAM;MAAC/D,KAAK,EAAE;QAAEgE,KAAK,EAAE;MAAU,CAAE;MAAAzE,QAAA,EACtCK,cAAc,CAACkE,IAAI;IAAC;MAAAtE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB;EAEV,CAAC,EACD;IACE0B,KAAK,EAAE,IAAI;IACXoC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACK,CAAC,EAAEJ,MAAM,kBAChBjG,OAAA,CAACzB,KAAK;MAAC+H,IAAI,EAAC,OAAO;MAAA3E,QAAA,gBACjB3B,OAAA,CAACf,OAAO;QAACwE,KAAK,EAAC,cAAI;QAAA9B,QAAA,eACjB3B,OAAA,CAAC1B,MAAM;UACLyE,IAAI,EAAC,MAAM;UACXwD,IAAI,eAAEvG,OAAA,CAACZ,YAAY;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBuE,IAAI,EAAC,OAAO;UACZE,OAAO,EAAEA,CAAA,KAAMrD,cAAc,CAAC8C,MAAM;QAAE;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV/B,OAAA,CAACf,OAAO;QAACwE,KAAK,EAAC,cAAI;QAAA9B,QAAA,eACjB3B,OAAA,CAAC1B,MAAM;UACLyE,IAAI,EAAC,MAAM;UACXwD,IAAI,eAAEvG,OAAA,CAACV,cAAc;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBuE,IAAI,EAAC,OAAO;UACZG,MAAM;UACND,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC2C,MAAM,CAACtF,EAAE;QAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;;EAED;EACA,MAAM2E,KAAK,GAAG;IACZC,SAAS,EAAE,EAAAnG,UAAA,GAAAQ,GAAG,CAAC6C,KAAK,cAAArD,UAAA,uBAATA,UAAA,CAAWoG,MAAM,KAAI,CAAC;IACjCtB,SAAS,EAAEtE,GAAG,CAACsE,SAAS,IAAI,CAAC;IAC7BuB,OAAO,EAAE,EAAApG,WAAA,GAAAO,GAAG,CAAC6C,KAAK,cAAApD,WAAA,uBAATA,WAAA,CAAWmG,MAAM,IAAG,CAAC,GAAG5F,GAAG,CAACsE,SAAS,GAAGtE,GAAG,CAAC6C,KAAK,CAAC+C,MAAM,GAAG,CAAC;IACrEE,aAAa,EAAE,IAAIC,GAAG,EAAArG,WAAA,GAACM,GAAG,CAAC6C,KAAK,cAAAnD,WAAA,uBAATA,WAAA,CAAWiE,GAAG,CAACvB,IAAI,IAAI;MAC5C,MAAM4D,QAAQ,GAAGjG,SAAS,CAACU,IAAI,CAAC4C,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKyC,IAAI,CAACkB,UAAU,CAAC;MAC9D,OAAO0C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,QAAQ;IAC3B,CAAC,CAAC,CAACnD,MAAM,CAACoD,OAAO,CAAC,CAAC,CAACZ;EACtB,CAAC;EAED,oBACEtG,OAAA;IAAA2B,QAAA,gBAEE3B,OAAA;MAAKoC,KAAK,EAAE;QAAE+E,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAA3F,QAAA,gBAC3G3B,OAAA;QAAKoC,KAAK,EAAE;UAAEgF,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA1F,QAAA,gBACpD3B,OAAA,CAAC1B,MAAM;UACLiI,IAAI,eAAEvG,OAAA,CAACb,iBAAiB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5ByE,OAAO,EAAEA,CAAA,KAAM5F,QAAQ,CAAC,MAAM,CAAE;UAChCwB,KAAK,EAAE;YAAEmF,WAAW,EAAE;UAAO,CAAE;UAAA5F,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/B,OAAA,CAACG,KAAK;UAACoF,KAAK,EAAE,CAAE;UAACnD,KAAK,EAAE;YAAEoF,MAAM,EAAE;UAAE,CAAE;UAAA7F,QAAA,EACnCX,GAAG,CAACgE;QAAI;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/B,OAAA,CAACzB,KAAK;QAAAoD,QAAA,EACHT,QAAQ,gBACPlB,OAAA,CAAAE,SAAA;UAAAyB,QAAA,gBACE3B,OAAA,CAAC1B,MAAM;YAACkI,OAAO,EAAEA,CAAA,KAAMrF,WAAW,CAAC,KAAK,CAAE;YAAAQ,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtD/B,OAAA,CAAC1B,MAAM;YAACyE,IAAI,EAAC,SAAS;YAACwD,IAAI,eAAEvG,OAAA,CAACP,YAAY;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACyE,OAAO,EAAEd,UAAW;YAAA/D,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEH/B,OAAA,CAAC1B,MAAM;UAACiI,IAAI,eAAEvG,OAAA,CAACZ,YAAY;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACyE,OAAO,EAAEA,CAAA,KAAMrF,WAAW,CAAC,IAAI,CAAE;UAAAQ,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN/B,OAAA,CAAC7B,GAAG;MAACsJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrF,KAAK,EAAE;QAAE+E,YAAY,EAAE;MAAO,CAAE;MAAAxF,QAAA,gBACrD3B,OAAA,CAAC5B,GAAG;QAACsJ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAhG,QAAA,eAClB3B,OAAA,CAAC9B,IAAI;UAACuF,KAAK,EAAC,0BAAM;UAAA9B,QAAA,eAChB3B,OAAA,CAAC3B,YAAY;YAACuJ,MAAM,EAAE;cAAEF,EAAE,EAAE,CAAC;cAAEG,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,gBAC5C3B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,iBAAO;cAAArG,QAAA,EAC7BX,GAAG,CAAC8D;YAAI;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACpB/B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,iBAAO;cAAArG,QAAA,EAC7BX,GAAG,CAACgE;YAAI;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACpB/B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,cAAI;cAAArG,QAAA,EAC1BX,GAAG,CAACiH;YAAO;cAAArG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACpB/B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,cAAI;cAAArG,QAAA,eAC3B3B,OAAA,CAACvB,GAAG;gBAAC2H,KAAK,EAAEtD,YAAY,CAAC9B,GAAG,CAAC+B,IAAI,CAAE;gBAAApB,QAAA,EAChCqB,WAAW,CAAChC,GAAG,CAAC+B,IAAI;cAAC;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACpB/B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,cAAI;cAAArG,QAAA,eAC3B3B,OAAA,CAACvB,GAAG;gBAAC2H,KAAK,EAAE3D,cAAc,CAACzB,GAAG,CAAC0B,MAAM,CAAE;gBAAAf,QAAA,EACpCiB,aAAa,CAAC5B,GAAG,CAAC0B,MAAM;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACpB/B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAArG,QAAA,EAC3BX,GAAG,CAACkH;YAAS;cAAAtG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACpB/B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArG,QAAA,EAC5BX,GAAG,CAACmH;YAAU;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACpB/B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArG,QAAA,EAC5BX,GAAG,CAACoH;YAAU;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACpB/B,OAAA,CAAC3B,YAAY,CAAC0J,IAAI;cAACC,KAAK,EAAC,cAAI;cAACK,IAAI,EAAE,CAAE;cAAA1G,QAAA,EACnCX,GAAG,CAACsH,WAAW,IAAI;YAAG;cAAA1G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN/B,OAAA,CAAC5B,GAAG;QAACsJ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAhG,QAAA,eACjB3B,OAAA,CAAC9B,IAAI;UAACuF,KAAK,EAAC,0BAAM;UAAA9B,QAAA,eAChB3B,OAAA,CAAC7B,GAAG;YAACsJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAA9F,QAAA,gBACpB3B,OAAA,CAAC5B,GAAG;cAACiK,IAAI,EAAE,EAAG;cAAA1G,QAAA,eACZ3B,OAAA,CAACrB,SAAS;gBACR8E,KAAK,EAAC,0BAAM;gBACZxB,KAAK,EAAEyE,KAAK,CAACC,SAAU;gBACvB4B,MAAM,EAAC,QAAG;gBACVC,MAAM,eAAExI,OAAA,CAACR,gBAAgB;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/B,OAAA,CAAC5B,GAAG;cAACiK,IAAI,EAAE,EAAG;cAAA1G,QAAA,eACZ3B,OAAA,CAACrB,SAAS;gBACR8E,KAAK,EAAC,0BAAM;gBACZxB,KAAK,EAAEyE,KAAK,CAACI,aAAc;gBAC3ByB,MAAM,EAAC;cAAG;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/B,OAAA,CAAC5B,GAAG;cAACiK,IAAI,EAAE,EAAG;cAAA1G,QAAA,eACZ3B,OAAA,CAACrB,SAAS;gBACR8E,KAAK,EAAC,oBAAK;gBACXxB,KAAK,EAAEyE,KAAK,CAACpB,SAAU;gBACvBmD,SAAS,EAAGxG,KAAK,IAAKD,cAAc,CAACC,KAAK,CAAE;gBAC5CyG,UAAU,EAAE;kBAAEtC,KAAK,EAAE;gBAAU;cAAE;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/B,OAAA,CAAC5B,GAAG;cAACiK,IAAI,EAAE,EAAG;cAAA1G,QAAA,eACZ3B,OAAA,CAACrB,SAAS;gBACR8E,KAAK,EAAC,0BAAM;gBACZxB,KAAK,EAAEyE,KAAK,CAACG,OAAQ;gBACrB4B,SAAS,EAAGxG,KAAK,IAAKD,cAAc,CAACC,KAAK;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/B,OAAA,CAAC9B,IAAI;MACHuF,KAAK,EAAC,iBAAO;MACbkF,KAAK,eACH3I,OAAA,CAACzB,KAAK;QAAAoD,QAAA,gBACJ3B,OAAA,CAAC1B,MAAM;UACLiI,IAAI,eAAEvG,OAAA,CAACT,kBAAkB;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7ByE,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAACf,gBAAgB,CAAC;YAAEiE,KAAK,EAAE/C,GAAG,CAACL;UAAG,CAAC,CAAC,CAAE;UAAAgB,QAAA,EAC9D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/B,OAAA,CAAC1B,MAAM;UACLyE,IAAI,EAAC,SAAS;UACdwD,IAAI,eAAEvG,OAAA,CAACX,YAAY;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvByE,OAAO,EAAEvD,aAAc;UAAAtB,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAJ,QAAA,eAED3B,OAAA,CAACtB,KAAK;QACJiH,OAAO,EAAEA,OAAQ;QACjBiD,UAAU,EAAE5H,GAAG,CAAC6C,KAAK,IAAI,EAAG;QAC5BgF,MAAM,EAAC,IAAI;QACXC,UAAU,EAAE,KAAM;QAClBxC,IAAI,EAAC,OAAO;QACZyC,OAAO,EAAGC,QAAQ,IAAK;UACrB,MAAM1D,SAAS,GAAG0D,QAAQ,CAACC,MAAM,CAAC,CAACC,GAAG,EAAE9F,IAAI,KAAK8F,GAAG,GAAG9F,IAAI,CAACkC,SAAS,EAAE,CAAC,CAAC;UACzE,oBACEtF,OAAA,CAACtB,KAAK,CAACyK,OAAO,CAAChL,GAAG;YAAAwD,QAAA,gBAChB3B,OAAA,CAACtB,KAAK,CAACyK,OAAO,CAACC,IAAI;cAACC,KAAK,EAAE,CAAE;cAACC,OAAO,EAAE,CAAE;cAAA3H,QAAA,eACvC3B,OAAA,CAACI,IAAI;gBAAC+F,MAAM;gBAAAxE,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACrB/B,OAAA,CAACtB,KAAK,CAACyK,OAAO,CAACC,IAAI;cAACC,KAAK,EAAE,CAAE;cAAA1H,QAAA,eAC3B3B,OAAA,CAACI,IAAI;gBAAC+F,MAAM;gBAAC/D,KAAK,EAAE;kBAAEgE,KAAK,EAAE;gBAAU,CAAE;gBAAAzE,QAAA,EACtCK,cAAc,CAACsD,SAAS;cAAC;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACrB/B,OAAA,CAACtB,KAAK,CAACyK,OAAO,CAACC,IAAI;cAACC,KAAK,EAAE;YAAE;cAAAzH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAExB;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP/B,OAAA,CAACpB,KAAK;MACJ6E,KAAK,EAAC,0BAAM;MACZ8F,IAAI,EAAEnI,YAAa;MACnBuC,IAAI,EAAEM,aAAc;MACpBuF,QAAQ,EAAEA,CAAA,KAAMnI,eAAe,CAAC,KAAK,CAAE;MACvCyE,KAAK,EAAE,GAAI;MACX2D,cAAc;MAAA9H,QAAA,eAEd3B,OAAA,CAACnB,IAAI;QACHyC,IAAI,EAAEA,IAAK;QACXoI,MAAM,EAAC,UAAU;QAAA/H,QAAA,gBAEjB3B,OAAA,CAACnB,IAAI,CAACkJ,IAAI;UACR/C,IAAI,EAAC,YAAY;UACjBgD,KAAK,EAAC,0BAAM;UACZ2B,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5K,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA2C,QAAA,eAE9C3B,OAAA,CAAClB,MAAM;YACL+K,WAAW,EAAC,gCAAO;YACnBC,UAAU;YACVC,gBAAgB,EAAC,UAAU;YAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACvI,QAAQ,CAACwI,WAAW,CAAC,CAAC,CAACC,OAAO,CAACH,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,IAAI,CAC/D;YAAAxI,QAAA,EAEAZ,SAAS,CAAC4D,GAAG,CAACqC,QAAQ,iBACrBhH,OAAA,CAACK,MAAM;cAAmB4B,KAAK,EAAE+E,QAAQ,CAACrG,EAAG;cAAAgB,QAAA,GAC1CqF,QAAQ,CAAClC,IAAI,EAAC,KAAG,EAACkC,QAAQ,CAAChC,IAAI;YAAA,GADrBgC,QAAQ,CAACrG,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ/B,OAAA,CAACnB,IAAI,CAACkJ,IAAI;UACR/C,IAAI,EAAC,UAAU;UACfgD,KAAK,EAAC,cAAI;UACV2B,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5K,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA2C,QAAA,eAE9C3B,OAAA,CAACjB,WAAW;YACVsL,GAAG,EAAE,IAAK;YACVC,SAAS,EAAE,CAAE;YACblI,KAAK,EAAE;cAAE0D,KAAK,EAAE;YAAO,CAAE;YACzB+D,WAAW,EAAC;UAAO;YAAAjI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACxB,EAAA,CA7aID,SAAS;EAAA,QACExC,SAAS,EACPC,WAAW,EACXE,WAAW,EACfD,WAAW,EACNA,WAAW,EAKda,IAAI,CAAC0C,OAAO;AAAA;AAAAgJ,EAAA,GAVvBjK,SAAS;AA+af,eAAeA,SAAS;AAAC,IAAAiK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}