import { createSlice } from '@reduxjs/toolkit';

// 模拟报价单数据
const mockQuotations = [
  {
    id: 'QUO-2024-001',
    customerId: 1,
    customerName: '华为技术有限公司',
    title: '服务器设备采购报价',
    totalAmount: 500000,
    estimatedCost: 375000,
    estimatedProfit: 125000,
    profitMargin: 25,
    status: 'accepted',
    validUntil: '2024-02-15',
    createTime: '2024-01-10',
    salesperson: '李业务',
    items: [
      {
        id: 1,
        name: '服务器主机',
        specification: 'Dell PowerEdge R750',
        quantity: 10,
        unitPrice: 35000,
        totalPrice: 350000,
        unitCost: 28000,
        totalCost: 280000
      },
      {
        id: 2,
        name: '网络设备',
        specification: 'Cisco Catalyst 9300',
        quantity: 5,
        unitPrice: 15000,
        totalPrice: 75000,
        unitCost: 12000,
        totalCost: 60000
      },
      {
        id: 3,
        name: '安装调试费',
        specification: '现场安装调试服务',
        quantity: 1,
        unitPrice: 75000,
        totalPrice: 75000,
        unitCost: 35000,
        totalCost: 35000
      }
    ],
    notes: '包含一年免费维护服务',
    convertedToOrder: 'ORD-2024-001',
    bomId: 'BOM-001',
    bomName: '高性能服务器配置'
  },
  {
    id: 'QUO-2024-002',
    customerId: 2,
    customerName: '小米科技有限公司',
    title: '智能设备定制开发报价',
    totalAmount: 300000,
    estimatedCost: 210000,
    estimatedProfit: 90000,
    profitMargin: 30,
    status: 'pending',
    validUntil: '2024-03-01',
    createTime: '2024-01-15',
    salesperson: '李业务',
    items: [
      {
        id: 1,
        name: '硬件开发',
        specification: '定制PCB设计及生产',
        quantity: 1,
        unitPrice: 150000,
        totalPrice: 150000,
        unitCost: 100000,
        totalCost: 100000
      },
      {
        id: 2,
        name: '软件开发',
        specification: '嵌入式软件开发',
        quantity: 1,
        unitPrice: 100000,
        totalPrice: 100000,
        unitCost: 70000,
        totalCost: 70000
      },
      {
        id: 3,
        name: '测试验证',
        specification: '功能测试及认证',
        quantity: 1,
        unitPrice: 50000,
        totalPrice: 50000,
        unitCost: 40000,
        totalCost: 40000
      }
    ],
    notes: '开发周期8周，包含技术支持',
    convertedToOrder: null,
    bomId: 'BOM-002',
    bomName: '图形工作站配置'
  },
  {
    id: 'QUO-2024-003',
    customerId: 4,
    customerName: '腾讯科技有限公司',
    title: '云服务解决方案报价',
    totalAmount: 450000,
    estimatedCost: 315000,
    estimatedProfit: 135000,
    profitMargin: 30,
    status: 'draft',
    validUntil: '2024-02-28',
    createTime: '2024-01-25',
    salesperson: '李业务',
    items: [
      {
        id: 1,
        name: '云服务器',
        specification: '高性能云服务器配置',
        quantity: 20,
        unitPrice: 15000,
        totalPrice: 300000,
        unitCost: 10500,
        totalCost: 210000
      },
      {
        id: 2,
        name: '数据库服务',
        specification: '高可用数据库集群',
        quantity: 2,
        unitPrice: 50000,
        totalPrice: 100000,
        unitCost: 35000,
        totalCost: 70000
      },
      {
        id: 3,
        name: '技术支持',
        specification: '7x24小时技术支持',
        quantity: 1,
        unitPrice: 50000,
        totalPrice: 50000,
        unitCost: 35000,
        totalCost: 35000
      }
    ],
    notes: '首年服务费用，续费享受优惠',
    convertedToOrder: null,
    bomId: 'BOM-003',
    bomName: '基础办公电脑配置'
  }
];

const initialState = {
  quotations: mockQuotations,
  currentQuotation: null,
  loading: false,
  error: null,
  searchKeyword: '',
  filters: {
    status: '',
    salesperson: '',
    dateRange: []
  },
  pagination: {
    current: 1,
    pageSize: 10,
    total: mockQuotations.length
  }
};

const quotationSlice = createSlice({
  name: 'quotation',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setQuotations: (state, action) => {
      state.quotations = action.payload;
      state.pagination.total = action.payload.length;
    },
    addQuotation: (state, action) => {
      const newQuotation = {
        ...action.payload,
        id: `QUO-${new Date().getFullYear()}-${String(state.quotations.length + 1).padStart(3, '0')}`,
        createTime: new Date().toISOString().split('T')[0],
        status: 'draft',
        convertedToOrder: null
      };
      state.quotations.unshift(newQuotation);
      state.pagination.total = state.quotations.length;
    },
    updateQuotation: (state, action) => {
      const index = state.quotations.findIndex(q => q.id === action.payload.id);
      if (index !== -1) {
        state.quotations[index] = { ...state.quotations[index], ...action.payload };
      }
    },
    updateQuotationStatus: (state, action) => {
      const { id, status } = action.payload;
      const quotation = state.quotations.find(q => q.id === id);
      if (quotation) {
        quotation.status = status;
      }
    },
    convertToOrder: (state, action) => {
      const { quotationId, orderId } = action.payload;
      const quotation = state.quotations.find(q => q.id === quotationId);
      if (quotation) {
        quotation.status = 'accepted';
        quotation.convertedToOrder = orderId;
      }
    },
    deleteQuotation: (state, action) => {
      state.quotations = state.quotations.filter(q => q.id !== action.payload);
      state.pagination.total = state.quotations.length;
    },
    setCurrentQuotation: (state, action) => {
      state.currentQuotation = action.payload;
    },
    setSearchKeyword: (state, action) => {
      state.searchKeyword = action.payload;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    resetFilters: (state) => {
      state.searchKeyword = '';
      state.filters = {
        status: '',
        salesperson: '',
        dateRange: []
      };
      state.pagination.current = 1;
    }
  }
});

export const {
  setLoading,
  setError,
  clearError,
  setQuotations,
  addQuotation,
  updateQuotation,
  updateQuotationStatus,
  convertToOrder,
  deleteQuotation,
  setCurrentQuotation,
  setSearchKeyword,
  setFilters,
  setPagination,
  resetFilters
} = quotationSlice.actions;

// 选择器
export const selectQuotations = (state) => state.quotation.quotations;
export const selectCurrentQuotation = (state) => state.quotation.currentQuotation;
export const selectQuotationLoading = (state) => state.quotation.loading;
export const selectQuotationError = (state) => state.quotation.error;
export const selectQuotationFilters = (state) => state.quotation.filters;
export const selectQuotationPagination = (state) => state.quotation.pagination;

// 过滤后的报价单列表
export const selectFilteredQuotations = (state) => {
  const { quotations, searchKeyword, filters } = state.quotation;
  
  return quotations.filter(quotation => {
    // 搜索关键词过滤
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      if (!quotation.id.toLowerCase().includes(keyword) &&
          !quotation.customerName.toLowerCase().includes(keyword) &&
          !quotation.title.toLowerCase().includes(keyword)) {
        return false;
      }
    }
    
    // 状态过滤
    if (filters.status && quotation.status !== filters.status) {
      return false;
    }
    
    // 业务员过滤
    if (filters.salesperson && quotation.salesperson !== filters.salesperson) {
      return false;
    }
    
    return true;
  });
};

export default quotationSlice.reducer;
