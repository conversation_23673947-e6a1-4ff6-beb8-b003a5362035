import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  message,
  Tooltip,
  Badge
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  ExportOutlined,
  AppstoreOutlined,
  CopyOutlined,
  DatabaseOutlined
} from '@ant-design/icons';

import {
  selectFilteredBOMs,
  selectBOMLoading,
  selectBOMFilters,
  selectBOMPagination,
  setSearchKeyword,
  setFilters,
  setPagination,
  resetFilters,
  deleteBOM,
  addBOM
} from '../../store/slices/bomSlice';

const { Title, Text } = Typography;
const { Option } = Select;

const BOMList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const boms = useSelector(selectFilteredBOMs);
  const loading = useSelector(selectBOMLoading);
  const filters = useSelector(selectBOMFilters);
  const pagination = useSelector(selectBOMPagination);

  const [modalVisible, setModalVisible] = useState(false);
  const [editingBOM, setEditingBOM] = useState(null);
  const [form] = Form.useForm();

  const handleSearch = (value) => {
    dispatch(setSearchKeyword(value));
    dispatch(setPagination({ current: 1 }));
  };

  const handleFilterChange = (key, value) => {
    dispatch(setFilters({ [key]: value }));
    dispatch(setPagination({ current: 1 }));
  };

  const handleTableChange = (paginationConfig) => {
    dispatch(setPagination({
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    }));
  };

  const handleAdd = () => {
    setEditingBOM(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (bom) => {
    setEditingBOM(bom);
    form.setFieldsValue(bom);
    setModalVisible(true);
  };

  const handleDelete = (bom) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除BOM"${bom.name}"吗？`,
      onOk: () => {
        dispatch(deleteBOM(bom.id));
        message.success('删除成功');
      }
    });
  };

  const handleCopy = (bom) => {
    const newBOM = {
      ...bom,
      name: `${bom.name} - 副本`,
      code: `${bom.code}-COPY`,
      version: 'V1.0'
    };
    dispatch(addBOM(newBOM));
    message.success('复制成功');
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      if (editingBOM) {
        // 更新逻辑
        message.success('更新成功');
      } else {
        dispatch(addBOM(values));
        message.success('添加成功');
      }
      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getStatusColor = (status) => {
    const colors = {
      'active': 'green',
      'inactive': 'red',
      'draft': 'orange'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      'active': '启用',
      'inactive': '停用',
      'draft': '草稿'
    };
    return texts[status] || status;
  };

  const getTypeColor = (type) => {
    const colors = {
      'product': 'blue',
      'component': 'green',
      'material': 'orange'
    };
    return colors[type] || 'default';
  };

  const getTypeText = (type) => {
    const texts = {
      'product': '产品',
      'component': '组件',
      'material': '物料'
    };
    return texts[type] || type;
  };

  const columns = [
    {
      title: 'BOM编码',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      fixed: 'left',
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <Button 
            type="link" 
            style={{ padding: 0, height: 'auto', fontWeight: 'bold' }}
            onClick={() => navigate(`/bom/${record.id}`)}
          >
            {text}
          </Button>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.version}
          </Text>
        </Space>
      )
    },
    {
      title: 'BOM名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type) => (
        <Tag color={getTypeColor(type)}>
          {getTypeText(type)}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Badge 
          status={status === 'active' ? 'success' : status === 'draft' ? 'warning' : 'error'} 
          text={getStatusText(status)}
        />
      )
    },
    {
      title: '物料数量',
      key: 'itemCount',
      width: 100,
      render: (_, record) => (
        <Text>{record.items?.length || 0} 项</Text>
      )
    },
    {
      title: '总成本',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 120,
      render: (cost) => (
        <Text strong style={{ color: '#1890ff' }}>
          {formatCurrency(cost)}
        </Text>
      )
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 100
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 100
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 100
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => navigate(`/bom/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button 
              type="text" 
              icon={<CopyOutlined />} 
              size="small"
              onClick={() => handleCopy(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 计算统计数据
  const stats = {
    total: boms.length,
    active: boms.filter(b => b.status === 'active').length,
    products: boms.filter(b => b.type === 'product').length,
    totalCost: boms.reduce((sum, b) => sum + (b.totalCost || 0), 0),
    avgCost: boms.length > 0 ? boms.reduce((sum, b) => sum + (b.totalCost || 0), 0) / boms.length : 0
  };

  return (
    <div>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ margin: 0 }}>BOM管理</Title>
        <Space>
          <Button icon={<DatabaseOutlined />} onClick={() => navigate('/materials')}>
            物料管理
          </Button>
          <Button icon={<ReloadOutlined />}>刷新</Button>
          <Button icon={<ExportOutlined />}>导出</Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新建BOM
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="BOM总数" 
              value={stats.total} 
              suffix="个"
              prefix={<AppstoreOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="启用中" value={stats.active} suffix="个" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic title="产品BOM" value={stats.products} suffix="个" />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="总成本" 
              value={stats.totalCost} 
              formatter={(value) => formatCurrency(value)}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic 
              title="平均成本" 
              value={stats.avgCost} 
              formatter={(value) => formatCurrency(value)}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索BOM编码、名称、描述"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="active">启用</Option>
              <Option value="inactive">停用</Option>
              <Option value="draft">草稿</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="类型"
              allowClear
              style={{ width: '100%' }}
              value={filters.type}
              onChange={(value) => handleFilterChange('type', value)}
            >
              <Option value="product">产品</Option>
              <Option value="component">组件</Option>
              <Option value="material">物料</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4} lg={3}>
            <Button onClick={() => dispatch(resetFilters())}>重置</Button>
          </Col>
        </Row>
      </Card>

      {/* BOM列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={boms}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
          size="small"
        />
      </Card>

      {/* 新增/编辑BOM弹窗 */}
      <Modal
        title={editingBOM ? '编辑BOM' : '新建BOM'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'product',
            status: 'draft',
            version: 'V1.0'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="BOM编码"
                rules={[{ required: true, message: '请输入BOM编码' }]}
              >
                <Input placeholder="请输入BOM编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="BOM名称"
                rules={[{ required: true, message: '请输入BOM名称' }]}
              >
                <Input placeholder="请输入BOM名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="type"
                label="类型"
              >
                <Select>
                  <Option value="product">产品</Option>
                  <Option value="component">组件</Option>
                  <Option value="material">物料</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="version"
                label="版本"
              >
                <Input placeholder="V1.0" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label="状态"
              >
                <Select>
                  <Option value="draft">草稿</Option>
                  <Option value="active">启用</Option>
                  <Option value="inactive">停用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="请输入BOM描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BOMList;
