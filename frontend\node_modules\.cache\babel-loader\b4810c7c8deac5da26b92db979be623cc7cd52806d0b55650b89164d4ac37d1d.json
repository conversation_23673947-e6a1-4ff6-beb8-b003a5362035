{"ast": null, "code": "import * as vec2 from './vector.js';\nimport BoundingRect from './BoundingRect.js';\nimport { devicePixelRatio as dpr } from '../config.js';\nimport { fromLine, fromCubic, fromQuadratic, fromArc } from './bbox.js';\nimport { cubicLength, cubicSubdivide, quadraticLength, quadraticSubdivide } from './curve.js';\nvar CMD = {\n  M: 1,\n  L: 2,\n  C: 3,\n  Q: 4,\n  A: 5,\n  Z: 6,\n  R: 7\n};\nvar tmpOutX = [];\nvar tmpOutY = [];\nvar min = [];\nvar max = [];\nvar min2 = [];\nvar max2 = [];\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathCos = Math.cos;\nvar mathSin = Math.sin;\nvar mathAbs = Math.abs;\nvar PI = Math.PI;\nvar PI2 = PI * 2;\nvar hasTypedArray = typeof Float32Array !== 'undefined';\nvar tmpAngles = [];\nfunction modPI2(radian) {\n  var n = Math.round(radian / PI * 1e8) / 1e8;\n  return n % 2 * PI;\n}\nexport function normalizeArcAngles(angles, anticlockwise) {\n  var newStartAngle = modPI2(angles[0]);\n  if (newStartAngle < 0) {\n    newStartAngle += PI2;\n  }\n  var delta = newStartAngle - angles[0];\n  var newEndAngle = angles[1];\n  newEndAngle += delta;\n  if (!anticlockwise && newEndAngle - newStartAngle >= PI2) {\n    newEndAngle = newStartAngle + PI2;\n  } else if (anticlockwise && newStartAngle - newEndAngle >= PI2) {\n    newEndAngle = newStartAngle - PI2;\n  } else if (!anticlockwise && newStartAngle > newEndAngle) {\n    newEndAngle = newStartAngle + (PI2 - modPI2(newStartAngle - newEndAngle));\n  } else if (anticlockwise && newStartAngle < newEndAngle) {\n    newEndAngle = newStartAngle - (PI2 - modPI2(newEndAngle - newStartAngle));\n  }\n  angles[0] = newStartAngle;\n  angles[1] = newEndAngle;\n}\nvar PathProxy = function () {\n  function PathProxy(notSaveData) {\n    this.dpr = 1;\n    this._xi = 0;\n    this._yi = 0;\n    this._x0 = 0;\n    this._y0 = 0;\n    this._len = 0;\n    if (notSaveData) {\n      this._saveData = false;\n    }\n    if (this._saveData) {\n      this.data = [];\n    }\n  }\n  PathProxy.prototype.increaseVersion = function () {\n    this._version++;\n  };\n  PathProxy.prototype.getVersion = function () {\n    return this._version;\n  };\n  PathProxy.prototype.setScale = function (sx, sy, segmentIgnoreThreshold) {\n    segmentIgnoreThreshold = segmentIgnoreThreshold || 0;\n    if (segmentIgnoreThreshold > 0) {\n      this._ux = mathAbs(segmentIgnoreThreshold / dpr / sx) || 0;\n      this._uy = mathAbs(segmentIgnoreThreshold / dpr / sy) || 0;\n    }\n  };\n  PathProxy.prototype.setDPR = function (dpr) {\n    this.dpr = dpr;\n  };\n  PathProxy.prototype.setContext = function (ctx) {\n    this._ctx = ctx;\n  };\n  PathProxy.prototype.getContext = function () {\n    return this._ctx;\n  };\n  PathProxy.prototype.beginPath = function () {\n    this._ctx && this._ctx.beginPath();\n    this.reset();\n    return this;\n  };\n  PathProxy.prototype.reset = function () {\n    if (this._saveData) {\n      this._len = 0;\n    }\n    if (this._pathSegLen) {\n      this._pathSegLen = null;\n      this._pathLen = 0;\n    }\n    this._version++;\n  };\n  PathProxy.prototype.moveTo = function (x, y) {\n    this._drawPendingPt();\n    this.addData(CMD.M, x, y);\n    this._ctx && this._ctx.moveTo(x, y);\n    this._x0 = x;\n    this._y0 = y;\n    this._xi = x;\n    this._yi = y;\n    return this;\n  };\n  PathProxy.prototype.lineTo = function (x, y) {\n    var dx = mathAbs(x - this._xi);\n    var dy = mathAbs(y - this._yi);\n    var exceedUnit = dx > this._ux || dy > this._uy;\n    this.addData(CMD.L, x, y);\n    if (this._ctx && exceedUnit) {\n      this._ctx.lineTo(x, y);\n    }\n    if (exceedUnit) {\n      this._xi = x;\n      this._yi = y;\n      this._pendingPtDist = 0;\n    } else {\n      var d2 = dx * dx + dy * dy;\n      if (d2 > this._pendingPtDist) {\n        this._pendingPtX = x;\n        this._pendingPtY = y;\n        this._pendingPtDist = d2;\n      }\n    }\n    return this;\n  };\n  PathProxy.prototype.bezierCurveTo = function (x1, y1, x2, y2, x3, y3) {\n    this._drawPendingPt();\n    this.addData(CMD.C, x1, y1, x2, y2, x3, y3);\n    if (this._ctx) {\n      this._ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n    }\n    this._xi = x3;\n    this._yi = y3;\n    return this;\n  };\n  PathProxy.prototype.quadraticCurveTo = function (x1, y1, x2, y2) {\n    this._drawPendingPt();\n    this.addData(CMD.Q, x1, y1, x2, y2);\n    if (this._ctx) {\n      this._ctx.quadraticCurveTo(x1, y1, x2, y2);\n    }\n    this._xi = x2;\n    this._yi = y2;\n    return this;\n  };\n  PathProxy.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n    this._drawPendingPt();\n    tmpAngles[0] = startAngle;\n    tmpAngles[1] = endAngle;\n    normalizeArcAngles(tmpAngles, anticlockwise);\n    startAngle = tmpAngles[0];\n    endAngle = tmpAngles[1];\n    var delta = endAngle - startAngle;\n    this.addData(CMD.A, cx, cy, r, r, startAngle, delta, 0, anticlockwise ? 0 : 1);\n    this._ctx && this._ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n    this._xi = mathCos(endAngle) * r + cx;\n    this._yi = mathSin(endAngle) * r + cy;\n    return this;\n  };\n  PathProxy.prototype.arcTo = function (x1, y1, x2, y2, radius) {\n    this._drawPendingPt();\n    if (this._ctx) {\n      this._ctx.arcTo(x1, y1, x2, y2, radius);\n    }\n    return this;\n  };\n  PathProxy.prototype.rect = function (x, y, w, h) {\n    this._drawPendingPt();\n    this._ctx && this._ctx.rect(x, y, w, h);\n    this.addData(CMD.R, x, y, w, h);\n    return this;\n  };\n  PathProxy.prototype.closePath = function () {\n    this._drawPendingPt();\n    this.addData(CMD.Z);\n    var ctx = this._ctx;\n    var x0 = this._x0;\n    var y0 = this._y0;\n    if (ctx) {\n      ctx.closePath();\n    }\n    this._xi = x0;\n    this._yi = y0;\n    return this;\n  };\n  PathProxy.prototype.fill = function (ctx) {\n    ctx && ctx.fill();\n    this.toStatic();\n  };\n  PathProxy.prototype.stroke = function (ctx) {\n    ctx && ctx.stroke();\n    this.toStatic();\n  };\n  PathProxy.prototype.len = function () {\n    return this._len;\n  };\n  PathProxy.prototype.setData = function (data) {\n    var len = data.length;\n    if (!(this.data && this.data.length === len) && hasTypedArray) {\n      this.data = new Float32Array(len);\n    }\n    for (var i = 0; i < len; i++) {\n      this.data[i] = data[i];\n    }\n    this._len = len;\n  };\n  PathProxy.prototype.appendPath = function (path) {\n    if (!(path instanceof Array)) {\n      path = [path];\n    }\n    var len = path.length;\n    var appendSize = 0;\n    var offset = this._len;\n    for (var i = 0; i < len; i++) {\n      appendSize += path[i].len();\n    }\n    if (hasTypedArray && this.data instanceof Float32Array) {\n      this.data = new Float32Array(offset + appendSize);\n    }\n    for (var i = 0; i < len; i++) {\n      var appendPathData = path[i].data;\n      for (var k = 0; k < appendPathData.length; k++) {\n        this.data[offset++] = appendPathData[k];\n      }\n    }\n    this._len = offset;\n  };\n  PathProxy.prototype.addData = function (cmd, a, b, c, d, e, f, g, h) {\n    if (!this._saveData) {\n      return;\n    }\n    var data = this.data;\n    if (this._len + arguments.length > data.length) {\n      this._expandData();\n      data = this.data;\n    }\n    for (var i = 0; i < arguments.length; i++) {\n      data[this._len++] = arguments[i];\n    }\n  };\n  PathProxy.prototype._drawPendingPt = function () {\n    if (this._pendingPtDist > 0) {\n      this._ctx && this._ctx.lineTo(this._pendingPtX, this._pendingPtY);\n      this._pendingPtDist = 0;\n    }\n  };\n  PathProxy.prototype._expandData = function () {\n    if (!(this.data instanceof Array)) {\n      var newData = [];\n      for (var i = 0; i < this._len; i++) {\n        newData[i] = this.data[i];\n      }\n      this.data = newData;\n    }\n  };\n  PathProxy.prototype.toStatic = function () {\n    if (!this._saveData) {\n      return;\n    }\n    this._drawPendingPt();\n    var data = this.data;\n    if (data instanceof Array) {\n      data.length = this._len;\n      if (hasTypedArray && this._len > 11) {\n        this.data = new Float32Array(data);\n      }\n    }\n  };\n  PathProxy.prototype.getBoundingRect = function () {\n    min[0] = min[1] = min2[0] = min2[1] = Number.MAX_VALUE;\n    max[0] = max[1] = max2[0] = max2[1] = -Number.MAX_VALUE;\n    var data = this.data;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    var i;\n    for (i = 0; i < this._len;) {\n      var cmd = data[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = data[i];\n        yi = data[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      switch (cmd) {\n        case CMD.M:\n          xi = x0 = data[i++];\n          yi = y0 = data[i++];\n          min2[0] = x0;\n          min2[1] = y0;\n          max2[0] = x0;\n          max2[1] = y0;\n          break;\n        case CMD.L:\n          fromLine(xi, yi, data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.C:\n          fromCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.Q:\n          fromQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.A:\n          var cx = data[i++];\n          var cy = data[i++];\n          var rx = data[i++];\n          var ry = data[i++];\n          var startAngle = data[i++];\n          var endAngle = data[i++] + startAngle;\n          i += 1;\n          var anticlockwise = !data[i++];\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          fromArc(cx, cy, rx, ry, startAngle, endAngle, anticlockwise, min2, max2);\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          x0 = xi = data[i++];\n          y0 = yi = data[i++];\n          var width = data[i++];\n          var height = data[i++];\n          fromLine(x0, y0, x0 + width, y0 + height, min2, max2);\n          break;\n        case CMD.Z:\n          xi = x0;\n          yi = y0;\n          break;\n      }\n      vec2.min(min, min, min2);\n      vec2.max(max, max, max2);\n    }\n    if (i === 0) {\n      min[0] = min[1] = max[0] = max[1] = 0;\n    }\n    return new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n  };\n  PathProxy.prototype._calculateLength = function () {\n    var data = this.data;\n    var len = this._len;\n    var ux = this._ux;\n    var uy = this._uy;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    if (!this._pathSegLen) {\n      this._pathSegLen = [];\n    }\n    var pathSegLen = this._pathSegLen;\n    var pathTotalLen = 0;\n    var segCount = 0;\n    for (var i = 0; i < len;) {\n      var cmd = data[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = data[i];\n        yi = data[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      var l = -1;\n      switch (cmd) {\n        case CMD.M:\n          xi = x0 = data[i++];\n          yi = y0 = data[i++];\n          break;\n        case CMD.L:\n          {\n            var x2 = data[i++];\n            var y2 = data[i++];\n            var dx = x2 - xi;\n            var dy = y2 - yi;\n            if (mathAbs(dx) > ux || mathAbs(dy) > uy || i === len - 1) {\n              l = Math.sqrt(dx * dx + dy * dy);\n              xi = x2;\n              yi = y2;\n            }\n            break;\n          }\n        case CMD.C:\n          {\n            var x1 = data[i++];\n            var y1 = data[i++];\n            var x2 = data[i++];\n            var y2 = data[i++];\n            var x3 = data[i++];\n            var y3 = data[i++];\n            l = cubicLength(xi, yi, x1, y1, x2, y2, x3, y3, 10);\n            xi = x3;\n            yi = y3;\n            break;\n          }\n        case CMD.Q:\n          {\n            var x1 = data[i++];\n            var y1 = data[i++];\n            var x2 = data[i++];\n            var y2 = data[i++];\n            l = quadraticLength(xi, yi, x1, y1, x2, y2, 10);\n            xi = x2;\n            yi = y2;\n            break;\n          }\n        case CMD.A:\n          var cx = data[i++];\n          var cy = data[i++];\n          var rx = data[i++];\n          var ry = data[i++];\n          var startAngle = data[i++];\n          var delta = data[i++];\n          var endAngle = delta + startAngle;\n          i += 1;\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          l = mathMax(rx, ry) * mathMin(PI2, Math.abs(delta));\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          {\n            x0 = xi = data[i++];\n            y0 = yi = data[i++];\n            var width = data[i++];\n            var height = data[i++];\n            l = width * 2 + height * 2;\n            break;\n          }\n        case CMD.Z:\n          {\n            var dx = x0 - xi;\n            var dy = y0 - yi;\n            l = Math.sqrt(dx * dx + dy * dy);\n            xi = x0;\n            yi = y0;\n            break;\n          }\n      }\n      if (l >= 0) {\n        pathSegLen[segCount++] = l;\n        pathTotalLen += l;\n      }\n    }\n    this._pathLen = pathTotalLen;\n    return pathTotalLen;\n  };\n  PathProxy.prototype.rebuildPath = function (ctx, percent) {\n    var d = this.data;\n    var ux = this._ux;\n    var uy = this._uy;\n    var len = this._len;\n    var x0;\n    var y0;\n    var xi;\n    var yi;\n    var x;\n    var y;\n    var drawPart = percent < 1;\n    var pathSegLen;\n    var pathTotalLen;\n    var accumLength = 0;\n    var segCount = 0;\n    var displayedLength;\n    var pendingPtDist = 0;\n    var pendingPtX;\n    var pendingPtY;\n    if (drawPart) {\n      if (!this._pathSegLen) {\n        this._calculateLength();\n      }\n      pathSegLen = this._pathSegLen;\n      pathTotalLen = this._pathLen;\n      displayedLength = percent * pathTotalLen;\n      if (!displayedLength) {\n        return;\n      }\n    }\n    lo: for (var i = 0; i < len;) {\n      var cmd = d[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = d[i];\n        yi = d[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      if (cmd !== CMD.L && pendingPtDist > 0) {\n        ctx.lineTo(pendingPtX, pendingPtY);\n        pendingPtDist = 0;\n      }\n      switch (cmd) {\n        case CMD.M:\n          x0 = xi = d[i++];\n          y0 = yi = d[i++];\n          ctx.moveTo(xi, yi);\n          break;\n        case CMD.L:\n          {\n            x = d[i++];\n            y = d[i++];\n            var dx = mathAbs(x - xi);\n            var dy = mathAbs(y - yi);\n            if (dx > ux || dy > uy) {\n              if (drawPart) {\n                var l = pathSegLen[segCount++];\n                if (accumLength + l > displayedLength) {\n                  var t = (displayedLength - accumLength) / l;\n                  ctx.lineTo(xi * (1 - t) + x * t, yi * (1 - t) + y * t);\n                  break lo;\n                }\n                accumLength += l;\n              }\n              ctx.lineTo(x, y);\n              xi = x;\n              yi = y;\n              pendingPtDist = 0;\n            } else {\n              var d2 = dx * dx + dy * dy;\n              if (d2 > pendingPtDist) {\n                pendingPtX = x;\n                pendingPtY = y;\n                pendingPtDist = d2;\n              }\n            }\n            break;\n          }\n        case CMD.C:\n          {\n            var x1 = d[i++];\n            var y1 = d[i++];\n            var x2 = d[i++];\n            var y2 = d[i++];\n            var x3 = d[i++];\n            var y3 = d[i++];\n            if (drawPart) {\n              var l = pathSegLen[segCount++];\n              if (accumLength + l > displayedLength) {\n                var t = (displayedLength - accumLength) / l;\n                cubicSubdivide(xi, x1, x2, x3, t, tmpOutX);\n                cubicSubdivide(yi, y1, y2, y3, t, tmpOutY);\n                ctx.bezierCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2], tmpOutX[3], tmpOutY[3]);\n                break lo;\n              }\n              accumLength += l;\n            }\n            ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n            xi = x3;\n            yi = y3;\n            break;\n          }\n        case CMD.Q:\n          {\n            var x1 = d[i++];\n            var y1 = d[i++];\n            var x2 = d[i++];\n            var y2 = d[i++];\n            if (drawPart) {\n              var l = pathSegLen[segCount++];\n              if (accumLength + l > displayedLength) {\n                var t = (displayedLength - accumLength) / l;\n                quadraticSubdivide(xi, x1, x2, t, tmpOutX);\n                quadraticSubdivide(yi, y1, y2, t, tmpOutY);\n                ctx.quadraticCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2]);\n                break lo;\n              }\n              accumLength += l;\n            }\n            ctx.quadraticCurveTo(x1, y1, x2, y2);\n            xi = x2;\n            yi = y2;\n            break;\n          }\n        case CMD.A:\n          var cx = d[i++];\n          var cy = d[i++];\n          var rx = d[i++];\n          var ry = d[i++];\n          var startAngle = d[i++];\n          var delta = d[i++];\n          var psi = d[i++];\n          var anticlockwise = !d[i++];\n          var r = rx > ry ? rx : ry;\n          var isEllipse = mathAbs(rx - ry) > 1e-3;\n          var endAngle = startAngle + delta;\n          var breakBuild = false;\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              endAngle = startAngle + delta * (displayedLength - accumLength) / l;\n              breakBuild = true;\n            }\n            accumLength += l;\n          }\n          if (isEllipse && ctx.ellipse) {\n            ctx.ellipse(cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise);\n          } else {\n            ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n          }\n          if (breakBuild) {\n            break lo;\n          }\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          x0 = xi = d[i];\n          y0 = yi = d[i + 1];\n          x = d[i++];\n          y = d[i++];\n          var width = d[i++];\n          var height = d[i++];\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              var d_1 = displayedLength - accumLength;\n              ctx.moveTo(x, y);\n              ctx.lineTo(x + mathMin(d_1, width), y);\n              d_1 -= width;\n              if (d_1 > 0) {\n                ctx.lineTo(x + width, y + mathMin(d_1, height));\n              }\n              d_1 -= height;\n              if (d_1 > 0) {\n                ctx.lineTo(x + mathMax(width - d_1, 0), y + height);\n              }\n              d_1 -= width;\n              if (d_1 > 0) {\n                ctx.lineTo(x, y + mathMax(height - d_1, 0));\n              }\n              break lo;\n            }\n            accumLength += l;\n          }\n          ctx.rect(x, y, width, height);\n          break;\n        case CMD.Z:\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              var t = (displayedLength - accumLength) / l;\n              ctx.lineTo(xi * (1 - t) + x0 * t, yi * (1 - t) + y0 * t);\n              break lo;\n            }\n            accumLength += l;\n          }\n          ctx.closePath();\n          xi = x0;\n          yi = y0;\n      }\n    }\n  };\n  PathProxy.prototype.clone = function () {\n    var newProxy = new PathProxy();\n    var data = this.data;\n    newProxy.data = data.slice ? data.slice() : Array.prototype.slice.call(data);\n    newProxy._len = this._len;\n    return newProxy;\n  };\n  PathProxy.CMD = CMD;\n  PathProxy.initDefaultProps = function () {\n    var proto = PathProxy.prototype;\n    proto._saveData = true;\n    proto._ux = 0;\n    proto._uy = 0;\n    proto._pendingPtDist = 0;\n    proto._version = 0;\n  }();\n  return PathProxy;\n}();\nexport default PathProxy;", "map": {"version": 3, "names": ["vec2", "BoundingRect", "devicePixelRatio", "dpr", "fromLine", "fromCubic", "fromQuadratic", "fromArc", "cubicLength", "cubicSubdivide", "quadraticLength", "quadraticSubdivide", "CMD", "M", "L", "C", "Q", "A", "Z", "R", "tmpOutX", "tmpOutY", "min", "max", "min2", "max2", "mathMin", "Math", "mathMax", "mathCos", "cos", "mathSin", "sin", "mathAbs", "abs", "PI", "PI2", "hasTypedArray", "Float32Array", "tmpAngles", "modPI2", "radian", "n", "round", "normalizeArcAngles", "angles", "anticlockwise", "newStartAngle", "delta", "newEndAngle", "PathProxy", "notSaveData", "_xi", "_yi", "_x0", "_y0", "_len", "_saveData", "data", "prototype", "increaseVersion", "_version", "getVersion", "setScale", "sx", "sy", "segmentIgnoreThreshold", "_ux", "_uy", "setDPR", "setContext", "ctx", "_ctx", "getContext", "beginPath", "reset", "_pathSegLen", "_pathLen", "moveTo", "x", "y", "_drawPendingPt", "addData", "lineTo", "dx", "dy", "exceedUnit", "_pendingPtDist", "d2", "_pendingPtX", "_pendingPtY", "bezierCurveTo", "x1", "y1", "x2", "y2", "x3", "y3", "quadraticCurveTo", "arc", "cx", "cy", "r", "startAngle", "endAngle", "arcTo", "radius", "rect", "w", "h", "closePath", "x0", "y0", "fill", "to<PERSON><PERSON><PERSON>", "stroke", "len", "setData", "length", "i", "appendPath", "path", "Array", "appendSize", "offset", "appendPathData", "k", "cmd", "a", "b", "c", "d", "e", "f", "g", "arguments", "_expandData", "newData", "getBoundingRect", "Number", "MAX_VALUE", "xi", "yi", "<PERSON><PERSON><PERSON><PERSON>", "rx", "ry", "width", "height", "_calculateLength", "ux", "uy", "pathSegLen", "pathTotalLen", "segCount", "l", "sqrt", "rebuildPath", "percent", "drawPart", "accumLength", "displayed<PERSON><PERSON><PERSON>", "pendingPtDist", "pendingPtX", "pendingPtY", "lo", "t", "psi", "isEllipse", "breakBuild", "ellipse", "d_1", "clone", "newProxy", "slice", "call", "initDefaultProps", "proto"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/zrender/lib/core/PathProxy.js"], "sourcesContent": ["import * as vec2 from './vector.js';\nimport BoundingRect from './BoundingRect.js';\nimport { devicePixelRatio as dpr } from '../config.js';\nimport { fromLine, fromCubic, fromQuadratic, fromArc } from './bbox.js';\nimport { cubicLength, cubicSubdivide, quadraticLength, quadraticSubdivide } from './curve.js';\nvar CMD = {\n    M: 1,\n    L: 2,\n    C: 3,\n    Q: 4,\n    A: 5,\n    Z: 6,\n    R: 7\n};\nvar tmpOutX = [];\nvar tmpOutY = [];\nvar min = [];\nvar max = [];\nvar min2 = [];\nvar max2 = [];\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathCos = Math.cos;\nvar mathSin = Math.sin;\nvar mathAbs = Math.abs;\nvar PI = Math.PI;\nvar PI2 = PI * 2;\nvar hasTypedArray = typeof Float32Array !== 'undefined';\nvar tmpAngles = [];\nfunction modPI2(radian) {\n    var n = Math.round(radian / PI * 1e8) / 1e8;\n    return (n % 2) * PI;\n}\nexport function normalizeArcAngles(angles, anticlockwise) {\n    var newStartAngle = modPI2(angles[0]);\n    if (newStartAngle < 0) {\n        newStartAngle += PI2;\n    }\n    var delta = newStartAngle - angles[0];\n    var newEndAngle = angles[1];\n    newEndAngle += delta;\n    if (!anticlockwise && newEndAngle - newStartAngle >= PI2) {\n        newEndAngle = newStartAngle + PI2;\n    }\n    else if (anticlockwise && newStartAngle - newEndAngle >= PI2) {\n        newEndAngle = newStartAngle - PI2;\n    }\n    else if (!anticlockwise && newStartAngle > newEndAngle) {\n        newEndAngle = newStartAngle + (PI2 - modPI2(newStartAngle - newEndAngle));\n    }\n    else if (anticlockwise && newStartAngle < newEndAngle) {\n        newEndAngle = newStartAngle - (PI2 - modPI2(newEndAngle - newStartAngle));\n    }\n    angles[0] = newStartAngle;\n    angles[1] = newEndAngle;\n}\nvar PathProxy = (function () {\n    function PathProxy(notSaveData) {\n        this.dpr = 1;\n        this._xi = 0;\n        this._yi = 0;\n        this._x0 = 0;\n        this._y0 = 0;\n        this._len = 0;\n        if (notSaveData) {\n            this._saveData = false;\n        }\n        if (this._saveData) {\n            this.data = [];\n        }\n    }\n    PathProxy.prototype.increaseVersion = function () {\n        this._version++;\n    };\n    PathProxy.prototype.getVersion = function () {\n        return this._version;\n    };\n    PathProxy.prototype.setScale = function (sx, sy, segmentIgnoreThreshold) {\n        segmentIgnoreThreshold = segmentIgnoreThreshold || 0;\n        if (segmentIgnoreThreshold > 0) {\n            this._ux = mathAbs(segmentIgnoreThreshold / dpr / sx) || 0;\n            this._uy = mathAbs(segmentIgnoreThreshold / dpr / sy) || 0;\n        }\n    };\n    PathProxy.prototype.setDPR = function (dpr) {\n        this.dpr = dpr;\n    };\n    PathProxy.prototype.setContext = function (ctx) {\n        this._ctx = ctx;\n    };\n    PathProxy.prototype.getContext = function () {\n        return this._ctx;\n    };\n    PathProxy.prototype.beginPath = function () {\n        this._ctx && this._ctx.beginPath();\n        this.reset();\n        return this;\n    };\n    PathProxy.prototype.reset = function () {\n        if (this._saveData) {\n            this._len = 0;\n        }\n        if (this._pathSegLen) {\n            this._pathSegLen = null;\n            this._pathLen = 0;\n        }\n        this._version++;\n    };\n    PathProxy.prototype.moveTo = function (x, y) {\n        this._drawPendingPt();\n        this.addData(CMD.M, x, y);\n        this._ctx && this._ctx.moveTo(x, y);\n        this._x0 = x;\n        this._y0 = y;\n        this._xi = x;\n        this._yi = y;\n        return this;\n    };\n    PathProxy.prototype.lineTo = function (x, y) {\n        var dx = mathAbs(x - this._xi);\n        var dy = mathAbs(y - this._yi);\n        var exceedUnit = dx > this._ux || dy > this._uy;\n        this.addData(CMD.L, x, y);\n        if (this._ctx && exceedUnit) {\n            this._ctx.lineTo(x, y);\n        }\n        if (exceedUnit) {\n            this._xi = x;\n            this._yi = y;\n            this._pendingPtDist = 0;\n        }\n        else {\n            var d2 = dx * dx + dy * dy;\n            if (d2 > this._pendingPtDist) {\n                this._pendingPtX = x;\n                this._pendingPtY = y;\n                this._pendingPtDist = d2;\n            }\n        }\n        return this;\n    };\n    PathProxy.prototype.bezierCurveTo = function (x1, y1, x2, y2, x3, y3) {\n        this._drawPendingPt();\n        this.addData(CMD.C, x1, y1, x2, y2, x3, y3);\n        if (this._ctx) {\n            this._ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n        }\n        this._xi = x3;\n        this._yi = y3;\n        return this;\n    };\n    PathProxy.prototype.quadraticCurveTo = function (x1, y1, x2, y2) {\n        this._drawPendingPt();\n        this.addData(CMD.Q, x1, y1, x2, y2);\n        if (this._ctx) {\n            this._ctx.quadraticCurveTo(x1, y1, x2, y2);\n        }\n        this._xi = x2;\n        this._yi = y2;\n        return this;\n    };\n    PathProxy.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n        this._drawPendingPt();\n        tmpAngles[0] = startAngle;\n        tmpAngles[1] = endAngle;\n        normalizeArcAngles(tmpAngles, anticlockwise);\n        startAngle = tmpAngles[0];\n        endAngle = tmpAngles[1];\n        var delta = endAngle - startAngle;\n        this.addData(CMD.A, cx, cy, r, r, startAngle, delta, 0, anticlockwise ? 0 : 1);\n        this._ctx && this._ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n        this._xi = mathCos(endAngle) * r + cx;\n        this._yi = mathSin(endAngle) * r + cy;\n        return this;\n    };\n    PathProxy.prototype.arcTo = function (x1, y1, x2, y2, radius) {\n        this._drawPendingPt();\n        if (this._ctx) {\n            this._ctx.arcTo(x1, y1, x2, y2, radius);\n        }\n        return this;\n    };\n    PathProxy.prototype.rect = function (x, y, w, h) {\n        this._drawPendingPt();\n        this._ctx && this._ctx.rect(x, y, w, h);\n        this.addData(CMD.R, x, y, w, h);\n        return this;\n    };\n    PathProxy.prototype.closePath = function () {\n        this._drawPendingPt();\n        this.addData(CMD.Z);\n        var ctx = this._ctx;\n        var x0 = this._x0;\n        var y0 = this._y0;\n        if (ctx) {\n            ctx.closePath();\n        }\n        this._xi = x0;\n        this._yi = y0;\n        return this;\n    };\n    PathProxy.prototype.fill = function (ctx) {\n        ctx && ctx.fill();\n        this.toStatic();\n    };\n    PathProxy.prototype.stroke = function (ctx) {\n        ctx && ctx.stroke();\n        this.toStatic();\n    };\n    PathProxy.prototype.len = function () {\n        return this._len;\n    };\n    PathProxy.prototype.setData = function (data) {\n        var len = data.length;\n        if (!(this.data && this.data.length === len) && hasTypedArray) {\n            this.data = new Float32Array(len);\n        }\n        for (var i = 0; i < len; i++) {\n            this.data[i] = data[i];\n        }\n        this._len = len;\n    };\n    PathProxy.prototype.appendPath = function (path) {\n        if (!(path instanceof Array)) {\n            path = [path];\n        }\n        var len = path.length;\n        var appendSize = 0;\n        var offset = this._len;\n        for (var i = 0; i < len; i++) {\n            appendSize += path[i].len();\n        }\n        if (hasTypedArray && (this.data instanceof Float32Array)) {\n            this.data = new Float32Array(offset + appendSize);\n        }\n        for (var i = 0; i < len; i++) {\n            var appendPathData = path[i].data;\n            for (var k = 0; k < appendPathData.length; k++) {\n                this.data[offset++] = appendPathData[k];\n            }\n        }\n        this._len = offset;\n    };\n    PathProxy.prototype.addData = function (cmd, a, b, c, d, e, f, g, h) {\n        if (!this._saveData) {\n            return;\n        }\n        var data = this.data;\n        if (this._len + arguments.length > data.length) {\n            this._expandData();\n            data = this.data;\n        }\n        for (var i = 0; i < arguments.length; i++) {\n            data[this._len++] = arguments[i];\n        }\n    };\n    PathProxy.prototype._drawPendingPt = function () {\n        if (this._pendingPtDist > 0) {\n            this._ctx && this._ctx.lineTo(this._pendingPtX, this._pendingPtY);\n            this._pendingPtDist = 0;\n        }\n    };\n    PathProxy.prototype._expandData = function () {\n        if (!(this.data instanceof Array)) {\n            var newData = [];\n            for (var i = 0; i < this._len; i++) {\n                newData[i] = this.data[i];\n            }\n            this.data = newData;\n        }\n    };\n    PathProxy.prototype.toStatic = function () {\n        if (!this._saveData) {\n            return;\n        }\n        this._drawPendingPt();\n        var data = this.data;\n        if (data instanceof Array) {\n            data.length = this._len;\n            if (hasTypedArray && this._len > 11) {\n                this.data = new Float32Array(data);\n            }\n        }\n    };\n    PathProxy.prototype.getBoundingRect = function () {\n        min[0] = min[1] = min2[0] = min2[1] = Number.MAX_VALUE;\n        max[0] = max[1] = max2[0] = max2[1] = -Number.MAX_VALUE;\n        var data = this.data;\n        var xi = 0;\n        var yi = 0;\n        var x0 = 0;\n        var y0 = 0;\n        var i;\n        for (i = 0; i < this._len;) {\n            var cmd = data[i++];\n            var isFirst = i === 1;\n            if (isFirst) {\n                xi = data[i];\n                yi = data[i + 1];\n                x0 = xi;\n                y0 = yi;\n            }\n            switch (cmd) {\n                case CMD.M:\n                    xi = x0 = data[i++];\n                    yi = y0 = data[i++];\n                    min2[0] = x0;\n                    min2[1] = y0;\n                    max2[0] = x0;\n                    max2[1] = y0;\n                    break;\n                case CMD.L:\n                    fromLine(xi, yi, data[i], data[i + 1], min2, max2);\n                    xi = data[i++];\n                    yi = data[i++];\n                    break;\n                case CMD.C:\n                    fromCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], min2, max2);\n                    xi = data[i++];\n                    yi = data[i++];\n                    break;\n                case CMD.Q:\n                    fromQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], min2, max2);\n                    xi = data[i++];\n                    yi = data[i++];\n                    break;\n                case CMD.A:\n                    var cx = data[i++];\n                    var cy = data[i++];\n                    var rx = data[i++];\n                    var ry = data[i++];\n                    var startAngle = data[i++];\n                    var endAngle = data[i++] + startAngle;\n                    i += 1;\n                    var anticlockwise = !data[i++];\n                    if (isFirst) {\n                        x0 = mathCos(startAngle) * rx + cx;\n                        y0 = mathSin(startAngle) * ry + cy;\n                    }\n                    fromArc(cx, cy, rx, ry, startAngle, endAngle, anticlockwise, min2, max2);\n                    xi = mathCos(endAngle) * rx + cx;\n                    yi = mathSin(endAngle) * ry + cy;\n                    break;\n                case CMD.R:\n                    x0 = xi = data[i++];\n                    y0 = yi = data[i++];\n                    var width = data[i++];\n                    var height = data[i++];\n                    fromLine(x0, y0, x0 + width, y0 + height, min2, max2);\n                    break;\n                case CMD.Z:\n                    xi = x0;\n                    yi = y0;\n                    break;\n            }\n            vec2.min(min, min, min2);\n            vec2.max(max, max, max2);\n        }\n        if (i === 0) {\n            min[0] = min[1] = max[0] = max[1] = 0;\n        }\n        return new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    };\n    PathProxy.prototype._calculateLength = function () {\n        var data = this.data;\n        var len = this._len;\n        var ux = this._ux;\n        var uy = this._uy;\n        var xi = 0;\n        var yi = 0;\n        var x0 = 0;\n        var y0 = 0;\n        if (!this._pathSegLen) {\n            this._pathSegLen = [];\n        }\n        var pathSegLen = this._pathSegLen;\n        var pathTotalLen = 0;\n        var segCount = 0;\n        for (var i = 0; i < len;) {\n            var cmd = data[i++];\n            var isFirst = i === 1;\n            if (isFirst) {\n                xi = data[i];\n                yi = data[i + 1];\n                x0 = xi;\n                y0 = yi;\n            }\n            var l = -1;\n            switch (cmd) {\n                case CMD.M:\n                    xi = x0 = data[i++];\n                    yi = y0 = data[i++];\n                    break;\n                case CMD.L: {\n                    var x2 = data[i++];\n                    var y2 = data[i++];\n                    var dx = x2 - xi;\n                    var dy = y2 - yi;\n                    if (mathAbs(dx) > ux || mathAbs(dy) > uy || i === len - 1) {\n                        l = Math.sqrt(dx * dx + dy * dy);\n                        xi = x2;\n                        yi = y2;\n                    }\n                    break;\n                }\n                case CMD.C: {\n                    var x1 = data[i++];\n                    var y1 = data[i++];\n                    var x2 = data[i++];\n                    var y2 = data[i++];\n                    var x3 = data[i++];\n                    var y3 = data[i++];\n                    l = cubicLength(xi, yi, x1, y1, x2, y2, x3, y3, 10);\n                    xi = x3;\n                    yi = y3;\n                    break;\n                }\n                case CMD.Q: {\n                    var x1 = data[i++];\n                    var y1 = data[i++];\n                    var x2 = data[i++];\n                    var y2 = data[i++];\n                    l = quadraticLength(xi, yi, x1, y1, x2, y2, 10);\n                    xi = x2;\n                    yi = y2;\n                    break;\n                }\n                case CMD.A:\n                    var cx = data[i++];\n                    var cy = data[i++];\n                    var rx = data[i++];\n                    var ry = data[i++];\n                    var startAngle = data[i++];\n                    var delta = data[i++];\n                    var endAngle = delta + startAngle;\n                    i += 1;\n                    if (isFirst) {\n                        x0 = mathCos(startAngle) * rx + cx;\n                        y0 = mathSin(startAngle) * ry + cy;\n                    }\n                    l = mathMax(rx, ry) * mathMin(PI2, Math.abs(delta));\n                    xi = mathCos(endAngle) * rx + cx;\n                    yi = mathSin(endAngle) * ry + cy;\n                    break;\n                case CMD.R: {\n                    x0 = xi = data[i++];\n                    y0 = yi = data[i++];\n                    var width = data[i++];\n                    var height = data[i++];\n                    l = width * 2 + height * 2;\n                    break;\n                }\n                case CMD.Z: {\n                    var dx = x0 - xi;\n                    var dy = y0 - yi;\n                    l = Math.sqrt(dx * dx + dy * dy);\n                    xi = x0;\n                    yi = y0;\n                    break;\n                }\n            }\n            if (l >= 0) {\n                pathSegLen[segCount++] = l;\n                pathTotalLen += l;\n            }\n        }\n        this._pathLen = pathTotalLen;\n        return pathTotalLen;\n    };\n    PathProxy.prototype.rebuildPath = function (ctx, percent) {\n        var d = this.data;\n        var ux = this._ux;\n        var uy = this._uy;\n        var len = this._len;\n        var x0;\n        var y0;\n        var xi;\n        var yi;\n        var x;\n        var y;\n        var drawPart = percent < 1;\n        var pathSegLen;\n        var pathTotalLen;\n        var accumLength = 0;\n        var segCount = 0;\n        var displayedLength;\n        var pendingPtDist = 0;\n        var pendingPtX;\n        var pendingPtY;\n        if (drawPart) {\n            if (!this._pathSegLen) {\n                this._calculateLength();\n            }\n            pathSegLen = this._pathSegLen;\n            pathTotalLen = this._pathLen;\n            displayedLength = percent * pathTotalLen;\n            if (!displayedLength) {\n                return;\n            }\n        }\n        lo: for (var i = 0; i < len;) {\n            var cmd = d[i++];\n            var isFirst = i === 1;\n            if (isFirst) {\n                xi = d[i];\n                yi = d[i + 1];\n                x0 = xi;\n                y0 = yi;\n            }\n            if (cmd !== CMD.L && pendingPtDist > 0) {\n                ctx.lineTo(pendingPtX, pendingPtY);\n                pendingPtDist = 0;\n            }\n            switch (cmd) {\n                case CMD.M:\n                    x0 = xi = d[i++];\n                    y0 = yi = d[i++];\n                    ctx.moveTo(xi, yi);\n                    break;\n                case CMD.L: {\n                    x = d[i++];\n                    y = d[i++];\n                    var dx = mathAbs(x - xi);\n                    var dy = mathAbs(y - yi);\n                    if (dx > ux || dy > uy) {\n                        if (drawPart) {\n                            var l = pathSegLen[segCount++];\n                            if (accumLength + l > displayedLength) {\n                                var t = (displayedLength - accumLength) / l;\n                                ctx.lineTo(xi * (1 - t) + x * t, yi * (1 - t) + y * t);\n                                break lo;\n                            }\n                            accumLength += l;\n                        }\n                        ctx.lineTo(x, y);\n                        xi = x;\n                        yi = y;\n                        pendingPtDist = 0;\n                    }\n                    else {\n                        var d2 = dx * dx + dy * dy;\n                        if (d2 > pendingPtDist) {\n                            pendingPtX = x;\n                            pendingPtY = y;\n                            pendingPtDist = d2;\n                        }\n                    }\n                    break;\n                }\n                case CMD.C: {\n                    var x1 = d[i++];\n                    var y1 = d[i++];\n                    var x2 = d[i++];\n                    var y2 = d[i++];\n                    var x3 = d[i++];\n                    var y3 = d[i++];\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            var t = (displayedLength - accumLength) / l;\n                            cubicSubdivide(xi, x1, x2, x3, t, tmpOutX);\n                            cubicSubdivide(yi, y1, y2, y3, t, tmpOutY);\n                            ctx.bezierCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2], tmpOutX[3], tmpOutY[3]);\n                            break lo;\n                        }\n                        accumLength += l;\n                    }\n                    ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n                    xi = x3;\n                    yi = y3;\n                    break;\n                }\n                case CMD.Q: {\n                    var x1 = d[i++];\n                    var y1 = d[i++];\n                    var x2 = d[i++];\n                    var y2 = d[i++];\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            var t = (displayedLength - accumLength) / l;\n                            quadraticSubdivide(xi, x1, x2, t, tmpOutX);\n                            quadraticSubdivide(yi, y1, y2, t, tmpOutY);\n                            ctx.quadraticCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2]);\n                            break lo;\n                        }\n                        accumLength += l;\n                    }\n                    ctx.quadraticCurveTo(x1, y1, x2, y2);\n                    xi = x2;\n                    yi = y2;\n                    break;\n                }\n                case CMD.A:\n                    var cx = d[i++];\n                    var cy = d[i++];\n                    var rx = d[i++];\n                    var ry = d[i++];\n                    var startAngle = d[i++];\n                    var delta = d[i++];\n                    var psi = d[i++];\n                    var anticlockwise = !d[i++];\n                    var r = (rx > ry) ? rx : ry;\n                    var isEllipse = mathAbs(rx - ry) > 1e-3;\n                    var endAngle = startAngle + delta;\n                    var breakBuild = false;\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            endAngle = startAngle + delta * (displayedLength - accumLength) / l;\n                            breakBuild = true;\n                        }\n                        accumLength += l;\n                    }\n                    if (isEllipse && ctx.ellipse) {\n                        ctx.ellipse(cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise);\n                    }\n                    else {\n                        ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n                    }\n                    if (breakBuild) {\n                        break lo;\n                    }\n                    if (isFirst) {\n                        x0 = mathCos(startAngle) * rx + cx;\n                        y0 = mathSin(startAngle) * ry + cy;\n                    }\n                    xi = mathCos(endAngle) * rx + cx;\n                    yi = mathSin(endAngle) * ry + cy;\n                    break;\n                case CMD.R:\n                    x0 = xi = d[i];\n                    y0 = yi = d[i + 1];\n                    x = d[i++];\n                    y = d[i++];\n                    var width = d[i++];\n                    var height = d[i++];\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            var d_1 = displayedLength - accumLength;\n                            ctx.moveTo(x, y);\n                            ctx.lineTo(x + mathMin(d_1, width), y);\n                            d_1 -= width;\n                            if (d_1 > 0) {\n                                ctx.lineTo(x + width, y + mathMin(d_1, height));\n                            }\n                            d_1 -= height;\n                            if (d_1 > 0) {\n                                ctx.lineTo(x + mathMax(width - d_1, 0), y + height);\n                            }\n                            d_1 -= width;\n                            if (d_1 > 0) {\n                                ctx.lineTo(x, y + mathMax(height - d_1, 0));\n                            }\n                            break lo;\n                        }\n                        accumLength += l;\n                    }\n                    ctx.rect(x, y, width, height);\n                    break;\n                case CMD.Z:\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            var t = (displayedLength - accumLength) / l;\n                            ctx.lineTo(xi * (1 - t) + x0 * t, yi * (1 - t) + y0 * t);\n                            break lo;\n                        }\n                        accumLength += l;\n                    }\n                    ctx.closePath();\n                    xi = x0;\n                    yi = y0;\n            }\n        }\n    };\n    PathProxy.prototype.clone = function () {\n        var newProxy = new PathProxy();\n        var data = this.data;\n        newProxy.data = data.slice ? data.slice()\n            : Array.prototype.slice.call(data);\n        newProxy._len = this._len;\n        return newProxy;\n    };\n    PathProxy.CMD = CMD;\n    PathProxy.initDefaultProps = (function () {\n        var proto = PathProxy.prototype;\n        proto._saveData = true;\n        proto._ux = 0;\n        proto._uy = 0;\n        proto._pendingPtDist = 0;\n        proto._version = 0;\n    })();\n    return PathProxy;\n}());\nexport default PathProxy;\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,SAASC,gBAAgB,IAAIC,GAAG,QAAQ,cAAc;AACtD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAEC,OAAO,QAAQ,WAAW;AACvE,SAASC,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,YAAY;AAC7F,IAAIC,GAAG,GAAG;EACNC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACP,CAAC;AACD,IAAIC,OAAO,GAAG,EAAE;AAChB,IAAIC,OAAO,GAAG,EAAE;AAChB,IAAIC,GAAG,GAAG,EAAE;AACZ,IAAIC,GAAG,GAAG,EAAE;AACZ,IAAIC,IAAI,GAAG,EAAE;AACb,IAAIC,IAAI,GAAG,EAAE;AACb,IAAIC,OAAO,GAAGC,IAAI,CAACL,GAAG;AACtB,IAAIM,OAAO,GAAGD,IAAI,CAACJ,GAAG;AACtB,IAAIM,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,IAAIC,OAAO,GAAGJ,IAAI,CAACK,GAAG;AACtB,IAAIC,OAAO,GAAGN,IAAI,CAACO,GAAG;AACtB,IAAIC,EAAE,GAAGR,IAAI,CAACQ,EAAE;AAChB,IAAIC,GAAG,GAAGD,EAAE,GAAG,CAAC;AAChB,IAAIE,aAAa,GAAG,OAAOC,YAAY,KAAK,WAAW;AACvD,IAAIC,SAAS,GAAG,EAAE;AAClB,SAASC,MAAMA,CAACC,MAAM,EAAE;EACpB,IAAIC,CAAC,GAAGf,IAAI,CAACgB,KAAK,CAACF,MAAM,GAAGN,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;EAC3C,OAAQO,CAAC,GAAG,CAAC,GAAIP,EAAE;AACvB;AACA,OAAO,SAASS,kBAAkBA,CAACC,MAAM,EAAEC,aAAa,EAAE;EACtD,IAAIC,aAAa,GAAGP,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;EACrC,IAAIE,aAAa,GAAG,CAAC,EAAE;IACnBA,aAAa,IAAIX,GAAG;EACxB;EACA,IAAIY,KAAK,GAAGD,aAAa,GAAGF,MAAM,CAAC,CAAC,CAAC;EACrC,IAAII,WAAW,GAAGJ,MAAM,CAAC,CAAC,CAAC;EAC3BI,WAAW,IAAID,KAAK;EACpB,IAAI,CAACF,aAAa,IAAIG,WAAW,GAAGF,aAAa,IAAIX,GAAG,EAAE;IACtDa,WAAW,GAAGF,aAAa,GAAGX,GAAG;EACrC,CAAC,MACI,IAAIU,aAAa,IAAIC,aAAa,GAAGE,WAAW,IAAIb,GAAG,EAAE;IAC1Da,WAAW,GAAGF,aAAa,GAAGX,GAAG;EACrC,CAAC,MACI,IAAI,CAACU,aAAa,IAAIC,aAAa,GAAGE,WAAW,EAAE;IACpDA,WAAW,GAAGF,aAAa,IAAIX,GAAG,GAAGI,MAAM,CAACO,aAAa,GAAGE,WAAW,CAAC,CAAC;EAC7E,CAAC,MACI,IAAIH,aAAa,IAAIC,aAAa,GAAGE,WAAW,EAAE;IACnDA,WAAW,GAAGF,aAAa,IAAIX,GAAG,GAAGI,MAAM,CAACS,WAAW,GAAGF,aAAa,CAAC,CAAC;EAC7E;EACAF,MAAM,CAAC,CAAC,CAAC,GAAGE,aAAa;EACzBF,MAAM,CAAC,CAAC,CAAC,GAAGI,WAAW;AAC3B;AACA,IAAIC,SAAS,GAAI,YAAY;EACzB,SAASA,SAASA,CAACC,WAAW,EAAE;IAC5B,IAAI,CAAChD,GAAG,GAAG,CAAC;IACZ,IAAI,CAACiD,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAIL,WAAW,EAAE;MACb,IAAI,CAACM,SAAS,GAAG,KAAK;IAC1B;IACA,IAAI,IAAI,CAACA,SAAS,EAAE;MAChB,IAAI,CAACC,IAAI,GAAG,EAAE;IAClB;EACJ;EACAR,SAAS,CAACS,SAAS,CAACC,eAAe,GAAG,YAAY;IAC9C,IAAI,CAACC,QAAQ,EAAE;EACnB,CAAC;EACDX,SAAS,CAACS,SAAS,CAACG,UAAU,GAAG,YAAY;IACzC,OAAO,IAAI,CAACD,QAAQ;EACxB,CAAC;EACDX,SAAS,CAACS,SAAS,CAACI,QAAQ,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,sBAAsB,EAAE;IACrEA,sBAAsB,GAAGA,sBAAsB,IAAI,CAAC;IACpD,IAAIA,sBAAsB,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACC,GAAG,GAAGlC,OAAO,CAACiC,sBAAsB,GAAG/D,GAAG,GAAG6D,EAAE,CAAC,IAAI,CAAC;MAC1D,IAAI,CAACI,GAAG,GAAGnC,OAAO,CAACiC,sBAAsB,GAAG/D,GAAG,GAAG8D,EAAE,CAAC,IAAI,CAAC;IAC9D;EACJ,CAAC;EACDf,SAAS,CAACS,SAAS,CAACU,MAAM,GAAG,UAAUlE,GAAG,EAAE;IACxC,IAAI,CAACA,GAAG,GAAGA,GAAG;EAClB,CAAC;EACD+C,SAAS,CAACS,SAAS,CAACW,UAAU,GAAG,UAAUC,GAAG,EAAE;IAC5C,IAAI,CAACC,IAAI,GAAGD,GAAG;EACnB,CAAC;EACDrB,SAAS,CAACS,SAAS,CAACc,UAAU,GAAG,YAAY;IACzC,OAAO,IAAI,CAACD,IAAI;EACpB,CAAC;EACDtB,SAAS,CAACS,SAAS,CAACe,SAAS,GAAG,YAAY;IACxC,IAAI,CAACF,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,SAAS,CAAC,CAAC;IAClC,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,OAAO,IAAI;EACf,CAAC;EACDzB,SAAS,CAACS,SAAS,CAACgB,KAAK,GAAG,YAAY;IACpC,IAAI,IAAI,CAAClB,SAAS,EAAE;MAChB,IAAI,CAACD,IAAI,GAAG,CAAC;IACjB;IACA,IAAI,IAAI,CAACoB,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,GAAG,IAAI;MACvB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACrB;IACA,IAAI,CAAChB,QAAQ,EAAE;EACnB,CAAC;EACDX,SAAS,CAACS,SAAS,CAACmB,MAAM,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACzC,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,OAAO,CAACtE,GAAG,CAACC,CAAC,EAAEkE,CAAC,EAAEC,CAAC,CAAC;IACzB,IAAI,CAACR,IAAI,IAAI,IAAI,CAACA,IAAI,CAACM,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnC,IAAI,CAAC1B,GAAG,GAAGyB,CAAC;IACZ,IAAI,CAACxB,GAAG,GAAGyB,CAAC;IACZ,IAAI,CAAC5B,GAAG,GAAG2B,CAAC;IACZ,IAAI,CAAC1B,GAAG,GAAG2B,CAAC;IACZ,OAAO,IAAI;EACf,CAAC;EACD9B,SAAS,CAACS,SAAS,CAACwB,MAAM,GAAG,UAAUJ,CAAC,EAAEC,CAAC,EAAE;IACzC,IAAII,EAAE,GAAGnD,OAAO,CAAC8C,CAAC,GAAG,IAAI,CAAC3B,GAAG,CAAC;IAC9B,IAAIiC,EAAE,GAAGpD,OAAO,CAAC+C,CAAC,GAAG,IAAI,CAAC3B,GAAG,CAAC;IAC9B,IAAIiC,UAAU,GAAGF,EAAE,GAAG,IAAI,CAACjB,GAAG,IAAIkB,EAAE,GAAG,IAAI,CAACjB,GAAG;IAC/C,IAAI,CAACc,OAAO,CAACtE,GAAG,CAACE,CAAC,EAAEiE,CAAC,EAAEC,CAAC,CAAC;IACzB,IAAI,IAAI,CAACR,IAAI,IAAIc,UAAU,EAAE;MACzB,IAAI,CAACd,IAAI,CAACW,MAAM,CAACJ,CAAC,EAAEC,CAAC,CAAC;IAC1B;IACA,IAAIM,UAAU,EAAE;MACZ,IAAI,CAAClC,GAAG,GAAG2B,CAAC;MACZ,IAAI,CAAC1B,GAAG,GAAG2B,CAAC;MACZ,IAAI,CAACO,cAAc,GAAG,CAAC;IAC3B,CAAC,MACI;MACD,IAAIC,EAAE,GAAGJ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;MAC1B,IAAIG,EAAE,GAAG,IAAI,CAACD,cAAc,EAAE;QAC1B,IAAI,CAACE,WAAW,GAAGV,CAAC;QACpB,IAAI,CAACW,WAAW,GAAGV,CAAC;QACpB,IAAI,CAACO,cAAc,GAAGC,EAAE;MAC5B;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDtC,SAAS,CAACS,SAAS,CAACgC,aAAa,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAClE,IAAI,CAAChB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,OAAO,CAACtE,GAAG,CAACG,CAAC,EAAE6E,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC3C,IAAI,IAAI,CAACzB,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACmB,aAAa,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IACnD;IACA,IAAI,CAAC7C,GAAG,GAAG4C,EAAE;IACb,IAAI,CAAC3C,GAAG,GAAG4C,EAAE;IACb,OAAO,IAAI;EACf,CAAC;EACD/C,SAAS,CAACS,SAAS,CAACuC,gBAAgB,GAAG,UAAUN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC7D,IAAI,CAACd,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,OAAO,CAACtE,GAAG,CAACI,CAAC,EAAE4E,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IACnC,IAAI,IAAI,CAACvB,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAAC0B,gBAAgB,CAACN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC9C;IACA,IAAI,CAAC3C,GAAG,GAAG0C,EAAE;IACb,IAAI,CAACzC,GAAG,GAAG0C,EAAE;IACb,OAAO,IAAI;EACf,CAAC;EACD7C,SAAS,CAACS,SAAS,CAACwC,GAAG,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAE1D,aAAa,EAAE;IAChF,IAAI,CAACmC,cAAc,CAAC,CAAC;IACrB1C,SAAS,CAAC,CAAC,CAAC,GAAGgE,UAAU;IACzBhE,SAAS,CAAC,CAAC,CAAC,GAAGiE,QAAQ;IACvB5D,kBAAkB,CAACL,SAAS,EAAEO,aAAa,CAAC;IAC5CyD,UAAU,GAAGhE,SAAS,CAAC,CAAC,CAAC;IACzBiE,QAAQ,GAAGjE,SAAS,CAAC,CAAC,CAAC;IACvB,IAAIS,KAAK,GAAGwD,QAAQ,GAAGD,UAAU;IACjC,IAAI,CAACrB,OAAO,CAACtE,GAAG,CAACK,CAAC,EAAEmF,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEA,CAAC,EAAEC,UAAU,EAAEvD,KAAK,EAAE,CAAC,EAAEF,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9E,IAAI,CAAC0B,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC2B,GAAG,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAE1D,aAAa,CAAC;IAC1E,IAAI,CAACM,GAAG,GAAGvB,OAAO,CAAC2E,QAAQ,CAAC,GAAGF,CAAC,GAAGF,EAAE;IACrC,IAAI,CAAC/C,GAAG,GAAGtB,OAAO,CAACyE,QAAQ,CAAC,GAAGF,CAAC,GAAGD,EAAE;IACrC,OAAO,IAAI;EACf,CAAC;EACDnD,SAAS,CAACS,SAAS,CAAC8C,KAAK,GAAG,UAAUb,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,MAAM,EAAE;IAC1D,IAAI,CAACzB,cAAc,CAAC,CAAC;IACrB,IAAI,IAAI,CAACT,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACiC,KAAK,CAACb,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,MAAM,CAAC;IAC3C;IACA,OAAO,IAAI;EACf,CAAC;EACDxD,SAAS,CAACS,SAAS,CAACgD,IAAI,GAAG,UAAU5B,CAAC,EAAEC,CAAC,EAAE4B,CAAC,EAAEC,CAAC,EAAE;IAC7C,IAAI,CAAC5B,cAAc,CAAC,CAAC;IACrB,IAAI,CAACT,IAAI,IAAI,IAAI,CAACA,IAAI,CAACmC,IAAI,CAAC5B,CAAC,EAAEC,CAAC,EAAE4B,CAAC,EAAEC,CAAC,CAAC;IACvC,IAAI,CAAC3B,OAAO,CAACtE,GAAG,CAACO,CAAC,EAAE4D,CAAC,EAAEC,CAAC,EAAE4B,CAAC,EAAEC,CAAC,CAAC;IAC/B,OAAO,IAAI;EACf,CAAC;EACD3D,SAAS,CAACS,SAAS,CAACmD,SAAS,GAAG,YAAY;IACxC,IAAI,CAAC7B,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,OAAO,CAACtE,GAAG,CAACM,CAAC,CAAC;IACnB,IAAIqD,GAAG,GAAG,IAAI,CAACC,IAAI;IACnB,IAAIuC,EAAE,GAAG,IAAI,CAACzD,GAAG;IACjB,IAAI0D,EAAE,GAAG,IAAI,CAACzD,GAAG;IACjB,IAAIgB,GAAG,EAAE;MACLA,GAAG,CAACuC,SAAS,CAAC,CAAC;IACnB;IACA,IAAI,CAAC1D,GAAG,GAAG2D,EAAE;IACb,IAAI,CAAC1D,GAAG,GAAG2D,EAAE;IACb,OAAO,IAAI;EACf,CAAC;EACD9D,SAAS,CAACS,SAAS,CAACsD,IAAI,GAAG,UAAU1C,GAAG,EAAE;IACtCA,GAAG,IAAIA,GAAG,CAAC0C,IAAI,CAAC,CAAC;IACjB,IAAI,CAACC,QAAQ,CAAC,CAAC;EACnB,CAAC;EACDhE,SAAS,CAACS,SAAS,CAACwD,MAAM,GAAG,UAAU5C,GAAG,EAAE;IACxCA,GAAG,IAAIA,GAAG,CAAC4C,MAAM,CAAC,CAAC;IACnB,IAAI,CAACD,QAAQ,CAAC,CAAC;EACnB,CAAC;EACDhE,SAAS,CAACS,SAAS,CAACyD,GAAG,GAAG,YAAY;IAClC,OAAO,IAAI,CAAC5D,IAAI;EACpB,CAAC;EACDN,SAAS,CAACS,SAAS,CAAC0D,OAAO,GAAG,UAAU3D,IAAI,EAAE;IAC1C,IAAI0D,GAAG,GAAG1D,IAAI,CAAC4D,MAAM;IACrB,IAAI,EAAE,IAAI,CAAC5D,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC4D,MAAM,KAAKF,GAAG,CAAC,IAAI/E,aAAa,EAAE;MAC3D,IAAI,CAACqB,IAAI,GAAG,IAAIpB,YAAY,CAAC8E,GAAG,CAAC;IACrC;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAAC7D,IAAI,CAAC6D,CAAC,CAAC,GAAG7D,IAAI,CAAC6D,CAAC,CAAC;IAC1B;IACA,IAAI,CAAC/D,IAAI,GAAG4D,GAAG;EACnB,CAAC;EACDlE,SAAS,CAACS,SAAS,CAAC6D,UAAU,GAAG,UAAUC,IAAI,EAAE;IAC7C,IAAI,EAAEA,IAAI,YAAYC,KAAK,CAAC,EAAE;MAC1BD,IAAI,GAAG,CAACA,IAAI,CAAC;IACjB;IACA,IAAIL,GAAG,GAAGK,IAAI,CAACH,MAAM;IACrB,IAAIK,UAAU,GAAG,CAAC;IAClB,IAAIC,MAAM,GAAG,IAAI,CAACpE,IAAI;IACtB,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,EAAE,EAAE;MAC1BI,UAAU,IAAIF,IAAI,CAACF,CAAC,CAAC,CAACH,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI/E,aAAa,IAAK,IAAI,CAACqB,IAAI,YAAYpB,YAAa,EAAE;MACtD,IAAI,CAACoB,IAAI,GAAG,IAAIpB,YAAY,CAACsF,MAAM,GAAGD,UAAU,CAAC;IACrD;IACA,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAIM,cAAc,GAAGJ,IAAI,CAACF,CAAC,CAAC,CAAC7D,IAAI;MACjC,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;QAC5C,IAAI,CAACpE,IAAI,CAACkE,MAAM,EAAE,CAAC,GAAGC,cAAc,CAACC,CAAC,CAAC;MAC3C;IACJ;IACA,IAAI,CAACtE,IAAI,GAAGoE,MAAM;EACtB,CAAC;EACD1E,SAAS,CAACS,SAAS,CAACuB,OAAO,GAAG,UAAU6C,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEzB,CAAC,EAAE;IACjE,IAAI,CAAC,IAAI,CAACpD,SAAS,EAAE;MACjB;IACJ;IACA,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI,IAAI,CAACF,IAAI,GAAG+E,SAAS,CAACjB,MAAM,GAAG5D,IAAI,CAAC4D,MAAM,EAAE;MAC5C,IAAI,CAACkB,WAAW,CAAC,CAAC;MAClB9E,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB;IACA,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,SAAS,CAACjB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACvC7D,IAAI,CAAC,IAAI,CAACF,IAAI,EAAE,CAAC,GAAG+E,SAAS,CAAChB,CAAC,CAAC;IACpC;EACJ,CAAC;EACDrE,SAAS,CAACS,SAAS,CAACsB,cAAc,GAAG,YAAY;IAC7C,IAAI,IAAI,CAACM,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACf,IAAI,IAAI,IAAI,CAACA,IAAI,CAACW,MAAM,CAAC,IAAI,CAACM,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;MACjE,IAAI,CAACH,cAAc,GAAG,CAAC;IAC3B;EACJ,CAAC;EACDrC,SAAS,CAACS,SAAS,CAAC6E,WAAW,GAAG,YAAY;IAC1C,IAAI,EAAE,IAAI,CAAC9E,IAAI,YAAYgE,KAAK,CAAC,EAAE;MAC/B,IAAIe,OAAO,GAAG,EAAE;MAChB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC/D,IAAI,EAAE+D,CAAC,EAAE,EAAE;QAChCkB,OAAO,CAAClB,CAAC,CAAC,GAAG,IAAI,CAAC7D,IAAI,CAAC6D,CAAC,CAAC;MAC7B;MACA,IAAI,CAAC7D,IAAI,GAAG+E,OAAO;IACvB;EACJ,CAAC;EACDvF,SAAS,CAACS,SAAS,CAACuD,QAAQ,GAAG,YAAY;IACvC,IAAI,CAAC,IAAI,CAACzD,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACwB,cAAc,CAAC,CAAC;IACrB,IAAIvB,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIA,IAAI,YAAYgE,KAAK,EAAE;MACvBhE,IAAI,CAAC4D,MAAM,GAAG,IAAI,CAAC9D,IAAI;MACvB,IAAInB,aAAa,IAAI,IAAI,CAACmB,IAAI,GAAG,EAAE,EAAE;QACjC,IAAI,CAACE,IAAI,GAAG,IAAIpB,YAAY,CAACoB,IAAI,CAAC;MACtC;IACJ;EACJ,CAAC;EACDR,SAAS,CAACS,SAAS,CAAC+E,eAAe,GAAG,YAAY;IAC9CpH,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGE,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGmH,MAAM,CAACC,SAAS;IACtDrH,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGE,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,CAACkH,MAAM,CAACC,SAAS;IACvD,IAAIlF,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAImF,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACV,IAAI/B,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIO,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC/D,IAAI,GAAG;MACxB,IAAIuE,GAAG,GAAGrE,IAAI,CAAC6D,CAAC,EAAE,CAAC;MACnB,IAAIwB,OAAO,GAAGxB,CAAC,KAAK,CAAC;MACrB,IAAIwB,OAAO,EAAE;QACTF,EAAE,GAAGnF,IAAI,CAAC6D,CAAC,CAAC;QACZuB,EAAE,GAAGpF,IAAI,CAAC6D,CAAC,GAAG,CAAC,CAAC;QAChBR,EAAE,GAAG8B,EAAE;QACP7B,EAAE,GAAG8B,EAAE;MACX;MACA,QAAQf,GAAG;QACP,KAAKnH,GAAG,CAACC,CAAC;UACNgI,EAAE,GAAG9B,EAAE,GAAGrD,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACnBuB,EAAE,GAAG9B,EAAE,GAAGtD,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACnB/F,IAAI,CAAC,CAAC,CAAC,GAAGuF,EAAE;UACZvF,IAAI,CAAC,CAAC,CAAC,GAAGwF,EAAE;UACZvF,IAAI,CAAC,CAAC,CAAC,GAAGsF,EAAE;UACZtF,IAAI,CAAC,CAAC,CAAC,GAAGuF,EAAE;UACZ;QACJ,KAAKpG,GAAG,CAACE,CAAC;UACNV,QAAQ,CAACyI,EAAE,EAAEC,EAAE,EAAEpF,IAAI,CAAC6D,CAAC,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,GAAG,CAAC,CAAC,EAAE/F,IAAI,EAAEC,IAAI,CAAC;UAClDoH,EAAE,GAAGnF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACduB,EAAE,GAAGpF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACd;QACJ,KAAK3G,GAAG,CAACG,CAAC;UACNV,SAAS,CAACwI,EAAE,EAAEC,EAAE,EAAEpF,IAAI,CAAC6D,CAAC,EAAE,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,EAAE,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,EAAE,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,EAAE,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,GAAG,CAAC,CAAC,EAAE/F,IAAI,EAAEC,IAAI,CAAC;UAC/FoH,EAAE,GAAGnF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACduB,EAAE,GAAGpF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACd;QACJ,KAAK3G,GAAG,CAACI,CAAC;UACNV,aAAa,CAACuI,EAAE,EAAEC,EAAE,EAAEpF,IAAI,CAAC6D,CAAC,EAAE,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,EAAE,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,CAAC,EAAE7D,IAAI,CAAC6D,CAAC,GAAG,CAAC,CAAC,EAAE/F,IAAI,EAAEC,IAAI,CAAC;UAC7EoH,EAAE,GAAGnF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACduB,EAAE,GAAGpF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACd;QACJ,KAAK3G,GAAG,CAACK,CAAC;UACN,IAAImF,EAAE,GAAG1C,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAClB,IAAIlB,EAAE,GAAG3C,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAClB,IAAIyB,EAAE,GAAGtF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAClB,IAAI0B,EAAE,GAAGvF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAClB,IAAIhB,UAAU,GAAG7C,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAC1B,IAAIf,QAAQ,GAAG9C,IAAI,CAAC6D,CAAC,EAAE,CAAC,GAAGhB,UAAU;UACrCgB,CAAC,IAAI,CAAC;UACN,IAAIzE,aAAa,GAAG,CAACY,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAC9B,IAAIwB,OAAO,EAAE;YACThC,EAAE,GAAGlF,OAAO,CAAC0E,UAAU,CAAC,GAAGyC,EAAE,GAAG5C,EAAE;YAClCY,EAAE,GAAGjF,OAAO,CAACwE,UAAU,CAAC,GAAG0C,EAAE,GAAG5C,EAAE;UACtC;UACA9F,OAAO,CAAC6F,EAAE,EAAEC,EAAE,EAAE2C,EAAE,EAAEC,EAAE,EAAE1C,UAAU,EAAEC,QAAQ,EAAE1D,aAAa,EAAEtB,IAAI,EAAEC,IAAI,CAAC;UACxEoH,EAAE,GAAGhH,OAAO,CAAC2E,QAAQ,CAAC,GAAGwC,EAAE,GAAG5C,EAAE;UAChC0C,EAAE,GAAG/G,OAAO,CAACyE,QAAQ,CAAC,GAAGyC,EAAE,GAAG5C,EAAE;UAChC;QACJ,KAAKzF,GAAG,CAACO,CAAC;UACN4F,EAAE,GAAG8B,EAAE,GAAGnF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACnBP,EAAE,GAAG8B,EAAE,GAAGpF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACnB,IAAI2B,KAAK,GAAGxF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACrB,IAAI4B,MAAM,GAAGzF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACtBnH,QAAQ,CAAC2G,EAAE,EAAEC,EAAE,EAAED,EAAE,GAAGmC,KAAK,EAAElC,EAAE,GAAGmC,MAAM,EAAE3H,IAAI,EAAEC,IAAI,CAAC;UACrD;QACJ,KAAKb,GAAG,CAACM,CAAC;UACN2H,EAAE,GAAG9B,EAAE;UACP+B,EAAE,GAAG9B,EAAE;UACP;MACR;MACAhH,IAAI,CAACsB,GAAG,CAACA,GAAG,EAAEA,GAAG,EAAEE,IAAI,CAAC;MACxBxB,IAAI,CAACuB,GAAG,CAACA,GAAG,EAAEA,GAAG,EAAEE,IAAI,CAAC;IAC5B;IACA,IAAI8F,CAAC,KAAK,CAAC,EAAE;MACTjG,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACzC;IACA,OAAO,IAAItB,YAAY,CAACqB,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7E,CAAC;EACD4B,SAAS,CAACS,SAAS,CAACyF,gBAAgB,GAAG,YAAY;IAC/C,IAAI1F,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI0D,GAAG,GAAG,IAAI,CAAC5D,IAAI;IACnB,IAAI6F,EAAE,GAAG,IAAI,CAAClF,GAAG;IACjB,IAAImF,EAAE,GAAG,IAAI,CAAClF,GAAG;IACjB,IAAIyE,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACV,IAAI/B,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACV,IAAI,CAAC,IAAI,CAACpC,WAAW,EAAE;MACnB,IAAI,CAACA,WAAW,GAAG,EAAE;IACzB;IACA,IAAI2E,UAAU,GAAG,IAAI,CAAC3E,WAAW;IACjC,IAAI4E,YAAY,GAAG,CAAC;IACpB,IAAIC,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,GAAG;MACtB,IAAIW,GAAG,GAAGrE,IAAI,CAAC6D,CAAC,EAAE,CAAC;MACnB,IAAIwB,OAAO,GAAGxB,CAAC,KAAK,CAAC;MACrB,IAAIwB,OAAO,EAAE;QACTF,EAAE,GAAGnF,IAAI,CAAC6D,CAAC,CAAC;QACZuB,EAAE,GAAGpF,IAAI,CAAC6D,CAAC,GAAG,CAAC,CAAC;QAChBR,EAAE,GAAG8B,EAAE;QACP7B,EAAE,GAAG8B,EAAE;MACX;MACA,IAAIY,CAAC,GAAG,CAAC,CAAC;MACV,QAAQ3B,GAAG;QACP,KAAKnH,GAAG,CAACC,CAAC;UACNgI,EAAE,GAAG9B,EAAE,GAAGrD,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACnBuB,EAAE,GAAG9B,EAAE,GAAGtD,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACnB;QACJ,KAAK3G,GAAG,CAACE,CAAC;UAAE;YACR,IAAIgF,EAAE,GAAGpC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAIxB,EAAE,GAAGrC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAInC,EAAE,GAAGU,EAAE,GAAG+C,EAAE;YAChB,IAAIxD,EAAE,GAAGU,EAAE,GAAG+C,EAAE;YAChB,IAAI7G,OAAO,CAACmD,EAAE,CAAC,GAAGiE,EAAE,IAAIpH,OAAO,CAACoD,EAAE,CAAC,GAAGiE,EAAE,IAAI/B,CAAC,KAAKH,GAAG,GAAG,CAAC,EAAE;cACvDsC,CAAC,GAAG/H,IAAI,CAACgI,IAAI,CAACvE,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;cAChCwD,EAAE,GAAG/C,EAAE;cACPgD,EAAE,GAAG/C,EAAE;YACX;YACA;UACJ;QACA,KAAKnF,GAAG,CAACG,CAAC;UAAE;YACR,IAAI6E,EAAE,GAAGlC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAI1B,EAAE,GAAGnC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAIzB,EAAE,GAAGpC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAIxB,EAAE,GAAGrC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAIvB,EAAE,GAAGtC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAItB,EAAE,GAAGvC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClBmC,CAAC,GAAGlJ,WAAW,CAACqI,EAAE,EAAEC,EAAE,EAAElD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;YACnD4C,EAAE,GAAG7C,EAAE;YACP8C,EAAE,GAAG7C,EAAE;YACP;UACJ;QACA,KAAKrF,GAAG,CAACI,CAAC;UAAE;YACR,IAAI4E,EAAE,GAAGlC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAI1B,EAAE,GAAGnC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAIzB,EAAE,GAAGpC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClB,IAAIxB,EAAE,GAAGrC,IAAI,CAAC6D,CAAC,EAAE,CAAC;YAClBmC,CAAC,GAAGhJ,eAAe,CAACmI,EAAE,EAAEC,EAAE,EAAElD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;YAC/C8C,EAAE,GAAG/C,EAAE;YACPgD,EAAE,GAAG/C,EAAE;YACP;UACJ;QACA,KAAKnF,GAAG,CAACK,CAAC;UACN,IAAImF,EAAE,GAAG1C,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAClB,IAAIlB,EAAE,GAAG3C,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAClB,IAAIyB,EAAE,GAAGtF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAClB,IAAI0B,EAAE,GAAGvF,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAClB,IAAIhB,UAAU,GAAG7C,IAAI,CAAC6D,CAAC,EAAE,CAAC;UAC1B,IAAIvE,KAAK,GAAGU,IAAI,CAAC6D,CAAC,EAAE,CAAC;UACrB,IAAIf,QAAQ,GAAGxD,KAAK,GAAGuD,UAAU;UACjCgB,CAAC,IAAI,CAAC;UACN,IAAIwB,OAAO,EAAE;YACThC,EAAE,GAAGlF,OAAO,CAAC0E,UAAU,CAAC,GAAGyC,EAAE,GAAG5C,EAAE;YAClCY,EAAE,GAAGjF,OAAO,CAACwE,UAAU,CAAC,GAAG0C,EAAE,GAAG5C,EAAE;UACtC;UACAqD,CAAC,GAAG9H,OAAO,CAACoH,EAAE,EAAEC,EAAE,CAAC,GAAGvH,OAAO,CAACU,GAAG,EAAET,IAAI,CAACO,GAAG,CAACc,KAAK,CAAC,CAAC;UACnD6F,EAAE,GAAGhH,OAAO,CAAC2E,QAAQ,CAAC,GAAGwC,EAAE,GAAG5C,EAAE;UAChC0C,EAAE,GAAG/G,OAAO,CAACyE,QAAQ,CAAC,GAAGyC,EAAE,GAAG5C,EAAE;UAChC;QACJ,KAAKzF,GAAG,CAACO,CAAC;UAAE;YACR4F,EAAE,GAAG8B,EAAE,GAAGnF,IAAI,CAAC6D,CAAC,EAAE,CAAC;YACnBP,EAAE,GAAG8B,EAAE,GAAGpF,IAAI,CAAC6D,CAAC,EAAE,CAAC;YACnB,IAAI2B,KAAK,GAAGxF,IAAI,CAAC6D,CAAC,EAAE,CAAC;YACrB,IAAI4B,MAAM,GAAGzF,IAAI,CAAC6D,CAAC,EAAE,CAAC;YACtBmC,CAAC,GAAGR,KAAK,GAAG,CAAC,GAAGC,MAAM,GAAG,CAAC;YAC1B;UACJ;QACA,KAAKvI,GAAG,CAACM,CAAC;UAAE;YACR,IAAIkE,EAAE,GAAG2B,EAAE,GAAG8B,EAAE;YAChB,IAAIxD,EAAE,GAAG2B,EAAE,GAAG8B,EAAE;YAChBY,CAAC,GAAG/H,IAAI,CAACgI,IAAI,CAACvE,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;YAChCwD,EAAE,GAAG9B,EAAE;YACP+B,EAAE,GAAG9B,EAAE;YACP;UACJ;MACJ;MACA,IAAI0C,CAAC,IAAI,CAAC,EAAE;QACRH,UAAU,CAACE,QAAQ,EAAE,CAAC,GAAGC,CAAC;QAC1BF,YAAY,IAAIE,CAAC;MACrB;IACJ;IACA,IAAI,CAAC7E,QAAQ,GAAG2E,YAAY;IAC5B,OAAOA,YAAY;EACvB,CAAC;EACDtG,SAAS,CAACS,SAAS,CAACiG,WAAW,GAAG,UAAUrF,GAAG,EAAEsF,OAAO,EAAE;IACtD,IAAI1B,CAAC,GAAG,IAAI,CAACzE,IAAI;IACjB,IAAI2F,EAAE,GAAG,IAAI,CAAClF,GAAG;IACjB,IAAImF,EAAE,GAAG,IAAI,CAAClF,GAAG;IACjB,IAAIgD,GAAG,GAAG,IAAI,CAAC5D,IAAI;IACnB,IAAIuD,EAAE;IACN,IAAIC,EAAE;IACN,IAAI6B,EAAE;IACN,IAAIC,EAAE;IACN,IAAI/D,CAAC;IACL,IAAIC,CAAC;IACL,IAAI8E,QAAQ,GAAGD,OAAO,GAAG,CAAC;IAC1B,IAAIN,UAAU;IACd,IAAIC,YAAY;IAChB,IAAIO,WAAW,GAAG,CAAC;IACnB,IAAIN,QAAQ,GAAG,CAAC;IAChB,IAAIO,eAAe;IACnB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,UAAU;IACd,IAAIC,UAAU;IACd,IAAIL,QAAQ,EAAE;MACV,IAAI,CAAC,IAAI,CAAClF,WAAW,EAAE;QACnB,IAAI,CAACwE,gBAAgB,CAAC,CAAC;MAC3B;MACAG,UAAU,GAAG,IAAI,CAAC3E,WAAW;MAC7B4E,YAAY,GAAG,IAAI,CAAC3E,QAAQ;MAC5BmF,eAAe,GAAGH,OAAO,GAAGL,YAAY;MACxC,IAAI,CAACQ,eAAe,EAAE;QAClB;MACJ;IACJ;IACAI,EAAE,EAAE,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,GAAG;MAC1B,IAAIW,GAAG,GAAGI,CAAC,CAACZ,CAAC,EAAE,CAAC;MAChB,IAAIwB,OAAO,GAAGxB,CAAC,KAAK,CAAC;MACrB,IAAIwB,OAAO,EAAE;QACTF,EAAE,GAAGV,CAAC,CAACZ,CAAC,CAAC;QACTuB,EAAE,GAAGX,CAAC,CAACZ,CAAC,GAAG,CAAC,CAAC;QACbR,EAAE,GAAG8B,EAAE;QACP7B,EAAE,GAAG8B,EAAE;MACX;MACA,IAAIf,GAAG,KAAKnH,GAAG,CAACE,CAAC,IAAImJ,aAAa,GAAG,CAAC,EAAE;QACpC1F,GAAG,CAACY,MAAM,CAAC+E,UAAU,EAAEC,UAAU,CAAC;QAClCF,aAAa,GAAG,CAAC;MACrB;MACA,QAAQlC,GAAG;QACP,KAAKnH,GAAG,CAACC,CAAC;UACNkG,EAAE,GAAG8B,EAAE,GAAGV,CAAC,CAACZ,CAAC,EAAE,CAAC;UAChBP,EAAE,GAAG8B,EAAE,GAAGX,CAAC,CAACZ,CAAC,EAAE,CAAC;UAChBhD,GAAG,CAACO,MAAM,CAAC+D,EAAE,EAAEC,EAAE,CAAC;UAClB;QACJ,KAAKlI,GAAG,CAACE,CAAC;UAAE;YACRiE,CAAC,GAAGoD,CAAC,CAACZ,CAAC,EAAE,CAAC;YACVvC,CAAC,GAAGmD,CAAC,CAACZ,CAAC,EAAE,CAAC;YACV,IAAInC,EAAE,GAAGnD,OAAO,CAAC8C,CAAC,GAAG8D,EAAE,CAAC;YACxB,IAAIxD,EAAE,GAAGpD,OAAO,CAAC+C,CAAC,GAAG8D,EAAE,CAAC;YACxB,IAAI1D,EAAE,GAAGiE,EAAE,IAAIhE,EAAE,GAAGiE,EAAE,EAAE;cACpB,IAAIQ,QAAQ,EAAE;gBACV,IAAIJ,CAAC,GAAGH,UAAU,CAACE,QAAQ,EAAE,CAAC;gBAC9B,IAAIM,WAAW,GAAGL,CAAC,GAAGM,eAAe,EAAE;kBACnC,IAAIK,CAAC,GAAG,CAACL,eAAe,GAAGD,WAAW,IAAIL,CAAC;kBAC3CnF,GAAG,CAACY,MAAM,CAAC0D,EAAE,IAAI,CAAC,GAAGwB,CAAC,CAAC,GAAGtF,CAAC,GAAGsF,CAAC,EAAEvB,EAAE,IAAI,CAAC,GAAGuB,CAAC,CAAC,GAAGrF,CAAC,GAAGqF,CAAC,CAAC;kBACtD,MAAMD,EAAE;gBACZ;gBACAL,WAAW,IAAIL,CAAC;cACpB;cACAnF,GAAG,CAACY,MAAM,CAACJ,CAAC,EAAEC,CAAC,CAAC;cAChB6D,EAAE,GAAG9D,CAAC;cACN+D,EAAE,GAAG9D,CAAC;cACNiF,aAAa,GAAG,CAAC;YACrB,CAAC,MACI;cACD,IAAIzE,EAAE,GAAGJ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;cAC1B,IAAIG,EAAE,GAAGyE,aAAa,EAAE;gBACpBC,UAAU,GAAGnF,CAAC;gBACdoF,UAAU,GAAGnF,CAAC;gBACdiF,aAAa,GAAGzE,EAAE;cACtB;YACJ;YACA;UACJ;QACA,KAAK5E,GAAG,CAACG,CAAC;UAAE;YACR,IAAI6E,EAAE,GAAGuC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAI1B,EAAE,GAAGsC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAIzB,EAAE,GAAGqC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAIxB,EAAE,GAAGoC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAIvB,EAAE,GAAGmC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAItB,EAAE,GAAGkC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAIuC,QAAQ,EAAE;cACV,IAAIJ,CAAC,GAAGH,UAAU,CAACE,QAAQ,EAAE,CAAC;cAC9B,IAAIM,WAAW,GAAGL,CAAC,GAAGM,eAAe,EAAE;gBACnC,IAAIK,CAAC,GAAG,CAACL,eAAe,GAAGD,WAAW,IAAIL,CAAC;gBAC3CjJ,cAAc,CAACoI,EAAE,EAAEjD,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEqE,CAAC,EAAEjJ,OAAO,CAAC;gBAC1CX,cAAc,CAACqI,EAAE,EAAEjD,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEoE,CAAC,EAAEhJ,OAAO,CAAC;gBAC1CkD,GAAG,CAACoB,aAAa,CAACvE,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzF,MAAM+I,EAAE;cACZ;cACAL,WAAW,IAAIL,CAAC;YACpB;YACAnF,GAAG,CAACoB,aAAa,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;YACzC4C,EAAE,GAAG7C,EAAE;YACP8C,EAAE,GAAG7C,EAAE;YACP;UACJ;QACA,KAAKrF,GAAG,CAACI,CAAC;UAAE;YACR,IAAI4E,EAAE,GAAGuC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAI1B,EAAE,GAAGsC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAIzB,EAAE,GAAGqC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAIxB,EAAE,GAAGoC,CAAC,CAACZ,CAAC,EAAE,CAAC;YACf,IAAIuC,QAAQ,EAAE;cACV,IAAIJ,CAAC,GAAGH,UAAU,CAACE,QAAQ,EAAE,CAAC;cAC9B,IAAIM,WAAW,GAAGL,CAAC,GAAGM,eAAe,EAAE;gBACnC,IAAIK,CAAC,GAAG,CAACL,eAAe,GAAGD,WAAW,IAAIL,CAAC;gBAC3C/I,kBAAkB,CAACkI,EAAE,EAAEjD,EAAE,EAAEE,EAAE,EAAEuE,CAAC,EAAEjJ,OAAO,CAAC;gBAC1CT,kBAAkB,CAACmI,EAAE,EAAEjD,EAAE,EAAEE,EAAE,EAAEsE,CAAC,EAAEhJ,OAAO,CAAC;gBAC1CkD,GAAG,CAAC2B,gBAAgB,CAAC9E,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpE,MAAM+I,EAAE;cACZ;cACAL,WAAW,IAAIL,CAAC;YACpB;YACAnF,GAAG,CAAC2B,gBAAgB,CAACN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;YACpC8C,EAAE,GAAG/C,EAAE;YACPgD,EAAE,GAAG/C,EAAE;YACP;UACJ;QACA,KAAKnF,GAAG,CAACK,CAAC;UACN,IAAImF,EAAE,GAAG+B,CAAC,CAACZ,CAAC,EAAE,CAAC;UACf,IAAIlB,EAAE,GAAG8B,CAAC,CAACZ,CAAC,EAAE,CAAC;UACf,IAAIyB,EAAE,GAAGb,CAAC,CAACZ,CAAC,EAAE,CAAC;UACf,IAAI0B,EAAE,GAAGd,CAAC,CAACZ,CAAC,EAAE,CAAC;UACf,IAAIhB,UAAU,GAAG4B,CAAC,CAACZ,CAAC,EAAE,CAAC;UACvB,IAAIvE,KAAK,GAAGmF,CAAC,CAACZ,CAAC,EAAE,CAAC;UAClB,IAAI+C,GAAG,GAAGnC,CAAC,CAACZ,CAAC,EAAE,CAAC;UAChB,IAAIzE,aAAa,GAAG,CAACqF,CAAC,CAACZ,CAAC,EAAE,CAAC;UAC3B,IAAIjB,CAAC,GAAI0C,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGC,EAAE;UAC3B,IAAIsB,SAAS,GAAGtI,OAAO,CAAC+G,EAAE,GAAGC,EAAE,CAAC,GAAG,IAAI;UACvC,IAAIzC,QAAQ,GAAGD,UAAU,GAAGvD,KAAK;UACjC,IAAIwH,UAAU,GAAG,KAAK;UACtB,IAAIV,QAAQ,EAAE;YACV,IAAIJ,CAAC,GAAGH,UAAU,CAACE,QAAQ,EAAE,CAAC;YAC9B,IAAIM,WAAW,GAAGL,CAAC,GAAGM,eAAe,EAAE;cACnCxD,QAAQ,GAAGD,UAAU,GAAGvD,KAAK,IAAIgH,eAAe,GAAGD,WAAW,CAAC,GAAGL,CAAC;cACnEc,UAAU,GAAG,IAAI;YACrB;YACAT,WAAW,IAAIL,CAAC;UACpB;UACA,IAAIa,SAAS,IAAIhG,GAAG,CAACkG,OAAO,EAAE;YAC1BlG,GAAG,CAACkG,OAAO,CAACrE,EAAE,EAAEC,EAAE,EAAE2C,EAAE,EAAEC,EAAE,EAAEqB,GAAG,EAAE/D,UAAU,EAAEC,QAAQ,EAAE1D,aAAa,CAAC;UACzE,CAAC,MACI;YACDyB,GAAG,CAAC4B,GAAG,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAE1D,aAAa,CAAC;UAC3D;UACA,IAAI0H,UAAU,EAAE;YACZ,MAAMJ,EAAE;UACZ;UACA,IAAIrB,OAAO,EAAE;YACThC,EAAE,GAAGlF,OAAO,CAAC0E,UAAU,CAAC,GAAGyC,EAAE,GAAG5C,EAAE;YAClCY,EAAE,GAAGjF,OAAO,CAACwE,UAAU,CAAC,GAAG0C,EAAE,GAAG5C,EAAE;UACtC;UACAwC,EAAE,GAAGhH,OAAO,CAAC2E,QAAQ,CAAC,GAAGwC,EAAE,GAAG5C,EAAE;UAChC0C,EAAE,GAAG/G,OAAO,CAACyE,QAAQ,CAAC,GAAGyC,EAAE,GAAG5C,EAAE;UAChC;QACJ,KAAKzF,GAAG,CAACO,CAAC;UACN4F,EAAE,GAAG8B,EAAE,GAAGV,CAAC,CAACZ,CAAC,CAAC;UACdP,EAAE,GAAG8B,EAAE,GAAGX,CAAC,CAACZ,CAAC,GAAG,CAAC,CAAC;UAClBxC,CAAC,GAAGoD,CAAC,CAACZ,CAAC,EAAE,CAAC;UACVvC,CAAC,GAAGmD,CAAC,CAACZ,CAAC,EAAE,CAAC;UACV,IAAI2B,KAAK,GAAGf,CAAC,CAACZ,CAAC,EAAE,CAAC;UAClB,IAAI4B,MAAM,GAAGhB,CAAC,CAACZ,CAAC,EAAE,CAAC;UACnB,IAAIuC,QAAQ,EAAE;YACV,IAAIJ,CAAC,GAAGH,UAAU,CAACE,QAAQ,EAAE,CAAC;YAC9B,IAAIM,WAAW,GAAGL,CAAC,GAAGM,eAAe,EAAE;cACnC,IAAIU,GAAG,GAAGV,eAAe,GAAGD,WAAW;cACvCxF,GAAG,CAACO,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC;cAChBT,GAAG,CAACY,MAAM,CAACJ,CAAC,GAAGrD,OAAO,CAACgJ,GAAG,EAAExB,KAAK,CAAC,EAAElE,CAAC,CAAC;cACtC0F,GAAG,IAAIxB,KAAK;cACZ,IAAIwB,GAAG,GAAG,CAAC,EAAE;gBACTnG,GAAG,CAACY,MAAM,CAACJ,CAAC,GAAGmE,KAAK,EAAElE,CAAC,GAAGtD,OAAO,CAACgJ,GAAG,EAAEvB,MAAM,CAAC,CAAC;cACnD;cACAuB,GAAG,IAAIvB,MAAM;cACb,IAAIuB,GAAG,GAAG,CAAC,EAAE;gBACTnG,GAAG,CAACY,MAAM,CAACJ,CAAC,GAAGnD,OAAO,CAACsH,KAAK,GAAGwB,GAAG,EAAE,CAAC,CAAC,EAAE1F,CAAC,GAAGmE,MAAM,CAAC;cACvD;cACAuB,GAAG,IAAIxB,KAAK;cACZ,IAAIwB,GAAG,GAAG,CAAC,EAAE;gBACTnG,GAAG,CAACY,MAAM,CAACJ,CAAC,EAAEC,CAAC,GAAGpD,OAAO,CAACuH,MAAM,GAAGuB,GAAG,EAAE,CAAC,CAAC,CAAC;cAC/C;cACA,MAAMN,EAAE;YACZ;YACAL,WAAW,IAAIL,CAAC;UACpB;UACAnF,GAAG,CAACoC,IAAI,CAAC5B,CAAC,EAAEC,CAAC,EAAEkE,KAAK,EAAEC,MAAM,CAAC;UAC7B;QACJ,KAAKvI,GAAG,CAACM,CAAC;UACN,IAAI4I,QAAQ,EAAE;YACV,IAAIJ,CAAC,GAAGH,UAAU,CAACE,QAAQ,EAAE,CAAC;YAC9B,IAAIM,WAAW,GAAGL,CAAC,GAAGM,eAAe,EAAE;cACnC,IAAIK,CAAC,GAAG,CAACL,eAAe,GAAGD,WAAW,IAAIL,CAAC;cAC3CnF,GAAG,CAACY,MAAM,CAAC0D,EAAE,IAAI,CAAC,GAAGwB,CAAC,CAAC,GAAGtD,EAAE,GAAGsD,CAAC,EAAEvB,EAAE,IAAI,CAAC,GAAGuB,CAAC,CAAC,GAAGrD,EAAE,GAAGqD,CAAC,CAAC;cACxD,MAAMD,EAAE;YACZ;YACAL,WAAW,IAAIL,CAAC;UACpB;UACAnF,GAAG,CAACuC,SAAS,CAAC,CAAC;UACf+B,EAAE,GAAG9B,EAAE;UACP+B,EAAE,GAAG9B,EAAE;MACf;IACJ;EACJ,CAAC;EACD9D,SAAS,CAACS,SAAS,CAACgH,KAAK,GAAG,YAAY;IACpC,IAAIC,QAAQ,GAAG,IAAI1H,SAAS,CAAC,CAAC;IAC9B,IAAIQ,IAAI,GAAG,IAAI,CAACA,IAAI;IACpBkH,QAAQ,CAAClH,IAAI,GAAGA,IAAI,CAACmH,KAAK,GAAGnH,IAAI,CAACmH,KAAK,CAAC,CAAC,GACnCnD,KAAK,CAAC/D,SAAS,CAACkH,KAAK,CAACC,IAAI,CAACpH,IAAI,CAAC;IACtCkH,QAAQ,CAACpH,IAAI,GAAG,IAAI,CAACA,IAAI;IACzB,OAAOoH,QAAQ;EACnB,CAAC;EACD1H,SAAS,CAACtC,GAAG,GAAGA,GAAG;EACnBsC,SAAS,CAAC6H,gBAAgB,GAAI,YAAY;IACtC,IAAIC,KAAK,GAAG9H,SAAS,CAACS,SAAS;IAC/BqH,KAAK,CAACvH,SAAS,GAAG,IAAI;IACtBuH,KAAK,CAAC7G,GAAG,GAAG,CAAC;IACb6G,KAAK,CAAC5G,GAAG,GAAG,CAAC;IACb4G,KAAK,CAACzF,cAAc,GAAG,CAAC;IACxByF,KAAK,CAACnH,QAAQ,GAAG,CAAC;EACtB,CAAC,CAAE,CAAC;EACJ,OAAOX,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}