{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n// 模拟物料数据\nconst mockMaterials = [{\n  id: 'MAT-001',\n  code: 'CPU-I7-12700K',\n  name: 'Intel Core i7-12700K',\n  specification: '12核20线程 3.6GHz',\n  unit: '个',\n  standardCost: 2800,\n  currentCost: 2850,\n  supplier: '英特尔',\n  category: 'CPU',\n  status: 'active',\n  stockQuantity: 50,\n  minStock: 10,\n  createTime: '2024-01-01'\n}, {\n  id: 'MAT-002',\n  code: 'MB-ASUS-Z690',\n  name: 'ASUS ROG STRIX Z690-E',\n  specification: 'ATX主板 LGA1700',\n  unit: '个',\n  standardCost: 1800,\n  currentCost: 1850,\n  supplier: '华硕',\n  category: '主板',\n  status: 'active',\n  stockQuantity: 30,\n  minStock: 5,\n  createTime: '2024-01-01'\n}, {\n  id: 'MAT-003',\n  code: 'RAM-32G-DDR5',\n  name: '金士顿 DDR5 32GB',\n  specification: '32GB DDR5-5600 套装',\n  unit: '套',\n  standardCost: 1200,\n  currentCost: 1250,\n  supplier: '金士顿',\n  category: '内存',\n  status: 'active',\n  stockQuantity: 80,\n  minStock: 20,\n  createTime: '2024-01-01'\n}, {\n  id: 'MAT-004',\n  code: 'SSD-1TB-NVME',\n  name: '三星 980 PRO 1TB',\n  specification: 'NVMe M.2 SSD 1TB',\n  unit: '个',\n  standardCost: 800,\n  currentCost: 820,\n  supplier: '三星',\n  category: '存储',\n  status: 'active',\n  stockQuantity: 60,\n  minStock: 15,\n  createTime: '2024-01-01'\n}, {\n  id: 'MAT-005',\n  code: 'GPU-RTX4070',\n  name: 'NVIDIA RTX 4070',\n  specification: '12GB GDDR6X 显卡',\n  unit: '个',\n  standardCost: 4500,\n  currentCost: 4600,\n  supplier: '英伟达',\n  category: '显卡',\n  status: 'active',\n  stockQuantity: 25,\n  minStock: 5,\n  createTime: '2024-01-01'\n}, {\n  id: 'MAT-006',\n  code: 'CASE-ATX-001',\n  name: '酷冷至尊 H500P',\n  specification: 'ATX中塔机箱',\n  unit: '个',\n  standardCost: 600,\n  currentCost: 620,\n  supplier: '酷冷至尊',\n  category: '机箱',\n  status: 'active',\n  stockQuantity: 40,\n  minStock: 10,\n  createTime: '2024-01-01'\n}, {\n  id: 'MAT-007',\n  code: 'PSU-850W-80PLUS',\n  name: '海盗船 RM850x',\n  specification: '850W 80PLUS金牌',\n  unit: '个',\n  standardCost: 900,\n  currentCost: 920,\n  supplier: '海盗船',\n  category: '电源',\n  status: 'active',\n  stockQuantity: 35,\n  minStock: 8,\n  createTime: '2024-01-01'\n}];\n\n// 模拟BOM数据\nconst mockBOMs = [{\n  id: 'BOM-001',\n  code: 'BOM-SERVER-001',\n  name: '高性能服务器配置',\n  version: 'V1.0',\n  status: 'active',\n  type: 'product',\n  description: '适用于企业级应用的高性能服务器',\n  totalCost: 12640,\n  createTime: '2024-01-10',\n  updateTime: '2024-01-15',\n  createdBy: '张工程师',\n  items: [{\n    id: 1,\n    materialId: 'MAT-001',\n    materialCode: 'CPU-I7-12700K',\n    materialName: 'Intel Core i7-12700K',\n    specification: '12核20线程 3.6GHz',\n    quantity: 2,\n    unit: '个',\n    unitCost: 2850,\n    totalCost: 5700,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 2,\n    materialId: 'MAT-002',\n    materialCode: 'MB-ASUS-Z690',\n    materialName: 'ASUS ROG STRIX Z690-E',\n    specification: 'ATX主板 LGA1700',\n    quantity: 2,\n    unit: '个',\n    unitCost: 1850,\n    totalCost: 3700,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 3,\n    materialId: 'MAT-003',\n    materialCode: 'RAM-32G-DDR5',\n    materialName: '金士顿 DDR5 32GB',\n    specification: '32GB DDR5-5600 套装',\n    quantity: 2,\n    unit: '套',\n    unitCost: 1250,\n    totalCost: 2500,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 4,\n    materialId: 'MAT-004',\n    materialCode: 'SSD-1TB-NVME',\n    materialName: '三星 980 PRO 1TB',\n    specification: 'NVMe M.2 SSD 1TB',\n    quantity: 1,\n    unit: '个',\n    unitCost: 820,\n    totalCost: 820,\n    level: 1,\n    parentId: null,\n    children: []\n  }]\n}, {\n  id: 'BOM-002',\n  code: 'BOM-WORKSTATION-001',\n  name: '图形工作站配置',\n  version: 'V1.0',\n  status: 'active',\n  type: 'product',\n  description: '适用于设计和渲染的专业工作站',\n  totalCost: 18540,\n  createTime: '2024-01-12',\n  updateTime: '2024-01-18',\n  createdBy: '李工程师',\n  items: [{\n    id: 1,\n    materialId: 'MAT-001',\n    materialCode: 'CPU-I7-12700K',\n    materialName: 'Intel Core i7-12700K',\n    specification: '12核20线程 3.6GHz',\n    quantity: 1,\n    unit: '个',\n    unitCost: 2850,\n    totalCost: 2850,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 2,\n    materialId: 'MAT-002',\n    materialCode: 'MB-ASUS-Z690',\n    materialName: 'ASUS ROG STRIX Z690-E',\n    specification: 'ATX主板 LGA1700',\n    quantity: 1,\n    unit: '个',\n    unitCost: 1850,\n    totalCost: 1850,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 3,\n    materialId: 'MAT-003',\n    materialCode: 'RAM-32G-DDR5',\n    materialName: '金士顿 DDR5 32GB',\n    specification: '32GB DDR5-5600 套装',\n    quantity: 2,\n    unit: '套',\n    unitCost: 1250,\n    totalCost: 2500,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 4,\n    materialId: 'MAT-004',\n    materialCode: 'SSD-1TB-NVME',\n    materialName: '三星 980 PRO 1TB',\n    specification: 'NVMe M.2 SSD 1TB',\n    quantity: 2,\n    unit: '个',\n    unitCost: 820,\n    totalCost: 1640,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 5,\n    materialId: 'MAT-005',\n    materialCode: 'GPU-RTX4070',\n    materialName: 'NVIDIA RTX 4070',\n    specification: '12GB GDDR6X 显卡',\n    quantity: 2,\n    unit: '个',\n    unitCost: 4600,\n    totalCost: 9200,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 6,\n    materialId: 'MAT-006',\n    materialCode: 'CASE-ATX-001',\n    materialName: '酷冷至尊 H500P',\n    specification: 'ATX中塔机箱',\n    quantity: 1,\n    unit: '个',\n    unitCost: 620,\n    totalCost: 620,\n    level: 1,\n    parentId: null,\n    children: []\n  }]\n}, {\n  id: 'BOM-003',\n  code: 'BOM-BASIC-PC-001',\n  name: '基础办公电脑配置',\n  version: 'V1.0',\n  status: 'active',\n  type: 'product',\n  description: '适用于日常办公的基础配置',\n  totalCost: 6790,\n  createTime: '2024-01-15',\n  updateTime: '2024-01-20',\n  createdBy: '王工程师',\n  items: [{\n    id: 1,\n    materialId: 'MAT-001',\n    materialCode: 'CPU-I7-12700K',\n    materialName: 'Intel Core i7-12700K',\n    specification: '12核20线程 3.6GHz',\n    quantity: 1,\n    unit: '个',\n    unitCost: 2850,\n    totalCost: 2850,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 2,\n    materialId: 'MAT-002',\n    materialCode: 'MB-ASUS-Z690',\n    materialName: 'ASUS ROG STRIX Z690-E',\n    specification: 'ATX主板 LGA1700',\n    quantity: 1,\n    unit: '个',\n    unitCost: 1850,\n    totalCost: 1850,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 3,\n    materialId: 'MAT-003',\n    materialCode: 'RAM-32G-DDR5',\n    materialName: '金士顿 DDR5 32GB',\n    specification: '32GB DDR5-5600 套装',\n    quantity: 1,\n    unit: '套',\n    unitCost: 1250,\n    totalCost: 1250,\n    level: 1,\n    parentId: null,\n    children: []\n  }, {\n    id: 4,\n    materialId: 'MAT-004',\n    materialCode: 'SSD-1TB-NVME',\n    materialName: '三星 980 PRO 1TB',\n    specification: 'NVMe M.2 SSD 1TB',\n    quantity: 1,\n    unit: '个',\n    unitCost: 820,\n    totalCost: 820,\n    level: 1,\n    parentId: null,\n    children: []\n  }]\n}];\nconst initialState = {\n  boms: mockBOMs,\n  materials: mockMaterials,\n  currentBOM: null,\n  currentMaterial: null,\n  loading: false,\n  error: null,\n  searchKeyword: '',\n  filters: {\n    status: '',\n    type: '',\n    category: ''\n  },\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: mockBOMs.length\n  }\n};\nconst bomSlice = createSlice({\n  name: 'bom',\n  initialState,\n  reducers: {\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    setBOMs: (state, action) => {\n      state.boms = action.payload;\n      state.pagination.total = action.payload.length;\n    },\n    setMaterials: (state, action) => {\n      state.materials = action.payload;\n    },\n    addBOM: (state, action) => {\n      const newBOM = {\n        ...action.payload,\n        id: `BOM-${String(state.boms.length + 1).padStart(3, '0')}`,\n        createTime: new Date().toISOString().split('T')[0],\n        updateTime: new Date().toISOString().split('T')[0],\n        status: 'active'\n      };\n      state.boms.unshift(newBOM);\n      state.pagination.total = state.boms.length;\n    },\n    updateBOM: (state, action) => {\n      const index = state.boms.findIndex(b => b.id === action.payload.id);\n      if (index !== -1) {\n        state.boms[index] = {\n          ...state.boms[index],\n          ...action.payload,\n          updateTime: new Date().toISOString().split('T')[0]\n        };\n      }\n    },\n    deleteBOM: (state, action) => {\n      state.boms = state.boms.filter(b => b.id !== action.payload);\n      state.pagination.total = state.boms.length;\n    },\n    addMaterial: (state, action) => {\n      const newMaterial = {\n        ...action.payload,\n        id: `MAT-${String(state.materials.length + 1).padStart(3, '0')}`,\n        createTime: new Date().toISOString().split('T')[0],\n        status: 'active'\n      };\n      state.materials.unshift(newMaterial);\n    },\n    updateMaterial: (state, action) => {\n      const index = state.materials.findIndex(m => m.id === action.payload.id);\n      if (index !== -1) {\n        state.materials[index] = {\n          ...state.materials[index],\n          ...action.payload\n        };\n      }\n    },\n    deleteMaterial: (state, action) => {\n      state.materials = state.materials.filter(m => m.id !== action.payload);\n    },\n    setCurrentBOM: (state, action) => {\n      state.currentBOM = action.payload;\n    },\n    setCurrentMaterial: (state, action) => {\n      state.currentMaterial = action.payload;\n    },\n    setSearchKeyword: (state, action) => {\n      state.searchKeyword = action.payload;\n    },\n    setFilters: (state, action) => {\n      state.filters = {\n        ...state.filters,\n        ...action.payload\n      };\n    },\n    setPagination: (state, action) => {\n      state.pagination = {\n        ...state.pagination,\n        ...action.payload\n      };\n    },\n    resetFilters: state => {\n      state.searchKeyword = '';\n      state.filters = {\n        status: '',\n        type: '',\n        category: ''\n      };\n      state.pagination.current = 1;\n    },\n    calculateBOMCost: (state, action) => {\n      const {\n        bomId\n      } = action.payload;\n      const bom = state.boms.find(b => b.id === bomId);\n      if (bom) {\n        bom.totalCost = bom.items.reduce((sum, item) => sum + item.totalCost, 0);\n      }\n    }\n  }\n});\nexport const {\n  setLoading,\n  setError,\n  clearError,\n  setBOMs,\n  setMaterials,\n  addBOM,\n  updateBOM,\n  deleteBOM,\n  addMaterial,\n  updateMaterial,\n  deleteMaterial,\n  setCurrentBOM,\n  setCurrentMaterial,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters,\n  calculateBOMCost\n} = bomSlice.actions;\n\n// 选择器\nexport const selectBOMs = state => state.bom.boms;\nexport const selectMaterials = state => state.bom.materials;\nexport const selectCurrentBOM = state => state.bom.currentBOM;\nexport const selectCurrentMaterial = state => state.bom.currentMaterial;\nexport const selectBOMLoading = state => state.bom.loading;\nexport const selectBOMError = state => state.bom.error;\nexport const selectBOMFilters = state => state.bom.filters;\nexport const selectBOMPagination = state => state.bom.pagination;\n\n// 过滤后的BOM列表\nexport const selectFilteredBOMs = state => {\n  const {\n    boms,\n    searchKeyword,\n    filters\n  } = state.bom;\n  return boms.filter(bom => {\n    // 搜索关键词过滤\n    if (searchKeyword) {\n      const keyword = searchKeyword.toLowerCase();\n      if (!bom.code.toLowerCase().includes(keyword) && !bom.name.toLowerCase().includes(keyword) && !bom.description.toLowerCase().includes(keyword)) {\n        return false;\n      }\n    }\n\n    // 状态过滤\n    if (filters.status && bom.status !== filters.status) {\n      return false;\n    }\n\n    // 类型过滤\n    if (filters.type && bom.type !== filters.type) {\n      return false;\n    }\n    return true;\n  });\n};\n\n// 过滤后的物料列表\nexport const selectFilteredMaterials = state => {\n  const {\n    materials,\n    searchKeyword,\n    filters\n  } = state.bom;\n  return materials.filter(material => {\n    // 搜索关键词过滤\n    if (searchKeyword) {\n      const keyword = searchKeyword.toLowerCase();\n      if (!material.code.toLowerCase().includes(keyword) && !material.name.toLowerCase().includes(keyword) && !material.specification.toLowerCase().includes(keyword)) {\n        return false;\n      }\n    }\n\n    // 分类过滤\n    if (filters.category && material.category !== filters.category) {\n      return false;\n    }\n\n    // 状态过滤\n    if (filters.status && material.status !== filters.status) {\n      return false;\n    }\n    return true;\n  });\n};\nexport default bomSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "mockMaterials", "id", "code", "name", "specification", "unit", "standardCost", "currentCost", "supplier", "category", "status", "stockQuantity", "minStock", "createTime", "mockBOMs", "version", "type", "description", "totalCost", "updateTime", "created<PERSON>y", "items", "materialId", "materialCode", "materialName", "quantity", "unitCost", "level", "parentId", "children", "initialState", "boms", "materials", "currentBOM", "currentMaterial", "loading", "error", "searchKeyword", "filters", "pagination", "current", "pageSize", "total", "length", "bomSlice", "reducers", "setLoading", "state", "action", "payload", "setError", "clearError", "setBOMs", "setMaterials", "addBOM", "newBOM", "String", "padStart", "Date", "toISOString", "split", "unshift", "updateBOM", "index", "findIndex", "b", "deleteBOM", "filter", "addMaterial", "newMaterial", "updateMaterial", "m", "deleteMaterial", "setCurrentBOM", "setCurrentMaterial", "setSearchKeyword", "setFilters", "setPagination", "resetFilters", "calculateBOMCost", "bomId", "bom", "find", "reduce", "sum", "item", "actions", "selectBOMs", "selectMaterials", "selectCurrentBOM", "selectCurrentMaterial", "selectBOMLoading", "selectBOMError", "selectBOMFilters", "selectBOMPagination", "selectFilteredBOMs", "keyword", "toLowerCase", "includes", "selectFilteredMaterials", "material", "reducer"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/store/slices/bomSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n// 模拟物料数据\nconst mockMaterials = [\n  {\n    id: 'MAT-001',\n    code: 'CPU-I7-12700K',\n    name: 'Intel Core i7-12700K',\n    specification: '12核20线程 3.6GHz',\n    unit: '个',\n    standardCost: 2800,\n    currentCost: 2850,\n    supplier: '英特尔',\n    category: 'CPU',\n    status: 'active',\n    stockQuantity: 50,\n    minStock: 10,\n    createTime: '2024-01-01'\n  },\n  {\n    id: 'MAT-002',\n    code: 'MB-ASUS-Z690',\n    name: 'ASUS ROG STRIX Z690-E',\n    specification: 'ATX主板 LGA1700',\n    unit: '个',\n    standardCost: 1800,\n    currentCost: 1850,\n    supplier: '华硕',\n    category: '主板',\n    status: 'active',\n    stockQuantity: 30,\n    minStock: 5,\n    createTime: '2024-01-01'\n  },\n  {\n    id: 'MAT-003',\n    code: 'RAM-32G-DDR5',\n    name: '金士顿 DDR5 32GB',\n    specification: '32GB DDR5-5600 套装',\n    unit: '套',\n    standardCost: 1200,\n    currentCost: 1250,\n    supplier: '金士顿',\n    category: '内存',\n    status: 'active',\n    stockQuantity: 80,\n    minStock: 20,\n    createTime: '2024-01-01'\n  },\n  {\n    id: 'MAT-004',\n    code: 'SSD-1TB-NVME',\n    name: '三星 980 PRO 1TB',\n    specification: 'NVMe M.2 SSD 1TB',\n    unit: '个',\n    standardCost: 800,\n    currentCost: 820,\n    supplier: '三星',\n    category: '存储',\n    status: 'active',\n    stockQuantity: 60,\n    minStock: 15,\n    createTime: '2024-01-01'\n  },\n  {\n    id: 'MAT-005',\n    code: 'GPU-RTX4070',\n    name: 'NVIDIA RTX 4070',\n    specification: '12GB GDDR6X 显卡',\n    unit: '个',\n    standardCost: 4500,\n    currentCost: 4600,\n    supplier: '英伟达',\n    category: '显卡',\n    status: 'active',\n    stockQuantity: 25,\n    minStock: 5,\n    createTime: '2024-01-01'\n  },\n  {\n    id: 'MAT-006',\n    code: 'CASE-ATX-001',\n    name: '酷冷至尊 H500P',\n    specification: 'ATX中塔机箱',\n    unit: '个',\n    standardCost: 600,\n    currentCost: 620,\n    supplier: '酷冷至尊',\n    category: '机箱',\n    status: 'active',\n    stockQuantity: 40,\n    minStock: 10,\n    createTime: '2024-01-01'\n  },\n  {\n    id: 'MAT-007',\n    code: 'PSU-850W-80PLUS',\n    name: '海盗船 RM850x',\n    specification: '850W 80PLUS金牌',\n    unit: '个',\n    standardCost: 900,\n    currentCost: 920,\n    supplier: '海盗船',\n    category: '电源',\n    status: 'active',\n    stockQuantity: 35,\n    minStock: 8,\n    createTime: '2024-01-01'\n  }\n];\n\n// 模拟BOM数据\nconst mockBOMs = [\n  {\n    id: 'BOM-001',\n    code: 'BOM-SERVER-001',\n    name: '高性能服务器配置',\n    version: 'V1.0',\n    status: 'active',\n    type: 'product',\n    description: '适用于企业级应用的高性能服务器',\n    totalCost: 12640,\n    createTime: '2024-01-10',\n    updateTime: '2024-01-15',\n    createdBy: '张工程师',\n    items: [\n      {\n        id: 1,\n        materialId: 'MAT-001',\n        materialCode: 'CPU-I7-12700K',\n        materialName: 'Intel Core i7-12700K',\n        specification: '12核20线程 3.6GHz',\n        quantity: 2,\n        unit: '个',\n        unitCost: 2850,\n        totalCost: 5700,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 2,\n        materialId: 'MAT-002',\n        materialCode: 'MB-ASUS-Z690',\n        materialName: 'ASUS ROG STRIX Z690-E',\n        specification: 'ATX主板 LGA1700',\n        quantity: 2,\n        unit: '个',\n        unitCost: 1850,\n        totalCost: 3700,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 3,\n        materialId: 'MAT-003',\n        materialCode: 'RAM-32G-DDR5',\n        materialName: '金士顿 DDR5 32GB',\n        specification: '32GB DDR5-5600 套装',\n        quantity: 2,\n        unit: '套',\n        unitCost: 1250,\n        totalCost: 2500,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 4,\n        materialId: 'MAT-004',\n        materialCode: 'SSD-1TB-NVME',\n        materialName: '三星 980 PRO 1TB',\n        specification: 'NVMe M.2 SSD 1TB',\n        quantity: 1,\n        unit: '个',\n        unitCost: 820,\n        totalCost: 820,\n        level: 1,\n        parentId: null,\n        children: []\n      }\n    ]\n  },\n  {\n    id: 'BOM-002',\n    code: 'BOM-WORKSTATION-001',\n    name: '图形工作站配置',\n    version: 'V1.0',\n    status: 'active',\n    type: 'product',\n    description: '适用于设计和渲染的专业工作站',\n    totalCost: 18540,\n    createTime: '2024-01-12',\n    updateTime: '2024-01-18',\n    createdBy: '李工程师',\n    items: [\n      {\n        id: 1,\n        materialId: 'MAT-001',\n        materialCode: 'CPU-I7-12700K',\n        materialName: 'Intel Core i7-12700K',\n        specification: '12核20线程 3.6GHz',\n        quantity: 1,\n        unit: '个',\n        unitCost: 2850,\n        totalCost: 2850,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 2,\n        materialId: 'MAT-002',\n        materialCode: 'MB-ASUS-Z690',\n        materialName: 'ASUS ROG STRIX Z690-E',\n        specification: 'ATX主板 LGA1700',\n        quantity: 1,\n        unit: '个',\n        unitCost: 1850,\n        totalCost: 1850,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 3,\n        materialId: 'MAT-003',\n        materialCode: 'RAM-32G-DDR5',\n        materialName: '金士顿 DDR5 32GB',\n        specification: '32GB DDR5-5600 套装',\n        quantity: 2,\n        unit: '套',\n        unitCost: 1250,\n        totalCost: 2500,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 4,\n        materialId: 'MAT-004',\n        materialCode: 'SSD-1TB-NVME',\n        materialName: '三星 980 PRO 1TB',\n        specification: 'NVMe M.2 SSD 1TB',\n        quantity: 2,\n        unit: '个',\n        unitCost: 820,\n        totalCost: 1640,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 5,\n        materialId: 'MAT-005',\n        materialCode: 'GPU-RTX4070',\n        materialName: 'NVIDIA RTX 4070',\n        specification: '12GB GDDR6X 显卡',\n        quantity: 2,\n        unit: '个',\n        unitCost: 4600,\n        totalCost: 9200,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 6,\n        materialId: 'MAT-006',\n        materialCode: 'CASE-ATX-001',\n        materialName: '酷冷至尊 H500P',\n        specification: 'ATX中塔机箱',\n        quantity: 1,\n        unit: '个',\n        unitCost: 620,\n        totalCost: 620,\n        level: 1,\n        parentId: null,\n        children: []\n      }\n    ]\n  },\n  {\n    id: 'BOM-003',\n    code: 'BOM-BASIC-PC-001',\n    name: '基础办公电脑配置',\n    version: 'V1.0',\n    status: 'active',\n    type: 'product',\n    description: '适用于日常办公的基础配置',\n    totalCost: 6790,\n    createTime: '2024-01-15',\n    updateTime: '2024-01-20',\n    createdBy: '王工程师',\n    items: [\n      {\n        id: 1,\n        materialId: 'MAT-001',\n        materialCode: 'CPU-I7-12700K',\n        materialName: 'Intel Core i7-12700K',\n        specification: '12核20线程 3.6GHz',\n        quantity: 1,\n        unit: '个',\n        unitCost: 2850,\n        totalCost: 2850,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 2,\n        materialId: 'MAT-002',\n        materialCode: 'MB-ASUS-Z690',\n        materialName: 'ASUS ROG STRIX Z690-E',\n        specification: 'ATX主板 LGA1700',\n        quantity: 1,\n        unit: '个',\n        unitCost: 1850,\n        totalCost: 1850,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 3,\n        materialId: 'MAT-003',\n        materialCode: 'RAM-32G-DDR5',\n        materialName: '金士顿 DDR5 32GB',\n        specification: '32GB DDR5-5600 套装',\n        quantity: 1,\n        unit: '套',\n        unitCost: 1250,\n        totalCost: 1250,\n        level: 1,\n        parentId: null,\n        children: []\n      },\n      {\n        id: 4,\n        materialId: 'MAT-004',\n        materialCode: 'SSD-1TB-NVME',\n        materialName: '三星 980 PRO 1TB',\n        specification: 'NVMe M.2 SSD 1TB',\n        quantity: 1,\n        unit: '个',\n        unitCost: 820,\n        totalCost: 820,\n        level: 1,\n        parentId: null,\n        children: []\n      }\n    ]\n  }\n];\n\nconst initialState = {\n  boms: mockBOMs,\n  materials: mockMaterials,\n  currentBOM: null,\n  currentMaterial: null,\n  loading: false,\n  error: null,\n  searchKeyword: '',\n  filters: {\n    status: '',\n    type: '',\n    category: ''\n  },\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: mockBOMs.length\n  }\n};\n\nconst bomSlice = createSlice({\n  name: 'bom',\n  initialState,\n  reducers: {\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setBOMs: (state, action) => {\n      state.boms = action.payload;\n      state.pagination.total = action.payload.length;\n    },\n    setMaterials: (state, action) => {\n      state.materials = action.payload;\n    },\n    addBOM: (state, action) => {\n      const newBOM = {\n        ...action.payload,\n        id: `BOM-${String(state.boms.length + 1).padStart(3, '0')}`,\n        createTime: new Date().toISOString().split('T')[0],\n        updateTime: new Date().toISOString().split('T')[0],\n        status: 'active'\n      };\n      state.boms.unshift(newBOM);\n      state.pagination.total = state.boms.length;\n    },\n    updateBOM: (state, action) => {\n      const index = state.boms.findIndex(b => b.id === action.payload.id);\n      if (index !== -1) {\n        state.boms[index] = { \n          ...state.boms[index], \n          ...action.payload,\n          updateTime: new Date().toISOString().split('T')[0]\n        };\n      }\n    },\n    deleteBOM: (state, action) => {\n      state.boms = state.boms.filter(b => b.id !== action.payload);\n      state.pagination.total = state.boms.length;\n    },\n    addMaterial: (state, action) => {\n      const newMaterial = {\n        ...action.payload,\n        id: `MAT-${String(state.materials.length + 1).padStart(3, '0')}`,\n        createTime: new Date().toISOString().split('T')[0],\n        status: 'active'\n      };\n      state.materials.unshift(newMaterial);\n    },\n    updateMaterial: (state, action) => {\n      const index = state.materials.findIndex(m => m.id === action.payload.id);\n      if (index !== -1) {\n        state.materials[index] = { ...state.materials[index], ...action.payload };\n      }\n    },\n    deleteMaterial: (state, action) => {\n      state.materials = state.materials.filter(m => m.id !== action.payload);\n    },\n    setCurrentBOM: (state, action) => {\n      state.currentBOM = action.payload;\n    },\n    setCurrentMaterial: (state, action) => {\n      state.currentMaterial = action.payload;\n    },\n    setSearchKeyword: (state, action) => {\n      state.searchKeyword = action.payload;\n    },\n    setFilters: (state, action) => {\n      state.filters = { ...state.filters, ...action.payload };\n    },\n    setPagination: (state, action) => {\n      state.pagination = { ...state.pagination, ...action.payload };\n    },\n    resetFilters: (state) => {\n      state.searchKeyword = '';\n      state.filters = {\n        status: '',\n        type: '',\n        category: ''\n      };\n      state.pagination.current = 1;\n    },\n    calculateBOMCost: (state, action) => {\n      const { bomId } = action.payload;\n      const bom = state.boms.find(b => b.id === bomId);\n      if (bom) {\n        bom.totalCost = bom.items.reduce((sum, item) => sum + item.totalCost, 0);\n      }\n    }\n  }\n});\n\nexport const {\n  setLoading,\n  setError,\n  clearError,\n  setBOMs,\n  setMaterials,\n  addBOM,\n  updateBOM,\n  deleteBOM,\n  addMaterial,\n  updateMaterial,\n  deleteMaterial,\n  setCurrentBOM,\n  setCurrentMaterial,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters,\n  calculateBOMCost\n} = bomSlice.actions;\n\n// 选择器\nexport const selectBOMs = (state) => state.bom.boms;\nexport const selectMaterials = (state) => state.bom.materials;\nexport const selectCurrentBOM = (state) => state.bom.currentBOM;\nexport const selectCurrentMaterial = (state) => state.bom.currentMaterial;\nexport const selectBOMLoading = (state) => state.bom.loading;\nexport const selectBOMError = (state) => state.bom.error;\nexport const selectBOMFilters = (state) => state.bom.filters;\nexport const selectBOMPagination = (state) => state.bom.pagination;\n\n// 过滤后的BOM列表\nexport const selectFilteredBOMs = (state) => {\n  const { boms, searchKeyword, filters } = state.bom;\n  \n  return boms.filter(bom => {\n    // 搜索关键词过滤\n    if (searchKeyword) {\n      const keyword = searchKeyword.toLowerCase();\n      if (!bom.code.toLowerCase().includes(keyword) &&\n          !bom.name.toLowerCase().includes(keyword) &&\n          !bom.description.toLowerCase().includes(keyword)) {\n        return false;\n      }\n    }\n    \n    // 状态过滤\n    if (filters.status && bom.status !== filters.status) {\n      return false;\n    }\n    \n    // 类型过滤\n    if (filters.type && bom.type !== filters.type) {\n      return false;\n    }\n    \n    return true;\n  });\n};\n\n// 过滤后的物料列表\nexport const selectFilteredMaterials = (state) => {\n  const { materials, searchKeyword, filters } = state.bom;\n  \n  return materials.filter(material => {\n    // 搜索关键词过滤\n    if (searchKeyword) {\n      const keyword = searchKeyword.toLowerCase();\n      if (!material.code.toLowerCase().includes(keyword) &&\n          !material.name.toLowerCase().includes(keyword) &&\n          !material.specification.toLowerCase().includes(keyword)) {\n        return false;\n      }\n    }\n    \n    // 分类过滤\n    if (filters.category && material.category !== filters.category) {\n      return false;\n    }\n    \n    // 状态过滤\n    if (filters.status && material.status !== filters.status) {\n      return false;\n    }\n    \n    return true;\n  });\n};\n\nexport default bomSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA,MAAMC,aAAa,GAAG,CACpB;EACEC,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,sBAAsB;EAC5BC,aAAa,EAAE,gBAAgB;EAC/BC,IAAI,EAAE,GAAG;EACTC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE;AACd,CAAC,EACD;EACEZ,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,uBAAuB;EAC7BC,aAAa,EAAE,eAAe;EAC9BC,IAAI,EAAE,GAAG;EACTC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE;AACd,CAAC,EACD;EACEZ,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,eAAe;EACrBC,aAAa,EAAE,mBAAmB;EAClCC,IAAI,EAAE,GAAG;EACTC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE;AACd,CAAC,EACD;EACEZ,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAE,kBAAkB;EACjCC,IAAI,EAAE,GAAG;EACTC,YAAY,EAAE,GAAG;EACjBC,WAAW,EAAE,GAAG;EAChBC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE;AACd,CAAC,EACD;EACEZ,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,gBAAgB;EAC/BC,IAAI,EAAE,GAAG;EACTC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE;AACd,CAAC,EACD;EACEZ,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,YAAY;EAClBC,aAAa,EAAE,SAAS;EACxBC,IAAI,EAAE,GAAG;EACTC,YAAY,EAAE,GAAG;EACjBC,WAAW,EAAE,GAAG;EAChBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE;AACd,CAAC,EACD;EACEZ,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,YAAY;EAClBC,aAAa,EAAE,eAAe;EAC9BC,IAAI,EAAE,GAAG;EACTC,YAAY,EAAE,GAAG;EACjBC,WAAW,EAAE,GAAG;EAChBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE;AACd,CAAC,CACF;;AAED;AACA,MAAMC,QAAQ,GAAG,CACf;EACEb,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,UAAU;EAChBY,OAAO,EAAE,MAAM;EACfL,MAAM,EAAE,QAAQ;EAChBM,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,iBAAiB;EAC9BC,SAAS,EAAE,KAAK;EAChBL,UAAU,EAAE,YAAY;EACxBM,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE,MAAM;EACjBC,KAAK,EAAE,CACL;IACEpB,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,sBAAsB;IACpCpB,aAAa,EAAE,gBAAgB;IAC/BqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,uBAAuB;IACrCpB,aAAa,EAAE,eAAe;IAC9BqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,eAAe;IAC7BpB,aAAa,EAAE,mBAAmB;IAClCqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,gBAAgB;IAC9BpB,aAAa,EAAE,kBAAkB;IACjCqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,GAAG;IACbR,SAAS,EAAE,GAAG;IACdS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC;AAEL,CAAC,EACD;EACE5B,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,SAAS;EACfY,OAAO,EAAE,MAAM;EACfL,MAAM,EAAE,QAAQ;EAChBM,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,gBAAgB;EAC7BC,SAAS,EAAE,KAAK;EAChBL,UAAU,EAAE,YAAY;EACxBM,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE,MAAM;EACjBC,KAAK,EAAE,CACL;IACEpB,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,sBAAsB;IACpCpB,aAAa,EAAE,gBAAgB;IAC/BqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,uBAAuB;IACrCpB,aAAa,EAAE,eAAe;IAC9BqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,eAAe;IAC7BpB,aAAa,EAAE,mBAAmB;IAClCqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,gBAAgB;IAC9BpB,aAAa,EAAE,kBAAkB;IACjCqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,GAAG;IACbR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,aAAa;IAC3BC,YAAY,EAAE,iBAAiB;IAC/BpB,aAAa,EAAE,gBAAgB;IAC/BqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,YAAY;IAC1BpB,aAAa,EAAE,SAAS;IACxBqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,GAAG;IACbR,SAAS,EAAE,GAAG;IACdS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC;AAEL,CAAC,EACD;EACE5B,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,UAAU;EAChBY,OAAO,EAAE,MAAM;EACfL,MAAM,EAAE,QAAQ;EAChBM,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,cAAc;EAC3BC,SAAS,EAAE,IAAI;EACfL,UAAU,EAAE,YAAY;EACxBM,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE,MAAM;EACjBC,KAAK,EAAE,CACL;IACEpB,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,sBAAsB;IACpCpB,aAAa,EAAE,gBAAgB;IAC/BqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,uBAAuB;IACrCpB,aAAa,EAAE,eAAe;IAC9BqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,eAAe;IAC7BpB,aAAa,EAAE,mBAAmB;IAClCqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,IAAI;IACdR,SAAS,EAAE,IAAI;IACfS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE5B,EAAE,EAAE,CAAC;IACLqB,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,gBAAgB;IAC9BpB,aAAa,EAAE,kBAAkB;IACjCqB,QAAQ,EAAE,CAAC;IACXpB,IAAI,EAAE,GAAG;IACTqB,QAAQ,EAAE,GAAG;IACbR,SAAS,EAAE,GAAG;IACdS,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC;AAEL,CAAC,CACF;AAED,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAEjB,QAAQ;EACdkB,SAAS,EAAEhC,aAAa;EACxBiC,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE,IAAI;EACrBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,aAAa,EAAE,EAAE;EACjBC,OAAO,EAAE;IACP5B,MAAM,EAAE,EAAE;IACVM,IAAI,EAAE,EAAE;IACRP,QAAQ,EAAE;EACZ,CAAC;EACD8B,UAAU,EAAE;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE5B,QAAQ,CAAC6B;EAClB;AACF,CAAC;AAED,MAAMC,QAAQ,GAAG7C,WAAW,CAAC;EAC3BI,IAAI,EAAE,KAAK;EACX2B,YAAY;EACZe,QAAQ,EAAE;IACRC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACZ,OAAO,GAAGa,MAAM,CAACC,OAAO;IAChC,CAAC;IACDC,QAAQ,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACX,KAAK,GAAGY,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDE,UAAU,EAAGJ,KAAK,IAAK;MACrBA,KAAK,CAACX,KAAK,GAAG,IAAI;IACpB,CAAC;IACDgB,OAAO,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MAC1BD,KAAK,CAAChB,IAAI,GAAGiB,MAAM,CAACC,OAAO;MAC3BF,KAAK,CAACR,UAAU,CAACG,KAAK,GAAGM,MAAM,CAACC,OAAO,CAACN,MAAM;IAChD,CAAC;IACDU,YAAY,EAAEA,CAACN,KAAK,EAAEC,MAAM,KAAK;MAC/BD,KAAK,CAACf,SAAS,GAAGgB,MAAM,CAACC,OAAO;IAClC,CAAC;IACDK,MAAM,EAAEA,CAACP,KAAK,EAAEC,MAAM,KAAK;MACzB,MAAMO,MAAM,GAAG;QACb,GAAGP,MAAM,CAACC,OAAO;QACjBhD,EAAE,EAAE,OAAOuD,MAAM,CAACT,KAAK,CAAChB,IAAI,CAACY,MAAM,GAAG,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC3D5C,UAAU,EAAE,IAAI6C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDzC,UAAU,EAAE,IAAIuC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDlD,MAAM,EAAE;MACV,CAAC;MACDqC,KAAK,CAAChB,IAAI,CAAC8B,OAAO,CAACN,MAAM,CAAC;MAC1BR,KAAK,CAACR,UAAU,CAACG,KAAK,GAAGK,KAAK,CAAChB,IAAI,CAACY,MAAM;IAC5C,CAAC;IACDmB,SAAS,EAAEA,CAACf,KAAK,EAAEC,MAAM,KAAK;MAC5B,MAAMe,KAAK,GAAGhB,KAAK,CAAChB,IAAI,CAACiC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAChE,EAAE,KAAK+C,MAAM,CAACC,OAAO,CAAChD,EAAE,CAAC;MACnE,IAAI8D,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBhB,KAAK,CAAChB,IAAI,CAACgC,KAAK,CAAC,GAAG;UAClB,GAAGhB,KAAK,CAAChB,IAAI,CAACgC,KAAK,CAAC;UACpB,GAAGf,MAAM,CAACC,OAAO;UACjB9B,UAAU,EAAE,IAAIuC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACnD,CAAC;MACH;IACF,CAAC;IACDM,SAAS,EAAEA,CAACnB,KAAK,EAAEC,MAAM,KAAK;MAC5BD,KAAK,CAAChB,IAAI,GAAGgB,KAAK,CAAChB,IAAI,CAACoC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAChE,EAAE,KAAK+C,MAAM,CAACC,OAAO,CAAC;MAC5DF,KAAK,CAACR,UAAU,CAACG,KAAK,GAAGK,KAAK,CAAChB,IAAI,CAACY,MAAM;IAC5C,CAAC;IACDyB,WAAW,EAAEA,CAACrB,KAAK,EAAEC,MAAM,KAAK;MAC9B,MAAMqB,WAAW,GAAG;QAClB,GAAGrB,MAAM,CAACC,OAAO;QACjBhD,EAAE,EAAE,OAAOuD,MAAM,CAACT,KAAK,CAACf,SAAS,CAACW,MAAM,GAAG,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAChE5C,UAAU,EAAE,IAAI6C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDlD,MAAM,EAAE;MACV,CAAC;MACDqC,KAAK,CAACf,SAAS,CAAC6B,OAAO,CAACQ,WAAW,CAAC;IACtC,CAAC;IACDC,cAAc,EAAEA,CAACvB,KAAK,EAAEC,MAAM,KAAK;MACjC,MAAMe,KAAK,GAAGhB,KAAK,CAACf,SAAS,CAACgC,SAAS,CAACO,CAAC,IAAIA,CAAC,CAACtE,EAAE,KAAK+C,MAAM,CAACC,OAAO,CAAChD,EAAE,CAAC;MACxE,IAAI8D,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBhB,KAAK,CAACf,SAAS,CAAC+B,KAAK,CAAC,GAAG;UAAE,GAAGhB,KAAK,CAACf,SAAS,CAAC+B,KAAK,CAAC;UAAE,GAAGf,MAAM,CAACC;QAAQ,CAAC;MAC3E;IACF,CAAC;IACDuB,cAAc,EAAEA,CAACzB,KAAK,EAAEC,MAAM,KAAK;MACjCD,KAAK,CAACf,SAAS,GAAGe,KAAK,CAACf,SAAS,CAACmC,MAAM,CAACI,CAAC,IAAIA,CAAC,CAACtE,EAAE,KAAK+C,MAAM,CAACC,OAAO,CAAC;IACxE,CAAC;IACDwB,aAAa,EAAEA,CAAC1B,KAAK,EAAEC,MAAM,KAAK;MAChCD,KAAK,CAACd,UAAU,GAAGe,MAAM,CAACC,OAAO;IACnC,CAAC;IACDyB,kBAAkB,EAAEA,CAAC3B,KAAK,EAAEC,MAAM,KAAK;MACrCD,KAAK,CAACb,eAAe,GAAGc,MAAM,CAACC,OAAO;IACxC,CAAC;IACD0B,gBAAgB,EAAEA,CAAC5B,KAAK,EAAEC,MAAM,KAAK;MACnCD,KAAK,CAACV,aAAa,GAAGW,MAAM,CAACC,OAAO;IACtC,CAAC;IACD2B,UAAU,EAAEA,CAAC7B,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACT,OAAO,GAAG;QAAE,GAAGS,KAAK,CAACT,OAAO;QAAE,GAAGU,MAAM,CAACC;MAAQ,CAAC;IACzD,CAAC;IACD4B,aAAa,EAAEA,CAAC9B,KAAK,EAAEC,MAAM,KAAK;MAChCD,KAAK,CAACR,UAAU,GAAG;QAAE,GAAGQ,KAAK,CAACR,UAAU;QAAE,GAAGS,MAAM,CAACC;MAAQ,CAAC;IAC/D,CAAC;IACD6B,YAAY,EAAG/B,KAAK,IAAK;MACvBA,KAAK,CAACV,aAAa,GAAG,EAAE;MACxBU,KAAK,CAACT,OAAO,GAAG;QACd5B,MAAM,EAAE,EAAE;QACVM,IAAI,EAAE,EAAE;QACRP,QAAQ,EAAE;MACZ,CAAC;MACDsC,KAAK,CAACR,UAAU,CAACC,OAAO,GAAG,CAAC;IAC9B,CAAC;IACDuC,gBAAgB,EAAEA,CAAChC,KAAK,EAAEC,MAAM,KAAK;MACnC,MAAM;QAAEgC;MAAM,CAAC,GAAGhC,MAAM,CAACC,OAAO;MAChC,MAAMgC,GAAG,GAAGlC,KAAK,CAAChB,IAAI,CAACmD,IAAI,CAACjB,CAAC,IAAIA,CAAC,CAAChE,EAAE,KAAK+E,KAAK,CAAC;MAChD,IAAIC,GAAG,EAAE;QACPA,GAAG,CAAC/D,SAAS,GAAG+D,GAAG,CAAC5D,KAAK,CAAC8D,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACnE,SAAS,EAAE,CAAC,CAAC;MAC1E;IACF;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACX4B,UAAU;EACVI,QAAQ;EACRC,UAAU;EACVC,OAAO;EACPC,YAAY;EACZC,MAAM;EACNQ,SAAS;EACTI,SAAS;EACTE,WAAW;EACXE,cAAc;EACdE,cAAc;EACdC,aAAa;EACbC,kBAAkB;EAClBC,gBAAgB;EAChBC,UAAU;EACVC,aAAa;EACbC,YAAY;EACZC;AACF,CAAC,GAAGnC,QAAQ,CAAC0C,OAAO;;AAEpB;AACA,OAAO,MAAMC,UAAU,GAAIxC,KAAK,IAAKA,KAAK,CAACkC,GAAG,CAAClD,IAAI;AACnD,OAAO,MAAMyD,eAAe,GAAIzC,KAAK,IAAKA,KAAK,CAACkC,GAAG,CAACjD,SAAS;AAC7D,OAAO,MAAMyD,gBAAgB,GAAI1C,KAAK,IAAKA,KAAK,CAACkC,GAAG,CAAChD,UAAU;AAC/D,OAAO,MAAMyD,qBAAqB,GAAI3C,KAAK,IAAKA,KAAK,CAACkC,GAAG,CAAC/C,eAAe;AACzE,OAAO,MAAMyD,gBAAgB,GAAI5C,KAAK,IAAKA,KAAK,CAACkC,GAAG,CAAC9C,OAAO;AAC5D,OAAO,MAAMyD,cAAc,GAAI7C,KAAK,IAAKA,KAAK,CAACkC,GAAG,CAAC7C,KAAK;AACxD,OAAO,MAAMyD,gBAAgB,GAAI9C,KAAK,IAAKA,KAAK,CAACkC,GAAG,CAAC3C,OAAO;AAC5D,OAAO,MAAMwD,mBAAmB,GAAI/C,KAAK,IAAKA,KAAK,CAACkC,GAAG,CAAC1C,UAAU;;AAElE;AACA,OAAO,MAAMwD,kBAAkB,GAAIhD,KAAK,IAAK;EAC3C,MAAM;IAAEhB,IAAI;IAAEM,aAAa;IAAEC;EAAQ,CAAC,GAAGS,KAAK,CAACkC,GAAG;EAElD,OAAOlD,IAAI,CAACoC,MAAM,CAACc,GAAG,IAAI;IACxB;IACA,IAAI5C,aAAa,EAAE;MACjB,MAAM2D,OAAO,GAAG3D,aAAa,CAAC4D,WAAW,CAAC,CAAC;MAC3C,IAAI,CAAChB,GAAG,CAAC/E,IAAI,CAAC+F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,IACzC,CAACf,GAAG,CAAC9E,IAAI,CAAC8F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,IACzC,CAACf,GAAG,CAAChE,WAAW,CAACgF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;QACpD,OAAO,KAAK;MACd;IACF;;IAEA;IACA,IAAI1D,OAAO,CAAC5B,MAAM,IAAIuE,GAAG,CAACvE,MAAM,KAAK4B,OAAO,CAAC5B,MAAM,EAAE;MACnD,OAAO,KAAK;IACd;;IAEA;IACA,IAAI4B,OAAO,CAACtB,IAAI,IAAIiE,GAAG,CAACjE,IAAI,KAAKsB,OAAO,CAACtB,IAAI,EAAE;MAC7C,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMmF,uBAAuB,GAAIpD,KAAK,IAAK;EAChD,MAAM;IAAEf,SAAS;IAAEK,aAAa;IAAEC;EAAQ,CAAC,GAAGS,KAAK,CAACkC,GAAG;EAEvD,OAAOjD,SAAS,CAACmC,MAAM,CAACiC,QAAQ,IAAI;IAClC;IACA,IAAI/D,aAAa,EAAE;MACjB,MAAM2D,OAAO,GAAG3D,aAAa,CAAC4D,WAAW,CAAC,CAAC;MAC3C,IAAI,CAACG,QAAQ,CAAClG,IAAI,CAAC+F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,IAC9C,CAACI,QAAQ,CAACjG,IAAI,CAAC8F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,IAC9C,CAACI,QAAQ,CAAChG,aAAa,CAAC6F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;QAC3D,OAAO,KAAK;MACd;IACF;;IAEA;IACA,IAAI1D,OAAO,CAAC7B,QAAQ,IAAI2F,QAAQ,CAAC3F,QAAQ,KAAK6B,OAAO,CAAC7B,QAAQ,EAAE;MAC9D,OAAO,KAAK;IACd;;IAEA;IACA,IAAI6B,OAAO,CAAC5B,MAAM,IAAI0F,QAAQ,CAAC1F,MAAM,KAAK4B,OAAO,CAAC5B,MAAM,EAAE;MACxD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;AAED,eAAekC,QAAQ,CAACyD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}