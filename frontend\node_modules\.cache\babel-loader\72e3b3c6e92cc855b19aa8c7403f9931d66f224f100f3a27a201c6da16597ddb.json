{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { defaults, createObject } from '../core/util.js';\nexport var DEFAULT_IMAGE_STYLE = defaults({\n  x: 0,\n  y: 0\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_IMAGE_ANIMATION_PROPS = {\n  style: defaults({\n    x: true,\n    y: true,\n    width: true,\n    height: true,\n    sx: true,\n    sy: true,\n    sWidth: true,\n    sHeight: true\n  }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nfunction isImageLike(source) {\n  return !!(source && typeof source !== 'string' && source.width && source.height);\n}\nvar ZRImage = function (_super) {\n  __extends(ZRImage, _super);\n  function ZRImage() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  ZRImage.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_IMAGE_STYLE, obj);\n  };\n  ZRImage.prototype._getSize = function (dim) {\n    var style = this.style;\n    var size = style[dim];\n    if (size != null) {\n      return size;\n    }\n    var imageSource = isImageLike(style.image) ? style.image : this.__image;\n    if (!imageSource) {\n      return 0;\n    }\n    var otherDim = dim === 'width' ? 'height' : 'width';\n    var otherDimSize = style[otherDim];\n    if (otherDimSize == null) {\n      return imageSource[dim];\n    } else {\n      return imageSource[dim] / imageSource[otherDim] * otherDimSize;\n    }\n  };\n  ZRImage.prototype.getWidth = function () {\n    return this._getSize('width');\n  };\n  ZRImage.prototype.getHeight = function () {\n    return this._getSize('height');\n  };\n  ZRImage.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_IMAGE_ANIMATION_PROPS;\n  };\n  ZRImage.prototype.getBoundingRect = function () {\n    var style = this.style;\n    if (!this._rect) {\n      this._rect = new BoundingRect(style.x || 0, style.y || 0, this.getWidth(), this.getHeight());\n    }\n    return this._rect;\n  };\n  return ZRImage;\n}(Displayable);\nZRImage.prototype.type = 'image';\nexport default ZRImage;", "map": {"version": 3, "names": ["__extends", "Displayable", "DEFAULT_COMMON_STYLE", "DEFAULT_COMMON_ANIMATION_PROPS", "BoundingRect", "defaults", "createObject", "DEFAULT_IMAGE_STYLE", "x", "y", "DEFAULT_IMAGE_ANIMATION_PROPS", "style", "width", "height", "sx", "sy", "sWidth", "sHeight", "isImageLike", "source", "ZRImage", "_super", "apply", "arguments", "prototype", "createStyle", "obj", "_getSize", "dim", "size", "imageSource", "image", "__image", "otherDim", "otherDimSize", "getWidth", "getHeight", "getAnimationStyleProps", "getBoundingRect", "_rect", "type"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/zrender/lib/graphic/Image.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { defaults, createObject } from '../core/util.js';\nexport var DEFAULT_IMAGE_STYLE = defaults({\n    x: 0,\n    y: 0\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_IMAGE_ANIMATION_PROPS = {\n    style: defaults({\n        x: true,\n        y: true,\n        width: true,\n        height: true,\n        sx: true,\n        sy: true,\n        sWidth: true,\n        sHeight: true\n    }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nfunction isImageLike(source) {\n    return !!(source\n        && typeof source !== 'string'\n        && source.width && source.height);\n}\nvar ZRImage = (function (_super) {\n    __extends(ZRImage, _super);\n    function ZRImage() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ZRImage.prototype.createStyle = function (obj) {\n        return createObject(DEFAULT_IMAGE_STYLE, obj);\n    };\n    ZRImage.prototype._getSize = function (dim) {\n        var style = this.style;\n        var size = style[dim];\n        if (size != null) {\n            return size;\n        }\n        var imageSource = isImageLike(style.image)\n            ? style.image : this.__image;\n        if (!imageSource) {\n            return 0;\n        }\n        var otherDim = dim === 'width' ? 'height' : 'width';\n        var otherDimSize = style[otherDim];\n        if (otherDimSize == null) {\n            return imageSource[dim];\n        }\n        else {\n            return imageSource[dim] / imageSource[otherDim] * otherDimSize;\n        }\n    };\n    ZRImage.prototype.getWidth = function () {\n        return this._getSize('width');\n    };\n    ZRImage.prototype.getHeight = function () {\n        return this._getSize('height');\n    };\n    ZRImage.prototype.getAnimationStyleProps = function () {\n        return DEFAULT_IMAGE_ANIMATION_PROPS;\n    };\n    ZRImage.prototype.getBoundingRect = function () {\n        var style = this.style;\n        if (!this._rect) {\n            this._rect = new BoundingRect(style.x || 0, style.y || 0, this.getWidth(), this.getHeight());\n        }\n        return this._rect;\n    };\n    return ZRImage;\n}(Displayable));\nZRImage.prototype.type = 'image';\nexport default ZRImage;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,IAAIC,oBAAoB,EAAEC,8BAA8B,QAAQ,kBAAkB;AACpG,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,IAAIC,mBAAmB,GAAGF,QAAQ,CAAC;EACtCG,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACP,CAAC,EAAEP,oBAAoB,CAAC;AACxB,OAAO,IAAIQ,6BAA6B,GAAG;EACvCC,KAAK,EAAEN,QAAQ,CAAC;IACZG,CAAC,EAAE,IAAI;IACPC,CAAC,EAAE,IAAI;IACPG,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;EACb,CAAC,EAAEd,8BAA8B,CAACQ,KAAK;AAC3C,CAAC;AACD,SAASO,WAAWA,CAACC,MAAM,EAAE;EACzB,OAAO,CAAC,EAAEA,MAAM,IACT,OAAOA,MAAM,KAAK,QAAQ,IAC1BA,MAAM,CAACP,KAAK,IAAIO,MAAM,CAACN,MAAM,CAAC;AACzC;AACA,IAAIO,OAAO,GAAI,UAAUC,MAAM,EAAE;EAC7BrB,SAAS,CAACoB,OAAO,EAAEC,MAAM,CAAC;EAC1B,SAASD,OAAOA,CAAA,EAAG;IACf,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACAH,OAAO,CAACI,SAAS,CAACC,WAAW,GAAG,UAAUC,GAAG,EAAE;IAC3C,OAAOpB,YAAY,CAACC,mBAAmB,EAAEmB,GAAG,CAAC;EACjD,CAAC;EACDN,OAAO,CAACI,SAAS,CAACG,QAAQ,GAAG,UAAUC,GAAG,EAAE;IACxC,IAAIjB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIkB,IAAI,GAAGlB,KAAK,CAACiB,GAAG,CAAC;IACrB,IAAIC,IAAI,IAAI,IAAI,EAAE;MACd,OAAOA,IAAI;IACf;IACA,IAAIC,WAAW,GAAGZ,WAAW,CAACP,KAAK,CAACoB,KAAK,CAAC,GACpCpB,KAAK,CAACoB,KAAK,GAAG,IAAI,CAACC,OAAO;IAChC,IAAI,CAACF,WAAW,EAAE;MACd,OAAO,CAAC;IACZ;IACA,IAAIG,QAAQ,GAAGL,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,OAAO;IACnD,IAAIM,YAAY,GAAGvB,KAAK,CAACsB,QAAQ,CAAC;IAClC,IAAIC,YAAY,IAAI,IAAI,EAAE;MACtB,OAAOJ,WAAW,CAACF,GAAG,CAAC;IAC3B,CAAC,MACI;MACD,OAAOE,WAAW,CAACF,GAAG,CAAC,GAAGE,WAAW,CAACG,QAAQ,CAAC,GAAGC,YAAY;IAClE;EACJ,CAAC;EACDd,OAAO,CAACI,SAAS,CAACW,QAAQ,GAAG,YAAY;IACrC,OAAO,IAAI,CAACR,QAAQ,CAAC,OAAO,CAAC;EACjC,CAAC;EACDP,OAAO,CAACI,SAAS,CAACY,SAAS,GAAG,YAAY;IACtC,OAAO,IAAI,CAACT,QAAQ,CAAC,QAAQ,CAAC;EAClC,CAAC;EACDP,OAAO,CAACI,SAAS,CAACa,sBAAsB,GAAG,YAAY;IACnD,OAAO3B,6BAA6B;EACxC,CAAC;EACDU,OAAO,CAACI,SAAS,CAACc,eAAe,GAAG,YAAY;IAC5C,IAAI3B,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,CAAC,IAAI,CAAC4B,KAAK,EAAE;MACb,IAAI,CAACA,KAAK,GAAG,IAAInC,YAAY,CAACO,KAAK,CAACH,CAAC,IAAI,CAAC,EAAEG,KAAK,CAACF,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC0B,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAChG;IACA,OAAO,IAAI,CAACG,KAAK;EACrB,CAAC;EACD,OAAOnB,OAAO;AAClB,CAAC,CAACnB,WAAW,CAAE;AACfmB,OAAO,CAACI,SAAS,CAACgB,IAAI,GAAG,OAAO;AAChC,eAAepB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}