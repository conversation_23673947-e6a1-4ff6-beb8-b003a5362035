{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM\\\\frontend\\\\src\\\\pages\\\\BOM\\\\BOMList.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { Table, Card, Button, Input, Select, Space, Tag, Typography, Row, Col, Statistic, Modal, Form, message, Tooltip, Badge } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined, ExportOutlined, AppstoreOutlined, CopyOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport { selectFilteredBOMs, selectBOMLoading, selectBOMFilters, selectBOMPagination, setSearchKeyword, setFilters, setPagination, resetFilters, deleteBOM, addBOM } from '../../store/slices/bomSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst BOMList = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const boms = useSelector(selectFilteredBOMs);\n  const loading = useSelector(selectBOMLoading);\n  const filters = useSelector(selectBOMFilters);\n  const pagination = useSelector(selectBOMPagination);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingBOM, setEditingBOM] = useState(null);\n  const [form] = Form.useForm();\n  const handleSearch = value => {\n    dispatch(setSearchKeyword(value));\n    dispatch(setPagination({\n      current: 1\n    }));\n  };\n  const handleFilterChange = (key, value) => {\n    dispatch(setFilters({\n      [key]: value\n    }));\n    dispatch(setPagination({\n      current: 1\n    }));\n  };\n  const handleTableChange = paginationConfig => {\n    dispatch(setPagination({\n      current: paginationConfig.current,\n      pageSize: paginationConfig.pageSize\n    }));\n  };\n  const handleAdd = () => {\n    setEditingBOM(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = bom => {\n    setEditingBOM(bom);\n    form.setFieldsValue(bom);\n    setModalVisible(true);\n  };\n  const handleDelete = bom => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除BOM\"${bom.name}\"吗？`,\n      onOk: () => {\n        dispatch(deleteBOM(bom.id));\n        message.success('删除成功');\n      }\n    });\n  };\n  const handleCopy = bom => {\n    const newBOM = {\n      ...bom,\n      name: `${bom.name} - 副本`,\n      code: `${bom.code}-COPY`,\n      version: 'V1.0'\n    };\n    dispatch(addBOM(newBOM));\n    message.success('复制成功');\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingBOM) {\n        // 更新逻辑\n        message.success('更新成功');\n      } else {\n        dispatch(addBOM(values));\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'active': 'green',\n      'inactive': 'red',\n      'draft': 'orange'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      'active': '启用',\n      'inactive': '停用',\n      'draft': '草稿'\n    };\n    return texts[status] || status;\n  };\n  const getTypeColor = type => {\n    const colors = {\n      'product': 'blue',\n      'component': 'green',\n      'material': 'orange'\n    };\n    return colors[type] || 'default';\n  };\n  const getTypeText = type => {\n    const texts = {\n      'product': '产品',\n      'component': '组件',\n      'material': '物料'\n    };\n    return texts[type] || type;\n  };\n  const columns = [{\n    title: 'BOM编码',\n    dataIndex: 'code',\n    key: 'code',\n    width: 150,\n    fixed: 'left',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        style: {\n          padding: 0,\n          height: 'auto',\n          fontWeight: 'bold'\n        },\n        onClick: () => navigate(`/bom/${record.id}`),\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: record.version\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'BOM名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 200,\n    ellipsis: true\n  }, {\n    title: '类型',\n    dataIndex: 'type',\n    key: 'type',\n    width: 80,\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getTypeColor(type),\n      children: getTypeText(type)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'active' ? 'success' : status === 'draft' ? 'warning' : 'error',\n      text: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物料数量',\n    key: 'itemCount',\n    width: 100,\n    render: (_, record) => {\n      var _record$items;\n      return /*#__PURE__*/_jsxDEV(Text, {\n        children: [((_record$items = record.items) === null || _record$items === void 0 ? void 0 : _record$items.length) || 0, \" \\u9879\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this);\n    }\n  }, {\n    title: '总成本',\n    dataIndex: 'totalCost',\n    key: 'totalCost',\n    width: 120,\n    render: cost => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#1890ff'\n      },\n      children: formatCurrency(cost)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建人',\n    dataIndex: 'createdBy',\n    key: 'createdBy',\n    width: 100\n  }, {\n    title: '创建时间',\n    dataIndex: 'createTime',\n    key: 'createTime',\n    width: 100\n  }, {\n    title: '更新时间',\n    dataIndex: 'updateTime',\n    key: 'updateTime',\n    width: 100\n  }, {\n    title: '操作',\n    key: 'actions',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => navigate(`/bom/${record.id}`)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u590D\\u5236\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => handleCopy(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          danger: true,\n          onClick: () => handleDelete(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 计算统计数据\n  const stats = {\n    total: boms.length,\n    active: boms.filter(b => b.status === 'active').length,\n    products: boms.filter(b => b.type === 'product').length,\n    totalCost: boms.reduce((sum, b) => sum + (b.totalCost || 0), 0),\n    avgCost: boms.length > 0 ? boms.reduce((sum, b) => sum + (b.totalCost || 0), 0) / boms.length : 0\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: \"BOM\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 25\n          }, this),\n          onClick: () => navigate('/materials'),\n          children: \"\\u7269\\u6599\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5237\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 40\n          }, this),\n          onClick: handleAdd,\n          children: \"\\u65B0\\u5EFABOM\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"BOM\\u603B\\u6570\",\n            value: stats.total,\n            suffix: \"\\u4E2A\",\n            prefix: /*#__PURE__*/_jsxDEV(AppstoreOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u542F\\u7528\\u4E2D\",\n            value: stats.active,\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4EA7\\u54C1BOM\",\n            value: stats.products,\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u6210\\u672C\",\n            value: stats.totalCost,\n            formatter: value => formatCurrency(value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u6210\\u672C\",\n            value: stats.avgCost,\n            formatter: value => formatCurrency(value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Input.Search, {\n            placeholder: \"\\u641C\\u7D22BOM\\u7F16\\u7801\\u3001\\u540D\\u79F0\\u3001\\u63CF\\u8FF0\",\n            allowClear: true,\n            onSearch: handleSearch,\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u72B6\\u6001\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: filters.status,\n            onChange: value => handleFilterChange('status', value),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"active\",\n              children: \"\\u542F\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"inactive\",\n              children: \"\\u505C\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"draft\",\n              children: \"\\u8349\\u7A3F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u7C7B\\u578B\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: filters.type,\n            onChange: value => handleFilterChange('type', value),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"product\",\n              children: \"\\u4EA7\\u54C1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"component\",\n              children: \"\\u7EC4\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"material\",\n              children: \"\\u7269\\u6599\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => dispatch(resetFilters()),\n            children: \"\\u91CD\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: boms,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          ...pagination,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        },\n        onChange: handleTableChange,\n        scroll: {\n          x: 1400\n        },\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingBOM ? '编辑BOM' : '新建BOM',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      width: 600,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          type: 'product',\n          status: 'draft',\n          version: 'V1.0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"BOM\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入BOM编码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u7F16\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"BOM\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入BOM名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u7C7B\\u578B\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"product\",\n                  children: \"\\u4EA7\\u54C1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"component\",\n                  children: \"\\u7EC4\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"material\",\n                  children: \"\\u7269\\u6599\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"version\",\n              label: \"\\u7248\\u672C\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"V1.0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"draft\",\n                  children: \"\\u8349\\u7A3F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"active\",\n                  children: \"\\u542F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"inactive\",\n                  children: \"\\u505C\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 319,\n    columnNumber: 5\n  }, this);\n};\n_s(BOMList, \"NjfJOU2twhNqbBLpuplFrtxuK6o=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector, useSelector, useSelector, Form.useForm];\n});\n_c = BOMList;\nexport default BOMList;\nvar _c;\n$RefreshReg$(_c, \"BOMList\");", "map": {"version": 3, "names": ["React", "useState", "useSelector", "useDispatch", "useNavigate", "Table", "Card", "<PERSON><PERSON>", "Input", "Select", "Space", "Tag", "Typography", "Row", "Col", "Statistic", "Modal", "Form", "message", "<PERSON><PERSON><PERSON>", "Badge", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ReloadOutlined", "ExportOutlined", "AppstoreOutlined", "CopyOutlined", "DatabaseOutlined", "selectFilteredBOMs", "selectBOMLoading", "selectBOMFilters", "selectBOMPagination", "setSearchKeyword", "setFilters", "setPagination", "resetFilters", "deleteBOM", "addBOM", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "BOMList", "_s", "dispatch", "navigate", "boms", "loading", "filters", "pagination", "modalVisible", "setModalVisible", "editingBOM", "setEditingBOM", "form", "useForm", "handleSearch", "value", "current", "handleFilterChange", "key", "handleTableChange", "paginationConfig", "pageSize", "handleAdd", "resetFields", "handleEdit", "bom", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "confirm", "title", "content", "name", "onOk", "id", "success", "handleCopy", "newBOM", "code", "version", "handleModalOk", "values", "validateFields", "error", "console", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "getStatusColor", "status", "colors", "getStatusText", "texts", "getTypeColor", "type", "getTypeText", "columns", "dataIndex", "width", "fixed", "render", "text", "record", "direction", "size", "children", "padding", "height", "fontWeight", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "ellipsis", "color", "_", "_record$items", "items", "length", "cost", "strong", "icon", "danger", "stats", "total", "active", "filter", "b", "products", "totalCost", "reduce", "sum", "avgCost", "marginBottom", "display", "justifyContent", "alignItems", "level", "margin", "gutter", "xs", "sm", "lg", "suffix", "prefix", "formatter", "md", "Search", "placeholder", "allowClear", "onSearch", "onChange", "dataSource", "<PERSON><PERSON><PERSON>", "showSizeChanger", "showQuickJumper", "showTotal", "range", "scroll", "x", "open", "onCancel", "destroyOnClose", "layout", "initialValues", "span", "<PERSON><PERSON>", "label", "rules", "required", "TextArea", "rows", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM/frontend/src/pages/BOM/BOMList.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Card,\n  Button,\n  Input,\n  Select,\n  Space,\n  Tag,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Modal,\n  Form,\n  message,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  ExportOutlined,\n  AppstoreOutlined,\n  CopyOutlined,\n  DatabaseOutlined\n} from '@ant-design/icons';\n\nimport {\n  selectFilteredBOMs,\n  selectBOMLoading,\n  selectBOMFilters,\n  selectBOMPagination,\n  setSearchKeyword,\n  setFilters,\n  setPagination,\n  resetFilters,\n  deleteBOM,\n  addBOM\n} from '../../store/slices/bomSlice';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nconst BOMList = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const boms = useSelector(selectFilteredBOMs);\n  const loading = useSelector(selectBOMLoading);\n  const filters = useSelector(selectBOMFilters);\n  const pagination = useSelector(selectBOMPagination);\n\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingBOM, setEditingBOM] = useState(null);\n  const [form] = Form.useForm();\n\n  const handleSearch = (value) => {\n    dispatch(setSearchKeyword(value));\n    dispatch(setPagination({ current: 1 }));\n  };\n\n  const handleFilterChange = (key, value) => {\n    dispatch(setFilters({ [key]: value }));\n    dispatch(setPagination({ current: 1 }));\n  };\n\n  const handleTableChange = (paginationConfig) => {\n    dispatch(setPagination({\n      current: paginationConfig.current,\n      pageSize: paginationConfig.pageSize\n    }));\n  };\n\n  const handleAdd = () => {\n    setEditingBOM(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (bom) => {\n    setEditingBOM(bom);\n    form.setFieldsValue(bom);\n    setModalVisible(true);\n  };\n\n  const handleDelete = (bom) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除BOM\"${bom.name}\"吗？`,\n      onOk: () => {\n        dispatch(deleteBOM(bom.id));\n        message.success('删除成功');\n      }\n    });\n  };\n\n  const handleCopy = (bom) => {\n    const newBOM = {\n      ...bom,\n      name: `${bom.name} - 副本`,\n      code: `${bom.code}-COPY`,\n      version: 'V1.0'\n    };\n    dispatch(addBOM(newBOM));\n    message.success('复制成功');\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingBOM) {\n        // 更新逻辑\n        message.success('更新成功');\n      } else {\n        dispatch(addBOM(values));\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const formatCurrency = (value) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      'active': 'green',\n      'inactive': 'red',\n      'draft': 'orange'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      'active': '启用',\n      'inactive': '停用',\n      'draft': '草稿'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeColor = (type) => {\n    const colors = {\n      'product': 'blue',\n      'component': 'green',\n      'material': 'orange'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      'product': '产品',\n      'component': '组件',\n      'material': '物料'\n    };\n    return texts[type] || type;\n  };\n\n  const columns = [\n    {\n      title: 'BOM编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 150,\n      fixed: 'left',\n      render: (text, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Button \n            type=\"link\" \n            style={{ padding: 0, height: 'auto', fontWeight: 'bold' }}\n            onClick={() => navigate(`/bom/${record.id}`)}\n          >\n            {text}\n          </Button>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.version}\n          </Text>\n        </Space>\n      )\n    },\n    {\n      title: 'BOM名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 200,\n      ellipsis: true\n    },\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n      width: 80,\n      render: (type) => (\n        <Tag color={getTypeColor(type)}>\n          {getTypeText(type)}\n        </Tag>\n      )\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status) => (\n        <Badge \n          status={status === 'active' ? 'success' : status === 'draft' ? 'warning' : 'error'} \n          text={getStatusText(status)}\n        />\n      )\n    },\n    {\n      title: '物料数量',\n      key: 'itemCount',\n      width: 100,\n      render: (_, record) => (\n        <Text>{record.items?.length || 0} 项</Text>\n      )\n    },\n    {\n      title: '总成本',\n      dataIndex: 'totalCost',\n      key: 'totalCost',\n      width: 120,\n      render: (cost) => (\n        <Text strong style={{ color: '#1890ff' }}>\n          {formatCurrency(cost)}\n        </Text>\n      )\n    },\n    {\n      title: '创建人',\n      dataIndex: 'createdBy',\n      key: 'createdBy',\n      width: 100\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      key: 'createTime',\n      width: 100\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updateTime',\n      key: 'updateTime',\n      width: 100\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 150,\n      fixed: 'right',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button \n              type=\"text\" \n              icon={<EyeOutlined />} \n              size=\"small\"\n              onClick={() => navigate(`/bom/${record.id}`)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button \n              type=\"text\" \n              icon={<EditOutlined />} \n              size=\"small\"\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"复制\">\n            <Button \n              type=\"text\" \n              icon={<CopyOutlined />} \n              size=\"small\"\n              onClick={() => handleCopy(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button \n              type=\"text\" \n              icon={<DeleteOutlined />} \n              size=\"small\"\n              danger\n              onClick={() => handleDelete(record)}\n            />\n          </Tooltip>\n        </Space>\n      )\n    }\n  ];\n\n  // 计算统计数据\n  const stats = {\n    total: boms.length,\n    active: boms.filter(b => b.status === 'active').length,\n    products: boms.filter(b => b.type === 'product').length,\n    totalCost: boms.reduce((sum, b) => sum + (b.totalCost || 0), 0),\n    avgCost: boms.length > 0 ? boms.reduce((sum, b) => sum + (b.totalCost || 0), 0) / boms.length : 0\n  };\n\n  return (\n    <div>\n      {/* 页面标题和操作 */}\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Title level={2} style={{ margin: 0 }}>BOM管理</Title>\n        <Space>\n          <Button icon={<DatabaseOutlined />} onClick={() => navigate('/materials')}>\n            物料管理\n          </Button>\n          <Button icon={<ReloadOutlined />}>刷新</Button>\n          <Button icon={<ExportOutlined />}>导出</Button>\n          <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAdd}>\n            新建BOM\n          </Button>\n        </Space>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"BOM总数\" \n              value={stats.total} \n              suffix=\"个\"\n              prefix={<AppstoreOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"启用中\" value={stats.active} suffix=\"个\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic title=\"产品BOM\" value={stats.products} suffix=\"个\" />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"总成本\" \n              value={stats.totalCost} \n              formatter={(value) => formatCurrency(value)}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6} lg={4}>\n          <Card>\n            <Statistic \n              title=\"平均成本\" \n              value={stats.avgCost} \n              formatter={(value) => formatCurrency(value)}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索和筛选 */}\n      <Card style={{ marginBottom: '16px' }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Input.Search\n              placeholder=\"搜索BOM编码、名称、描述\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Select\n              placeholder=\"状态\"\n              allowClear\n              style={{ width: '100%' }}\n              value={filters.status}\n              onChange={(value) => handleFilterChange('status', value)}\n            >\n              <Option value=\"active\">启用</Option>\n              <Option value=\"inactive\">停用</Option>\n              <Option value=\"draft\">草稿</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Select\n              placeholder=\"类型\"\n              allowClear\n              style={{ width: '100%' }}\n              value={filters.type}\n              onChange={(value) => handleFilterChange('type', value)}\n            >\n              <Option value=\"product\">产品</Option>\n              <Option value=\"component\">组件</Option>\n              <Option value=\"material\">物料</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4} lg={3}>\n            <Button onClick={() => dispatch(resetFilters())}>重置</Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* BOM列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={boms}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            ...pagination,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => \n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n          }}\n          onChange={handleTableChange}\n          scroll={{ x: 1400 }}\n          size=\"small\"\n        />\n      </Card>\n\n      {/* 新增/编辑BOM弹窗 */}\n      <Modal\n        title={editingBOM ? '编辑BOM' : '新建BOM'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n        width={600}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            type: 'product',\n            status: 'draft',\n            version: 'V1.0'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"code\"\n                label=\"BOM编码\"\n                rules={[{ required: true, message: '请输入BOM编码' }]}\n              >\n                <Input placeholder=\"请输入BOM编码\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"BOM名称\"\n                rules={[{ required: true, message: '请输入BOM名称' }]}\n              >\n                <Input placeholder=\"请输入BOM名称\" />\n              </Form.Item>\n            </Col>\n          </Row>\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"type\"\n                label=\"类型\"\n              >\n                <Select>\n                  <Option value=\"product\">产品</Option>\n                  <Option value=\"component\">组件</Option>\n                  <Option value=\"material\">物料</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"version\"\n                label=\"版本\"\n              >\n                <Input placeholder=\"V1.0\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n              >\n                <Select>\n                  <Option value=\"draft\">草稿</Option>\n                  <Option value=\"active\">启用</Option>\n                  <Option value=\"inactive\">停用</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n          >\n            <Input.TextArea rows={3} placeholder=\"请输入BOM描述\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default BOMList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,YAAY,EACZC,gBAAgB,QACX,mBAAmB;AAE1B,SACEC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,EACnBC,gBAAgB,EAChBC,UAAU,EACVC,aAAa,EACbC,YAAY,EACZC,SAAS,EACTC,MAAM,QACD,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGhC,UAAU;AAClC,MAAM;EAAEiC;AAAO,CAAC,GAAGpC,MAAM;AAEzB,MAAMqC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,IAAI,GAAGhD,WAAW,CAAC6B,kBAAkB,CAAC;EAC5C,MAAMoB,OAAO,GAAGjD,WAAW,CAAC8B,gBAAgB,CAAC;EAC7C,MAAMoB,OAAO,GAAGlD,WAAW,CAAC+B,gBAAgB,CAAC;EAC7C,MAAMoB,UAAU,GAAGnD,WAAW,CAACgC,mBAAmB,CAAC;EAEnD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyD,IAAI,CAAC,GAAGzC,IAAI,CAAC0C,OAAO,CAAC,CAAC;EAE7B,MAAMC,YAAY,GAAIC,KAAK,IAAK;IAC9Bb,QAAQ,CAACb,gBAAgB,CAAC0B,KAAK,CAAC,CAAC;IACjCb,QAAQ,CAACX,aAAa,CAAC;MAAEyB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACC,GAAG,EAAEH,KAAK,KAAK;IACzCb,QAAQ,CAACZ,UAAU,CAAC;MAAE,CAAC4B,GAAG,GAAGH;IAAM,CAAC,CAAC,CAAC;IACtCb,QAAQ,CAACX,aAAa,CAAC;MAAEyB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,MAAMG,iBAAiB,GAAIC,gBAAgB,IAAK;IAC9ClB,QAAQ,CAACX,aAAa,CAAC;MACrByB,OAAO,EAAEI,gBAAgB,CAACJ,OAAO;MACjCK,QAAQ,EAAED,gBAAgB,CAACC;IAC7B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtBX,aAAa,CAAC,IAAI,CAAC;IACnBC,IAAI,CAACW,WAAW,CAAC,CAAC;IAClBd,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMe,UAAU,GAAIC,GAAG,IAAK;IAC1Bd,aAAa,CAACc,GAAG,CAAC;IAClBb,IAAI,CAACc,cAAc,CAACD,GAAG,CAAC;IACxBhB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkB,YAAY,GAAIF,GAAG,IAAK;IAC5BvD,KAAK,CAAC0D,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,YAAYL,GAAG,CAACM,IAAI,KAAK;MAClCC,IAAI,EAAEA,CAAA,KAAM;QACV9B,QAAQ,CAACT,SAAS,CAACgC,GAAG,CAACQ,EAAE,CAAC,CAAC;QAC3B7D,OAAO,CAAC8D,OAAO,CAAC,MAAM,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIV,GAAG,IAAK;IAC1B,MAAMW,MAAM,GAAG;MACb,GAAGX,GAAG;MACNM,IAAI,EAAE,GAAGN,GAAG,CAACM,IAAI,OAAO;MACxBM,IAAI,EAAE,GAAGZ,GAAG,CAACY,IAAI,OAAO;MACxBC,OAAO,EAAE;IACX,CAAC;IACDpC,QAAQ,CAACR,MAAM,CAAC0C,MAAM,CAAC,CAAC;IACxBhE,OAAO,CAAC8D,OAAO,CAAC,MAAM,CAAC;EACzB,CAAC;EAED,MAAMK,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM5B,IAAI,CAAC6B,cAAc,CAAC,CAAC;MAC1C,IAAI/B,UAAU,EAAE;QACd;QACAtC,OAAO,CAAC8D,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACLhC,QAAQ,CAACR,MAAM,CAAC8C,MAAM,CAAC,CAAC;QACxBpE,OAAO,CAAC8D,OAAO,CAAC,MAAM,CAAC;MACzB;MACAzB,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAME,cAAc,GAAI7B,KAAK,IAAK;IAChC,OAAO,IAAI8B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMqC,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,OAAO;MACjB,UAAU,EAAE,KAAK;MACjB,OAAO,EAAE;IACX,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAME,aAAa,GAAIF,MAAM,IAAK;IAChC,MAAMG,KAAK,GAAG;MACZ,QAAQ,EAAE,IAAI;MACd,UAAU,EAAE,IAAI;MAChB,OAAO,EAAE;IACX,CAAC;IACD,OAAOA,KAAK,CAACH,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMI,YAAY,GAAIC,IAAI,IAAK;IAC7B,MAAMJ,MAAM,GAAG;MACb,SAAS,EAAE,MAAM;MACjB,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,MAAM,CAACI,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAMC,WAAW,GAAID,IAAI,IAAK;IAC5B,MAAMF,KAAK,GAAG;MACZ,SAAS,EAAE,IAAI;MACf,WAAW,EAAE,IAAI;MACjB,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,KAAK,CAACE,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,MAAME,OAAO,GAAG,CACd;IACE/B,KAAK,EAAE,OAAO;IACdgC,SAAS,EAAE,MAAM;IACjB3C,GAAG,EAAE,MAAM;IACX4C,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBtE,OAAA,CAAChC,KAAK;MAACuG,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,gBAClCzE,OAAA,CAACnC,MAAM;QACLiG,IAAI,EAAC,MAAM;QACXX,KAAK,EAAE;UAAEuB,OAAO,EAAE,CAAC;UAAEC,MAAM,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAO,CAAE;QAC1DC,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAC,QAAQ+D,MAAM,CAACjC,EAAE,EAAE,CAAE;QAAAoC,QAAA,EAE5CJ;MAAI;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACTjF,OAAA,CAACE,IAAI;QAAC4D,IAAI,EAAC,WAAW;QAACX,KAAK,EAAE;UAAE+B,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAChDH,MAAM,CAAC5B;MAAO;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACEhD,KAAK,EAAE,OAAO;IACdgC,SAAS,EAAE,MAAM;IACjB3C,GAAG,EAAE,MAAM;IACX4C,KAAK,EAAE,GAAG;IACViB,QAAQ,EAAE;EACZ,CAAC,EACD;IACElD,KAAK,EAAE,IAAI;IACXgC,SAAS,EAAE,MAAM;IACjB3C,GAAG,EAAE,MAAM;IACX4C,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGN,IAAI,iBACX9D,OAAA,CAAC/B,GAAG;MAACmH,KAAK,EAAEvB,YAAY,CAACC,IAAI,CAAE;MAAAW,QAAA,EAC5BV,WAAW,CAACD,IAAI;IAAC;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAET,CAAC,EACD;IACEhD,KAAK,EAAE,IAAI;IACXgC,SAAS,EAAE,QAAQ;IACnB3C,GAAG,EAAE,QAAQ;IACb4C,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGX,MAAM,iBACbzD,OAAA,CAACtB,KAAK;MACJ+E,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAGA,MAAM,KAAK,OAAO,GAAG,SAAS,GAAG,OAAQ;MACnFY,IAAI,EAAEV,aAAa,CAACF,MAAM;IAAE;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B;EAEL,CAAC,EACD;IACEhD,KAAK,EAAE,MAAM;IACbX,GAAG,EAAE,WAAW;IAChB4C,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACiB,CAAC,EAAEf,MAAM;MAAA,IAAAgB,aAAA;MAAA,oBAChBtF,OAAA,CAACE,IAAI;QAAAuE,QAAA,GAAE,EAAAa,aAAA,GAAAhB,MAAM,CAACiB,KAAK,cAAAD,aAAA,uBAAZA,aAAA,CAAcE,MAAM,KAAI,CAAC,EAAC,SAAE;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;EAE9C,CAAC,EACD;IACEhD,KAAK,EAAE,KAAK;IACZgC,SAAS,EAAE,WAAW;IACtB3C,GAAG,EAAE,WAAW;IAChB4C,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGqB,IAAI,iBACXzF,OAAA,CAACE,IAAI;MAACwF,MAAM;MAACvC,KAAK,EAAE;QAAEiC,KAAK,EAAE;MAAU,CAAE;MAAAX,QAAA,EACtCzB,cAAc,CAACyC,IAAI;IAAC;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB;EAEV,CAAC,EACD;IACEhD,KAAK,EAAE,KAAK;IACZgC,SAAS,EAAE,WAAW;IACtB3C,GAAG,EAAE,WAAW;IAChB4C,KAAK,EAAE;EACT,CAAC,EACD;IACEjC,KAAK,EAAE,MAAM;IACbgC,SAAS,EAAE,YAAY;IACvB3C,GAAG,EAAE,YAAY;IACjB4C,KAAK,EAAE;EACT,CAAC,EACD;IACEjC,KAAK,EAAE,MAAM;IACbgC,SAAS,EAAE,YAAY;IACvB3C,GAAG,EAAE,YAAY;IACjB4C,KAAK,EAAE;EACT,CAAC,EACD;IACEjC,KAAK,EAAE,IAAI;IACXX,GAAG,EAAE,SAAS;IACd4C,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAEA,CAACiB,CAAC,EAAEf,MAAM,kBAChBtE,OAAA,CAAChC,KAAK;MAACwG,IAAI,EAAC,OAAO;MAAAC,QAAA,gBACjBzE,OAAA,CAACvB,OAAO;QAACwD,KAAK,EAAC,0BAAM;QAAAwC,QAAA,eACnBzE,OAAA,CAACnC,MAAM;UACLiG,IAAI,EAAC,MAAM;UACX6B,IAAI,eAAE3F,OAAA,CAACjB,WAAW;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBT,IAAI,EAAC,OAAO;UACZK,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAC,QAAQ+D,MAAM,CAACjC,EAAE,EAAE;QAAE;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVjF,OAAA,CAACvB,OAAO;QAACwD,KAAK,EAAC,cAAI;QAAAwC,QAAA,eACjBzE,OAAA,CAACnC,MAAM;UACLiG,IAAI,EAAC,MAAM;UACX6B,IAAI,eAAE3F,OAAA,CAACnB,YAAY;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBT,IAAI,EAAC,OAAO;UACZK,OAAO,EAAEA,CAAA,KAAMjD,UAAU,CAAC0C,MAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVjF,OAAA,CAACvB,OAAO;QAACwD,KAAK,EAAC,cAAI;QAAAwC,QAAA,eACjBzE,OAAA,CAACnC,MAAM;UACLiG,IAAI,EAAC,MAAM;UACX6B,IAAI,eAAE3F,OAAA,CAACb,YAAY;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBT,IAAI,EAAC,OAAO;UACZK,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAAC+B,MAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVjF,OAAA,CAACvB,OAAO;QAACwD,KAAK,EAAC,cAAI;QAAAwC,QAAA,eACjBzE,OAAA,CAACnC,MAAM;UACLiG,IAAI,EAAC,MAAM;UACX6B,IAAI,eAAE3F,OAAA,CAAClB,cAAc;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBT,IAAI,EAAC,OAAO;UACZoB,MAAM;UACNf,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAACuC,MAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;;EAED;EACA,MAAMY,KAAK,GAAG;IACZC,KAAK,EAAEtF,IAAI,CAACgF,MAAM;IAClBO,MAAM,EAAEvF,IAAI,CAACwF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,QAAQ,CAAC,CAAC+B,MAAM;IACtDU,QAAQ,EAAE1F,IAAI,CAACwF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnC,IAAI,KAAK,SAAS,CAAC,CAAC0B,MAAM;IACvDW,SAAS,EAAE3F,IAAI,CAAC4F,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAG,IAAIJ,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/DG,OAAO,EAAE9F,IAAI,CAACgF,MAAM,GAAG,CAAC,GAAGhF,IAAI,CAAC4F,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAG,IAAIJ,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG3F,IAAI,CAACgF,MAAM,GAAG;EAClG,CAAC;EAED,oBACExF,OAAA;IAAAyE,QAAA,gBAEEzE,OAAA;MAAKmD,KAAK,EAAE;QAAEoD,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAjC,QAAA,gBAC3GzE,OAAA,CAACC,KAAK;QAAC0G,KAAK,EAAE,CAAE;QAACxD,KAAK,EAAE;UAAEyD,MAAM,EAAE;QAAE,CAAE;QAAAnC,QAAA,EAAC;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpDjF,OAAA,CAAChC,KAAK;QAAAyG,QAAA,gBACJzE,OAAA,CAACnC,MAAM;UAAC8H,IAAI,eAAE3F,OAAA,CAACZ,gBAAgB;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACJ,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAC,YAAY,CAAE;UAAAkE,QAAA,EAAC;QAE3E;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjF,OAAA,CAACnC,MAAM;UAAC8H,IAAI,eAAE3F,OAAA,CAAChB,cAAc;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CjF,OAAA,CAACnC,MAAM;UAAC8H,IAAI,eAAE3F,OAAA,CAACf,cAAc;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CjF,OAAA,CAACnC,MAAM;UAACiG,IAAI,EAAC,SAAS;UAAC6B,IAAI,eAAE3F,OAAA,CAACrB,YAAY;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACJ,OAAO,EAAEnD,SAAU;UAAA+C,QAAA,EAAC;QAEnE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjF,OAAA,CAAC7B,GAAG;MAAC0I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC1D,KAAK,EAAE;QAAEoD,YAAY,EAAE;MAAO,CAAE;MAAA9B,QAAA,gBACrDzE,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACxBzE,OAAA,CAACpC,IAAI;UAAA6G,QAAA,eACHzE,OAAA,CAAC3B,SAAS;YACR4D,KAAK,EAAC,iBAAO;YACbd,KAAK,EAAE0E,KAAK,CAACC,KAAM;YACnBmB,MAAM,EAAC,QAAG;YACVC,MAAM,eAAElH,OAAA,CAACd,gBAAgB;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjF,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACxBzE,OAAA,CAACpC,IAAI;UAAA6G,QAAA,eACHzE,OAAA,CAAC3B,SAAS;YAAC4D,KAAK,EAAC,oBAAK;YAACd,KAAK,EAAE0E,KAAK,CAACE,MAAO;YAACkB,MAAM,EAAC;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjF,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACxBzE,OAAA,CAACpC,IAAI;UAAA6G,QAAA,eACHzE,OAAA,CAAC3B,SAAS;YAAC4D,KAAK,EAAC,iBAAO;YAACd,KAAK,EAAE0E,KAAK,CAACK,QAAS;YAACe,MAAM,EAAC;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjF,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACxBzE,OAAA,CAACpC,IAAI;UAAA6G,QAAA,eACHzE,OAAA,CAAC3B,SAAS;YACR4D,KAAK,EAAC,oBAAK;YACXd,KAAK,EAAE0E,KAAK,CAACM,SAAU;YACvBgB,SAAS,EAAGhG,KAAK,IAAK6B,cAAc,CAAC7B,KAAK;UAAE;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjF,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACxBzE,OAAA,CAACpC,IAAI;UAAA6G,QAAA,eACHzE,OAAA,CAAC3B,SAAS;YACR4D,KAAK,EAAC,0BAAM;YACZd,KAAK,EAAE0E,KAAK,CAACS,OAAQ;YACrBa,SAAS,EAAGhG,KAAK,IAAK6B,cAAc,CAAC7B,KAAK;UAAE;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA,CAACpC,IAAI;MAACuF,KAAK,EAAE;QAAEoD,YAAY,EAAE;MAAO,CAAE;MAAA9B,QAAA,eACpCzE,OAAA,CAAC7B,GAAG;QAAC0I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAApC,QAAA,gBACpBzE,OAAA,CAAC5B,GAAG;UAAC0I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACK,EAAE,EAAE,CAAE;UAACJ,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAChCzE,OAAA,CAAClC,KAAK,CAACuJ,MAAM;YACXC,WAAW,EAAC,iEAAe;YAC3BC,UAAU;YACVC,QAAQ,EAAEtG,YAAa;YACvBiC,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjF,OAAA,CAAC5B,GAAG;UAAC0I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACK,EAAE,EAAE,CAAE;UAACJ,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAC/BzE,OAAA,CAACjC,MAAM;YACLuJ,WAAW,EAAC,cAAI;YAChBC,UAAU;YACVpE,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YACzB/C,KAAK,EAAET,OAAO,CAAC+C,MAAO;YACtBgE,QAAQ,EAAGtG,KAAK,IAAKE,kBAAkB,CAAC,QAAQ,EAAEF,KAAK,CAAE;YAAAsD,QAAA,gBAEzDzE,OAAA,CAACG,MAAM;cAACgB,KAAK,EAAC,QAAQ;cAAAsD,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCjF,OAAA,CAACG,MAAM;cAACgB,KAAK,EAAC,UAAU;cAAAsD,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCjF,OAAA,CAACG,MAAM;cAACgB,KAAK,EAAC,OAAO;cAAAsD,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjF,OAAA,CAAC5B,GAAG;UAAC0I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACK,EAAE,EAAE,CAAE;UAACJ,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAC/BzE,OAAA,CAACjC,MAAM;YACLuJ,WAAW,EAAC,cAAI;YAChBC,UAAU;YACVpE,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YACzB/C,KAAK,EAAET,OAAO,CAACoD,IAAK;YACpB2D,QAAQ,EAAGtG,KAAK,IAAKE,kBAAkB,CAAC,MAAM,EAAEF,KAAK,CAAE;YAAAsD,QAAA,gBAEvDzE,OAAA,CAACG,MAAM;cAACgB,KAAK,EAAC,SAAS;cAAAsD,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCjF,OAAA,CAACG,MAAM;cAACgB,KAAK,EAAC,WAAW;cAAAsD,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCjF,OAAA,CAACG,MAAM;cAACgB,KAAK,EAAC,UAAU;cAAAsD,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjF,OAAA,CAAC5B,GAAG;UAAC0I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACK,EAAE,EAAE,CAAE;UAACJ,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAC/BzE,OAAA,CAACnC,MAAM;YAACgH,OAAO,EAAEA,CAAA,KAAMvE,QAAQ,CAACV,YAAY,CAAC,CAAC,CAAE;YAAA6E,QAAA,EAAC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPjF,OAAA,CAACpC,IAAI;MAAA6G,QAAA,eACHzE,OAAA,CAACrC,KAAK;QACJqG,OAAO,EAAEA,OAAQ;QACjB0D,UAAU,EAAElH,IAAK;QACjBmH,MAAM,EAAC,IAAI;QACXlH,OAAO,EAAEA,OAAQ;QACjBE,UAAU,EAAE;UACV,GAAGA,UAAU;UACbiH,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAChC,KAAK,EAAEiC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQjC,KAAK;QAC1C,CAAE;QACF2B,QAAQ,EAAElG,iBAAkB;QAC5ByG,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBzD,IAAI,EAAC;MAAO;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPjF,OAAA,CAAC1B,KAAK;MACJ2D,KAAK,EAAEnB,UAAU,GAAG,OAAO,GAAG,OAAQ;MACtCoH,IAAI,EAAEtH,YAAa;MACnBwB,IAAI,EAAEO,aAAc;MACpBwF,QAAQ,EAAEA,CAAA,KAAMtH,eAAe,CAAC,KAAK,CAAE;MACvCqD,KAAK,EAAE,GAAI;MACXkE,cAAc;MAAA3D,QAAA,eAEdzE,OAAA,CAACzB,IAAI;QACHyC,IAAI,EAAEA,IAAK;QACXqH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbxE,IAAI,EAAE,SAAS;UACfL,MAAM,EAAE,OAAO;UACff,OAAO,EAAE;QACX,CAAE;QAAA+B,QAAA,gBAEFzE,OAAA,CAAC7B,GAAG;UAAC0I,MAAM,EAAE,EAAG;UAAApC,QAAA,gBACdzE,OAAA,CAAC5B,GAAG;YAACmK,IAAI,EAAE,EAAG;YAAA9D,QAAA,eACZzE,OAAA,CAACzB,IAAI,CAACiK,IAAI;cACRrG,IAAI,EAAC,MAAM;cACXsG,KAAK,EAAC,iBAAO;cACbC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAW,CAAC,CAAE;cAAAiG,QAAA,eAEjDzE,OAAA,CAAClC,KAAK;gBAACwJ,WAAW,EAAC;cAAU;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjF,OAAA,CAAC5B,GAAG;YAACmK,IAAI,EAAE,EAAG;YAAA9D,QAAA,eACZzE,OAAA,CAACzB,IAAI,CAACiK,IAAI;cACRrG,IAAI,EAAC,MAAM;cACXsG,KAAK,EAAC,iBAAO;cACbC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAW,CAAC,CAAE;cAAAiG,QAAA,eAEjDzE,OAAA,CAAClC,KAAK;gBAACwJ,WAAW,EAAC;cAAU;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjF,OAAA,CAAC7B,GAAG;UAAC0I,MAAM,EAAE,EAAG;UAAApC,QAAA,gBACdzE,OAAA,CAAC5B,GAAG;YAACmK,IAAI,EAAE,CAAE;YAAA9D,QAAA,eACXzE,OAAA,CAACzB,IAAI,CAACiK,IAAI;cACRrG,IAAI,EAAC,MAAM;cACXsG,KAAK,EAAC,cAAI;cAAAhE,QAAA,eAEVzE,OAAA,CAACjC,MAAM;gBAAA0G,QAAA,gBACLzE,OAAA,CAACG,MAAM;kBAACgB,KAAK,EAAC,SAAS;kBAAAsD,QAAA,EAAC;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCjF,OAAA,CAACG,MAAM;kBAACgB,KAAK,EAAC,WAAW;kBAAAsD,QAAA,EAAC;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCjF,OAAA,CAACG,MAAM;kBAACgB,KAAK,EAAC,UAAU;kBAAAsD,QAAA,EAAC;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjF,OAAA,CAAC5B,GAAG;YAACmK,IAAI,EAAE,CAAE;YAAA9D,QAAA,eACXzE,OAAA,CAACzB,IAAI,CAACiK,IAAI;cACRrG,IAAI,EAAC,SAAS;cACdsG,KAAK,EAAC,cAAI;cAAAhE,QAAA,eAEVzE,OAAA,CAAClC,KAAK;gBAACwJ,WAAW,EAAC;cAAM;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjF,OAAA,CAAC5B,GAAG;YAACmK,IAAI,EAAE,CAAE;YAAA9D,QAAA,eACXzE,OAAA,CAACzB,IAAI,CAACiK,IAAI;cACRrG,IAAI,EAAC,QAAQ;cACbsG,KAAK,EAAC,cAAI;cAAAhE,QAAA,eAEVzE,OAAA,CAACjC,MAAM;gBAAA0G,QAAA,gBACLzE,OAAA,CAACG,MAAM;kBAACgB,KAAK,EAAC,OAAO;kBAAAsD,QAAA,EAAC;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCjF,OAAA,CAACG,MAAM;kBAACgB,KAAK,EAAC,QAAQ;kBAAAsD,QAAA,EAAC;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjF,OAAA,CAACG,MAAM;kBAACgB,KAAK,EAAC,UAAU;kBAAAsD,QAAA,EAAC;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjF,OAAA,CAACzB,IAAI,CAACiK,IAAI;UACRrG,IAAI,EAAC,aAAa;UAClBsG,KAAK,EAAC,cAAI;UAAAhE,QAAA,eAEVzE,OAAA,CAAClC,KAAK,CAAC8K,QAAQ;YAACC,IAAI,EAAE,CAAE;YAACvB,WAAW,EAAC;UAAU;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAvdID,OAAO;EAAA,QACM3C,WAAW,EACXC,WAAW,EACfF,WAAW,EACRA,WAAW,EACXA,WAAW,EACRA,WAAW,EAIfe,IAAI,CAAC0C,OAAO;AAAA;AAAA6H,EAAA,GAVvB1I,OAAO;AAydb,eAAeA,OAAO;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}