{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Animate multiple elements with a single done-callback.\r\n *\r\n * @example\r\n *  animation\r\n *      .createWrap()\r\n *      .add(el1, {x: 10, y: 10})\r\n *      .add(el2, {shape: {width: 500}, style: {fill: 'red'}}, 400)\r\n *      .done(function () { // done })\r\n *      .start('cubicOut');\r\n */\nvar AnimationWrap = /** @class */function () {\n  function AnimationWrap() {\n    this._storage = [];\n    this._elExistsMap = {};\n  }\n  /**\r\n   * Caution: a el can only be added once, otherwise 'done'\r\n   * might not be called. This method checks this (by el.id),\r\n   * suppresses adding and returns false when existing el found.\r\n   *\r\n   * @return Whether adding succeeded.\r\n   */\n  AnimationWrap.prototype.add = function (el, target, duration, delay, easing) {\n    if (this._elExistsMap[el.id]) {\n      return false;\n    }\n    this._elExistsMap[el.id] = true;\n    this._storage.push({\n      el: el,\n      target: target,\n      duration: duration,\n      delay: delay,\n      easing: easing\n    });\n    return true;\n  };\n  /**\r\n   * Only execute when animation done/aborted.\r\n   */\n  AnimationWrap.prototype.finished = function (callback) {\n    this._finishedCallback = callback;\n    return this;\n  };\n  /**\r\n   * Will stop exist animation firstly.\r\n   */\n  AnimationWrap.prototype.start = function () {\n    var _this = this;\n    var count = this._storage.length;\n    var checkTerminate = function () {\n      count--;\n      if (count <= 0) {\n        // Guard.\n        _this._storage.length = 0;\n        _this._elExistsMap = {};\n        _this._finishedCallback && _this._finishedCallback();\n      }\n    };\n    for (var i = 0, len = this._storage.length; i < len; i++) {\n      var item = this._storage[i];\n      item.el.animateTo(item.target, {\n        duration: item.duration,\n        delay: item.delay,\n        easing: item.easing,\n        setToFinal: true,\n        done: checkTerminate,\n        aborted: checkTerminate\n      });\n    }\n    return this;\n  };\n  return AnimationWrap;\n}();\nexport function createWrap() {\n  return new AnimationWrap();\n}", "map": {"version": 3, "names": ["AnimationWrap", "_storage", "_elExistsMap", "prototype", "add", "el", "target", "duration", "delay", "easing", "id", "push", "finished", "callback", "_finished<PERSON><PERSON><PERSON>", "start", "_this", "count", "length", "checkTerminate", "i", "len", "item", "animateTo", "setToFinal", "done", "aborted", "createWrap"], "sources": ["D:/customerDemo/Link-BOM/frontend/node_modules/echarts/lib/util/animation.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Animate multiple elements with a single done-callback.\r\n *\r\n * @example\r\n *  animation\r\n *      .createWrap()\r\n *      .add(el1, {x: 10, y: 10})\r\n *      .add(el2, {shape: {width: 500}, style: {fill: 'red'}}, 400)\r\n *      .done(function () { // done })\r\n *      .start('cubicOut');\r\n */\nvar AnimationWrap = /** @class */function () {\n  function AnimationWrap() {\n    this._storage = [];\n    this._elExistsMap = {};\n  }\n  /**\r\n   * Caution: a el can only be added once, otherwise 'done'\r\n   * might not be called. This method checks this (by el.id),\r\n   * suppresses adding and returns false when existing el found.\r\n   *\r\n   * @return Whether adding succeeded.\r\n   */\n  AnimationWrap.prototype.add = function (el, target, duration, delay, easing) {\n    if (this._elExistsMap[el.id]) {\n      return false;\n    }\n    this._elExistsMap[el.id] = true;\n    this._storage.push({\n      el: el,\n      target: target,\n      duration: duration,\n      delay: delay,\n      easing: easing\n    });\n    return true;\n  };\n  /**\r\n   * Only execute when animation done/aborted.\r\n   */\n  AnimationWrap.prototype.finished = function (callback) {\n    this._finishedCallback = callback;\n    return this;\n  };\n  /**\r\n   * Will stop exist animation firstly.\r\n   */\n  AnimationWrap.prototype.start = function () {\n    var _this = this;\n    var count = this._storage.length;\n    var checkTerminate = function () {\n      count--;\n      if (count <= 0) {\n        // Guard.\n        _this._storage.length = 0;\n        _this._elExistsMap = {};\n        _this._finishedCallback && _this._finishedCallback();\n      }\n    };\n    for (var i = 0, len = this._storage.length; i < len; i++) {\n      var item = this._storage[i];\n      item.el.animateTo(item.target, {\n        duration: item.duration,\n        delay: item.delay,\n        easing: item.easing,\n        setToFinal: true,\n        done: checkTerminate,\n        aborted: checkTerminate\n      });\n    }\n    return this;\n  };\n  return AnimationWrap;\n}();\nexport function createWrap() {\n  return new AnimationWrap();\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAaA,CAAA,EAAG;IACvB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;EACxB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEF,aAAa,CAACG,SAAS,CAACC,GAAG,GAAG,UAAUC,EAAE,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC3E,IAAI,IAAI,CAACP,YAAY,CAACG,EAAE,CAACK,EAAE,CAAC,EAAE;MAC5B,OAAO,KAAK;IACd;IACA,IAAI,CAACR,YAAY,CAACG,EAAE,CAACK,EAAE,CAAC,GAAG,IAAI;IAC/B,IAAI,CAACT,QAAQ,CAACU,IAAI,CAAC;MACjBN,EAAE,EAAEA,EAAE;MACNC,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,QAAQ;MAClBC,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA;IACV,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACET,aAAa,CAACG,SAAS,CAACS,QAAQ,GAAG,UAAUC,QAAQ,EAAE;IACrD,IAAI,CAACC,iBAAiB,GAAGD,QAAQ;IACjC,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACEb,aAAa,CAACG,SAAS,CAACY,KAAK,GAAG,YAAY;IAC1C,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,KAAK,GAAG,IAAI,CAAChB,QAAQ,CAACiB,MAAM;IAChC,IAAIC,cAAc,GAAG,SAAAA,CAAA,EAAY;MAC/BF,KAAK,EAAE;MACP,IAAIA,KAAK,IAAI,CAAC,EAAE;QACd;QACAD,KAAK,CAACf,QAAQ,CAACiB,MAAM,GAAG,CAAC;QACzBF,KAAK,CAACd,YAAY,GAAG,CAAC,CAAC;QACvBc,KAAK,CAACF,iBAAiB,IAAIE,KAAK,CAACF,iBAAiB,CAAC,CAAC;MACtD;IACF,CAAC;IACD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAACpB,QAAQ,CAACiB,MAAM,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACxD,IAAIE,IAAI,GAAG,IAAI,CAACrB,QAAQ,CAACmB,CAAC,CAAC;MAC3BE,IAAI,CAACjB,EAAE,CAACkB,SAAS,CAACD,IAAI,CAAChB,MAAM,EAAE;QAC7BC,QAAQ,EAAEe,IAAI,CAACf,QAAQ;QACvBC,KAAK,EAAEc,IAAI,CAACd,KAAK;QACjBC,MAAM,EAAEa,IAAI,CAACb,MAAM;QACnBe,UAAU,EAAE,IAAI;QAChBC,IAAI,EAAEN,cAAc;QACpBO,OAAO,EAAEP;MACX,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb,CAAC;EACD,OAAOnB,aAAa;AACtB,CAAC,CAAC,CAAC;AACH,OAAO,SAAS2B,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAI3B,aAAa,CAAC,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}